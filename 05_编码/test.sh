#!/usr/bin/bash

# $0: pcap dir to test.

cd $(dirname $0)

data_dir=/data/pcaps/lite
pkg_dir=/data/release/01_yaEty
install_dir=$(pwd)/../06_测试/test_$(date +%F)
result_dir=$install_dir/test_out

mkdir -p $install_dir
mkdir -p $result_dir

function estimateFileVer
{
    filename=$1
    fileVersion=$(echo $filename | sed "s/.*$filePattern/\1/")

    bVerTest=$(expr $fileVersion \> $newestFileVer)
    if [ $bVerTest -eq 1 ]
    then
       newestFileVer=$fileVersion
       newestFile=$filename
    fi
}

function newestPkg
{
    findDir=$1
    filePattern=$2

    newestFile=""
    newestFileVer=0

    for filename in `find $findDir -regex ".*$filePattern"`
    do
        estimateFileVer $filename
    done

    echo "$newestFile"
}

# check args: pcap data
if [ ${1:-""} == "" ]
then
    echo "args error:need pcap data dir."
    exit
fi

if [ -d $1 ]
	then
		data_dir=$1
        echo "--- pcap data dir: $data_dir"
	else
		echo "+++ invalid data source!"
	exit
fi

# get latest install pkg.
if [ -d $prj_dir ]
then
    latest_pkg=`newestPkg $pkg_dir 'yaEty_pkg_\(.*\)_.*\.tgz'`
    echo "--- latest pkg:$latest_pkg"
else
    echo "+++ no latest_pkg found, exit."
    exit -1														#无可测试数据
fi

# install
program_dir=$install_dir/$(basename ${latest_pkg%.*})
echo "--- program dir:$program_dir"
echo "--- result dir:$result_dir"
if [ -f $latest_pkg -a -n $latest_pkg ]
then
    tar xf $latest_pkg -C $install_dir
    # must sudo, rpm need root privilege.
    sudo $program_dir/install.sh -l >/dev/null
else
    echo "+++ no latest_pkg found, exit."
    exit
fi

# 打开core限制
echo "open core limit ..."
ulimit -c unlimited

# run program test
cd $program_dir
if [ -f $program_dir/yaEty ]
then
    echo "run yaEty:$program_dir/yaEty ..."
    # must sudo, dump need root privilege.
    sudo ./yaEty -r -f $data_dir $result_dir
fi

# check coredump
if [ -f core* ]
then
    echo "+++ running error, found coredump core*!"				#有 coredump 文件
    exit 1
fi

# check writing files
cd $result_dir
writing_res=$(find . -name "*writing")
if [ -z $writing_res ]
then
    echo "--- yaEty tested ok!"
    exit 0
else
    echo "+++ running error, found writing files!"				#有.writing 文件
    exit 2
fi
