#! /usr/bin/sh
# pack yaEty install tar

cd $(dirname $0)

exe_name=yaEty
exe_path=run/$exe_name

service_path=run/yaEty.service
radius_cfg_path=wireshark_2.6.2/radius
diameter_cfg_path=wireshark_2.6.2/diameter

exe_version=$($exe_path --version | sed  -r 's/.*([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+).*/\1/')

pkg_name=yaEty_pkg
pkg_dir=${pkg_name}_${exe_version}_$(date +%m%d)

editcap_path=wireshark_2.6.2/build/run/editcap

cfg_path=../08_发布/yaEty.conf
ieee80211_keys_path=../08_发布/80211_keys
change_log=yaEty/change_log_yaEty.txt
install_sh=install.sh
amrpy=../08_发布/amr.py
dumpcap=../08_发布/dumpcap
g728=../08_发布/g728fp
ffmpeg=../08_发布/ffmpeg
reorder_cap=../08_发布/reordercap_rtp

function estimateFileVer
{
    filename=$1
    fileVersion=$(echo $filename | sed "s/.*$filePattern/\1/")

    if [ $fileVersion -gt $newestFileVer ]
    then
       newestFileVer=$fileVersion
       newestFile=$filename
    fi
}

function newestPkg
{
    findDir=$1
    filePattern=$2

    newestFile=""
    newestFileVer=0

    for filename in `find $findDir -regex ".*$filePattern"`
    do
        estimateFileVer $filename
    done

    echo "$newestFile"
}

# 创建 pkg 临时目录
if [ -d $pkg_dir ]; then
   rm -rf $pkg_dir/*
else
   mkdir -p $pkg_dir
fi

# 获取最新的 yaEty 到 bin 中
cp -v $exe_path $pkg_dir/
# strip $pkg_dir/$exe_name

cp -rvl $radius_cfg_path $pkg_dir/
cp -rvl $diameter_cfg_path $pkg_dir/
cp -f $cfg_path $pkg_dir/
cp -f ${ieee80211_keys_path} $pkg_dir/
cp -f ${amrpy} $pkg_dir/
cp -f ${dumpcap} $pkg_dir/
cp -f ${g728} $pkg_dir/
cp -f ${gffmpeg} $pkg_dir/
cp -f ${greorder_cap} $pkg_dir/
cp -v $change_log $pkg_dir/
cp -v $service_path $pkg_dir/

mkdir $pkg_dir/editcap/
cp -v $editcap_path $pkg_dir/editcap/


# 生成 install.sh
cat <<"END" > $pkg_dir/install.sh
#! /usr/bin/sh

install_dir=/root/program/yaEty
install_local=false
local_dir=$(dirname $0)

# parse opts
while getopts ":lh" opt
 do
    case $opt in
              l ) echo "install to current dir."
                  install_local=true
                  install_dir=$local_dir
                  ;;
              h ) echo "$0 -l : install to current dir."
                  echo "$0 [dir] : install to dir (default is /root/program/yaEty/)"
                  exit 0
                  ;;
              ? ) echo "error opt"
                  exit 1
                  ;;
   esac
done

# 调整 args
shift $(($OPTIND - 1))

# mkdir
if [ ! -z $1 ] ; then
   install_dir=$1
fi

if [ ! -d $install_dir ] ; then
   mkdir $install_dir
fi

# install files
if [ $install_local == false ] ; then
   cp -rv yaEty                $install_dir
   cp -rv radius               $install_dir
   cp -rv diameter             $install_dir
   cp -rv yaEty.conf           $install_dir
   cp -rv 80211_keys           $install_dir
   cp -rv yaEty.service        $install_dir
   cp -rv change_log_yaEty.txt $install_dir
fi

# install yaEty.service
cp -rv yaEty.service /usr/lib/systemd/system/
systemctl daemon-reload

# done
echo "yaEty 可通过 systemctl start yaEty 启动，服务文件位于: /usr/lib/systemd/system/yaEty.service"

END

# 生成 editcap使用方法文档
cat <<"USE" > $pkg_dir/editcap/editcap_use.txt
editcap使用方法
    editcap -c (分割结果文件内的packet帧数目) 源文件路径 分割结果文件路径
USE

chmod 755 $pkg_dir/install.sh
chmod 644 $pkg_dir/editcap/editcap_use.txt

# make tar ball
if [ -e ${pkg_dir}.tgz ]; then
   rm -rf ${pkg_dir}.tgz
fi
tar cfvz ${pkg_dir}.tgz $pkg_dir

# clean
rm -rf ${pkg_dir}

if [ $# -gt 0 ]
then
    mv ${pkg_dir}.tgz $1/
    echo "dir $1 holds the pack result ${pkg_dir}.tgz"
fi

echo "pack done!"
