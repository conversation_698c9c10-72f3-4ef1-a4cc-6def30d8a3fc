#include <string>
#include <map>
#include <stdint.h>
#include <unistd.h>
#include <string.h>
#include "yaEty_target_info.h"
#include "yaEty_config.h"

std::map<std::string, std::map<std::string, std::string>> target_info_map;
std::map<std::string, std::map<std::string, std::string>> target_info_map_tmp;


//从signal5 map获取共性字段信息
int dpi_get_fields_from_signal5_target_map(common_line_info *linfo)
{
    int fields_num, rows_num, i, j; 
    std::string temp;
    if(linfo->fileID == "144")
        temp = "TempID172EWS" + linfo->stationID;
    else if (linfo->fileID == "172")
        temp = "TempID172EN" + linfo->stationID;
    else if (linfo->fileID == "166")
        temp = "TempID166" + linfo->stationID;

    auto find_iter = target_info_map.find(temp);
    if (find_iter ==  target_info_map.end())
        return -1;

    std::map<std::string, std::string>  ele_map = find_iter->second;

    linfo->PTMC = ele_map["PTMC"];
    linfo->SlaveDID = ele_map["SlaveDID"];
    linfo->UserType = ele_map["UserType"];
    linfo->JXH = ele_map["JXH"];
    linfo->TargetCategory = ele_map["TargetCategory"];
    linfo->TempID172EWS = ele_map["TempID172EWS"];
    linfo->TempID172EN = ele_map["TempID172EN"];
    linfo->TempID166 = ele_map["TempID166"];
    return 0;
}

int dpi_get_all_signal5_target_info(PGconn *conn_xhts)
{
    int fields_num, rows_num, i, j; 
    PGresult *res = NULL;
    char sql[2048] = {0};
    snprintf(sql, sizeof(sql)-1, "SELECT \"PTMC\",\"SlaveDID\",\"UserType\",\"JXH\",\"TargetCategory\",\"TempID172EWS\",\"TempID172EN\",\"TempID166\" FROM public.\"signal5_1_target\";");
    res = PQexec(conn_xhts, sql);
    rows_num = PQntuples(res);
    if(rows_num <= 0){
        return 0;
    }
    fields_num = PQnfields(res);
    target_info_map_tmp.clear();
    for(i = 0; i < rows_num; i++){
        std::map<std::string, std::string> ele_map;
        for(j = 0; j < fields_num; j++){
            switch(j){
                case 0:
                    ele_map.emplace("PTMC", PQgetvalue(res, i, j));
                    break;
                case 1:
                    ele_map.emplace("SlaveDID", PQgetvalue(res, i, j));
                    break;
                case 2:
                    ele_map.emplace("UserType", PQgetvalue(res, i, j));
                    break;
                case 3:
                    ele_map.emplace("JXH", PQgetvalue(res, i, j));
                    break;
                case 4:
                    ele_map.emplace("TargetCategory", PQgetvalue(res, i, j));
                    break;
                case 5:
                    ele_map.emplace("TempID172EWS", PQgetvalue(res, i, j));
                    break;
                case 6:
                    ele_map.emplace("TempID172EN", PQgetvalue(res, i, j));
                    break;
                case 7:
                    ele_map.emplace("TempID166", PQgetvalue(res, i, j));
                    break;
                default:
                    break;
            }
        }
        target_info_map_tmp.emplace("TempID172EWS"+ele_map["TempID172EWS"], ele_map);
        target_info_map_tmp.emplace("TempID172EN"+ele_map["TempID172EN"], ele_map);
        target_info_map_tmp.emplace("TempID166"+ele_map["TempID166"], ele_map);
    }
    target_info_map  = target_info_map_tmp;
    PQclear(res);
    return 0;
}

/* public.signal5_1_target信息管理线程 */
void *signal5_target_info_manager_from_db(void *args)
{
    const char *conn_info = (const char *)args;
    char conn_info_xhts[1024] = "user=postgres password=postgres hostaddr=************** port=5432 sslmode=disable dbname=XHTS";
    //char *ptr = strstr((char*)conn_info, (char*)"dbname=");
    //if(ptr){
    //    snprintf(conn_info_xhts, ptr-conn_info+8, "%s", conn_info);
    //    snprintf(conn_info_xhts+strlen(conn_info_xhts), sizeof(conn_info_xhts)-strlen(conn_info_xhts), "XHTS");
    //}
    PGconn *conn_xhts = PQconnectdb(conn_info_xhts);
    do
    {
        if(PQstatus(conn_xhts) != CONNECTION_OK){
            fprintf(stderr, "Signal5 manager connection to database xhts failed: %s\n", PQerrorMessage(conn_xhts));
            PQreset(conn_xhts);
            sleep(5);
            continue;
        }

        dpi_get_all_signal5_target_info(conn_xhts);
        sleep(10);

    } while (1);

    PQfinish(conn_xhts);

    return NULL;
}



GCHFieldsExtractor::GCHFieldsExtractor(const std::string &file_path)
{
    // 获取 linfo 文件名, 格式: file_path + '.linfo'
    // 单进程 和 多进程 对pcap文件rename的时机不一致, 
    // 多进程时, 主进程在扫描时pcap文件就被追加了'.ext'后缀, 需要回滚到源文件名
    if (CFG->GetValueOf<int>("child_proc_num") > 0 && CFG->FilenameRename())
    {
        std::size_t p = file_path.find_last_of('.');
        file_path_    = file_path.substr(0, p) + get_file_ext();
    }
    else //单进程 或 多进程不rename
    {
        file_path_    = file_path + get_file_ext();
    }

    std::string extName(file_path_ + CFG->GetValueOf<const char*>("rename_suffix"));
    memset(buff, 0, sizeof(buff));

    if (fp_ = fopen(file_path_.c_str(), "r"))
    {
        fgets(buff, sizeof(buff), fp_);
    }else if (fp_ = fopen(extName.c_str(), "r"))
    {
        fgets(buff, sizeof(buff), fp_);
    }
    else
    {
        // 写入 n个'|'占位
        for (int i=0; i<get_fields_cnt(); i++)
            buff[i] = '|';
    }
}


GCHFieldsExtractor::~GCHFieldsExtractor()
{
    if (fp_)
    {
        fclose(fp_);
        fp_ = nullptr;

        if (CFG->GetValueOf<bool>("del_pcap_after_process"))
        {
            remove(file_path_.c_str());
        }
        else
        {
            std::string extName(file_path_ + CFG->GetValueOf<const char*>("rename_suffix"));
            rename(file_path_.c_str(), extName.c_str());
        }
    }
}


bool GCHFieldsExtractor::failed()
{
    return fp_ == nullptr;
}


const char* GCHFieldsExtractor::get_value()
{
    return buff;
}
