/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_wxf.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-08-04
* 编    码 : root      '2018-08-04
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include "config.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/dissectors/packet-wxf.h"

#include "yaEty_utils.h"
#include "yaEty_field_extractor_wxf.h"

static bool check_trans_func(funTransfrom_t value)
{
    return (NULL == value) ? false:true;
}

// macro
#define F_FID       transform_field_for_may_occur_bin
#define X_OP_DEFINE_F_D_ITEM(name, strname, type, transFunc, display, desc)                              \
                    F_D_ITEM(#name, "wxf." #name, check_trans_func(transFunc) ? eMT_transform : eMT_direct, "", transFunc),

#define F_LMT       string_format_limit_length(256, 255)

// 根据 tcp 端口判断 C2S
static std::string transform_field_for_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{
    guint32 tcpDstPort = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));

    switch (tcpDstPort)
    {
    case 80:
    case 8080:
    case 443:
        return "C2S";
    }

    return "S2C";
}

static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // RTL tags fields
    F_D_ITEM_RTL_10(),

    // common fields
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    // special fields
    F_D_ITEM("SrcPort"            , "tcp.srcport",          eMT_direct,    "",      NULL),
    F_D_ITEM("DstPort"            , "tcp.dstport",          eMT_direct,    "",      NULL),
    F_D_ITEM("C2S"                , "tcp.dstport",          eMT_transform, "C2S",   transform_field_for_C2S),
    F_D_ITEM("Proto"              , "ip.proto",             eMT_direct,    "",      NULL),
    F_D_ITEM("TTL"                , "ip.ttl",               eMT_direct,    "",      NULL),

    // WXF header fields and unknown fields
    F_D_ITEM("WXF_totalLen"       , "wxf.total_len",        eMT_direct,    "",      NULL),
    F_D_ITEM("WXF_resv1"          , "wxf.resv1",            eMT_direct,    "",      NULL),
    F_D_ITEM("WXF_resv2"          , "wxf.resv2",            eMT_direct,    "",      NULL),
    F_D_ITEM("WXF_resv3"          , "wxf.resv3",            eMT_direct,    "",      NULL),
    F_D_ITEM("WXF_resv4"          , "wxf.resv4",            eMT_direct,    "",      NULL),
    F_D_ITEM("WXF_msgLen"         , "wxf.msg_len",          eMT_direct,    "",      NULL),
    F_D_ITEM("WXF_unknown"        , "wxf.unknown",          eMT_transform, "",      transform_field_for_may_occur_tbl_sep),

    // fields of 'wxf in http'
    F_D_ITEM("Http-Method"        , "http.request.method",  eMT_direct,    "",      NULL),
    F_D_ITEM("Http-Uri"           , "http.request.uri",     eMT_direct,    "",      NULL),
    F_D_ITEM("Http-Version"       , "http.request.version", eMT_direct,    "",      NULL),
    F_D_ITEM("Http-ContentLength" , "http.content_length",  eMT_direct,    "",      NULL),

    // wxf msg fields
    WXF_FIELD_X_LIST(X_OP_DEFINE_F_D_ITEM)
};

ProtoFieldExtractorWxf::ProtoFieldExtractorWxf()
    : ProtoFieldExtractor("WXF", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}
