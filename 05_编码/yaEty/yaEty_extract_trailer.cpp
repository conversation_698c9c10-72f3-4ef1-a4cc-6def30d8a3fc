#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ftypes/ftypes.h"             // for field_info

#include "yaEty_config.h"
#include "yaEty_field_extractor_def.h"
#include "yaEty_field_extractor.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"


trailerType trailerFieldSelector::use_trailer_type = trailerType::noTrailer;

static std::map<std::string,std::string> rt_fields =
{
    { "teid",      "rtl.teid",      },
    { "outer_src", "rtl.outer_src", },
    { "outer_dst", "rtl.outer_dst", },
    { "msisdn",    "rtl.msisdn",    },
    { "imei",      "rtl.imei",      },
    { "imsi",      "rtl.imsi",      },
    { "tac",       "rtl.tac",       },
    { "plmnid",    "rtl.plmnid",    },
    { "uli",       "rtl.uli",       },
    { "basetype",  "rtl.basetype",  },
};

static std::map<std::string,std::string> hw_fields =
{
    { "teid",      "hwl.TEID",      },
    { "outer_src", "",              },
    { "outer_dst", "",              },
    { "msisdn",    "hwl.msisdn",    },
    { "imei",      "hwl.imei",      },
    { "imsi",      "hwl.imsi",      },
    { "tac",       "",              },
    { "plmnid",    "",              },
    { "uli",       "",              },
    { "basetype",  "",              },
};

static std::map<std::string,std::string> jsc_fields =
{
    { "teid",      "jscl.meid",     },
    { "outer_src", "",              },
    { "outer_dst", "",              },
    { "msisdn",    "jscl.msisdn",   },
    { "imei",      "jscl.imei",     },
    { "imsi",      "jscl.imsi",     },
    { "tac",       "",              },
    { "plmnid",    "",              },
    { "uli",       "",              },
    { "basetype",  "",              },
};

static const char* line_470_map_type[] = 
{ 
    "0",
    "GFD",
    "HDLC",
    "EOS",
    "ATM"
};

static const char* line_470_concatenate_type[] = 
{
    "0",
    "VC4_Xc",
    "VC4_Xv",
    "VC3_Xv",
    "VC2_Xv",
    "VC12_Xv",
    "VC11_Xv"
};

static const char* line_470_signal_no[] = 
{
    "0",
    "VC4_Xv",
    "VC3_Xv",
    "VC2_Xv",
    "VC12_Xv",
    "VC11_Xv"
};

std::vector<std::map<std::string,std::string>> trailerFieldSelector::trailer_fields = {
    //rt_fields, hw_fields, jsc_fields
    [trailerType::rtTrailer] = rt_fields,
    [trailerType::hwTrailer] = hw_fields,
    [trailerType::jscTrailer] = jsc_fields,
};

trailerFieldSelector::trailerFieldSelector(const char *field_name):_field_name(field_name)
{
}

const char * trailerFieldSelector::operator()(void)
{
    if (use_trailer_type == trailerType::noTrailer)
    {
        if (strBeginwith(CFG->GetTrailerType(), "hw"))
        {
            use_trailer_type = trailerType::hwTrailer;
        }
        else if (strBeginwith(CFG->GetTrailerType(), "rt"))
        {
            use_trailer_type = trailerType::rtTrailer;
        }
        else if (strBeginwith(CFG->GetTrailerType(), "jsc"))
        {
            use_trailer_type = trailerType::jscTrailer;
        }
    }
    if (use_trailer_type != trailerType::noTrailer)
    {
        return trailer_fields[use_trailer_type][_field_name].c_str();
    }
    else
    {
        return "";
    }
    
    // else
    // {
    //     if (use_trailer_type == trailerType::jscTrailer)
    //     {
    //         field_info * pfinfo = get_first_field_info_from_interesting_fields(edt, trailer_fields[use_trailer_type][_field_name].c_str());

    //         std::string raw_value = std::string(pfinfo->rep->representation);
            
    //         std::size_t val_start = raw_value.find_first_of(':') + 2;
    //         return val_start != std::string::npos ? raw_value.substr( val_start, raw_value.length() - val_start ): "";
    //     }
    //     else
    //     {
    //         return get_first_field_value_from_interesting_fields(edt, trailer_fields[use_trailer_type][_field_name].c_str());
    //     }
        
    // }
    
}

transform_trailer_choose_field::transform_trailer_choose_field()
{
}

std::string transform_trailer_choose_field::operator()(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (!(nullptr != edt && nullptr != pFinfo))
    {
        return "";
    }

    if (pFinfo->hfinfo->type == FT_BYTES)
    {
        std::string raw_value = std::string(pFinfo->rep->representation);
    
        std::size_t val_start = raw_value.find_first_of(':') + 2;

        return val_start != std::string::npos ? raw_value.substr( val_start, raw_value.length() - val_start ): "";
    }
    else
    {
        return ety_ws_get_node_field_value(const_cast<field_info *>(pFinfo), edt);
    }
}

::std::string transform_mac_for_lineno(epan_dissect_t *edt, const field_info *pFinfo)
{
    char linenumber[64] = {0};
    if (nullptr == pFinfo || pFinfo->length == 0) {
        return "";
    }

    uint8_t * macdata = (uint8_t *)fvalue_get(const_cast<fvalue_t *>(&pFinfo->value));

    uint8_t array0 = macdata[0];
    uint8_t array1 = macdata[1];
    snprintf(linenumber, 63, "%i-%s-%s-%s-%hhu-%hhu-%hhu" , array0 & 0x07,
             line_470_map_type[((array0 >> 3) & 0x07) > 4 ? 0 : (array0 >> 3) & 0x07],
             line_470_concatenate_type[(array1 & 0x07) > 6 ? 0 : array1 & 0x07],
             line_470_signal_no[(array1 >> 3) > 5 ? 0 : array1 >> 3], 
             macdata[2],
             macdata[3],
             macdata[4]);
        

    return linenumber[0] == '\0'? "": std::string(linenumber);
}

::std::string transform_mac_for_lineno_le(epan_dissect_t *edt, const field_info *pFinfo)
{
    char linenumber[64] = {0};
    if (nullptr == pFinfo || pFinfo->length == 0) {
        return "";
    }

    uint8_t * macdata = (uint8_t *)fvalue_get(const_cast<fvalue_t *>(&pFinfo->value));

    uint8_t array0 = macdata[5];
    uint8_t array1 = macdata[4];
    snprintf(linenumber, 63, "%i-%s-%s-%s-%hhu-%hhu-%hhu" , array0 & 0x07,
             line_470_map_type[((array0 >> 3) & 0x07) > 4 ? 0 : (array0 >> 3) & 0x07],
             line_470_concatenate_type[(array1 & 0x07) > 6 ? 0 : array1 & 0x07],
             line_470_signal_no[(array1 >> 3) > 5 ? 0 : array1 >> 3], 
             macdata[3],
             macdata[2],
             macdata[1]);
        

    return linenumber[0] == '\0'? "": std::string(linenumber);
}

::std::string transform_null_lineno(epan_dissect_t *edt, const field_info *pFinfo)
{
    return "";
}