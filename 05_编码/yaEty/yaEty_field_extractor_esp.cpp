/****************************************************************************************
 * 文 件 名 : yaEty_proto_field_desc_http.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : liugh      '2018-06-11'
* 编    码 : liugh      '2018-06-11'
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include <algorithm>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_esp.h"
#include "yaEty_content_reassembly_share_mem.h"

static std::string transform_field_for_esp_response(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (fvalue_type_ftenum(const_cast<fvalue_t *>(&pFinfo->value)) != FT_BOOLEAN)
    {
        return "error";
    }

    return fvalue_get_uinteger64(const_cast<fvalue_t *>(&pFinfo->value)) ? "S2C" : "C2S";
}


#define F_D_ITEM_SPI_PKT(count,NO, mapType)                                                                     \
    F_D_ITEM("SPI_" count "_" NO                , NULL,                       mapType,     "",      NULL),      \
    F_D_ITEM("SPIPkts_" count "_" NO            , NULL,                       mapType,     "",      NULL)

#define F_D_ITEM_SPI_PKT_5(count, a, b, c, d, e, mapType)   \
        F_D_ITEM_SPI_PKT(count, a, mapType),                \
        F_D_ITEM_SPI_PKT(count, b, mapType),                \
        F_D_ITEM_SPI_PKT(count, c, mapType),                \
        F_D_ITEM_SPI_PKT(count, d, mapType),                \
        F_D_ITEM_SPI_PKT(count, e, mapType)

#define F_D_ITEM_SPI_PKT_10(count, a, b, c, d, e, f, g, h, i, j, mapType)   \
        F_D_ITEM_SPI_PKT_5(count, a, b, c, d, e, mapType),                  \
        F_D_ITEM_SPI_PKT_5(count, f, g, h, i, j, mapType)


static ProtoFieldDesc ms_protoFieldDescArray[] =
{

    // RTL tags fields
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort",                 "tcp.srcport",                eMT_direct,        "",                NULL),
    F_D_ITEM("DstPort",                 "tcp.dstport",                eMT_direct,        "",                NULL),
    F_D_ITEM("C2S",                     "ftp.response",               eMT_transform,     "C2S",             transform_field_for_esp_response),
    F_D_ITEM("Proto",                   "ip.proto",                   eMT_direct,        "",                NULL),
    F_D_ITEM("TTL",                     "ip.ttl",                     eMT_direct | eMT_lastplain,        "",NULL),


    F_D_ITEM("SPI_COUNT_0",             NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("SPI_0_0",                 NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("SPIPkts_0_0",             NULL,                         eMT_fixed,         "",                NULL),

    F_D_ITEM_SPI_PKT_10("0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", eMT_fixed),
    F_D_ITEM_SPI_PKT_5("0", "11", "12", "13", "14", "15", eMT_fixed),

    F_D_ITEM("SPI_COUNT_1",             NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("SPI_1_0",                 NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("SPIPkts_1_0",             NULL,                         eMT_fixed,         "",                NULL),

    F_D_ITEM_SPI_PKT_10("1", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", eMT_fixed),
    F_D_ITEM_SPI_PKT_5("1", "11", "12", "13", "14", "15", eMT_fixed),

};


ProtoFieldExtractorEsp::ProtoFieldExtractorEsp()
    : ProtoFieldExtractor("ESP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}


bool ProtoFieldExtractorEsp::ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter)
{
    ProtoFieldDesc *pFieldDescSPI = NULL;
  //ProtoFieldDesc *pFieldDescV   = NULL;
    field_info     *finfo         = NULL;
    GPtrArray      *finfos        = NULL;
    gchar          *pWsFieldValue = NULL;
    const gchar    *pSPI          = NULL;
    std::string     strPKT;
    int             field_id      = 0;
    int             finfos_cnt    = 0;
    int             fields_cnt    = 0;

    // 继续输出后续的 spi spi_pkt, 尽管可能它们都为空
    fields_cnt = this->GetFieldsCount();
    for (int i = nFieldIndexToExtract; i < fields_cnt; i++)
    {
        pFieldDescSPI = this->GetProtoFieldDescByIndex(i);
        pSPI = pFieldDescSPI->defaultFieldValue;

        // 进行可能地转换
        if (ProtoFieldDesc_GetType(pFieldDescSPI) == eMT_fromEdt
            && pFieldDescSPI->funTransfrom != NULL)
        {
            strPKT = pFieldDescSPI->funTransfrom(edt, NULL);
            strPKT = !strPKT.empty() ? strPKT : pFieldDescSPI->defaultFieldValue;
        }

#if 1   // 解析中暂时用不到 eMT_direct
        if (ProtoFieldDesc_GetType(pFieldDescSPI) == eMT_direct)
        {
            strPKT = get_first_field_value_from_interesting_fields(edt, pFieldDescSPI->wsFieldName);
        }
#endif
        // 输出
        pWriter->writeRecordField(pFieldDescSPI->etyFieldName, strPKT);
    }

    nFieldIndexToExtract = 0;

    return TRUE;
}
