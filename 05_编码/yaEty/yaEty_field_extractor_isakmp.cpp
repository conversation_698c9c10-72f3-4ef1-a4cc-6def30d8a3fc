/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_isakmp.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-11-20
* 编    码 : zhangsx      '2018-11-20
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include"config.h"
#include"epan/epan_dissect.h"
#include"yaEty_utils.h"
#include"yaEty_ws_utils.h"
#include"yaEty_rec_writer_tbl.h"
#include"yaEty_field_extractor.h"
#include"yaEty_field_extractor_isakmp.h"
#include"yaEty_content_reassembly_share_mem.h"

#define ISAKMP_PRO_FIELD(NO) \
	F_D_ITEM("Proposal" #NO "_DOI"				, "isakmp.sa.doi"						, eMT_direct,		"",		NULL),\
    F_D_ITEM("Proposal" #NO "_transformNum"		, ""								    , eMT_direct,		"",		NULL),\
    F_D_ITEM("Proposal" #NO "_transforms"		, ""								    , eMT_direct,		"",		NULL)
	
#define ISAKMP_PRO_GROUP(a,b,c,d,e)	\
	ISAKMP_PRO_FIELD(a),			\
	ISAKMP_PRO_FIELD(b),			\
	ISAKMP_PRO_FIELD(c),			\
	ISAKMP_PRO_FIELD(d),			\
	ISAKMP_PRO_FIELD(e)
	
static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort"						, "tcp.srcport"						    , eMT_direct,		"",		NULL),
    F_D_ITEM("DstPort"						, "tcp.dstport"						    , eMT_direct,		"",		NULL),
    F_D_ITEM("C2S"							, ""								    , eMT_fixed,		"",		NULL),
    F_D_ITEM("Proto"						, "ip.proto"						    , eMT_direct,		"",		NULL),
    F_D_ITEM("TTL"							, "ip.ttl"							    , eMT_direct,		"",		NULL),
    F_D_ITEM("Version"						, "isakmp.version"					    , eMT_direct,		"",		NULL),
    F_D_ITEM("InitiatorCookie"				, "isakmp.ispi"							, eMT_direct,		"",		NULL),
    F_D_ITEM("ResponderCookie"				, "isakmp.rspi"							, eMT_direct,		"",		NULL),
    F_D_ITEM("ExchangeType"					, "isakmp.exchangetype"				    , eMT_direct,		"",		NULL),
    F_D_ITEM("MessageID"					, "isakmp.messageid"				    , eMT_direct,		"",		NULL),
    F_D_ITEM("ProposalNum"					, ""								    , eMT_direct,		"",		NULL),
    ISAKMP_PRO_GROUP(1,2,3,4,5),
    ISAKMP_PRO_GROUP(6,7,8,9,10),
    ISAKMP_PRO_GROUP(11,12,13,14,15),
    ISAKMP_PRO_GROUP(16,17,18,19,20),
    F_D_ITEM("VendorID"						, ""				    				, eMT_fromEdt,		"",		choose_for_two_type_field("isakmp.vid_bytes","") ),
    F_D_ITEM("IKE2_KE_DiffieHellmanGroup"	, ""								    , eMT_direct,		"",		NULL),
    F_D_ITEM("KE_key_len"					, "isakmp.ike2.attr.key_length"	    	, eMT_direct,		"",		NULL),
    F_D_ITEM("KE"							, "isakmp.key_exchange.data"		    , eMT_direct,		"",		NULL),
    F_D_ITEM("Nonce_len"					, ""								    , eMT_transform,    "",		NULL),
    F_D_ITEM("Nounce"						, ""    								, eMT_fromEdt,		"",		choose_for_two_type_field("isakmp.nonce","isakmp.notify.data.ha.nonce_data") ),
};

ProtoFieldExtractorIsakmp::ProtoFieldExtractorIsakmp():ProtoFieldExtractor("ISAKMP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}
