/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_radius.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-14
* 编    码 : licl         '2018-06-21
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <iconv.h>

#include <algorithm>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_sip.h"
#include "yaEty_content_reassembly_share_mem.h"


/*****************************************************************
*Function    :transform_field_for_C2S
*Description :Radius C2S
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_field_for_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{
    return "C2S";
}

static std::string transform_field_for_sip_file_content(epan_dissect_t *edt, const field_info *pFinfo)
{
    field_info   *finfo     = NULL;
    voip_data    *voip_file = NULL;
    PktInfo       ptf;

    // 从 edt 中收集五元组信息
    finfo = get_first_field_info_from_interesting_fields(edt, "ip.src");
    if(finfo==NULL){return "";}
    ptf.src_ip = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.dst");
    if(finfo==NULL){return "";}
    ptf.dst_ip = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.proto");
    if(finfo==NULL){return "";}
    ptf.proto = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));
    
    
    if(ptf.proto==17){
        finfo = get_first_field_info_from_interesting_fields(edt, "udp.srcport");
    }else if(ptf.proto==6){
        finfo = get_first_field_info_from_interesting_fields(edt, "tcp.srcport");
    }else{
        return "";
    }
    if(NULL==finfo){return "";}
    ptf.src_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    if(ptf.proto==17){
        finfo = get_first_field_info_from_interesting_fields(edt, "udp.dstport");
    }else if(ptf.proto==6){
        finfo = get_first_field_info_from_interesting_fields(edt, "tcp.dstport");
    }else{
        return "";
    }
    if(NULL==finfo){return "";}
    ptf.dst_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));


    // hash 获取其还原出的内容文件 path list.
    voip_file = (voip_data *)DispGetHashdb(ptf, MEM_TYPE_VOIP_DATA);
    if (NULL == voip_file)
    {
        return "";
    }

    std::string strJsonFileList;

    // 输出为 json 数组
    Json::Value jArrayPath;
    Json::StreamWriterBuilder jBuilder;
    jBuilder["commentStyle"] = "None";
    jBuilder["indentation"]  = "";

    for(int i = 0; i < voip_file->voip_number; i++){
         jArrayPath.append(Json::Value(voip_file->voip_gather[i].path));
    }

    strJsonFileList = Json::writeString(jBuilder, jArrayPath);
    return strJsonFileList;
}


static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // 10 个 RTL 标签头
    F_D_ITEM_RTL_10(),

    // 27 个通用字段头
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    /* SIP SDP 字段 */
    F_D_ITEM("SrcPort"                       , "tcp.srcport"                        , eMT_direct,    "",      NULL),
    F_D_ITEM("DstPort"                       , "tcp.dstport"                        , eMT_direct,    "",      NULL),
    //F_D_ITEM("C2S"                           , "frame.number" /* Debug */           , eMT_direct,    "",      NULL),
    F_D_ITEM("C2S"                           , "sip.Request-Line"                   , eMT_transform, "S2C",   transform_field_for_C2S),
    F_D_ITEM("Proto"                         , "ip.proto"                           , eMT_direct,    "",      NULL),
    F_D_ITEM("TTL"                           , "ip.ttl"                             , eMT_direct,    "",      NULL),
    F_D_ITEM("sip_method"                    , "sip.Method"                         , eMT_direct,    "",      NULL),
    F_D_ITEM("sip_version"                   , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("req_uri"                       , "sip.r-uri"                          , eMT_direct,    "",      NULL),
    F_D_ITEM("Via"                           , "sip.Via"                            , eMT_direct,    "",      NULL),
    F_D_ITEM("From"                          , "sip.From"                           , eMT_direct,    "",      NULL),
    F_D_ITEM("To"                            , "sip.To"                             , eMT_direct,    "",      NULL),
    F_D_ITEM("Call-ID"                       , "sip.Call-ID"                        , eMT_direct,    "",      NULL),
    F_D_ITEM("Call-Info"                     , "sip.Call-Info"                      , eMT_direct,    "",      NULL),
    F_D_ITEM("CSeq"                          , "sip.CSeq"                           , eMT_direct,    "",      NULL),
    F_D_ITEM("Contact"                       , "sip.Contact"                        , eMT_direct,    "",      NULL),
    F_D_ITEM("Allow"                         , "sip.Allow"                          , eMT_direct,    "",      NULL),
    F_D_ITEM("status_code"                   , "sip.Status-Code"                    , eMT_direct,    "",      NULL),
    F_D_ITEM("max-forwards"                  , "sip.Max-Forwards"                   , eMT_direct,    "",      NULL),
    F_D_ITEM("user-agent"                    , "sip.User-Agent"                     , eMT_direct,    "",      NULL),
    F_D_ITEM("date"                          , "sip.Date"                           , eMT_direct,    "",      NULL),
    F_D_ITEM("supported"                     , "sip.Supported"                      , eMT_direct,    "",      NULL),
    F_D_ITEM("expires"                       , "sip.Expires"                        , eMT_direct,    "",      NULL),
    F_D_ITEM("reason_phrase"                 , "sip.Reason-Phrase"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("server"                        , "sip.Server"                         , eMT_direct,    "",      NULL),
    F_D_ITEM("allow-events"                  , "sip.Allow-Events"                   , eMT_direct,    "",      NULL),
    F_D_ITEM("Record-Route"                  , "sip.Record-Route"                   , eMT_direct,    "",      NULL),
    F_D_ITEM("Content-Type"                  , "sip.Content-Type"                   , eMT_direct,    "",      NULL),
    F_D_ITEM("content-length"                , "sip.Content-Length"                 , eMT_direct,    "",      NULL),
    F_D_ITEM("Content-Encoding"              , "sip.Content-Encoding"               , eMT_direct,    "",      NULL),
    F_D_ITEM("content-disposition"           , "sip.Content-Disposition"            , eMT_direct,    "",      NULL),
    F_D_ITEM("Accept"                        , "sip.Accept"                         , eMT_direct,    "",      NULL),
    F_D_ITEM("Accept-Language"               , "sip.Accept-Language"                , eMT_direct,    "",      NULL),
    F_D_ITEM("Accept-Encoding"               , "sip.Accept-Encoding"                , eMT_direct,    "",      NULL),
    F_D_ITEM("Authorization"                 , "sip.Authorization"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("Proxy-Authorization"           , "sip.Proxy-Authenticate"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Route"                         , "sip.Route"                          , eMT_direct,    "",      NULL),
    F_D_ITEM("require"                       , "sip.Require"                        , eMT_direct,    "",      NULL),
    F_D_ITEM("reason"                        , "sip.Reason"                         , eMT_direct,    "",      NULL),
    F_D_ITEM("nat-proxy"                     , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("Proxy-Authenticate"            , "sip.Proxy-Authenticate"             , eMT_direct,    "",      NULL),
    F_D_ITEM("WWW-Authenticate"              , "sip.WWW-Authenticate"               , eMT_direct,    "",      NULL),
    F_D_ITEM("event"                         , "sip.Event"                          , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_v_version"                 , "sdp.version"                        , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_o_username"                , "sdp.owner.username"                 , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_o_sess_id"                 , "sdp.owner.sessionid"                , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_o_sess_version"            , "sdp.owner.version"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_o_nettype"                 , "sdp.owner.network_type"             , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_o_addrtype"                , "sdp.owner.address_type"             , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_o_addr"                    , "sdp.owner.address"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_s_name"                    , "sdp.session_name"                   , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_c_nettype"                 , "sdp.connection_info.network_type"   , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_c_addrtype"                , "sdp.connection_info.address_type"   , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_c_addr"                    , "sdp.connection_info.address"        , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_i_info"                    , "sdp.session_info"                   , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_b_bandwidths"              , "sdp.bandwidth"                      , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_t_start_time0"             , "sdp.time.start"                     , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_t_stop_time0"              , "sdp.time.stop"                      , eMT_direct,    "",      NULL),

    F_D_ITEM("sdp_m_media0"                  , "sdp.media.media"                    , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_m_port0"                   , "sdp.media.port"                     , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_m_proto0"                  , "dp.media.proto"                     , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_m_payloads0"               , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("sdp_c_connections0"            , "sdp.connection_info"                , eMT_direct,    "",      NULL),
    F_D_ITEM("sdp_a_attributes0"             , NULL                                 , eMT_fixed,     "",      NULL),

    F_D_ITEM("sdp_m_media1"                  , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("sdp_m_port1"                   , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("sdp_m_proto1"                  , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("sdp_m_payloads1"               , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("sdp_c_connections1"            , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("sdp_a_attributes1"             , NULL                                 , eMT_fixed,     "",      NULL),

    F_D_ITEM("timestamp"                     , "sip.Timestamp"                      , eMT_direct,    "",      NULL),
    F_D_ITEM("rseq"                          , "sip.RSeq"                           , eMT_direct,    "",      NULL),
    F_D_ITEM("subscription-state"            , "sip.Subscription-State"             , eMT_direct,    "",      NULL),
    F_D_ITEM("remote-party-id"               , "sip.Session-ID.remote_uuid"         , eMT_direct,    "",      NULL),
    F_D_ITEM("min-se"                        , "sip.Min-SE"                         , eMT_direct,    "",      NULL),
    F_D_ITEM("cisco-guid"                    , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("p-station-name"                , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("p-hint"                        , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("p-rtp-stat"                    , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("x-rtp-stat"                    , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("x-status"                      , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("x-id"                          , NULL                                 , eMT_fromEdt,   "",      transform_field_for_sip_file_content),
    F_D_ITEM("x-web"                         , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("x-tag"                         , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("x-asterisk-hangupcause"        , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("x-asterisk-hangupcausecode"    , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("x-replace"                     , NULL                                 , eMT_fixed,     "",      NULL),
    F_D_ITEM("warning"                       , NULL                                 , eMT_fixed,     "",      NULL),
    
};

ProtoFieldExtractorSip::ProtoFieldExtractorSip():ProtoFieldExtractor("SIP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}
