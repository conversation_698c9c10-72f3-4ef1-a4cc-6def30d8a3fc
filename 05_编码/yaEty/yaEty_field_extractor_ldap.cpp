/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_ldap.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-11-20
* 编    码 : zhangsx      '2018-11-20
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include"config.h"
#include"epan/epan_dissect.h"

#include"yaEty_utils.h"
#include"yaEty_ws_utils.h"
#include"yaEty_rec_writer_tbl.h"
#include"yaEty_field_extractor.h"
#include"yaEty_field_extractor_ldap.h"
#include"yaEty_content_reassembly_share_mem.h"

static std::string transform_for_ldap_filter(epan_dissect_t *edt, const field_info *pFinfo)
{
    std::string rst = "";
    field_info * p_dstFinfo = nullptr;
    
    if (!pFinfo->pnode->next)
    {
        return "";
    }
    else
    {
        p_dstFinfo = pFinfo->pnode->next->finfo;
        rst += ety_ws_get_node_field_value(p_dstFinfo, edt);
    }
    return rst;
}

static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort"                      , "tcp.srcport"                     , eMT_direct,       "",     NULL),
    F_D_ITEM("DstPort"                      , "tcp.dstport"                     , eMT_direct,       "",     NULL),
    F_D_ITEM("C2S"                          , ""                                , eMT_direct,       "",     NULL),
    F_D_ITEM("Proto"                        , "ip.proto"                        , eMT_direct,       "",     NULL),
    F_D_ITEM("TTL"                          , "ip.ttl"                          , eMT_direct,       "",     NULL),
    F_D_ITEM("MessageID"                    , "ldap.messageID"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("ProtocolOp"                   , "ldap.protocolOp"                 , eMT_direct,       "",     NULL),
    F_D_ITEM("BindRequest_Version"          , "ldap.version"                    , eMT_direct,       "",     NULL),
    F_D_ITEM("BindRequest_name"             , "ldap.name"                       , eMT_direct,       "",     NULL),
    F_D_ITEM("authenticationType"           , "ldap.authentication"             , eMT_direct,       "",     NULL),
    F_D_ITEM("srchResEty_objName"           , "ldap.objectname"                 , eMT_direct,       "",     NULL),
    F_D_ITEM("srchResEty_attriNum"          , "ldap.attributes"                 , eMT_direct,       "",     NULL),
    F_D_ITEM("baseObject"                   , "ldap.baseObject"                 , eMT_direct,       "",     NULL),
    F_D_ITEM("scope"                        , "ldap.scope"                      , eMT_direct,       "",     NULL),
    F_D_ITEM("derefAliases"                 , "ldap.derefAliases"               , eMT_direct,       "",     NULL),
    F_D_ITEM("sizeLimit"                    , "ldap.sizeLimit"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("timeLimit"                    , "ldap.timeLimit"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("typeOnly"                     , "ldap.typesOnly"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("SearchResDone_rltCode"        , "ldap.resultCode"                 , eMT_direct,       "",     NULL),
    F_D_ITEM("SearchResDone_matchedDN"      , "ldap.matchedDN"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("SearchResDone_errorMsg"       , "ldap.errorMessage"               , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_Opcode"              , "mscldap.netlogon.opcode"         , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_Version"             , "mscldap.ntver.flags"             , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_Flags"               , "mscldap.netlogon.flags"          , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_DomainGUID"          , "mscldap.domain.guid"             , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_Forest"              , "mscldap.forest"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_Domain"              , "mscldap.domain"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_Hostname"            , "mscldap.hostname"                , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_NetBIOSDomain"       , "mscldap.nb_domain"               , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_NetBIOSHostname"     , "mscldap.nb_hostname"             , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_User"                , "mscldap.username"                , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_ServerSite"          , "mscldap.sitename"                , eMT_direct,       "",     NULL),
    F_D_ITEM("netlogon_ClientSite"          , "mscldap.clientsitename"          , eMT_direct,       "",     NULL),
    F_D_ITEM("FilterGroupType"              , "ldap.filter"                     , eMT_direct,       "",     NULL),
    F_D_ITEM("Filter"                       , "ldap.filter"                     , eMT_transform,    "",     transform_for_ldap_filter),
    F_D_ITEM("referenceURL"                 , "ldap.LDAPURL"                    , eMT_direct,       "",     NULL),
    F_D_ITEM("AttributesJson"               , "ldap.attributes"                 , eMT_direct,       "",     NULL),
};

ProtoFieldExtractorLdap::ProtoFieldExtractorLdap():ProtoFieldExtractor("LDAP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}
