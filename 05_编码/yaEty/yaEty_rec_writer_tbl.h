/****************************************************************************************
 * 文 件 名 : rtxdr_tlv_writer.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-03-16
* 编    码 : root      '2018-03-16
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _YAETY_REC_WRITER_TBL_H_
#define _YAETY_REC_WRITER_TBL_H_

#include <stdlib.h>
#include <stdio.h>
#include <linux/limits.h>

#include <map>
#include <string>

#include "yaEty_config.h"
#include "yaEty_rec_writer.h"

constexpr static const int   RTXDR_RECORD_CNT_PERFILE = 2000;
constexpr static const char *TBL_WRITING_SUFFIX       = ".writing";

class IProtoFieldExtractor;
class TblRecordWriter : public RecordWriter
{
public:
    TblRecordWriter(const std::string &strProtoName,
                    const std::string &strTblProtoName,
                    const std::string &strFileDirPath = "",
                    int lRecordCntPerFile = CFG->GetTblCountLimit(),/*RTXDR_RECORD_CNT_PERFILE */
                    int lThreadNO = 42);

    ~TblRecordWriter();

public:
    static void Register();

    // 输出所有支持协议的 fields 字段表文件
    static int  WriteTblFields(const std::string &strTblFieldsDir);
    static int  WriteTblsDone();
    static void  updateInMapTblTaskID();
    

public: // interface
    int   writeTblField(const std::string &strTblFieldsDir, IProtoFieldExtractor *pExtractor);
    int   writeRecordField(const char *strFieldName, const char *strFieldValue);
    int   writeRecordField(const char *strFieldName, const std::string &strFieldValue);
    int   writeRawFields(const char *fieldsValue, int field_cnt);
    int   writeNextRecord();
    void  writeTblDone();
    void renameAllTmpTbl();
    
    inline int   getWriten()
    {
        return lRecordCntWriten_;
    }

private:
    static int  RegisterWriter(const std::string &strProtoName, TblRecordWriter *pWriter);
    static int  UnregisterWriter(const std::string &strProtoName);
    static void WriteAfterTime(int sig);
    static int  AlarmWriteTbls(const uint32_t interval);

    void updateTblTaskID();
private:
    void writeCurrentFileDone();
    void generateFileName();
    int  createTblFile();
    void insertTmpTbl(char*);
private:
    int getThreadId();

private:
    std::string  strTblFileDir_;
    std::string  strProtoName_;
    std::string  strTblProtoName_;
    std::string  strTblTaskID_ = "";
    char         fileNameBuf_[PATH_MAX] = {0};
    char         fileNameBufWriting_[PATH_MAX] = {0};
    FILE        *tblFile_          = NULL;
    int          lRecordCntWriten_ = 0;
    int          lRecordCntPerFile_;
    int          lThreadNO_;
    int          lFieldsWritenCntForThisRecord_ = 0;
    std::vector<std::string> tmp_tbl_v_;
private:
    IProtoFieldExtractor *pFieldExtractor_ = NULL;

private:
    static std::map<std::string, TblRecordWriter *> cs_mapTblWriter;
    static uint32_t cs_timeOutLimits_;
};

#endif /* _YAETY_REC_WRITER_TBL_H_ */
