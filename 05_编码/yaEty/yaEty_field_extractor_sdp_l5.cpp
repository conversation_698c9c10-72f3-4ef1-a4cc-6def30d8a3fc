#include"config.h"
#include"epan/epan_dissect.h"
#include"yaEty_utils.h"
#include"yaEty_ws_utils.h"
#include"yaEty_rec_writer_tbl.h"
#include"yaEty_field_extractor.h"
#include"yaEty_field_extractor_sdp_l5.h"
#include"yaEty_content_reassembly_share_mem.h"


struct transform_for_sdp_field
{
public:
    transform_for_sdp_field(int id, const char * fieldname)
        : _index(id), _field_name(fieldname)
    {
    }
    std::string operator()(epan_dissect * edt, const field_info * pFinfo)
    {
        field_info * pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _index);

        return pfinfo ? ety_ws_get_node_field_value(pfinfo, edt) : "";
    }
public:
    uint8_t _index;
    const char* _field_name;
};

struct transform_for_sdp_field_extern : public transform_for_sdp_field
{
public:
    transform_for_sdp_field_extern(int group_id, int id, const char * fieldname)
    :_group_id(group_id), transform_for_sdp_field(id,fieldname)
    {
    }
    std::string operator()(epan_dissect * edt, const field_info * pFinfo)
    {
        field_info * pfinfo = get_nth_field_info_from_interesting_fields(edt, "sdp.media", _group_id);
        
        if (nullptr == pfinfo)
        {
            return "";
        }

        pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _index);

        return pfinfo ? ety_ws_get_node_field_value(pfinfo, edt) : "";
    }
public:
    uint8_t _group_id;
};


#define SDP_MEDIA_ATTR(NO,a,b,c,d)     \
    F_D_ITEM("media_attr_" #NO "_"#a                        , ""                            , eMT_fromEdt,      "",     transform_for_sdp_field_extern((NO), a, "sdp.media_attr")),\
    F_D_ITEM("media_attr_" #NO "_"#b                        , ""                            , eMT_fromEdt,      "",     transform_for_sdp_field_extern((NO), b, "sdp.media_attr")),\
    F_D_ITEM("media_attr_" #NO "_"#c                        , ""                            , eMT_fromEdt,      "",     transform_for_sdp_field_extern((NO), c, "sdp.media_attr")),\
    F_D_ITEM("media_attr_" #NO "_"#d                        , ""                            , eMT_fromEdt,      "",     transform_for_sdp_field_extern((NO), d, "sdp.media_attr"))


static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort"                              , ""                                , eMT_direct,       "",     NULL),
    F_D_ITEM("DstPort"                              , ""                                , eMT_direct,       "",     NULL),
    F_D_ITEM("C2S"                                  , ""                                , eMT_direct,       "",     NULL),
    F_D_ITEM("Proto"                                , "ip.proto"                        , eMT_direct,       "",     NULL),
    F_D_ITEM("TTL"                                  , "ip.ttl"                          , eMT_direct,       "",     NULL),
    F_D_ITEM("Owner"                                , "sdp.owner"                       , eMT_direct,       "",     NULL),
    F_D_ITEM("session_name"                         , "sdp.session_name"                , eMT_direct,       "",     NULL),
    F_D_ITEM("time"                                 , "sdp.time"                        , eMT_direct,       "",     NULL),
    F_D_ITEM("connection_info_00"                   , ""                                , eMT_fromEdt,      "",     transform_for_sdp_field(0, "sdp.connection_info")),
    F_D_ITEM("connection_info_01"                   , ""                                , eMT_fromEdt,      "",     transform_for_sdp_field(1, "sdp.connection_info")),
    F_D_ITEM("media_00"                             , ""                                , eMT_fromEdt,      "",     transform_for_sdp_field(0, "sdp.media")),
    SDP_MEDIA_ATTR(00, 0,  1,  2,  3),
    SDP_MEDIA_ATTR(00, 4,  5,  6,  7),
    SDP_MEDIA_ATTR(00, 8,  9,  10, 11),
    SDP_MEDIA_ATTR(00, 12, 13, 14, 15),
    F_D_ITEM("media_01"                             , ""                                , eMT_fromEdt,      "",     transform_for_sdp_field(1,"sdp.media")),
    SDP_MEDIA_ATTR(01, 0,  1,  2,  3),
    SDP_MEDIA_ATTR(01, 4,  5,  6,  7),
    SDP_MEDIA_ATTR(01, 8,  9,  10, 11),
    SDP_MEDIA_ATTR(01, 12, 13, 14, 15)
};

ProtoFieldExtractorSdp_l5::ProtoFieldExtractorSdp_l5():SubFormatFieldExtractor("SDP_L5", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}

bool ProtoFieldExtractorSdp_l5::CanExtractFromThisProtocol(const std::string &strProtoName)
{
    return strBeginwith(strProtoName, "SDP") || strEndwith(strProtoName, "SDP");
}
