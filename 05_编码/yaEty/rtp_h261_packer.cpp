#include "rtp_h261_packer.h"
#include <algorithm>
#include <arpa/inet.h>

RtpH261Unpacker::RtpH261Unpacker(onGotH261Nalu_callback_t callback, void *userdata)
    : onGotNalu_func_(callback)
    , userdata_(userdata)
{
}


int RtpH261Unpacker::enqueueRtpPayload(uint8_t *rtpPayload, int len)
{

    if (rtpPayload == NULL) {
        return 0;
    }
      onGotNalu_func_(EM_H261_no_PSC, rtpPayload + 4 , len - 4, userdata_);

   return 0;
}
