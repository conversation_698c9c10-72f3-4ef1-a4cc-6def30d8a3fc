/****************************************************************************************
 * 文 件 名 : yaEty_process_keeper.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-07-05
* 编    码 : root      '2018-07-05
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _YAETY_PROCESS_KEEPER_H_
#define _YAETY_PROCESS_KEEPER_H_

#include <memory>
#include <functional>

#define EtyMsgLenMax           (12 * 1024)

struct ST_EtyProcMsg
{
    int  lMsg;                  // 消息号
    int  payloadLen;            // 负载长度
    char payload[];             // 负载数据
};

enum EN_EtyProcMsg
{
    EPM_AlmostNoTask = -1,
    EPM_NoTask       = 0,
    EPM_AskForTask   = 1,       // 请求 task
    EPM_GrantTask    = 2,       // 授予 task
    EPM_DumpPkt      = 3,       // 请求父进程进行 pkt dump
};

#define CPKPER  ChildProcKeeper::GetInstance()

// forward declaration
struct pollfd;

class ChildProcKeeper
{
public:
    typedef std::function<int (int)> childProcFunc_t;
    typedef std::function<int (int, ST_EtyProcMsg *, int)> childMsgProcFunc_t;

public:
    static void CreateInstance(int procNum, childProcFunc_t fun);
    static ChildProcKeeper *GetInstance();

public:
    int CreateProcess();
    int GetChildProcNum()
    {
        return pNum_;
    }

    int GetChildProcIndex()
    {
        return pIndex_;
    }

public:
    int pollChildren(int timeoutInMs, childMsgProcFunc_t func);

public:
    int SendMsgToChild(int procIndex,  const char *pBuff, int len);
    int RecvMsgFromParent(char *pBuff, int buffLen);
    int BroadcastMsgToChildren(const char *pBuff, int len);

    int SendMsgToParent(int lMsgCode, const char *pBuff, int len);

public:
    int AskParentForTask(std::string &strFileName);
    int GrantTaskToChild(int procIndex, std::string &strFileName);

private:
    ChildProcKeeper(int procNum, childProcFunc_t fun);
    ~ChildProcKeeper();

private:
    int onChildBornInChildExecution(int childIndex);
    int onChildBornInParentExecution(int childIndex);

private:
    int                        pid_;
    int                        pIndex_;
    int                        pNum_;
    childProcFunc_t            childProcFun_;
    std::unique_ptr<int [][2]> socketPairArrayPtr_;
    std::unique_ptr<pollfd []> pollfdArrayPtr_;

private:
    static ChildProcKeeper *s_pInstance;
};

#endif /* _YAETY_PROCESS_KEEPER_H_ */
