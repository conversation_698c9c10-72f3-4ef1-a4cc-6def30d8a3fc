/****************************************************************************************
 * 文 件 名 : yaEty_proto_field_desc_http.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : liugh      '2018-05-29
* 编    码 : liugh      '2018-05-29
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include <algorithm>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_ftp.h"
#include "yaEty_content_reassembly_share_mem.h"

void split_str(const std::string& s, std::vector<std::string>& v, const std::string& c)
{
    std::string::size_type pos1, pos2;
    pos2 = s.find(c);
    pos1 = 0;
    while(std::string::npos != pos2)
    {
        v.push_back(s.substr(pos1, pos2-pos1));

        pos1 = pos2 + c.size();
        pos2 = s.find(c, pos1);
    }
    if(pos1 != s.length())
        v.push_back(s.substr(pos1));

    return;
}

static std::string transform_field_for_ftp_response(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (fvalue_type_ftenum(const_cast<fvalue_t *>(&pFinfo->value)) != FT_BOOLEAN)
    {
        return "error";
    }

    return fvalue_get_uinteger64(const_cast<fvalue_t *>(&pFinfo->value)) ? "S2C" : "C2S";
}


static std::string transform_field_for_ftp_file(epan_dissect_t *edt, const field_info *pFinfo)
{
    field_info   *finfo     = NULL;
    ftp_data     *get_ftp = NULL;
    PktInfo       ptf;

    // 从 edt 中收集五元组信息
    finfo = get_first_field_info_from_interesting_fields(edt, "ip.src");
	if(finfo==NULL){return "";}
    ptf.src_ip = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.dst");
	if(finfo==NULL){return "";}
    ptf.dst_ip = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.proto");
	if(finfo==NULL){return "";}
    ptf.proto = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "tcp.srcport");
	if(finfo==NULL){return "";}
    ptf.src_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "tcp.dstport");
	if(finfo==NULL){return "";}
    ptf.dst_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    // hash 获取其还原出的内容文件 path list.
    get_ftp = (ftp_data *)DispGetHashdb(ptf, MEM_TYPE_FTP_DATA );
    if (NULL == get_ftp)
    {
        return "";
    }

    std::string strJsonFileList;

    // 输出为 json 数组
    Json::Value jArrayPath;
    Json::StreamWriterBuilder jBuilder;
    jBuilder["commentStyle"] = "None";
    jBuilder["indentation"]  = "";

    for(int i = 0; i < get_ftp->file_number; i++){
         jArrayPath.append(Json::Value(get_ftp->ftp_gather[i].path));
    }

    strJsonFileList = Json::writeString(jBuilder, jArrayPath);
    return strJsonFileList;
}


static std::string transform_request_for_value(epan_dissect_t *edt, const field_info *pFinfo, const char *cmd)
{
    field_info  *finfo             = NULL;
    GPtrArray   *finfos            = NULL;
    int          field_id          = 0;
    int          finfos_cnt        = 0;
    int          fields_cnt        = 0;
    GPtrArray   *finfos_b          = NULL;
    field_info  *finfo_b           = NULL;
    int          field_id_b        = 0;
    int          finfos_cnt_b      = 0;
    std::string strWsFieldValue;
    std::string strWsFieldValue_b;
    std::string strRes;

    field_id   = proto_registrar_get_id_byname("ftp.request.command");
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);

    field_id_b = proto_registrar_get_id_byname("ftp.request.arg");
    finfos_b   = proto_get_finfo_ptr_array(edt->tree, field_id_b);
    if(finfos==NULL || finfos_b==NULL){
        return "";
    }

    finfos_cnt = g_ptr_array_len(finfos);
    finfos_cnt_b = g_ptr_array_len(finfos_b);
    if(finfos_cnt!=finfos_cnt_b){
        return "";
    }

    for(int i=0;i<finfos_cnt;i++)
    {
        // 获取需要写入的头域值
        finfo = (field_info *)g_ptr_array_index(finfos, i);
        strWsFieldValue = ety_ws_get_node_field_value(finfo, edt);

        finfo_b = (field_info *)g_ptr_array_index(finfos_b, i);
        strWsFieldValue_b = ety_ws_get_node_field_value(finfo_b, edt);

        if(strcasecmp(strWsFieldValue.c_str(),cmd)==0 ){
            strRes = strWsFieldValue_b;
            //printf("[value] cmd;%s, value:%s\n",cmd,strWsFieldValue_b);
        }
    }

    return strRes;
}


static const std::string transform_response_for_value(epan_dissect_t *edt, const field_info *pFinfo, const char *cmd)
{
    field_info     *finfo         = NULL;
    GPtrArray      *finfos        = NULL;
    std::string     strWsFieldValue;
    int             field_id      = 0;
    int             finfos_cnt    = 0;
    int             fields_cnt    = 0;

    GPtrArray      *finfos_b        = NULL;
    field_info     *finfo_b         = NULL;
    std::string    strWsFieldValue_b;
    int            field_id_b       = 0;
    int            finfos_cnt_b     = 0;

    std::string strRes;

    field_id   = proto_registrar_get_id_byname("ftp.response.code");
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);

    field_id_b = proto_registrar_get_id_byname("ftp.response.arg");
    finfos_b   = proto_get_finfo_ptr_array(edt->tree, field_id_b);
    if(finfos==NULL || finfos_b==NULL){
        return "";
    }

    finfos_cnt = g_ptr_array_len(finfos);
    finfos_cnt_b = g_ptr_array_len(finfos_b);
    if(finfos_cnt!=finfos_cnt_b){
        return "";
    }

    for(int i=0;i<finfos_cnt;i++)
    {
        // 获取需要写入的头域值
        finfo = (field_info *)g_ptr_array_index(finfos, i);
        strWsFieldValue = ety_ws_get_node_field_value(finfo, edt);

        finfo_b = (field_info *)g_ptr_array_index(finfos_b, i);
        strWsFieldValue_b = ety_ws_get_node_field_value(finfo_b, edt);

        if(strstr(strWsFieldValue.c_str(),cmd)!=NULL ){
            strRes = strWsFieldValue_b;
            //printf("[value] cmd;%s, value:%s\n",cmd,strWsFieldValue_b);
        }
    }

    return strRes;
}


static std::string transform_user_for_extractor_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    return transform_request_for_value(edt, pFinfo, "user");
}

static std::string transform_pass_for_extractor_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    return transform_request_for_value(edt, pFinfo, "pass");
}

static std::string transform_port_for_extractor_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    return transform_request_for_value(edt, pFinfo, "port");
}

static std::string transform_retr_for_extractor_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    return transform_request_for_value(edt, pFinfo, "retr");
}

static std::string transform_stor_for_extractor_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    return transform_request_for_value(edt, pFinfo, "stor");
}

static std::string transform_appe_for_extractor_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    return transform_request_for_value(edt, pFinfo, "appe");
}

static std::string transform_list_for_extractor_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    return transform_request_for_value(edt, pFinfo, "list");
}

static std::string transform_site_for_extractor_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    return transform_request_for_value(edt, pFinfo, "site");
}


static std::string transform_hostname_for_extractor_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    std::string str;
    std::vector<std::string> v_str;
    str=transform_response_for_value(edt, pFinfo, "220");

    split_str(str,v_str," ");

    if(v_str.size()>0){
        return v_str[0];
    }
    return "";
}



static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // RTL tags fields
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort",               "tcp.srcport",            eMT_direct,           "",                NULL),
    F_D_ITEM("DstPort",               "tcp.dstport",            eMT_direct,           "",                NULL),
    F_D_ITEM("C2S",                   "ftp.response",           eMT_transform,        "C2S",             transform_field_for_ftp_response),
    F_D_ITEM("Proto",                 "ip.proto",               eMT_direct,           "",                NULL),
    F_D_ITEM("TTL",                   "ip.ttl",                 eMT_direct,           "",                NULL),

    F_D_ITEM("Message_type",          "",                       eMT_direct,           "",                NULL),
    F_D_ITEM("UserName",              "",                       eMT_fromEdt,          "",                transform_user_for_extractor_value),
    F_D_ITEM("PassWord",              "",                       eMT_fromEdt,          "",                transform_pass_for_extractor_value),
    F_D_ITEM("Account",               "",                       eMT_direct,           "",                NULL),
    F_D_ITEM("AuthData",              "",                       eMT_direct,           "",                NULL),
    F_D_ITEM("AuthMechansim",         "",                       eMT_direct,           "",                NULL),
    F_D_ITEM("Address",               "ftp.active.cip",         eMT_direct,           "",                NULL),
    F_D_ITEM("Port",                  "ftp.active.port",        eMT_direct,           "",                NULL),
    F_D_ITEM("ExtendAddress",         "ftp.eprt.ip",            eMT_direct,           "",                NULL),
    F_D_ITEM("ExtendPort",            "ftp.eprt.port",          eMT_direct,           "",                NULL),
    F_D_ITEM("LongAddress",           "ftp.eprt.ipv6",          eMT_direct,           "",                NULL),
    F_D_ITEM("LongPort",              "ftp.eprt.port",          eMT_direct,           "",                NULL),
    F_D_ITEM("DataType",              "",                       eMT_direct,           "",                NULL),
    F_D_ITEM("FileStructure",         "",                       eMT_direct,           "",                NULL),
    F_D_ITEM("TransMode",             "",                       eMT_direct,           "",                NULL),
    F_D_ITEM("HostName",              "",                       eMT_fromEdt,          "",                transform_hostname_for_extractor_value),
    F_D_ITEM("SiteParameters",        "",                       eMT_fromEdt,          "",                transform_site_for_extractor_value),
    F_D_ITEM("RetrPath",              "",                       eMT_fromEdt,          "",                transform_retr_for_extractor_value),
    F_D_ITEM("StorPath",              "",                       eMT_fromEdt,          "",                transform_stor_for_extractor_value),
    F_D_ITEM("AppePath",              "",                       eMT_fromEdt,          "",                transform_appe_for_extractor_value),
    F_D_ITEM("List",                  "",                       eMT_fromEdt,          "",                transform_list_for_extractor_value),
    F_D_ITEM("RequestCommand",        "ftp.request.command",    eMT_direct,           "",                NULL),
    F_D_ITEM("RequestArg",            "ftp.request.arg",        eMT_direct,           "",                NULL),
    F_D_ITEM("ResponseCode",          "ftp.response.code",      eMT_direct,           "",                NULL),
    F_D_ITEM("ResponseArg",           "ftp.response.arg",       eMT_direct,           "",                NULL),
    F_D_ITEM("DatachanlProteclevel",  "",                       eMT_direct,           "",                NULL),
    F_D_ITEM("Integrity",             "",                       eMT_direct,           "",                NULL),
    F_D_ITEM("Confidential",          "",                       eMT_direct,           "",                NULL),
    F_D_ITEM("Privacy",               "",                       eMT_direct,           "",                NULL),
    F_D_ITEM("Ftp_data",              "",                       eMT_fromEdt,          "",                transform_field_for_ftp_file),
    F_D_ITEM("IsPasv",                "ftp-data.setup-method",  eMT_direct,           "",                NULL),
};


ProtoFieldExtractorFtp::ProtoFieldExtractorFtp()
    : ProtoFieldExtractor("FTP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}

bool ProtoFieldExtractorFtp::ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter)
{
    return TRUE;
}
