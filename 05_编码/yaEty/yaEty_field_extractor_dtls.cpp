/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_dtls.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-09-14
* 编    码 : zhangsx      '2018-09-14
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"

#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_dtls.h"
#include "yaEty_content_reassembly_share_mem.h"

static std::string transform_field_for_field_length(epan_dissect_t *, const field_info *);

#define DTLS_CERT_GROUP(NO)                                                                                                                     \
        F_D_ITEM("Certificate_" #NO "Length"              , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"dtls.handshake.certificate_length")),  \
        F_D_ITEM("Certificate_" #NO "Info"                , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"x509af.subjectPublicKeyInfo_element")),  \
        F_D_ITEM("Certificate_" #NO "Version"             , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"x509af.version")),  \
        F_D_ITEM("Certificate_" #NO "Squence"             , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"dtls.handshake.message_seq")),  \
        F_D_ITEM("Certificate_" #NO "SquenceLength"       , ""                  , eMT_fromEdt,          "",         transform_for_cert_field_length((NO),"dtls.handshake.message_seq")),  \
        F_D_ITEM("Certificate_" #NO "Signature"           , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"x509af.algorithm_element")),  \
        F_D_ITEM("Certificate_" #NO "BeginTime"           , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"x509af.utcTime")),  \
        F_D_ITEM("Certificate_" #NO "EndTime"             , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"x509af.utcTime")),  \
        F_D_ITEM("Certificate_" #NO "Algorithm"           , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"x509af.algorithm_element")),  \
        F_D_ITEM("Certificate_" #NO "PubKey"              , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"dtls.handshake,server_point")),  \
        F_D_ITEM("Certificate_" #NO "PubKeyLength"        , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"dtls.handshake,server_point_len")),  \
        F_D_ITEM("Certificate_" #NO "KeyUse"              , ""                  , eMT_fixed,            "",         NULL),  \
        F_D_ITEM("Certificate_" #NO "DNSSNumber"          , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"x509af.rdnSeuqence")),  \
        F_D_ITEM("Certificate_" #NO "DNSS"                , ""                  , eMT_fixed,            "",         NULL),  \
        F_D_ITEM("Certificate_" #NO "Issuernumber"        , ""                  , eMT_fixed,            "",         NULL),  \
        F_D_ITEM("Certificate_" #NO "Issuer"              , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"x509af.issuer")),  \
        F_D_ITEM("Certificate_" #NO "Subjectnumber"       , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"x509af.subject")), \
        F_D_ITEM("Certificate_" #NO "Subject"             , ""                  , eMT_fromEdt,          "",         transform_for_cert_field((NO),"x509af.subjectPublicKeyInfo_element"))


#define DTLS_CERT_ALL_GROUP(a,b,c,d,e)  \
        DTLS_CERT_GROUP(a),             \
        DTLS_CERT_GROUP(b),             \
        DTLS_CERT_GROUP(c),             \
        DTLS_CERT_GROUP(d),             \
        DTLS_CERT_GROUP(e)


/*****************************************************************
*Function    :transform_field_for_field_length
*Description :get field length
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_field_length(epan_dissect_t *edt, const field_info *pFinfo)
{

    return (NULL != pFinfo) ? std::to_string(pFinfo->length) : "";
}


/*****************************************************************
*Function    :transform_field_for_C2S
*Description :dtls C2S
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{

    field_info * pTempfinfo = get_first_field_info_from_interesting_fields(edt, "udp.srcport");
    unsigned int && src_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pTempfinfo->value)));


    pTempfinfo = get_first_field_info_from_interesting_fields(edt, "udp.dstport");
    unsigned int && dst_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pTempfinfo->value)));

    return src_port < dst_port ? "S2C" : "C2S";

}

/*****************************************************************
*Function    :transform_for_cert_field
*Description :获取每个证书中的字段值
*Input       :epan_dissect_t, field_info, int, const char*
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

struct transform_for_cert_field
{
public:
    transform_for_cert_field(int num, const char * field_name)
        : _num(num), _field_name(field_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        int field_id = proto_registrar_get_id_byname(_field_name);

        GPtrArray *finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);

        if (!(_num - 1< g_ptr_array_len(finfos_array)))
        {
            return "";
        }

        if (NULL == finfos_array)
        {   // 没有这组值，或者 index 出错？
            return "";
        }

        field_info *pfinfo = (field_info *)g_ptr_array_index(finfos_array, _num - 1);

        std::string && strRst = ety_ws_get_node_field_value(pfinfo, edt);

        return strRst;
    }

    int _num;
    const char * _field_name;
};

/*****************************************************************
*Function    :transform_for_cert_field_length
*Description :获取每个证书中的字段长度
*Input       :epan_dissect_t, field_info, int, const char*
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

struct transform_for_cert_field_length : public transform_for_cert_field
{

    transform_for_cert_field_length(int num, const char * field_name)
        : transform_for_cert_field(num, field_name)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        int field_id = proto_registrar_get_id_byname(_field_name);

        GPtrArray *finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);

        if (!(_num < g_ptr_array_len(finfos_array)))
        {
            return "";
        }

        if (NULL == finfos_array)
        {   // 没有这组值，或者 index 出错？
            return "";
        }

        field_info *pfinfo = (field_info *)g_ptr_array_index(finfos_array, _num);

        return transform_field_for_field_length(edt, pfinfo);
    }
    
};
/*****************************************************************
*Function    :transform_for_field_spec
*Description :根据指定字段获取按状态的字段值
*Input       :epan_dissect_t, field_info, int, const char*
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
struct transform_for_field_spec : public transform_for_cert_field
{
public:
    transform_for_field_spec(int num, const char * field_name, const char * field_value)
        : transform_for_cert_field(num, field_name), _field_value(field_value)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info * pfinfo = nullptr;
        int indx = -1;

        int field_id = proto_registrar_get_id_byname(_field_name);

        GPtrArray *finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);

        for (int i = 0; i < g_ptr_array_len(finfos_array) && finfos_array != nullptr ; ++ i)
        {
            pfinfo = (field_info *)g_ptr_array_index(finfos_array, i);
            if (nullptr == pfinfo)
            {
                return "";
            }

            if (_num == fvalue_get_uinteger( const_cast<fvalue_t *>( &(pfinfo->value) ) ) )
            {
                indx = i;
                break;
            }
        }
        field_id = proto_registrar_get_id_byname(_field_value);

        finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);

        if ((indx != -1 && indx < g_ptr_array_len(finfos_array)) && finfos_array != nullptr)
        {
            pfinfo = (field_info *)g_ptr_array_index(finfos_array, indx);
            if (nullptr == pfinfo)
            {
                return "";
            }
            return ety_ws_get_node_field_value(pfinfo, edt);
        }
        else
        {
            return "";
        }
        

    }
    const char * _field_value;
};

/*****************************************************************
*Function    :transform_field_for_C2S
*Description :dtls C2S
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_Version(epan_dissect_t *edt, const field_info *pFinfo)
{
    //return (NULL != pFinfo) ? std::string(pFinfo->hfinfo->name) : "";
    int tds_type = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));

    const char *pString = try_val_to_str(tds_type, (const value_string *)(pFinfo->hfinfo->strings));

    if (NULL == pString)
    {
        return "DTLS";
    }

    return std::string(pString);
}

static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    
    F_D_ITEM("SrcPort"                             , "udp.srcport"                                , eMT_direct,           "",          NULL),
    F_D_ITEM("DstPort"                             , "udp.dstport"                                , eMT_direct,           "",          NULL),
    F_D_ITEM("C2S"                                 , ""                                           , eMT_fromEdt,          "",          transform_field_for_C2S),
    F_D_ITEM("Proto"                               , "ip.proto"                                   , eMT_direct,           "",          NULL),
    F_D_ITEM("TTL"                                 , "ip.ttl"                                     , eMT_direct,           "",          NULL),
    
    F_D_ITEM("ContentType"                         , "dtls.record.content_type"                   , eMT_direct,           "",          NULL),
    F_D_ITEM("Version"                             , "dtls.record.version"                        , eMT_transform,        "",          transform_field_for_Version),
    F_D_ITEM("Epoch"                               , "dtls.record.epoch"                          , eMT_direct,           "",          NULL),
    F_D_ITEM("Sequence_number"                     , "dtls.record.sequence_number"                , eMT_direct,           "",          NULL),
    F_D_ITEM("RecordLayerLength"                   , "dtls.record.length"                         , eMT_direct,           "",          NULL),
    F_D_ITEM("ChangeCipherSpec"                    , "dtls.change_cipher_spec"                    , eMT_direct,           "",          NULL),
    F_D_ITEM("AlertLevel"                          , "dtls.alert_message.level"                   , eMT_direct,           "",          NULL),
    F_D_ITEM("AlertDescription"                    , "dtls.alert_message.desc"                    , eMT_direct,           "",          NULL),
    F_D_ITEM("HandshakeType"                       , "dtls.handshake.type"                        , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientHelloLength"                   , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.length") ),
    F_D_ITEM("ClientHelloMessage_seq"              , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.message_seq") ),
    F_D_ITEM("ClientHelloFragment_offset"          , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.fragment_offset") ),
    F_D_ITEM("ClientHelloFragment_length"          , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.fragment_length") ),
    F_D_ITEM("ClientProtocolVersion"               , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.version") ),
    F_D_ITEM("ClientGMTUnixTime"                   , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.random_time") ),
    F_D_ITEM("ClientRandomBytes"                   , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.random_bytes") ),
    F_D_ITEM("ClientSessionIDLength"               , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.session_id_length") ),
    F_D_ITEM("ClientSessionID"                     , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.session_id") ),
    F_D_ITEM("ClientCookieLength"                  , "dtls.handshake.cookie_length"               , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientCookie"                        , "dtls.handshake.cookie"                      , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientCipherSuitesLength"            , "dtls.handshake.cipher_suites_length"        , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientCipherSuites"                  , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.ciphersuite") ),
    F_D_ITEM("ClientCompressionMethodsLen"         , "dtls.handshake.comp_methods_length"         , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientCompressionMethods"            , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.comp_methods") ),
    F_D_ITEM("ClientExtensionsLength"              , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.extensions_length") ),
    F_D_ITEM("ClientExtensions"                    , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(1,"dtls.handshake.type","dtls.handshake.extensions") ),
    F_D_ITEM("HelloVerifyRequestLength"            , ""                                           , eMT_direct,           "",          transform_for_field_spec(3,"dtls.handshake.type","dtls.handshake.length")),
    F_D_ITEM("HelloVerReqMessage_seq"              , ""                                           , eMT_direct,           "",          transform_for_field_spec(3,"dtls.handshake.type","dtls.handshake.message_seq")),
    F_D_ITEM("HelloVerReqFragment_offset"          , ""                                           , eMT_direct,           "",          transform_for_field_spec(3,"dtls.handshake.type","dtls.handshake.fragment_offset")),
    F_D_ITEM("HelloVerReqFragment_length"          , ""                                           , eMT_direct,           "",          transform_for_field_spec(3,"dtls.handshake.type","dtls.handshake.fragment_length")),
    F_D_ITEM("ServerVersion"                       , "dtls.record.version"                        , eMT_direct,           "",          NULL),
    F_D_ITEM("ServertoClientcookieLength"          , "dtls.handshake.cookie_length"               , eMT_direct,           "",          NULL),
    F_D_ITEM("ServertoClientcookie"                , "dtls.handshake.cookie"                      , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerHelloLength"                   , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.length") ),
    F_D_ITEM("ServerHelloMessage_seq"              , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.message_seq") ),
    F_D_ITEM("ServerHelloFragment_offset"          , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.fragment_offset") ),
    F_D_ITEM("ServerHelloFragment_length"          , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.fragment_length") ),
    F_D_ITEM("ServerProtocolVersion"               , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.version") ),
    F_D_ITEM("ServerGMTUnixTime"                   , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.random_time") ),
    F_D_ITEM("ServerRandomBytes"                   , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.random_bytes") ),
    F_D_ITEM("ServerSessionIDLength"               , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.session_id_length") ),
    F_D_ITEM("ServerSessionID"                     , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.session_id") ),
    F_D_ITEM("ServerCipherSuite"                   , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.ciphersuite") ),
    F_D_ITEM("ServerCompressionMethod"             , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.comp_methods") ),
    F_D_ITEM("ServerExtensionsLength"              , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.extensions_length") ),
    F_D_ITEM("ServerExtensions"                    , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(2,"dtls.handshake.type","dtls.handshake.extensions") ),
    F_D_ITEM("CertificatesLength"                  , "dtls.handshake.certificates_length"         , eMT_transform,        "",          transform_field_for_field_length),
    F_D_ITEM("CertificatesMessage_seq"             , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(15,"dtls.handshake.type","dtls.handshake.message_seq")),
    F_D_ITEM("CertificatesFragment_offset"         , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(15,"dtls.handshake.type","dtls.handshake.fragment_offset")),
    F_D_ITEM("CertificatesFragment_length"         , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(15,"dtls.handshake.type","dtls.handshake.fragment_length")),
    F_D_ITEM("CertificatesNums"                    , ""                                           , eMT_direct,           "",          NULL),
    DTLS_CERT_ALL_GROUP(1,2,3,4,5),
    DTLS_CERT_ALL_GROUP(6,7,8,9,10),
    F_D_ITEM("ServerKexLength"                     , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(12,"dtls.handshake.type","dtls.handshake.length") ),//当handshake类型为server key exchange时.
    F_D_ITEM("ServerKexMessage_seq"                , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(12,"dtls.handshake.type","dtls.handshake.message_seq") ),
    F_D_ITEM("ServerKexFragment_offset"            , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(12,"dtls.handshake.type","dtls.handshake.fragment_offset") ),
    F_D_ITEM("ServerKexFragment_length"            , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(12,"dtls.handshake.type","dtls.handshake.fragment_length") ),
    F_D_ITEM("ECDHCurveType"                       , "dtls.handshake.cert_type"                   , eMT_direct,           "",          NULL),
    F_D_ITEM("ECDHNamedCurve"                      , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("ECDHPubKeyLength"                    , "dtls.handshake.server_point_len"            , eMT_direct,           "",          NULL),
    F_D_ITEM("ECDHPubkey"                          , "dtls.handshake.server_point"                , eMT_direct,           "",          NULL),
    F_D_ITEM("ECDHSignatureHashAlgorithm"          , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("ECDHSignatureSigAlgorithm"           , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("ECDHSignatureLength"                 , "dtls.handshake.sig_len"                     , eMT_direct,           "",          NULL),
    F_D_ITEM("ECDHSignature"                       , "dtls.handshake.sig"                         , eMT_direct,           "",          NULL),
    F_D_ITEM("RSAModulusLength"                    , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("RSAModulus"                          , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("RSAExponentLength"                   , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("RSAExponent"                         , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("RSASignatureHashAlgorithm"           , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("RSASignatureSigAlgorithm"            , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("RSASignatureLength"                  , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("RSASignature"                        , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("DHEpLength"                          , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("DHEp"                                , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("DHEgLength"                          , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("DHEg"                                , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("DHEPubKeyLength"                     , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("DHEPubkey"                           , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("DHESignatureHashAlgorithm"           , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("DHESignatureSigAlgorithm"            , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("DHESignatureLength"                  , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("DHESignature"                        , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("ServerKexData"                       , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("ClientKexLength"                     , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(16,"dtls.handshake.type","dtls.handshake.length") ),
    F_D_ITEM("ClientKexMessage_seq"                , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(16,"dtls.handshake.type","dtls.handshake.message_seq") ),
    F_D_ITEM("ClientKexFragment_offset"            , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(16,"dtls.handshake.type","dtls.handshake.fragment_offset") ),
    F_D_ITEM("ClientKexFragment_length"            , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(16,"dtls.handshake.type","dtls.handshake.fragment_length") ),
    F_D_ITEM("EncrypedPubkey"                      , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("EncrypedPubkeyLength"                , ""                                           , eMT_fixed,            "",          NULL),
    F_D_ITEM("CertificateRequestLength"            , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(13,"dtls.handshake.type","dtls.handshake.length") ),
    F_D_ITEM("CertReqMessage_seq"                  , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(13,"dtls.handshake.type","dtls.handshake.message_seq") ),
    F_D_ITEM("CertReqFragment_offset"              , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(13,"dtls.handshake.type","dtls.handshake.fragment_offset") ),
    F_D_ITEM("CertReqFragment_length"              , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(13,"dtls.handshake.type","dtls.handshake.fragment_length") ),
    F_D_ITEM("ClientCertificateTypesCount"         , "dtls.handshake.cert_types_count"            , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientCertificateTypes"              , "dtls.handshake.cert_types"                  , eMT_direct,           "",          NULL),
    F_D_ITEM("DistinguishedNameLength"             , "dtls.handshake.dnames_len"                  , eMT_direct,           "",          NULL),
    F_D_ITEM("DistinguishedName"                   , "dtls.handshake.dnames"                      , eMT_direct,           "",          NULL),
    F_D_ITEM("CertificateVerifyLength"             , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(15,"dtls.handshake.type","dtls.handshake.length") ),
    F_D_ITEM("CertificateVerifyMessage_seq"        , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(15,"dtls.handshake.type","dtls.handshake.message_seq") ),
    F_D_ITEM("CertVerifyFragment_offset"           , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(15,"dtls.handshake.type","dtls.handshake.fragment_offset") ),
    F_D_ITEM("CertVerifyFragment_length"           , ""                                           , eMT_fromEdt,          "",          transform_for_field_spec(15,"dtls.handshake.type","dtls.handshake.fragment_length") ),
    F_D_ITEM("ClientCertificateSignature"          , "dtls.handshake.client_cert_vrfy.sig"        , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientCertSignatureLength"           , "dtls.handshake.client_cert_vrfy.sig_len"    , eMT_direct,           "",          NULL),
};
ProtoFieldExtractorDtls::ProtoFieldExtractorDtls():MatchPrefixProtoFieldExtractor("DTLS", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}



bool ProtoFieldExtractorDtls::ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter)
{
    return true;
}
