/****************************************************************************************
 * 文 件 名 : rtxdr_tbl_writer.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-03-16
* 编    码 : root      '2018-03-16
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <glib.h>
#include "yaEty_utils.h"
#include "yaEty_config.h"
#include "yaEty_field_extractor.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_child_process.h"
#include "yaEty_cap_file_processor.h"
#include "yaEty_target_info.h"

std::map<std::string, TblRecordWriter *> TblRecordWriter::cs_mapTblWriter;
uint32_t TblRecordWriter::cs_timeOutLimits_ = 60;

const char *STR_TBL_FILE_DIR_DEF = "/tmp/tbls/";

/* Check if an entire UTF-8 string is printable. */
/* 借助 glib 实现字符串是否可见的检测 */
/* 移植自 wireshark 2.6 isprint_utf8_string */
bool isprint_utf8_str(const gchar *str, guint length)
{
	const char *c;

	if (!g_utf8_validate (str, length, NULL)) {
		return FALSE;
	}

	for (c = str; *c; c = g_utf8_next_char(c)) {
		if (!g_unichar_isprint(g_utf8_get_char(c))) {
			return FALSE;
		}
	}

	return TRUE;
}

TblRecordWriter::TblRecordWriter(const std::string &strProtoName,
                                 const std::string &strTblProtoName,
                                 const std::string &strFileDirPath /* = "" */,
                                 int lRecordCntPerFile /* = RTXDR_RECORD_CNT_PERFILE */,
                                 int lThreadNO /* = 42 */)
    : strProtoName_(strProtoName)
    , strTblProtoName_(strTblProtoName)
    , strTblFileDir_(strFileDirPath)
    , lRecordCntPerFile_(lRecordCntPerFile)
    , lThreadNO_(lThreadNO)
{
    if (strTblFileDir_.empty())
    {
        strTblFileDir_ = EtyConfig::GetInstance()->GetTblsDir()  + "/" + strTblProtoName_;
    }

    if (CFG->AddTaskID())
        strTblTaskID_  = "_"+EtyConfig::GetInstance()->GetTaskID();

    pFieldExtractor_ = ProtoFieldExtractorBase::FindProtoFieldExtractorOf(strProtoName_.c_str());
    if (NULL == pFieldExtractor_)
    {
        printf("warning: there is no writer for proto %s\n", strProtoName_.c_str());
        return;
    }

    // register myself
    RegisterWriter(strProtoName_, this);
    
}

TblRecordWriter::~TblRecordWriter()
{
    // unregister myself
    UnregisterWriter(strProtoName_);
    writeCurrentFileDone();
}

void TblRecordWriter::updateInMapTblTaskID()
{
    if (CFG->AddTaskID()){
        for(const auto & item : cs_mapTblWriter)
            item.second->updateTblTaskID();
    }
}
void TblRecordWriter::updateTblTaskID()
{
    if (CFG->AddTaskID())
        strTblTaskID_  = "_"+EtyConfig::GetInstance()->GetTaskID();
}

void TblRecordWriter::writeCurrentFileDone()
{

    if (NULL == tblFile_)
    {
        //rename(fileNameBufWriting_, fileNameBuf_);
        return;
    }
    // close file
    fclose(tblFile_);
    tblFile_ = NULL;
    //
    lRecordCntWriten_ = 0;

    // rename file name (del its ".writing")
    rename(fileNameBufWriting_, fileNameBuf_);
    insertTmpTbl(fileNameBuf_);
}

void TblRecordWriter::generateFileName()
{
    // done:    20180226173528_45038_rtxdr_014.tbl
    // writing: 20180226173528_45038_rtxdr_014.tbl.writing
    char *pFileName = fileNameBuf_;
    time_t unixTime = time(0);
    tm     tm       = *localtime((time_t *)&unixTime);

    // 选择 threadid
    lThreadNO_ = getThreadId();

    // 补全目录
    pFileName += sprintf(pFileName, "%s/", strTblFileDir_.c_str());
    if(CFG->GetValueOf<int>("DATE_SUBDIR_FLAG") == 1){
        char time_str[32] ={0};
        strftime(time_str, sizeof time_str, "%Y%m%d", &tm);
        std::string dir =   strTblFileDir_ + "/" + time_str;
        pFileName +=sprintf(pFileName, "/%s/",time_str);
        mkdir(dir.c_str(),  0755);
    }

    // date string
    pFileName += strftime(pFileName, sizeof fileNameBuf_, "tmp_%Y%m%d%H%M%S", &tm);

    // etc
    pFileName += sprintf(pFileName, "_%05d_%s_%03d%s.tbl",
                         hundredThousandthSecond(),
                         strTblProtoName_.c_str(),
                         lThreadNO_,
                         strTblTaskID_.c_str());

    sprintf(fileNameBufWriting_, "%s%s", fileNameBuf_, TBL_WRITING_SUFFIX);
}

int TblRecordWriter::createTblFile()
{   // TODO: 1) 若创建目录或者文件失败，退出程序
    // 创建 xdr 目录
    int lSts = 0;

    if (access(strTblFileDir_.c_str(),R_OK) != 0)
    {
        lSts = makeDir(strTblFileDir_.c_str());
        EXIT_IF_ERROR(lSts < 0, lSts, strTblFileDir_.c_str());
    }

    // 生成文件名
    generateFileName();

    // 创建文件
    tblFile_ = fopen(fileNameBufWriting_, "w");
    EXIT_IF_ERROR(NULL == tblFile_, -errno, fileNameBufWriting_);

    return 0;
}

int TblRecordWriter::writeNextRecord()
{
    int lSts = 0;
    if (NULL == tblFile_)
    {
        return 0;
    }

    fprintf(tblFile_, "\n");

    // fields 计数器归零
    lFieldsWritenCntForThisRecord_ = 0;

    // 计数还未达到上限
    if (lRecordCntWriten_++ <= lRecordCntPerFile_)
    {
        return 0;
    }

    // 更换文件
    writeCurrentFileDone();
    return createTblFile();
}

int TblRecordWriter::writeRecordField(const char *strFieldName, const std::string &strFieldValue)
{
    return writeRecordField(strFieldName, strFieldValue.c_str());
}

int  TblRecordWriter::writeRecordField(const char *strFieldName, const char *strFieldValue)
{
    int lSts = 0;
    if (NULL == tblFile_)
    {
        lSts = createTblFile();
        CHECK_NOR_EXIT(lSts < 0, lSts, "create tbl file in dir %s error, 1 record missed!\n", strTblFileDir_.c_str());
    }

#ifdef DEBUG
    std::string strFieldNameShouldBe = pFieldExtractor_->GetEtyFieldNameByIndex(lFieldsWritenCntForThisRecord_);
    if (strFieldNameShouldBe != strFieldName)
    {
        printf("write the %dth field, it should be %s, but %s will be writen!!\n",
               lFieldsWritenCntForThisRecord_,
               strFieldNameShouldBe.c_str(),
               strFieldName);
    }
#endif // DEBUG

    if (CFG->GetValueOf<bool>("debug_mode"))
    {
        // 检查 fieldvalue 中是否含有 '|'
        if (!isprint_utf8_str(strFieldValue, strlen(strFieldValue)))
        {
            printf("warning: tbl format error, found nonprintable char in [%s]:[%s]:[%s]\n",
                   CapFileProcessor::GetCurrentProcessingPcapFilename().c_str(),
                   CapFileProcessor::GetCurrentProcessingFrameNum().c_str(),
                   strFieldName);
        }
        // 检查 fieldValue 中是否有不可见字段，并告警
        else if (strchr(strFieldValue, '|') != NULL)
        {
            printf("warning: tbl format error, found '|' in [%s]:[%s]:[%s]\n",
                   CapFileProcessor::GetCurrentProcessingPcapFilename().c_str(),
                   CapFileProcessor::GetCurrentProcessingFrameNum().c_str(),
                   strFieldName);
        }
    }

    //lSts = fprintf(tblFile_, "\"%s\"|", strFieldValue);
    lSts = fprintf(tblFile_, "%s|", strFieldValue);
    //fflush(tblFile_);

    // 对 fields 进行计数
    lFieldsWritenCntForThisRecord_++;
    return 0;
}


 int TblRecordWriter::writeRawFields(const char *fieldsValue, int field_cnt)
 {
    int lSts = 0;
    if (NULL == tblFile_)
    {
        lSts = createTblFile();
        CHECK_NOR_EXIT(lSts < 0, lSts, "create tbl file in dir %s error, 1 record missed!\n", strTblFileDir_.c_str());
    }

    if (fieldsValue != nullptr  &&
        strlen(fieldsValue) > 0 &&
        field_cnt > 0)
    {
        fprintf(tblFile_, "%s", fieldsValue);

        lFieldsWritenCntForThisRecord_ += field_cnt;
    }

    return 0;
 }


int TblRecordWriter::RegisterWriter(const std::string &strProtoName, TblRecordWriter *pWriter)
{
    IProtoFieldExtractor *pExtractor = ProtoFieldExtractorBase::FindProtoFieldExtractorOf(strProtoName.c_str());
    if (NULL == pExtractor)
    {
        // log there is not a Extractor with the same name
        return 0;
    }

    // associate to extractor
    pExtractor->SetRecordWriter(pWriter);

    cs_mapTblWriter.emplace(strProtoName, pWriter);
    return 0;
}

int TblRecordWriter::UnregisterWriter(const std::string &strProtoName)
{
    cs_mapTblWriter.erase(strProtoName);
    return 0;
}

int TblRecordWriter::WriteTblFields(const std::string &strTblFieldsDir)
{
    int lSts = 0;

    // TODO:如果目录创建失败
    if (access(strTblFieldsDir.c_str(), R_OK) != 0)
    {
        lSts = makeDir(strTblFieldsDir.c_str());
        EXIT_IF_ERROR(lSts < 0, lSts, strTblFieldsDir.c_str());
    }

    char strFieldsFilePath[PATH_MAX] = { 0 };
    IProtoFieldExtractor *pExtractor = NULL;

    for(const auto & item : cs_mapTblWriter)
    {
        std::string strProtoFieldContent;

        // field 文件名：目录 + filename, xxx_f.txt
        sprintf(strFieldsFilePath, "%s/%s_f.txt", strTblFieldsDir.c_str(), item.second->strTblProtoName_.c_str());

        // 收集 field name
        pExtractor = ProtoFieldExtractorBase::FindProtoFieldExtractorOf(item.first.c_str());
        if (NULL == pExtractor)
        {
           printf("error: there is no writer for proto %s\n", item.first.c_str());
           continue;
        }

        for(int i = 0; i < pExtractor->GetFieldsCount(); i++)
        {
            strProtoFieldContent += pExtractor->GetProtoFieldDescByIndex(i)->etyFieldName;
            strProtoFieldContent += "\n";
        }

        // 写入到文件
        FILE *pFieldFile = fopen(strFieldsFilePath, "w");
        CHECK_NOR_EXIT(NULL == pFieldFile, -1, "open file [%s] failed : %s\n", strFieldsFilePath, strerror(errno));

        fprintf(pFieldFile, "%s", strProtoFieldContent.c_str());
        fclose(pFieldFile);
    }
}

void TblRecordWriter::insertTmpTbl(char* filename){
  std::string file_name_str(filename);
  tmp_tbl_v_.push_back(file_name_str);
}

void TblRecordWriter::renameAllTmpTbl(){
    for (auto index:tmp_tbl_v_) {
      auto newname = index;
      if(index.find( "tmp_") != std::string::npos){
        newname.erase(index.find( "tmp_"),4);
        int rc = rename(index.c_str(), newname.c_str());
      }
  }
  tmp_tbl_v_.clear();
}

int TblRecordWriter::WriteTblsDone()
{
    for(auto& iter: cs_mapTblWriter)
    {
        iter.second->writeCurrentFileDone();
    }
    return 0;
}

int TblRecordWriter::writeTblField(const std::string &strTblFieldsDir, IProtoFieldExtractor *pExtractor)
{
    int lSts = 0;

    // TODO:如果目录创建失败
    if (access(strTblFieldsDir.c_str(), R_OK) != 0)
    {
        lSts = makeDir(strTblFieldsDir.c_str());
        EXIT_IF_ERROR(lSts < 0, lSts, strTblFieldsDir.c_str());
    }

    char strFieldsFilePath[PATH_MAX] = { 0 };

    if(pExtractor != nullptr)
    {
        std::string strProtoFieldContent;

        // field 文件名：目录 + filename, xxx_f.txt
        sprintf(strFieldsFilePath, "%s/%s_f.txt", strTblFieldsDir.c_str(), this->strTblProtoName_.c_str());

        for(int i = 0; i < pExtractor->GetFieldsCount(); i++)
        {
            strProtoFieldContent += pExtractor->GetProtoFieldDescByIndex(i)->etyFieldName;
            strProtoFieldContent += "\n";
        }

        // 写入到文件
        FILE *pFieldFile = fopen(strFieldsFilePath, "w");
        CHECK_NOR_EXIT(NULL == pFieldFile, -1, "open file [%s] failed : %s\n", strFieldsFilePath, strerror(errno));

        fprintf(pFieldFile, "%s", strProtoFieldContent.c_str());
        fclose(pFieldFile);
    }
    else
    {
        printf("error: there is no writer for proto %s\n", this->strTblProtoName_.c_str());
    }
    
    return 0;
}

void TblRecordWriter::writeTblDone()
{
    this->writeCurrentFileDone();
}

int TblRecordWriter::getThreadId()
{
    if (CFG->GetValueOf<int>("child_proc_num") == 0)
    {   // 未开启子进程时，
        return 42;
    }

    return CPKPER->GetChildProcIndex();
}

int TblRecordWriter::AlarmWriteTbls(const uint32_t interval)
{
    cs_timeOutLimits_ = interval;
    signal(SIGALRM, TblRecordWriter::WriteAfterTime);
    alarm(cs_timeOutLimits_);
}

void TblRecordWriter::WriteAfterTime(int sig)
{
    for(auto& iter: cs_mapTblWriter)
    {
        if(iter.second->getWriten() > 0)
        {
            iter.second->writeCurrentFileDone();
            iter.second->createTblFile();
        }
    }
    alarm(cs_timeOutLimits_);
}

void TblRecordWriter::Register()
{
    static TblRecordWriter      ms_httpWriter          ("HTTP",         "http_n");
    static TblRecordWriter      ms_dnsWriter           ("DNS",          "dns");
    static TblRecordWriter      ms_ftpWriter           ("FTP",          "ftp");
    static TblRecordWriter      ms_smtpWriter          ("SMTP",         "smtp");
    static TblRecordWriter      ms_popWriter           ("POP",          "pop");
    static TblRecordWriter      ms_imapWriter          ("IMAP",         "imap");
    static TblRecordWriter      ms_radiusWriter        ("RADIUS",       "radius");
    static TblRecordWriter      ms_sipWriter           ("SIP",          "sip");
    static TblRecordWriter      ms_rtpWriter           ("RTP",          "rtp");
    static TblRecordWriter      ms_espWriter           ("ESP",          "esp");
    static TblRecordWriter      ms_l2tpWriter          ("L2TP",         "l2tp");
    static TblRecordWriter      ms_pptpWriter          ("PPTP",         "pptpcontrol");
    static TblRecordWriter      ms_wxfWriter           ("WXF",          "wxf");
    static TblRecordWriter      ms_dhcpWriter          ("DHCP",         "dhcp");
    static TblRecordWriter      ms_telnetWriter        ("TELNET",       "telnet");
    static TblRecordWriter      ms_bgpWriter           ("BGP",          "bgp");
    static TblRecordWriter      ms_sshWriter           ("SSH",          "ssh");
    static TblRecordWriter      ms_mysqlWriter         ("MySQL",        "mysql");
    static TblRecordWriter      ms_tdsWriter           ("TDS",          "tds");
    static TblRecordWriter      ms_dtlsWriter          ("DTLS",         "dtls");
    static TblRecordWriter      ms_ripWriter           ("RIP",          "rip");
    static TblRecordWriter      ms_snmpWriter          ("SNMP",         "snmp");
    static TblRecordWriter      ms_megacoWriter        ("MEGACO",       "megaco");
    static TblRecordWriter      ms_rdpWriter           ("RDP",          "rdp");
    static TblRecordWriter      ms_diameterWriter      ("DIAMETER",     "diameter");
    static TblRecordWriter      ms_eigrpWriter         ("EIGRP",        "eigrp");
    static TblRecordWriter      ms_ospfWriter          ("OSPF",         "ospf");
    static TblRecordWriter      ms_classicstunWriter   ("CLASSIC-STUN", "classicstun");
    static TblRecordWriter      ms_stunWriter          ("STUN",         "stun");
    static TblRecordWriter      ms_ahWriter            ("AH",           "ah");
    static TblRecordWriter      ms_mgcpWriter          ("MGCP",         "mgcp");
    static TblRecordWriter      ms_ldapWriter          ("LDAP",         "ldap");
    static TblRecordWriter      ms_greWriter           ("GRE",          "gre");
    static TblRecordWriter      ms_gtp_cWriter         ("GTP",          "gtp_control");
    static TblRecordWriter      ms_isakmpWriter        ("ISAKMP",       "isakmp");
    static TblRecordWriter      ms_ssl_nWriter         ("SSL/TLS",      "ssl_n");
    static TblRecordWriter      ms_tftpWriter          ("TFTP",         "tftp");
    static TblRecordWriter      ms_iaxWriter           ("IAX",          "iax");
    static TblRecordWriter      ms_sdp_l5Writer        ("SDP_L5",       "sdp_l5");
    static TblRecordWriter      ms_wxaWriter           ("WXA",          "wxa");
    static TblRecordWriter      ms_smsWriter           ("GSM SMS",      "sms");
    
}
