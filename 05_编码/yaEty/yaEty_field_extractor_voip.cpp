/****************************************************************************************
 * �� �� �� : yaEty_field_extractor_voip.cpp
 * ��Ŀ���� : YVBD1207001B
 * ģ �� �� :
 * ��    �� :
 * ����ϵͳ : LINUX
 * �޸ļ�¼ : ��
 * ��    �� : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* ��    �� : zhangsx      '2019-03-20
* ��    �� : zhangsx      '2019-03-20
* ��    �� :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* ��˾���ܼ���Ȩ˵��
*
*           (C)Copyright 2019 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
****************************************************************************************/

#include "wsutil/utf8_entities.h"
#include "yaEty_field_extractor_voip.h"
#include "yaEty_voip_stream_keeper.h"
#include "yaEty_stream_keeper_def.h"
#include "yaEty_cap_file_processor.h"

#include "rapidjson/rapidjson.h"
#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/reader.h"
#include "rapidjson/stringbuffer.h"
#include <sys/stat.h>	/* for stat() and struct stat */

#include <functional>
static int isUTF8(const char *pData, int len)
{
    if(NULL == pData || len < 1)
    {
        return -1;
    }

    int loop = len;
    const char *p = pData;
    while(loop)
    {
        if(0X0A == *p|| 0X0D == *p)
        {
            p++;
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }
        else if(loop>=3 &&  (0XE0 == (p[0] & 0XF0)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0)) )
        {
            p = p + 3;
            loop = loop - 3;
            continue;
        }
        return 0;/* 这不是 UTF-8 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}

std::set<rtp_stream_info_t *> ProtoFieldExtractorVoip::voip_ssrc = {};
std::map<rtp_stream_info_t *,uint32_t,compare_rsi> ProtoFieldExtractorVoip::voip_rtp_stream = {};

struct trans_for_rtl_msidsn
{
public:
    trans_for_rtl_msidsn(std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
     :_frame_to_trailer_map(frame_to_trailer_map),
      _rtp_info_map(rtp_info_map),
      _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.msisdn;
                }
              }
        }
        return "";
    }
public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};


struct trans_for_rtl_imei
{
public:
    trans_for_rtl_imei(std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.imei;
                }
              }
        }
        return "";
    }
public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_rtl_imsi
{
public:
   trans_for_rtl_imsi(std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.imsi;
                }
              }
        }
        return "";
    }
public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_rtl_tac
{
public:
    trans_for_rtl_tac        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.tac;
                }
              }
        }
        return "";
    }
public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_rtl_teid
{
public:
    trans_for_rtl_teid        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.teid;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_rtl_plmnid
{
public:
    trans_for_rtl_plmnid        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.rt_plmnid;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_rtl_uli
{
public:
    trans_for_rtl_uli        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.rt_uli;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};

struct trans_for_rtl_bs
{
public:
    trans_for_rtl_bs        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.rt_basetype;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};
struct trans_for_rtl_outsrc
{
public:
    trans_for_rtl_outsrc        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.rt_outsrc_ip;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};
struct trans_for_rtl_outdst
{
public:
    trans_for_rtl_outdst        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.rt_outdst_ip;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};


struct trans_for_hwl_operator
{
public:
    trans_for_hwl_operator        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_operator;

                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_hwl_nettype
{
public:
    trans_for_hwl_nettype        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_nettype;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};

struct trans_for_hwl_ecgi
{
public:
    trans_for_hwl_ecgi        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_ecgi;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};

struct trans_for_hwl_esn
{
public:
    trans_for_hwl_esn        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_esn;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_hwl_lac
{
public:
    trans_for_hwl_lac        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_lac;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};

struct trans_for_hwl_sac
{
public:
   trans_for_hwl_sac         (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_sac;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};

struct trans_for_hwl_meid
{
public:
    trans_for_hwl_meid        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_meid;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};

struct trans_for_hwl_cellid
{
public:
    trans_for_hwl_cellid        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_cellid;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_hwl_apn
{
public:
    trans_for_hwl_apn        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_apn;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};

struct trans_for_hwl_mnc
{
public:
    trans_for_hwl_mnc        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_ecgi_mnc;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};

struct trans_for_hwl_ncode
{
public:
    trans_for_hwl_ncode        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_ncode;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_hwl_bsid
{
public:
    trans_for_hwl_bsid        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_bsid;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_hwl_accont
{
public:
    trans_for_hwl_accont        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_accont;

                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_hwl_grekey
{
public:
    trans_for_hwl_grekey        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_gre_key;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};

struct trans_for_hwl_tai
{
public:
    trans_for_hwl_tai        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.hw_tai;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

#if 0
struct trans_for_src_address
{
public:
    trans_for_src_address        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.src_ip;
                  break;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_dst_address
{
public:
    trans_for_dst_address        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.dst_ip;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};

struct trans_for_src_port
{
public:
    trans_for_src_port        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.src_port;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_dst_port
{
public:
    trans_for_dst_port        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.dst_port;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_trans_proto
{
public:
    trans_for_trans_proto        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){
                  return trailer_iter->second.proto;
                  break;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_trans_ttl
{
public:
    trans_for_trans_ttl        (std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.ttl;
                  break;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;
};

struct trans_for_trans_capdate
{
public:
      trans_for_trans_capdate(std::map<uint32_t, ya_trailer_t> &frame_to_trailer_map, std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map,std::map<voip_key_t, uint32_t,compare_voip_key> & rsi_setup_frame_map)
      :_frame_to_trailer_map(frame_to_trailer_map),
       _rtp_info_map(rtp_info_map),
       _rsi_setup_frame_map(rsi_setup_frame_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        for (const auto& iter : _rtp_info_map)
        {
              if (iter.second != call_info->call_num)
              {
                  continue;
              }
              rtp_stream_info_t * rsi = iter.first;
              voip_key_t v_key = fromRtpStreamInfo(rsi);
              const auto& frame_iter =  _rsi_setup_frame_map.find(v_key);
              if(frame_iter != _rsi_setup_frame_map.end()){
                const auto& trailer_iter = _frame_to_trailer_map.find(frame_iter->second);
                if(trailer_iter !=_frame_to_trailer_map.end()){                  return trailer_iter->second.capdate;
                }
              }
        }
        return "";
    }

public:
    std::map<uint32_t, ya_trailer_t> &_frame_to_trailer_map;
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
    std::map<voip_key_t, uint32_t,compare_voip_key> & _rsi_setup_frame_map;

};

#endif

static std::string trans_for_src_address(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    if(call_info->s_src_addr)
      return call_info->s_src_addr;
    return "";

}
static std::string trans_for_dst_address(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    if(call_info->s_dst_addr)
          return  call_info->s_dst_addr;
        return "";

}
static std::string trans_for_src_port(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    if(call_info->s_src_port)
        return call_info->s_src_port;
    return "";

}
static std::string trans_for_dst_port(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    if(call_info->s_dst_port)
        return call_info->s_dst_port;
    return "";

}
static std::string trans_for_trans_proto(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    if(call_info->s_ip_proto)
        return call_info->s_ip_proto;
    return "";

}
static std::string trans_for_trans_ttl(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    if(call_info->s_ttl)
        return call_info->s_ttl;
    return "";

}
static std::string trans_for_trans_capdate(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    if(call_info->s_capdate)
        return call_info->s_capdate;
    return "";

}

static std::string trans_for_trans_dealdate(epan_dissect_t *edt, const field_info *pFinfo)
{
    return unixTime2Str(time(NULL));
}

static std::string trans_for_start_time(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;

    voip_calls_info_t * call_info = _call_info->voip_info;

    return std::to_string(nstime_to_sec(&(call_info->start_rel_ts)));
}

static std::string trans_for_end_time(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;

    voip_calls_info_t * call_info = _call_info->voip_info;
    
    return std::to_string(nstime_to_sec(&(call_info->stop_rel_ts)));
}

static std::string trans_for_initial(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    char * str = address_to_display(NULL, &(call_info->initial_speaker));
    if (str)
    {
        std::string addr(str);
        wmem_free(NULL, str);
        return addr;
    }
    return "";  
}

static std::string transform_field_for_from(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    return call_info->from_identity;
}

static std::string transform_field_for_to(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    return call_info->to_identity;
}

static std::string transform_field_for_proto(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    return ((call_info->protocol == VOIP_COMMON) && call_info->protocol_name) ?
        call_info->protocol_name : voip_protocol_name[call_info->protocol];
}

static std::string transform_field_for_duration(epan_dissect_t *edt, const field_info *pFinfo)
{
    char strTimeBuf[30] = { 0 };
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    time_t callDuration = (call_info->stop_rel_ts.secs) - (call_info->start_rel_ts.secs);
    
    struct tm *tms = gmtime((time_t *)&callDuration);
    mktime(tms);
    strftime(strTimeBuf, sizeof strTimeBuf, "%H:%M:%S", tms);
    return strTimeBuf;
    //return ctime(&callDuration);
}

static std::string transform_field_for_npacket(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    return std::to_string(call_info->npackets);
}

static std::string transform_field_for_state(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    return voip_call_state_name[call_info->call_state];
}

static std::string transform_field_for_comments(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;
    switch (call_info->protocol) {
    case VOIP_ISUP:
    {
        isup_calls_info_t *isup_info = (isup_calls_info_t *)call_info->prot_info;

        return std::to_string(isup_info->ni) 
             + '-'
             + std::to_string(isup_info->opc)
             + ' '
             + UTF8_RIGHTWARDS_ARROW
             + ' '
             + std::to_string(isup_info->ni)
             + std::to_string(isup_info->dpc);
    }
    break;
    case VOIP_H323:
    {
        h323_calls_info_t *h323_info = (h323_calls_info_t *)call_info->prot_info;
        gboolean flag = FALSE;
        static const char *  on_str  = "On";
        static const char *  off_str = "Off";
        if (call_info->call_state == VOIP_CALL_SETUP) {
            flag = h323_info->is_faststart_Setup;
        }
        else {
            if ((h323_info->is_faststart_Setup) && (h323_info->is_faststart_Proc)) {
                flag = TRUE;
            }
        }
        return std::string("Tunneling:") + std::string(h323_info->is_h245Tunneling ? on_str : off_str) + std::string(" Fast Start: ") + std::string(flag ? on_str : off_str);
    }
    break;
    case VOIP_COMMON:
    default:
        return call_info->call_comment == nullptr ? "" : std::string(call_info->call_comment);
    }
}

static std::string transform_field_for_from_dir_name(epan_dissect_t *edt, const field_info *pFinfo)
{
    const std::string & pcap_file_name = CapFileProcessor::GetCurrentProcessingPcapFilename();

    size_t dir_end = pcap_file_name.find_last_of('/');

    if (dir_end == std::string::npos)
        return "";

    return std::string(pcap_file_name.substr(0, dir_end));
}

struct transform_field_for_mixfilename {
public:
  transform_field_for_mixfilename(std::set<rtp_stream_info_t *> &ssrc_set) : _ssrc_set(ssrc_set) {}

  std::string get_mix_filename_no_suffix() {
    std::string Mixfilename = EtyConfig::GetInstance()->GetTblsDir() + "/voip/";
    if(CFG->GetValueOf<int>("DATE_SUBDIR_FLAG") == 1){
        time_t unixTime = time(0);
        tm     tm       = *localtime((time_t *)&unixTime);
        char time_str[32] = {0};
        strftime(time_str, sizeof time_str, "%Y%m%d/", &tm);
        Mixfilename += time_str;
        mkdir(Mixfilename.c_str(),  0755);
    }
    Mixfilename += "voip_mix_" + std::to_string(hundredThousandthSecond()) + std::to_string(random());
    return Mixfilename;
  }
  int do_system_cmd(std::string cmd) {
    int status = system(cmd.c_str());
    if (!(-1 != status && WIFEXITED(status) && 0 == WEXITSTATUS(status))) {
      return -1;
    }
    return 1;
  }
  //送进这个函数，表示一定有需要合路的超过两个文件
  std::string ffmpeg_mix_file(std::map<std::string,time_t> strAudioSet, std::set<std::string> strVideoSet) {

    std::string strTblTaskID_ = "_" + EtyConfig::GetInstance()->GetTaskID();
    std::string outstr = get_mix_filename_no_suffix();
    time_t                duration  = 0;  //单位为 '豪秒'
    std::string cmd = "ffmpeg -y";
    std::string audio_mix_file = outstr + "_audio" + strTblTaskID_ + ".wav";
    std::string video_mix_file = outstr + "_video" + strTblTaskID_ + ".mp4";
    std::string last_file_name ;
    // 先进行音频合路
    if (strAudioSet.size() > 1) {
      for (auto it = strAudioSet.begin(); it != strAudioSet.end(); ++it) {
        // 保证填充值为正数
        duration = it->second > duration ? it->second - duration : duration - it->second;
        if( it != strAudioSet.begin()){
          if (it->second >= duration) {
            //第二个文件开始时间在后面 设置第一个文件为左声道，也就是cmd中的第一个文件名
            cmd += " -i "  + last_file_name + " -i "+ it->first ;
            break;
          } else {
            //第一个文件开始时间在后面 设置第二个文件为左声道，也就是cmd中的第一个文件名
            cmd += " -i " + it->first + " -i " + last_file_name;
            break;
          }
        }
        last_file_name = it->first;
      }
      cmd +=
          " -filter_complex "//复杂滤镜
          "\"[0:a]pan=mono|c0=c0[left];" //合路左声道
          "[1:a]pan=mono|c0=c0[right];" ;//合路右声道
          if(duration > 0){
            cmd += "[right]adelay=" + std::to_string(duration*10) +  "|" +  std::to_string(duration*10) + "[rightdelayed];"; //设置右声道为短的音频  单位为 '毫秒'
            cmd += "[left][rightdelayed]amix=inputs=2:duration=longest[aout]\""  ; //设置合并为合路 以最长的音频为总时长
          }else {
            cmd += "[left][right]amix=inputs=2:duration=longest[aout]\""   ;//设置合并为合路 以最长的音频为总时长
          }
          cmd +=" -map \"[aout]\" " +
          audio_mix_file;
      do_system_cmd(cmd);
    } else if (strAudioSet.size() == 1) {
      audio_mix_file = strAudioSet.begin()->first;
    } else {
      audio_mix_file.clear();
    }
    cmd.clear();
    // 再进行视频合路
    if (strVideoSet.size() > 1) {
      cmd = "ffmpeg -y";
      for (std::set<std::string>::iterator it = strVideoSet.begin(); it != strVideoSet.end(); ++it) {
        cmd += " -i " + *it;
      }
      cmd +=
          "  -filter_complex \"[0:v][1:v]hstack=inputs=2[v]\" -map \"[v]\" -c:v "
          "libx264 -c:a copy " +
          video_mix_file;
      do_system_cmd(cmd);
    } else if (strVideoSet.size() == 1) {
      video_mix_file = *strVideoSet.begin();
    } else {
      video_mix_file.clear();
    }
    cmd.clear();

    //需要音视频合路
    if (strVideoSet.size() > 0 && strAudioSet.size() > 0) {
      outstr += strTblTaskID_ + ".mp4";
      cmd = "ffmpeg -y -i " + video_mix_file + " -i " + audio_mix_file + "  -c:v copy -c:a aac -strict experimental " + outstr;
      int status = system(cmd.c_str());
      if (!(-1 != status && WIFEXITED(status) && 0 == WEXITSTATUS(status))) {
        return "merge audio video err";
      }
      if (access(audio_mix_file.c_str(), F_OK) == 0) {
        remove(audio_mix_file.c_str());
      }
      if (access(video_mix_file.c_str(), F_OK) == 0) {
        remove(video_mix_file.c_str());
      }
    } else if (strVideoSet.size() > 0 && strAudioSet.size() == 0) {
      return video_mix_file;
    } else if (strVideoSet.size() == 0 && strAudioSet.size() > 0) {
      return audio_mix_file;
    }
    return outstr;
  }

  std::string operator()(epan_dissect_t *edt, const field_info *pFinfo) {
    std::string          strRst = "";
    ya_voip_info_keeper *_call_info = (ya_voip_info_keeper *)edt;

    //过滤重复数据
    int                   fileNum = 0;
    std::set<std::string> strVideoSet;
    std::set<std::string> strAudioSet;
    std::map<std::string,time_t> strAudioMap;   //通过文件名作为key  可以做到去重的功能
    for (auto &iter : _ssrc_set) {
      std::string strFileName = _call_info->getVoipFilenameOf(iter);
      time_t streamStartTime = _call_info->getVoipRtpStreamStartTime(iter);
      if (strFileName.empty()) {
        continue;
      }
      if (strFileName.find("wav")!= std::string::npos||strFileName.find("amr")!= std::string::npos) {
        strAudioMap[strFileName] = streamStartTime; //对音频做插入
      } else if(strFileName.find("raw")== std::string::npos){
        strVideoSet.emplace(strFileName);
      }
    }

    fileNum = strAudioMap.size() + strVideoSet.size();
    if (fileNum < 2) {
      return "";
    }
    std::string file_name = ffmpeg_mix_file(strAudioMap, strVideoSet);
    struct stat file_state;
    memset(&file_state ,0, sizeof(struct stat));
    if(0 != stat(file_name.c_str(), &file_state) && file_state.st_size == 0)
    {
      return "";
    }
    return file_name;
  }

public:
  std::set<rtp_stream_info_t *> &_ssrc_set;
};
struct transform_field_for_filename
{
public: 
    transform_field_for_filename(std::set<rtp_stream_info_t *> & ssrc_set):_ssrc_set(ssrc_set)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        std::string strRst = "";
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        const char * div = "";
        std::set<std::string> filename_set;
        // 去重
        for (auto &iter: _ssrc_set)
        {
            std::string strFileName = _call_info->getVoipFilenameOf(iter);
            if (strFileName.empty())
            {
                continue;
            }
            filename_set.insert(strFileName);
        }
        // 还是设定为 最多允许4个文件进行合路
        int num = 0;
        for(auto iter :filename_set){
            if(num > 3){
              break;
            }
            strRst += div;
            strRst += '"';
            iter.erase(iter.find("tmp_"),4);
            strRst += iter;
            strRst += '"';
            div = ",";
            num ++;
        }


        return strRst;
    }
public:
    std::set<rtp_stream_info_t *> & _ssrc_set;
};


struct transform_field_for_data_type_num
{
public: 
    transform_field_for_data_type_num(std::set<rtp_stream_info_t *> & ssrc_set):_ssrc_set(ssrc_set)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        std::string strRst = "";
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;

        const char * div = "";

        for (auto &iter: _ssrc_set)
        {
            std::string strFileName = _call_info->getVoipFilenameOf(iter);
            if (strFileName.empty())
            {
                continue;
            }

            strRst += _call_info->getVoipFilenameOf(iter);
            if(strRst.find("wav")!=std::string::npos ||
               strRst.find("amr")!=std::string::npos)
	    {
		return std::to_string(2);
  	    } else {
		return std::to_string(7);
	    }
           
        }
    }
public:
    std::set<rtp_stream_info_t *> & _ssrc_set;
};

struct transform_field_for_line_no
{
public: 
    transform_field_for_line_no(std::set<rtp_stream_info_t *> & ssrc_set):_ssrc_set(ssrc_set)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        std::string strRst = "";
        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;

        const char * div = "";

        for (auto &iter: _ssrc_set)
        {
            std::string strLineNo = _call_info->getVoipLineno(iter);
            if (strLineNo.empty())
            {
                continue;
            }

            strRst += div;
            strRst += '"';
            strRst += strLineNo;
            strRst += '"';
            div = ",";
        }

        return strRst;
    }
public:
    std::set<rtp_stream_info_t *> & _ssrc_set;
};

struct transform_field_for_rtp_stream_info
{
public:
    transform_field_for_rtp_stream_info(std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & rtp_info_map) : _rtp_info_map(rtp_info_map)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        std::string strRst = "";

        ya_voip_info_keeper * _call_info = (ya_voip_info_keeper *)edt;
        voip_calls_info_t * call_info = _call_info->voip_info;
        int num = 0;
        rapidjson::StringBuffer rtp_info_buf;
        rapidjson::Writer<rapidjson::StringBuffer> rtp_json(rtp_info_buf);
        rtp_json.StartArray();

        for (const auto& iter : _rtp_info_map)
        {
            if(num > 3){
              break;
            }
            if (iter.second != call_info->call_num)
            {
                continue;
            }
            rtp_json.StartObject();

            rtp_json.Key("Source Address");
            rtp_json.String(alloced_uptr(address_to_display(NULL, &(iter.first->src_addr)), [](void *p) { wmem_free(NULL, p); }).get());

            rtp_json.Key("Source Port");
            rtp_json.Int(iter.first->src_port);

            rtp_json.Key("Destination Address");

            rtp_json.String(alloced_uptr(address_to_display(NULL, &(iter.first->dest_addr)), [](void *p) { wmem_free(NULL, p); }).get());

            rtp_json.Key("Destination Port");
            rtp_json.Int(iter.first->dest_port);
            rtp_json.Key("SSRC");

            char hex_ssrc[11] = {0};
            strcpy(hex_ssrc, "0x");
            dword_to_hex(&hex_ssrc[2], iter.first->ssrc);
            rtp_json.String(hex_ssrc);

            rtp_json.Key("PayloadType");
            std::string payload_type_name(iter.first->payload_type_name);
            if(payload_type_name.size() == 0||
              payload_type_name.size() >10 ||
              payload_type_name.size() <3 ||
              payload_type_name.find("\\u") != std::string::npos||
              payload_type_name.find("@") != std::string::npos||
              payload_type_name.find("00") != std::string::npos||
              payload_type_name.find("not") != std::string::npos||
              payload_type_name.find("Source") != std::string::npos||
              payload_type_name.find("SiteID") != std::string::npos||
               payload_type_name.find("255") != std::string::npos||
               payload_type_name.find("?") != std::string::npos||
               isUTF8(iter.first->payload_type_name, 3)<0||
            !(payload_type_name == "AMR"  ||
              payload_type_name == "H264" ||
              payload_type_name == "g711U"||
              payload_type_name == "g711A"||
              payload_type_name == "opus" ||
              payload_type_name == "g723" ||
              payload_type_name == "H263-1998"||
              payload_type_name == "rtp_stream"))
            {
              payload_type_name = " ";
            }
            rtp_json.String(payload_type_name.c_str());
            rtp_json.Key("Packets");
            rtp_json.Int(iter.first->packet_count);
            rtp_json.Key("RtpStatus");
            rtp_json.String(iter.first->problem ? "Problem" : "");
            rtp_json.Key("RtpFileList");
            rtp_json.String(VoipStreamKeeper::getInstance()->get_voip_rtp_file_list(iter.first).c_str());
            rtp_json.EndObject();
            num ++;
        }

        rtp_json.EndArray();
        strRst = rtp_info_buf.GetString();

        return strRst;
    }


public:
    std::map<rtp_stream_info_t *, uint32_t,compare_rsi> & _rtp_info_map;
};

static std::string transform_field_for_zj_tunnel_layer(epan_dissect_t *edt, const field_info *pFinfo){

    ya_voip_info_keeper *_call_info = (ya_voip_info_keeper *)edt;

    return _call_info->flow_tunnel_info->tunnel_layer;

}

static std::string transform_field_for_zj_tunnel_addr(epan_dissect_t *edt, const field_info *pFinfo){

    ya_voip_info_keeper *_call_info = (ya_voip_info_keeper *)edt;

    return _call_info->flow_tunnel_info->tunnel_str;

}
static std::string  transform_field_for_Q931CallingPartyNumber(epan_dissect_t *edt, const field_info *pFinfo){


    ya_voip_info_keeper *_call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;


    if(call_info->protocol == VOIP_H323){
      return call_info->from_identity;;
    }
    return "";

}
static std::string  transform_field_for_Q931CalledPartyNumber(epan_dissect_t *edt, const field_info *pFinfo){


    ya_voip_info_keeper *_call_info = (ya_voip_info_keeper *)edt;
    voip_calls_info_t * call_info = _call_info->voip_info;

    if(call_info->protocol == VOIP_H323){
      return call_info->to_identity;;
    }
    return "";

}
static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // gch 2j 公共字段
    F_D_ITEM_GCH_COMMON_FIELDS(),

    F_D_ITEM("TAGTYPE"                      , ""                                , eMT_fromEdt      , "",     NULL),//trans_for_hwl_nettype(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("OPERATOR_TYPE"                , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_operator(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_NCODE"                     , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_ncode(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_ACCOUNT"                   , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_accont(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_ESN"                       , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_esn(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_MEID"                      , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_meid(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_LAC"                       , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_lac(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_SAC"                       , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_sac(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_CI"                        , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_cellid(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_ECGI"                      , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_ecgi(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_BSID"                      , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_bsid(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_GRE_KEY"                   , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_grekey(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_TAI"                       , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_tai(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_ECGI_MNC"                  , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_mnc(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("HW_APN"                       , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_hwl_apn(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("RTL_TEID"                     , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_rtl_teid(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("RTL_OUTTER_SRC"               , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_rtl_outsrc(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("RTL_OUTTER_DST"               , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_rtl_outdst(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("RTL_MSISDN"                   , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_rtl_msidsn(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("RTL_IMEI"                     , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_rtl_imei(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("RTL_IMSI"                     , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_rtl_imsi(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("RTL_TAC"                      , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_rtl_tac(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("RTL_PLMN_ID"                  , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_rtl_plmnid(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("RTL_ULI"                      , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_rtl_uli(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("RTL_BS"                       , ""                                , eMT_fromEdt      , "",     NULL), // trans_for_rtl_bs(ProtoFieldExtractorVoip::voip_frame_to_trailer_map,ProtoFieldExtractorVoip::voip_rtp_stream,ProtoFieldExtractorVoip::voip_rsi_setup_frame_map)),
    F_D_ITEM("DevNo"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("LineNo"                       , ""                                , eMT_fromEdt      , "",     transform_field_for_line_no((ProtoFieldExtractorVoip::voip_ssrc))),
    F_D_ITEM("LinkLayerType"                , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("isIPv6"                       , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("isMPLS"                       , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("nLabel"                       , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("innerLabel"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("otherLabel"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv1"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv2"                        , ""                                , eMT_fromEdt      , "0",    NULL),
    F_D_ITEM("resv3"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv4"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv5"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv6"                        , ""                                , eMT_fixed        , "",     transform_field_for_task_id),
    F_D_ITEM("resv7"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv8"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("CapDate"                      , ""                                , eMT_fromEdt      , "",     trans_for_trans_capdate),
    F_D_ITEM("DealDate"                     , ""                                , eMT_fromEdt      , "",     trans_for_trans_dealdate),
    F_D_ITEM("SrcIp"                        , ""                                , eMT_fromEdt      , "",     trans_for_src_address),
    F_D_ITEM("SrcCountry"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("SrcArea"                      , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("SrcProvince"                  , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("SrcCity"                      , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("SrcCarrier"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("DstIp"                        , ""                                , eMT_fromEdt      , "",     trans_for_dst_address),
    F_D_ITEM("DstCountry"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("DstArea"                      , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("DstProvince"                  , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("DstCity"                      , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("DstCarrier"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("SrcPort"                      , ""                                , eMT_fromEdt      , "",     trans_for_src_port),
    F_D_ITEM("DstPort"                      , ""                                , eMT_fromEdt      , "",     trans_for_dst_port),
    F_D_ITEM("C2S"                          , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("Proto"                        , ""                                , eMT_fromEdt      , "",     trans_for_trans_proto),
    F_D_ITEM("TTL"                          , ""                                , eMT_fromEdt      , "",     trans_for_trans_ttl),
    F_D_ITEM("StartTime"                    , ""                                , eMT_fromEdt      , "",     trans_for_start_time),
    F_D_ITEM("StopTime"                     , ""                                , eMT_fromEdt      , "",     trans_for_end_time),
    F_D_ITEM("InitialSpeaker"               , ""                                , eMT_fromEdt      , "",     trans_for_initial),
    F_D_ITEM("From"                         , ""                                , eMT_fromEdt      , "",     transform_field_for_from),
    F_D_ITEM("To"                           , ""                                , eMT_fromEdt      , "",     transform_field_for_to),
    F_D_ITEM("Q931CallingPartyNumber"                     , ""                  , eMT_fromEdt      , "",     transform_field_for_Q931CallingPartyNumber),
    F_D_ITEM("Q931CalledPartyNumber"                     , ""                   , eMT_fromEdt      , "",     transform_field_for_Q931CalledPartyNumber),
    F_D_ITEM("VoipProto"                    , ""                                , eMT_fromEdt      , "",     transform_field_for_proto),
    F_D_ITEM("Duration"                     , ""                                , eMT_fromEdt      , "",     transform_field_for_duration),
    F_D_ITEM("NPacket"                      , ""                                , eMT_fromEdt      , "",     transform_field_for_npacket),
    F_D_ITEM("State"                        , ""                                , eMT_fromEdt      , "",     transform_field_for_state),
    F_D_ITEM("Comments"                     , ""                                , eMT_fromEdt      , "",     transform_field_for_comments),
    F_D_ITEM("FromDir"                      , ""                                , eMT_fromEdt      , "",     transform_field_for_from_dir_name),
    F_D_ITEM("Json_of_rtp"                  , ""                                , eMT_fromEdt      , "",     transform_field_for_rtp_stream_info(ProtoFieldExtractorVoip::voip_rtp_stream)),
    F_D_ITEM("FileName"                     , ""                                , eMT_fromEdt      , "",     transform_field_for_filename(ProtoFieldExtractorVoip::voip_ssrc)),
    F_D_ITEM("MixFileName"                  , ""                                , eMT_fromEdt        , "",     transform_field_for_mixfilename(ProtoFieldExtractorVoip::voip_ssrc)),

};

ProtoFieldExtractorVoip::ProtoFieldExtractorVoip():ProtoFieldExtractor("VOIP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}


bool ProtoFieldExtractorVoip::ExtractSpecialFields(epan_dissect_t * edt, RecordWriter * pWriter)
{
    return false;
}

uint32_t ProtoFieldExtractorVoip::AddVoipSsrc(rtp_stream_info_t *rtp_stream)
{
    voip_ssrc.emplace(rtp_stream);
    return voip_ssrc.size();
}

uint32_t ProtoFieldExtractorVoip::AddVoipRtpStream(uint32_t num,rtp_stream_info_t * p_rtp_stream)
{
    voip_rtp_stream.emplace(p_rtp_stream,num);
    return voip_rtp_stream.size();
}

void ProtoFieldExtractorVoip::CleanVoipSsrc()
{
    voip_ssrc.clear();
}

void ProtoFieldExtractorVoip::CleanVoipRtpStream()
{
    voip_rtp_stream.clear();
}

