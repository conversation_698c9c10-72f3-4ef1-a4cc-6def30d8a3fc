/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_mysql.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-09-21
* 编    码 : zhangsx      '2018-09-21
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include <iconv.h>

#include <algorithm>
#include <iostream>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"


#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_mysql.h"

#include "yaEty_content_reassembly_share_mem.h"
#include "yaEty_field_extractor_telnet.h"

/*****************************************************************
*Function    :transform_field_for_C2S
*Description :MySQL C2S
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
        
static std::string transform_field_for_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{

    //field_info * pTempfinfo = get_first_field_info_from_interesting_fields(edt, "rst.dstport");
    //unsigned int && src_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pTempfinfo->value)));


    //pTempfinfo = get_first_field_info_from_interesting_fields(edt, "tcp.dstport");
    //unsigned int && dst_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pTempfinfo->value)));

    std::string && mysql_c2s = get_first_field_value_from_interesting_fields(edt, "mysql.salt");

    

    return mysql_c2s != "" ? "S2C" : "C2S";

}

/*****************************************************************
*Function    :transform_field_for_RequestArgs
*Description :MySQL RequestArgs (从最后输出显示的文本信息中获取)
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_RequestArgs(epan_dissect_t *edt, const field_info *pFinfo)
{
    std::string strRes = "";
    
    field_info * pFinfoCmd = get_first_field_info_from_interesting_fields(edt, "mysql.command");

    if (NULL != pFinfoCmd)
    {
        char *pFormated = format_text(NULL,
            tvb_get_ptr(edt->tvb, pFinfoCmd->start + pFinfoCmd->length, -1),
            (int)tvb_captured_length(edt->tvb) - (pFinfoCmd->start + pFinfoCmd->length));                                       //这里直接获取mysql.command原始数据的地址和长度，以直接转换
       
        strRes = pFormated;

        wmem_free(NULL, pFormated);
    }
    return strRes;
   
}

/*****************************************************************
*Function    :transform_field_for_Cmd
*Description :MySQL Cmd (从源码所在文件内的局部变量中获取)
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_Cmd(epan_dissect_t *edt, const field_info *pFinfo)
{

    int lValue = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));          //mysqk协议中字段对应值为整数         
    const char *pString = (const char *)try_val_to_str_ext(lValue, (value_string_ext *)(pFinfo->hfinfo->strings)); //提取对应字符串
    if (NULL == pString)
    {
        return "";
    }
    return std::string(pString);

}

/*****************************************************************
*Function    :transform_field_for_Level
*Description :MySQL Level (从源码所在文件内的局部变量中获取)
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_Level(epan_dissect_t *edt, const field_info *pFinfo)
{
    int tds_type = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));

    const char *pString = try_val_to_str(tds_type, (const value_string *)(pFinfo->hfinfo->strings));

    if (NULL == pString)
    {
        return "";
    }

    return std::string(pString);
}
static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    
    F_D_ITEM("SrcPort"                          , "tcp.srcport"                     , eMT_direct,       "",         NULL),
    F_D_ITEM("DstPort"                          , "tcp.dstport"                     , eMT_direct,       "",         NULL),
    F_D_ITEM("C2S"                              , ""                                , eMT_fromEdt,      "",         transform_field_for_C2S),
    F_D_ITEM("Proto"                            , "ip.proto"                        , eMT_direct,       "",         NULL),
    F_D_ITEM("TTL"                              , "ip.ttl"                          , eMT_direct,       "",         NULL),

    F_D_ITEM("MessageType"                      , "mysql.message"                   , eMT_direct,       "",         NULL),
    F_D_ITEM("ProtocolVersion"                  , "mysql.protocal"                  , eMT_direct,       "",         NULL),
    F_D_ITEM("ServerVersion"                    , "mysql.version"                   , eMT_direct,       "",         NULL),
    F_D_ITEM("ServerThreadid"                   , "mysql.thread_id"                 , eMT_direct,       "",         NULL),
    F_D_ITEM("ServerChallenge"                  , "mysql.salt"                      , eMT_direct,       "",         NULL),
    F_D_ITEM("ExtServerChallenge"               , "mysql.salt2"                     , eMT_direct,       "",         NULL),
    F_D_ITEM("ServerCapabilities"               , "mysql.caps.server"               , eMT_direct,       "",         NULL),
    F_D_ITEM("ServerStatus"                     , "mysql.server_status"             , eMT_direct,       "",         NULL),
    F_D_ITEM("Charset"                          , "mysql.charset"                   , eMT_direct,       "",         NULL),
    F_D_ITEM("ClientCapabilities"               , "mysql.caps.client"               , eMT_direct,       "",         NULL),
    F_D_ITEM("ExtClientCapabilities"            , "mysql.extcaps.client"            , eMT_direct,       "",         NULL),
    F_D_ITEM("UserName"                         , "mysql.user"                      , eMT_direct,       "",         NULL),
    F_D_ITEM("ClientChallenge"                  , "mysql.password"                  , eMT_fixed,        "",         NULL),
    F_D_ITEM("DbName"                           , "mysql.field.db"                  , eMT_direct,       "",         NULL),
    F_D_ITEM("TableName"                        , "mysql.field.table"               , eMT_direct,       "",         NULL),
    F_D_ITEM("SlaveServerId"                    , ""                                , eMT_fixed,        "",         NULL),
    F_D_ITEM("MainServerId"                     , "mysql.binlog.server_id"          , eMT_direct,       "",         NULL),
    F_D_ITEM("MainServerIp"                     , ""                                , eMT_fixed,        "",         NULL),
    F_D_ITEM("MainServerUserName"               , ""                                , eMT_fixed,        "",         NULL),
    F_D_ITEM("MainServerPass"                   , ""                                , eMT_fixed,        "",         NULL),
    F_D_ITEM("MainServerPort"                   , ""                                , eMT_fixed,        "",         NULL),
    F_D_ITEM("SafeLevel"                        , "mysql.shutdown"                  , eMT_transform,    "",         transform_field_for_Level),
    F_D_ITEM("RequestCommand"                   , "mysql.command"                   , eMT_transform,    "",         transform_field_for_Cmd),
    F_D_ITEM("RequestArgs"                      , ""                                , eMT_fromEdt,      "",         transform_field_for_RequestArgs),
};

ProtoFieldExtractorMysql::ProtoFieldExtractorMysql() :ProtoFieldExtractor("MySQL", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}


bool ProtoFieldExtractorMysql::ExtractSpecialFields(epan_dissect_t * edt, RecordWriter * pWriter)
{
    return true;
}














