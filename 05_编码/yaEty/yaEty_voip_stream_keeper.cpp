/****************************************************************************************
* 文 件 名 : yaEty_voip_stream_keeper.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2019-03-20
* 编    码 : zhangsx      '2019-03-20
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2019 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_voip_stream_keeper.h"
#include "yaEty_rtp_stream.h"
#include "yaEty_rtp_stream_keeper.h"
#include <fstream>
#include <stdio.h>

#include "ui/voip_calls.h"
#include <epan/rtp_pt.h>

ya_voip_info_keeper VoipStreamKeeper::voip_info_keeper_;
funTransfrom_t VoipStreamKeeper::lineNoParser = transform_null_lineno;

VoipStreamKeeper::VoipStreamKeeper()
    : m_voipWriter("VOIP", "voip", CFG->GetValueOf<CSTR>("voip_tbl_dir"))
    , m_rtpStreamWriter("RTP_STREAM", "rtp_stream")
{
    memset(&tapinfo_, 0, sizeof(tapinfo_));
    tapinfo_.tap_packet  = onTapPacket;
    tapinfo_.tap_draw    = onDrawPackets;
    tapinfo_.tap_data    = this;
    tapinfo_.callsinfos  = g_queue_new();
    tapinfo_.h225_cstype = H225_OTHER;
    tapinfo_.fs_option   = FLOW_ALL;                      /* flow show option */

    tapinfo_.graph_analysis = sequence_analysis_info_new();
    tapinfo_.graph_analysis->name = "voip";

    registerTapListener();
    voip_calls_init_all_taps(&tapinfo_);


    m_voipExt.AddDebugFieldsDesc();
    m_rtpStreamExt.AddDebugFieldsDesc();

    m_voipWriter.writeTblField(CFG->GetFieldsDir(), &m_voipExt);
    m_rtpStreamWriter.writeTblField(CFG->GetFieldsDir(), &m_rtpStreamExt);

    lineNoParserInit();
    voip_file_num_ = 0;
    rtp_stream_file_num_ = 0;
}

VoipStreamKeeper::~VoipStreamKeeper()
{
    sequence_analysis_info_free(tapinfo_.graph_analysis);
    voip_calls_reset_all_taps(&tapinfo_);
    voip_calls_remove_all_tap_listeners(&tapinfo_);
    g_queue_free(tapinfo_.callsinfos);
    removeTapListener();

}
void VoipStreamKeeper::RtpStreamRenameAllTmpFile(){
  for (auto index:rtp_filename_v_) {
        auto newname = index;
      if(index.find( "tmp_") != std::string::npos){
        newname.erase(index.find( "tmp_"),4);
        int rc = rename(index.c_str(), newname.c_str());
      }
  }
  rtp_filename_v_.clear();
}
VoipStreamKeeper* VoipStreamKeeper::getInstance()
{
    static VoipStreamKeeper *voipStreamKeeper = new VoipStreamKeeper;
    
    return voipStreamKeeper;
}

void VoipStreamKeeper::registerTapListener()
{
    GString *error_string;

    error_string = register_tap_listener("sip", this, "", TL_REQUIRES_PROTO_TREE, NULL, &VoipStreamKeeper::onTapSipPackets, NULL);

    if (error_string != NULL)
    {
        // TODO:
    }
    error_string = register_tap_listener("q931", this, "", TL_REQUIRES_PROTO_TREE, NULL, &VoipStreamKeeper::onTapQ931Packets, NULL);

    if (error_string != NULL)
    {
        // TODO:
    }

    error_string = register_tap_listener("h225", this, "", TL_REQUIRES_PROTO_TREE, NULL, &VoipStreamKeeper::onTapQ931Packets, NULL);

    if (error_string != NULL)
    {
        // TODO:
    }
    error_string = register_tap_listener("h245", this, "", TL_REQUIRES_PROTO_TREE, NULL, &VoipStreamKeeper::onTapQ931Packets, NULL);

    if (error_string != NULL)
    {
        // TODO:
    }
    error_string = register_tap_listener("frame", &this->tapinfo_, "", 0, &VoipStreamKeeper::onResetPackets, NULL, &VoipStreamKeeper::onDrawPackets);
    if (error_string != NULL)
    {
        // TODO:
    }

    
}

void VoipStreamKeeper::removeTapListener()
{
    remove_tap_listener(&this->tapinfo_);
    remove_tap_listener(this);
}

void VoipStreamKeeper::onResetPackets(void * arg)
{
    voip_calls_tapinfo_t *tapinfo = (voip_calls_tapinfo_t *)arg;

    sequence_analysis_info_free(tapinfo->graph_analysis);

    tapinfo->graph_analysis = sequence_analysis_info_new();

    voip_calls_reset_all_taps(tapinfo);

    VoipStreamKeeper *voip_stream_keeper = static_cast<VoipStreamKeeper *>(tapinfo->tap_data);
    if (voip_stream_keeper) {
        voip_stream_keeper->cleanRtpStreamSsrc();
        voip_stream_keeper->cleanRtpStreamInfo();
    }
}

// arg  -> voip_calls_tapinfo_t
// arg2 -> _rtp_info *
int VoipStreamKeeper::onTapPacket(void * arg, packet_info * pinfo, epan_dissect_t * edt, const void * arg2 /* _rtp_info */)
{
    voip_calls_tapinfo_t *tapinfo = (voip_calls_tapinfo_t *)arg;
    VoipStreamKeeper *p = (VoipStreamKeeper *)tapinfo->tap_data;

    p->dissectFlowTunnel(edt);
    return p->onTapVoipPacket(tapinfo, pinfo, edt);
}

int VoipStreamKeeper::onTapVoipPacket(voip_calls_tapinfo_t *tapinfo, packet_info * pinfo, epan_dissect_t * edt)
{
    // 获取该文件中的首个 voip packet 的时间(无论是 rtp, sip, h323)
    if (!voip_trailer_.capdate.empty())
    {
        return 0;
    }

    voip_trailer_.capdate  = unixTime2Str(pinfo->abs_ts.secs);
    return 0;
}

void VoipStreamKeeper::onDrawPackets(void * arg)
{
    voip_calls_tapinfo_t *tapinfo = (voip_calls_tapinfo_t *)arg;

    if (!tapinfo || !tapinfo->redraw) {
        return;
    }

    //回传this指针
    VoipStreamKeeper *voip_stream_keeper = static_cast<VoipStreamKeeper *>(tapinfo->tap_data);

    //将call_num与rsi做关联
    GList *graph_item = g_queue_peek_nth_link(tapinfo->graph_analysis->items, 0);
    for (; graph_item; graph_item = g_list_next(graph_item)) {
        for (GList *rsi_entry = g_list_first(tapinfo->rtp_stream_list); rsi_entry; rsi_entry = g_list_next(rsi_entry)) {
            seq_analysis_item_t * sai = (seq_analysis_item_t *)graph_item->data;
            rtp_stream_info_t *rsi = (rtp_stream_info_t *)rsi_entry->data;

            if (rsi->call_num < 0 && rsi->start_fd->num == sai->frame_number) {
                rsi->call_num = sai->conv_num;
            }
        }
    }

    // 在 writeVoipTbl 之前先通过 RtpStreamKeeper pcap 文件处理完成
    RtpStreamKeeper::getInstance()->onPcapProcessDone();

    // 检测 rtp stream 是否还原完成, 必要时进行打印
    // RtpStreamKeeper::getInstance()->anyStreamHasPktToDecode();

    if (voip_stream_keeper) {
        voip_stream_keeper->updateList(tapinfo->callsinfos);
        voip_stream_keeper->writeSipFileTbl(tapinfo);

        g_queue_clear(tapinfo->callsinfos);
    }

}


int VoipStreamKeeper::onTapSipPackets(void *arg, packet_info *pinfo, epan_dissect_t *edt, const void *arg2)
{
    VoipStreamKeeper *voip_stream_keeper = (VoipStreamKeeper *)arg;
    voip_stream_keeper->dissectSip(edt);
    voip_stream_keeper->dissectTrailer(edt);

    return 0;
}

int VoipStreamKeeper::onTapQ931Packets(void *arg, packet_info *pinfo, epan_dissect_t *edt, const void *arg2)
{
    VoipStreamKeeper *voip_stream_keeper = (VoipStreamKeeper *)arg;
    voip_stream_keeper->dissectH323(edt);
    voip_stream_keeper->dissectTrailer(edt);
    return 0;
}

void VoipStreamKeeper::doVoipClean(void)
{
    //do clean
    voip_tbl_init();
    voip_map.clear();
    writeVoipTblDone();
    m_voipExt.CleanVoipSsrc();
    m_voipExt.CleanVoipRtpStream();
    m_rtpStreamWriter.renameAllTmpTbl();
    m_voipWriter.renameAllTmpTbl();
    RtpStreamRenameAllTmpFile();
}
void VoipStreamKeeper::voip_tbl_init()
{
    if (voip_info_keeper_.voip_info != nullptr)
    {
        voip_info_keeper_.voip_info = nullptr;
    }

    if (voip_info_keeper_.trailer != nullptr)
    {
        voip_info_keeper_.trailer = nullptr;
    }
    voip_info_keeper_.reset();
}
void VoipStreamKeeper::set_voip_rtp_stream_start_time(const rtp_stream_info_t *p, time_t start_time)
{
    voip_info_keeper_.setVoipRtpStreamStartTime(p, start_time);
}
void VoipStreamKeeper::set_voip_tbl_name(const rtp_stream_info_t *p, const char * file_name)
{
    voip_info_keeper_.setVoipFileInfo(p, file_name, 0);
}
void VoipStreamKeeper::set_voip_file_creat_time_name(const rtp_stream_info_t *p, time_t creat_time)
{
    voip_info_keeper_.setVoipFileInfo(p, creat_time);
}
void VoipStreamKeeper::set_voip_pkt_loss_count(const rtp_stream_info_t *p, const ya_rtp_loss_info LossInfo)
{
    voip_info_keeper_.setVoipLossInfo(p, LossInfo);
}
void VoipStreamKeeper::set_voip_line_no(const rtp_stream_info_t *p, epan_dissect_t *edt)
{
    if (edt == nullptr)
        return;

    field_info * pinfo = get_first_field_info_from_interesting_fields(edt, "eth.src");
    std::string lineno = lineNoParser(edt, pinfo);

    voip_info_keeper_.setVoipLineno(p, lineno.c_str());
}

void VoipStreamKeeper::set_voip_rtp_size(const rtp_stream_info_t *info, uint32_t file_size)
{
    voip_info_keeper_.setVoipFileSize(info, file_size);
}

void VoipStreamKeeper::set_voip_tbl_info(voip_calls_info_t * voip_info)
{
    voip_info_keeper_.voip_info = voip_info;
}

ya_voip_info_keeper * VoipStreamKeeper::get_voip_info_keeper()
{
    return &voip_info_keeper_;
}

std::string VoipStreamKeeper::get_voip_rtp_file_list(rtp_stream_info_t * rsi){
  std::string rtp_stream_filelist_str;
      voip_key_t v_key = fromRtpStreamInfo(rsi);
  for(int i = 0;i < rtp_stream_filelist_[v_key].size();i++){
        rtp_stream_filelist_str += rtp_stream_filelist_[v_key].at(i).substr(rtp_stream_filelist_[v_key].at(i).find("tmp_") + 4);
        rtp_stream_filelist_str += ",";
  }
  if(rtp_stream_filelist_str.size()>1){
      rtp_stream_filelist_str.pop_back();
  }
  return rtp_stream_filelist_str;
}
void VoipStreamKeeper::lineNoParserInit()
{
    lineNoParser = CFG->LineBeOrLe() ?  transform_mac_for_lineno_le : transform_mac_for_lineno;

    if (!CFG->AddLineNo())
        lineNoParser = transform_null_lineno;
}

void VoipStreamKeeper::cleanRtpStreamSsrc()
{
    rtp_streams_set.clear();
}

void VoipStreamKeeper::cleanMapSipInfoInfo(RtpPortInfo rtp_tuple)
{
    map_sip_info_.erase(rtp_tuple);

}
void VoipStreamKeeper::cleanRtpStreamInfo()
{
    sip_info_.stream_status = 0;
    sip_info_.rtp_info = nullptr;
    sip_info_.file_name.clear();

    sip_info_.sample_rate.clear();
    sip_info_.from.clear();
    sip_info_.to.clear();
    sip_info_.payload_name.clear();

    sip_info_.trailer = nullptr;
    sip_info_.file_size = 0;
    sip_info_.line_no.clear();

    voip_trailer_.cleanAll();
    voip_flow_tunnel_info_.cleanAll();
}

void VoipStreamKeeper::insertRtpStreamFileToVector(std::string filename){
  rtp_filename_v_.push_back(filename);
}
void VoipStreamKeeper::writeRtpStreamFileTbl(rtp_stream_info_t * rsi,RtpStream * rtpStream)
{

    auto rtpStreamInfo = getRsiFromTapinfo(rsi);
    matchSipConvNum();
    if(rtpStreamInfo == NULL){
        rtpStreamInfo = rsi;
    }
    const char* gch_fields_value  = rtpStream->getTrailerInfo();
    RtpPortInfo rtpPort;
    rtpPort.port_dst = rsi->src_port;
    rtpPort.port_src = rsi->dest_port;
    rtpPort.payload_type = rsi->payload_type;

    auto sip_info_iter = map_sip_info_.find(rtpPort);
    if(sip_info_iter != map_sip_info_.end()){
        sip_info_.payload_name = sip_info_iter->second.payload_name;
        sip_info_.sample_rate  = sip_info_iter->second.sample_rate;
        sip_info_.from         = sip_info_iter->second.from;
        sip_info_.to           = sip_info_iter->second.to;
    }else {
      // 查找反向表
        rtpPort.port_dst = rsi->dest_port;
        rtpPort.port_src = rsi->src_port;
        sip_info_iter = map_sip_info_.find(rtpPort);
        if(sip_info_iter != map_sip_info_.end()){
        sip_info_.payload_name = sip_info_iter->second.payload_name;
        sip_info_.sample_rate  = sip_info_iter->second.sample_rate;
        sip_info_.from         = sip_info_iter->second.from;
        sip_info_.to           = sip_info_iter->second.to;
        }
    }
    sip_info_.trailer   = &voip_trailer_;
    sip_info_.flow_tunnel_info   = &voip_flow_tunnel_info_;
    sip_info_.rtp_info  = rtpStreamInfo;
    sip_info_.file_name = get_voip_info_keeper()->getVoipFilenameOf(rsi);
    sip_info_.file_size = get_voip_info_keeper()->getVoipFilesizeOf(rsi);
    sip_info_.line_no   = get_voip_info_keeper()->getVoipLineno(rsi);
    sip_info_.setPktLossInfo(get_voip_info_keeper()->getVoipLossInfo(rsi));
    sip_info_.setFileCreatTime(get_voip_info_keeper()->getVoipFileCreatTime(rsi));
    get_voip_info_keeper()->setFlowTrailerInfo(rsi, rtpStream->getTrailerInfo());
    for (auto &iter : voip_map) {
      voip_calls_info_t *voip_info = &(iter.second);
      if(voip_info->call_num ==rtpStreamInfo->call_num){
          if(voip_info->protocol == VOIP_H323){
            sip_info_.Q931CallingPartyNumber = voip_info->from_identity;
            sip_info_.Q931CalledPartyNumber =  voip_info->to_identity;
          }
      }
    }
    std::string file_name;
    //第一次输出 传入rtp信息
    if(rtp_streams_set.find(rsi->ssrc) == rtp_streams_set.end())
    {
        rtp_streams_set.emplace(rsi->ssrc);
    }
    auto p_rtpStreamWriter = m_rtpStreamExt.GetRecordWriter();
    if (p_rtpStreamWriter != nullptr
        && RtpStreamKeeper::getInstance()->getRtpMediaType(rsi) != RtpMediaType::unknown)
    {
        p_rtpStreamWriter->writeRawFields(gch_fields_value, GCHFieldsExtractor::get_fields_cnt());
        m_rtpStreamExt.ExtractPlainFields((epan_dissect_t *)&sip_info_, p_rtpStreamWriter);
        rtp_stream_file_num_ ++;
        p_rtpStreamWriter->writeNextRecord();
    }
    //写完rtp_stream自己的文件后，为VOIP合路做准备，输出解码后的文件名
    if (sip_info_.file_name.size() == 0) {
    return;
    }
    file_name = sip_info_.file_name;
    std::string payload_type_name ;
    if (sip_info_.rtp_info->payload_type_name != NULL) {
      payload_type_name = sip_info_.rtp_info->payload_type_name;
    }
    if (file_name.find(".raw") != std::string::npos) {
      if (sip_info_.payload_name.find( "AMR")!=std::string::npos || payload_type_name.find( "AMR") != std::string::npos ) {
          file_name.erase(file_name.find(".raw"));
          file_name += ".amr";
      } else if (sip_info_.payload_name.find( "263")!=std::string::npos ||payload_type_name .find("263")!= std::string::npos)  {
          file_name.erase(file_name.find(".raw"));
          file_name += ".mkv";
      } else if (sip_info_.payload_name.find( "g728")!=std::string::npos ||
                  sip_info_.payload_name.find( "g723")!=std::string::npos ||
                  payload_type_name.find("g723")!= std::string::npos ||
                  payload_type_name.find("g728") != std::string::npos) {
          file_name.erase(file_name.find(".raw"));
          file_name += ".wav";
      } else if (sip_info_.payload_name.find( "261")!=std::string::npos ||payload_type_name.find("261") != std::string::npos) {
          file_name.erase(file_name.find(".raw"));
          file_name += ".avi";
      }
    }
    voip_key_t v_key = fromRtpStreamInfo(rsi);
    if(file_name.find(".raw") == std::string::npos){
        rtp_stream_filelist_[v_key].push_back(file_name);
    }

    if (sip_info_.stream_status == 1)
    {
        sip_info_.stream_status = 0;
    }
    sip_info_.Q931CallingPartyNumber.clear();
    sip_info_.Q931CalledPartyNumber .clear();


}
void VoipStreamKeeper::writeSipFileTbl(voip_calls_tapinfo_t *p_tapinfo){
    matchSipConvNum();
    RecordWriter *pWriter = m_voipExt.GetRecordWriter();
    long          rtp_state = -1;
    std::string  gch_fields_value;
    //遍历每一个rsi，如果存在call_num,则将rsi添加到m_voipExt中
    for (GList *rsi_entry = g_list_first(p_tapinfo->rtp_stream_list); rsi_entry; rsi_entry = g_list_next(rsi_entry)) {
        rtp_stream_info_t *rsi = (rtp_stream_info_t *)rsi_entry->data;
        if (!rsi)
            continue;
        //利用这个接口更新filelist
        if (!(rsi->call_num < 0)) {
            getRsiFromTapinfo(rsi);
            rtp_state = rsi->call_num;
        }
    }

    if (rtp_state < 0) {
        m_voipExt.CleanVoipSsrc();
        m_voipExt.CleanVoipRtpStream();
        return;
    }
    for (auto &iter : voip_map) {
        voip_calls_info_t *voip_info = &(iter.second);

        set_voip_tbl_info(voip_info);
        get_voip_info_keeper()->trailer = &voip_trailer_;
        get_voip_info_keeper()->flow_tunnel_info = &voip_flow_tunnel_info_;

        // 通过voip_filelist_中保存的rsi,从rtp_stream_filelist_ 中拿取文件数量
        int max_file_num = 0;
        for (auto &index : voip_rsi_list_[voip_info->call_num]) {
            m_voipExt.AddVoipSsrc(index);
            m_voipExt.AddVoipRtpStream(voip_info->call_num,index);
            voip_key_t v_key = fromRtpStreamInfo(index);
            if(max_file_num == 0){
                max_file_num =rtp_stream_filelist_[v_key].size() ;
            }
            max_file_num= max_file_num < rtp_stream_filelist_[v_key].size() ? max_file_num:rtp_stream_filelist_[v_key].size();
        }
        // hs2j : 在同一个文件中出现端口相同但ssrc不同的rtp_stream 会被关联到同一个voip中
        // 实际上这种情况是错误的 不能判断哪一对rtp_stream是真正的的主对端
        // 直接退出
        std::set <std::tuple<uint16_t, uint16_t,uint32_t>> voip_rsi_port_;
        for (auto &index : voip_rsi_list_[voip_info->call_num]) {
              voip_rsi_port_.emplace(std::make_tuple(index->src_port,index->dest_port,index->ssrc));
        }
        if (voip_rsi_list_[voip_info->call_num].size()!= voip_rsi_port_.size()) {
            max_file_num = 0;
        }
          //在这里做多个文件的切分输出
          for (int file_num = 0 ; file_num < max_file_num; file_num++) {
              for (auto &index : voip_rsi_list_[voip_info->call_num]) {
                  voip_key_t v_key = fromRtpStreamInfo(index);
                  if (file_num < rtp_stream_filelist_[v_key].size()) {
                    gch_fields_value = get_voip_info_keeper()->getFlowTrailerInfo(index);
                    if (gch_fields_value.size() == 0) {
                      for (int i = 0; i < GCHFieldsExtractor::get_fields_cnt(); i++) {
                        gch_fields_value += '|';
                      }
                    }
                    VoipStreamKeeper::set_voip_tbl_name(index, rtp_stream_filelist_[v_key].at(file_num).c_str());
                    VoipStreamKeeper::set_voip_file_creat_time_name(index, time(NULL));
                  } else {
                    VoipStreamKeeper::set_voip_tbl_name(index, "");
                    VoipStreamKeeper::set_voip_file_creat_time_name(index, time(NULL));
                  }
              }
              if (pWriter != nullptr) {
                  pWriter->writeRawFields(gch_fields_value.c_str(), GCHFieldsExtractor::get_fields_cnt());
                  m_voipExt.ExtractPlainFields((epan_dissect_t *)get_voip_info_keeper(), pWriter);
                  voip_file_num_ ++;
                  pWriter->writeNextRecord();
              }
          }


        m_voipExt.CleanVoipSsrc();
        m_voipExt.CleanVoipRtpStream();
    }
    rtp_stream_filelist_.clear();
    voip_rsi_list_.clear();
    voip_tbl_init();
    voip_map.clear();
}


//进行rtp与sip协议的关联
void VoipStreamKeeper::matchSipConvNum(){
   GList *graph_item = g_queue_peek_nth_link(tapinfo_.graph_analysis->items, 0);
    for (; graph_item; graph_item = g_list_next(graph_item)) {
        seq_analysis_item_t * sai = (seq_analysis_item_t *)graph_item->data;
        for (GList *rsi_entry = g_list_first(tapinfo_.rtp_stream_list); rsi_entry; rsi_entry = g_list_next(rsi_entry)) {
            rtp_stream_info_t *rsi = (rtp_stream_info_t *)rsi_entry->data;

            if (rsi->call_num < 0 && rsi->start_fd->num == sai->frame_number) {
                rsi->call_num = sai->conv_num;
            }
        }
    }
    updateList(tapinfo_.callsinfos);
}

//在rtp解码时，回传的rtp_stream_info内容不全，使用tapinfo_中的rtp_stream_info
rtp_stream_info_t * VoipStreamKeeper::getRsiFromTapinfo(rtp_stream_info_t* rsi)
{
   rtp_stream_info_t *tap_rsi =NULL;
    updateList(tapinfo_.callsinfos);
    for (GList *rsi_entry = g_list_first(tapinfo_.rtp_stream_list); rsi_entry; rsi_entry = g_list_next(rsi_entry)) {
            rtp_stream_info_t *tmp_rsi = (rtp_stream_info_t *)rsi_entry->data;
            if (!rsi) continue;

            if (voip_map.find((guint)tmp_rsi->call_num) != voip_map.end()) {
                if (!(tmp_rsi->call_num < 0))
                {
                    m_voipExt.AddVoipRtpStream(tmp_rsi->call_num, tmp_rsi);
                    voip_rsi_list_[tmp_rsi->call_num].emplace(tmp_rsi);
                }
                else
                {
                    voip_tbl_init();
                }
            }
            if(tmp_rsi->ssrc == rsi->ssrc &&  tmp_rsi->payload_type == rsi->payload_type){
              tap_rsi = tmp_rsi;
            }
    }
    return tap_rsi;
}
#if 0
void VoipStreamKeeper::writeVoipTbl(voip_calls_tapinfo_t *p_tapinfo)
{
    RecordWriter* pWriter = m_voipExt.GetRecordWriter();
    long rtp_state = -1;

    const char* gch_fields_value = m_gch_extr ? m_gch_extr->get_value() : NULL;

    std::map<uint16_t, std::set<rtp_stream_info_t *> > voip_filelist;

    for (GList *rsi_entry = g_list_first(p_tapinfo->rtp_stream_list); rsi_entry; rsi_entry = g_list_next(rsi_entry)) {
        rtp_stream_info_t *rsi = (rtp_stream_info_t *)rsi_entry->data;
        if (!rsi) continue;

        if (voip_map.find((guint)rsi->call_num) != voip_map.end()) {
            if (!(rsi->call_num < 0))
            {
                rtp_state = rsi->call_num;
                m_voipExt.AddVoipRtpStream(rsi->call_num, rsi);
                voip_filelist[rsi->call_num].emplace(rsi);
            }
            else
            {
                voip_tbl_init();
            }
        }

        if(rtp_streams_set.find(rsi->ssrc) == rtp_streams_set.end())
        {
            sip_info_.trailer   = &voip_trailer_;
            sip_info_.flow_tunnel_info   = &voip_flow_tunnel_info_;
            sip_info_.rtp_info  = rsi;
            sip_info_.file_name = get_voip_info_keeper()->getVoipFilenameOf(rsi);
            sip_info_.file_size = get_voip_info_keeper()->getVoipFilesizeOf(rsi);
            sip_info_.line_no   = get_voip_info_keeper()->getVoipLineno(rsi);
            sip_info_.setPktLossInfo(get_voip_info_keeper()->getVoipLossInfo(rsi));
            sip_info_.setFileCreatTime(get_voip_info_keeper()->getVoipFileCreatTime(rsi));

            auto p_rtpStreamWriter = m_rtpStreamExt.GetRecordWriter();
            if (p_rtpStreamWriter != nullptr
                && RtpStreamKeeper::getInstance()->getRtpMediaType(rsi) != RtpMediaType::unknown)
            {

                p_rtpStreamWriter->writeRawFields(gch_fields_value, GCHFieldsExtractor::get_fields_cnt());
                m_rtpStreamExt.ExtractPlainFields((epan_dissect_t *)&sip_info_, p_rtpStreamWriter);
                p_rtpStreamWriter->writeNextRecord();
            }

            if (sip_info_.stream_status == 1 )
            {
                sip_info_.stream_status = 0;
            }

            rtp_streams_set.emplace(rsi->ssrc);
        }
    }

    if (!(rtp_state < 0))
    {
        for (auto & iter : voip_map)
        {
            voip_calls_info_t *voip_info = &(iter.second);

            set_voip_tbl_info(voip_info);
            for (auto & index: voip_filelist[voip_info->call_num])
            {
                m_voipExt.AddVoipSsrc(index);
            }
            get_voip_info_keeper()->trailer = &voip_trailer_;
            get_voip_info_keeper()->flow_tunnel_info = &voip_flow_tunnel_info_;

            if(pWriter != nullptr)
            {
                pWriter->writeRawFields(gch_fields_value, GCHFieldsExtractor::get_fields_cnt());

                m_voipExt.ExtractPlainFields((epan_dissect_t *)get_voip_info_keeper(), pWriter);

                pWriter->writeNextRecord();
            }
            m_voipExt.CleanVoipSsrc();
        }
   
        voip_tbl_init();

        voip_map.clear();
        rtp_state = -1;
    }

    m_voipExt.CleanVoipSsrc();
    m_voipExt.CleanVoipRtpStream();
}
#endif
void VoipStreamKeeper::writeVoipTblDone()
{
    m_voipWriter.writeTblDone();
    m_rtpStreamWriter.writeTblDone();
}

int VoipStreamKeeper::updateList(GQueue * callsinfos)
{
    if (callsinfos) {
        GList *cur_call = g_queue_peek_nth_link(callsinfos, voip_map.size());
        guint extra = g_list_length(cur_call);

        if (extra > 0) {

            while (cur_call && cur_call->data) {
                voip_calls_info_t *call_info = (voip_calls_info_t *)cur_call->data;
                voip_map.emplace(std::make_pair(call_info->call_num, *call_info));
                cur_call = g_list_next(cur_call);
            }

        }
    }
    return 0;
}

gpointer
simple_dialog(ESD_TYPE_E type, gint btn_mask, const gchar *msg_format, ...)
{
    return NULL;
}

static std::string transform_for_field_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (nullptr == pFinfo || pFinfo->length == 0)
    {
        return "";
    }

    int lValue = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));          //协议字段对应值为整数

    const char* pString = try_val_to_str(lValue, (const value_string *)(pFinfo->hfinfo->strings)); //提取对应字符串

    if (nullptr == pString)
    {
        return std::to_string(lValue);
    }
    else
    {
        return std::string(pString);
    }
}

void VoipStreamKeeper::dissectFlowTunnel(epan_dissect_t *edt){
    voip_flow_tunnel_info_.magic_code = get_first_field_value_from_interesting_fields(edt, "zj_tunnel.magic_code");
    voip_flow_tunnel_info_.tunnel_layer      = get_first_field_value_from_interesting_fields(edt, "zj_tunnel.layer");
    voip_flow_tunnel_info_.tunnel_str_len      = get_first_field_value_from_interesting_fields(edt, "zj_tunnel.addr_len");
    // voip_flow_tunnel_info_.tunnel_str      = get_first_field_value_from_interesting_fields(edt, "zj_tunnel.tunnel_addr");
    if (voip_flow_tunnel_info_.tunnel_str.size() == 0) {
        //获取一下
        voip_flow_tunnel_info_.tunnel_str = get_first_field_value_from_interesting_fields(edt, "gch.ext_data");
    }
    if (voip_flow_tunnel_info_.tunnel_str.size() == 0) {
        // 没获取到，填充空值
        for (int i = 0; i < GCHFieldsExtractor::get_fields_cnt(); i++) voip_flow_tunnel_info_.tunnel_str += '|';
    }
}
//解析 media description
void VoipStreamKeeper::dissectSipMeidaDescription(epan_dissect_t *edt) {
    int media_num = get_nth_field_info_from_interesting_fields_count(edt, "sdp.media");
    if (media_num == 0) {
        return;
    }
    // 每一行 media description解析一次
    for (int i = 0; i < media_num; i++) {
      RtpPortInfo rtpPort;      //建立 port->rtp的conversion 的 key
      RtpMediaInfo minfo;       //建立 port->rtp的conversion 的 value
      minfo.sip_from = sip_info_.from;
      minfo.sip_to = sip_info_.to;

      std::string sipMeidaDescription = get_n_field_value_from_interesting_fields(edt, "sdp.media",i);
      // 先进行 sipMeidaDescription 切割 以空格为分割
      // 格式为 媒体类型 端口 传输协议 媒体能力 ...
      std::string mediaType;      // 媒体类型 audio; video
      std::string portStr;      // 端口
      std::string transportType;      // 传输协议 RTP/AVP(默认 等价于RTP/AVP/UDP); RTP/AVP/TCP; RTP/AVP/UDP
      std::vector<std::string> media_attr;      // 媒体能力 多个
      int count = 0 ; //切割次数计数器
      while (sipMeidaDescription.find(" ") != std::string::npos) {
            auto findPos = sipMeidaDescription.find(" ");
            if (count == 0) {
                mediaType = sipMeidaDescription.substr(0,findPos);
            } else if (count == 1) {
                portStr = sipMeidaDescription.substr(0,findPos);
            } else if (count == 2) {
                transportType = sipMeidaDescription.substr(0,findPos);
            } else if (count >= 3) {
                media_attr.push_back(sipMeidaDescription.substr(0,findPos));
            }
            sipMeidaDescription = sipMeidaDescription.substr(findPos + 1);
            count++;
      }
      media_attr.push_back(sipMeidaDescription);

      //填充端口
      const char *pPort = portStr.c_str();
      uint32_t    port = strtol(pPort, NULL, 10);

      //建立 sip INVITE 与 200OK 的conv
      std::string server_ip = get_n_field_value_from_interesting_fields(edt, "ip.src", -1);
      std::string cli_ip = get_n_field_value_from_interesting_fields(edt, "ip.dst", -1);
      //正反向ip
      auto tuple = std::make_pair(server_ip, cli_ip);
      auto tuple_resv = std::make_pair(cli_ip, server_ip);
      // 区分音视频
      auto key = std::make_pair(tuple, mediaType);
      auto key_resv = std::make_pair(tuple_resv, mediaType);

      auto mapFindIter = RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.find(key);
      if (mapFindIter == RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.end()) {
            mapFindIter = RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.find(key_resv);
      }
      if (mapFindIter ==
          RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.end()) {  // 未预先保存过该 sip 相关的 sdp 信息
            rtpPort.port_src = port;

            RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.emplace(key, rtpPort);
            RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.emplace(key_resv, rtpPort);
            continue;
      } else {
            rtpPort.port_src = mapFindIter->second.port_src;
            rtpPort.port_dst = port;
            // 移除已经处理过的conv
            RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.erase(key);
            RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.erase(key_resv);
      }

      //填充媒体类型 minfo.mediaType 只有当INVITE与200OK都到达时才有意义
      // 默认 200OK后到达
      // TODO:需要考虑 200OK先到达的乱序情况
      // 处理 media_attr 媒体能力
      // 虽然上面检查过两个端口齐全才会到这里 不过还是先检查一下吧
      std::string sip_status = get_first_field_value_from_interesting_fields(edt, "sip.Status-Code");
      if(sip_status!= "200"){
        break;
      }
      // 200OK 中 默认 media_attr 的第一个值为媒体参数
      int media_attr_rtp_map_num = get_nth_field_info_from_interesting_fields_count(edt, "sdp.media_attr");

      for(auto it:media_attr){
        int payload_type = std::stoi(it);
        minfo.rtpPayloadType = payload_type;
        rtpPort.payload_type = payload_type;
        if(payload_type <= PT_H263){
          RtpPortInfo rtpPortResv;
          rtpPortResv.port_dst = rtpPort.port_src;
          rtpPortResv.port_src = rtpPort.port_dst;
          rtpPort.payload_type = payload_type;

          RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.emplace(rtpPort, minfo);
          RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.emplace(rtpPortResv, minfo);
          continue;
        }
        for (int i = 0; i< media_attr_rtp_map_num; i++) {
          std::string media_attr_str = get_n_field_value_from_interesting_fields(edt, "sdp.media_attr", i);
          if(media_attr_str.find("rtpmap")==std::string::npos){
            continue;
          }
          if(media_attr_str.find(it) == std::string::npos){
            continue;
          }
          std::string mediaFormatType_type =
              media_attr_str.substr(media_attr_str.find_first_of(' ') + 1, media_attr_str.find_first_of('/'));
          if (mediaFormatType_type.find("H264") != std::string::npos) {
            minfo.mediaType = RtpMediaType::video_h264;
            std::string mediaH264PackMode =
                get_first_field_value_from_interesting_fields(edt, "sdp.fmtp.h264_packetization_mode");
            minfo.pack_mode = atoi(mediaH264PackMode.c_str());
          } else if (mediaFormatType_type.find("H263") != std::string::npos) {
            minfo.mediaType = RtpMediaType::video_h263;
          } else if (mediaFormatType_type.find("H261") != std::string::npos) {
            minfo.mediaType = RtpMediaType::video_h261;
          } else if (mediaFormatType_type.find("opus") != std::string::npos ||
                     mediaFormatType_type.find("OPUS") != std::string::npos ||
                     mediaFormatType_type.find("G729") != std::string::npos ||
                     mediaFormatType_type.find("G726") != std::string::npos ||
                     mediaFormatType_type.find("SIREN") != std::string::npos) {
            minfo.mediaType = RtpMediaType::audio;
          } else if (mediaFormatType_type.find("AMR") != std::string::npos ||
                     mediaFormatType_type.find("723") != std::string::npos ||
                     mediaFormatType_type.find("728") != std::string::npos) {
            minfo.mediaType = RtpMediaType::raw;
          }

          RtpPortInfo rtpPortResv;
          rtpPortResv.port_dst = rtpPort.port_src;
          rtpPortResv.port_src = rtpPort.port_dst;
          rtpPort.payload_type = payload_type;

          RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.emplace(rtpPort, minfo);
          RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.emplace(rtpPortResv, minfo);
          if (!sip_info_.from.empty() || !sip_info_.to.empty()) {
              sip_info_.payload_name = mediaFormatType_type;
              map_sip_info_.emplace(rtpPort, sip_info_);
              //获取到本条sip的完整信息，清空成员变量！
          }
        }
      }
    }
    sip_info_.voip_status = 0;
    sip_info_.sample_rate.clear();
    sip_info_.from.clear();
    sip_info_.to.clear();
    sip_info_.payload_name.clear();
}
void VoipStreamKeeper::dissectSipSDP(epan_dissect_t *edt) {
    //如果没有 m: 不解析
    field_info *finfoTypeNumber = get_first_field_info_from_interesting_fields(edt, "sdp.media.format");
    if (finfoTypeNumber == NULL) {
        return;
    }

    dissectSipMeidaDescription(edt);


}
// void VoipStreamKeeper::dissectSipInvite(epan_dissect_t *edt){

// }
// void VoipStreamKeeper::dissectSip200OK(epan_dissect_t *edt){
  
// }
void VoipStreamKeeper::dissectSip(epan_dissect_t *edt){
    // sip_info_作为一个类成员，会在每次sip来时，进行更新，更新后的info插入map_sip_info_中
    if (!sip_info_.from.empty() || !sip_info_.to.empty())
    {
        sip_info_.voip_status = 1;
    }

    if (sip_info_.voip_status == 0)
    {
        sip_info_.from         = get_first_field_value_from_interesting_fields(edt, "sip.From");

        sip_info_.to           = get_first_field_value_from_interesting_fields(edt, "sip.To");
    }
    if(sip_info_.payload_name.size() == 0){
        sip_info_.payload_name = get_first_field_value_from_interesting_fields(edt, "sdp.mime.type");
    }
    if(sip_info_.sample_rate.size() == 0){
        sip_info_.sample_rate  = get_first_field_value_from_interesting_fields(edt, "sdp.sample_rate");
    }

    std::string sipContentType = get_first_field_value_from_interesting_fields(edt, "sip.Content-Type");
    std::string  mediaH264PackMode      = get_first_field_value_from_interesting_fields(edt, "sdp.fmtp.h264_packetization_mode");


    // INVITE 与 200OK 存在 sip content
    //且存在h264
    if (sipContentType == "application/sdp"){
      dissectSipSDP(edt);
      // field_info *finfoTypeNumber = get_first_field_info_from_interesting_fields(edt, "sdp.media.format");
      // if (finfoTypeNumber == NULL) {
      //     return;
      // }
      // std::string sipTransport_a = get_first_field_value_from_interesting_fields(edt, "sdp.media");
      // std::string sipTransport_v = get_n_field_value_from_interesting_fields(edt, "sdp.media", 1);
      // // 先判断是否有audio与vidoe
      // auto findPos_a = sipTransport_a.find("audio ");
      // auto findPos_v = sipTransport_v.find("video ");
      // if (findPos_a == std::string::npos && findPos_v == std::string::npos) {
      //     return;
      // }

      // RtpPortInfo rtpPort;
      // std::string portStr;
      // std::string rtpPayloadTypeStr;
      // std::string mediaFormatType_type;
      // if (findPos_v != std::string::npos) {
      //     portStr = sipTransport_v.substr(findPos_v + 6, 8);
      //     for (int i = 0; i < 8; i++) {
      //         std::string tmp = get_n_field_value_from_interesting_fields(edt, "sdp.mime.type", i);
      //         if (tmp.empty())
      // //             break;
      //         mediaFormatType_type = tmp;
      //     }
      //     auto const pos = sipTransport_v.find_last_of(' ');
      //     rtpPayloadTypeStr = sipTransport_v.substr(pos + 1);
      // } else if (findPos_a != std::string::npos && findPos_v == std::string::npos) {
      //     portStr = sipTransport_a.substr(findPos_a + 6, 8);
      //     mediaFormatType_type = get_n_field_value_from_interesting_fields(edt, "sdp.mime.type", 0);
      //     auto const pos = sipTransport_a.find_last_of(' ');
      //     rtpPayloadTypeStr = sipTransport_a.substr(pos + 1);
      // }
      // sip_info_.payload_name = get_first_field_value_from_interesting_fields(edt, "sdp.mime.type");
      // const char *pPort = portStr.c_str();
      // uint32_t    port = strtol(pPort, NULL, 10);

      // const char *pRtpPayloadType = rtpPayloadTypeStr.c_str();
      // uint8_t     rtpPayloadType = strtol(pRtpPayloadType, NULL, 10);

      // std::string server_ip = get_n_field_value_from_interesting_fields(edt, "ip.src", -1);
      // std::string cli_ip = get_n_field_value_from_interesting_fields(edt, "ip.dst", -1);
      // auto        tuple = std::make_pair(server_ip, cli_ip);
      // auto        tuple_resv = std::make_pair(cli_ip, server_ip);
      // auto        mapFindIter_src = RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.find(tuple);
      // auto        mapFindIter_dst = RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.find(tuple_resv);
      // if (mapFindIter_src ==
      //     RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.end()) {  // 未预先保存过该 sip 相关的 sdp 信息
      //     rtpPort.port_src = port;
      //     RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.emplace(tuple, rtpPort);
      //     RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.emplace(tuple_resv, rtpPort);
      // } else {
      //     RtpPortInfo rtpPortFind = mapFindIter_src->second;
      //     rtpPortFind.port_dst = port;

      //     RtpMediaInfo minfo;
      //     if (mediaFormatType_type == "H264") {
      //         minfo.mediaType = RtpMediaType::video_h264;
      //         minfo.pack_mode = atoi(mediaH264PackMode.c_str());
      //     } else if (mediaFormatType_type.find("H263") != std::string::npos) {
      //         minfo.mediaType = RtpMediaType::video_h263;
      //     } else if (mediaFormatType_type == "H261") {
      //         minfo.mediaType = RtpMediaType::video_h261;
      //     } else if (mediaFormatType_type == "opus" || mediaFormatType_type == "OPUS" || mediaFormatType_type == "G729" ||
      //               mediaFormatType_type.find("G726") != std::string::npos ||
      //               mediaFormatType_type.find("SIREN") != std::string::npos) {
      //         minfo.mediaType = RtpMediaType::audio;
      //     } else {
      //         minfo.mediaType = RtpMediaType::unknown;
      //     }
      //     minfo.rtpPayloadType = rtpPayloadType;
      //     minfo.sip_from = sip_info_.from;
      //     minfo.sip_to = sip_info_.to;
      //     RtpPortInfo rtpPortResv;
      //     rtpPortResv.port_dst = rtpPortFind.port_src;
      //     rtpPortResv.port_src = rtpPortFind.port_dst;

      //     RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.emplace(rtpPortFind, minfo);
      //     RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.emplace(rtpPortResv, minfo);
      //     // 建立了 port 与 info 关联后， server 不再需要;
      //     RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.erase(tuple);
      //     RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.erase(tuple_resv);
      //     if (!sip_info_.from.empty() || !sip_info_.to.empty()) {
      //         if (sip_info_.payload_name.size() == 0) {
      //             sip_info_.payload_name = mediaFormatType_type;
      //         }
      //         map_sip_info_.emplace(rtpPortFind, sip_info_);
      //         //获取到本条sip的完整信息，清空成员变量！
      //         sip_info_.voip_status = 0;
      //         sip_info_.sample_rate.clear();
      //         sip_info_.from.clear();
      //         sip_info_.to.clear();
      //         sip_info_.payload_name.clear();
      //     }
      // }
      // return;
    }
}

void VoipStreamKeeper::dissectH323(epan_dissect_t *edt){
    //设置当前帧的q931number
    std::string Q931CallingPartyNumber = get_first_field_value_from_interesting_fields(edt, "q931.calling_party_number.digits");
    if(Q931CallingPartyNumber.size()!=  0)
    {
      voip_trailer_.Q931CallingPartyNumber = Q931CallingPartyNumber;
    }
      std::string Q931CalledPartyNumber  =  get_first_field_value_from_interesting_fields(edt, "q931.called_party_number.digits");
    if(Q931CalledPartyNumber.size()!= 0){
      voip_trailer_.Q931CalledPartyNumber  = Q931CalledPartyNumber;
    }
    //对h323的号码做map
    int h255portnum   = get_nth_field_info_from_interesting_fields_count(edt, "h245.tsapIdentifier");
    if(h255portnum > 1){
        uint32_t     port = 0;
        RtpPortInfo  rtpPort;
        for(int i = 0 ;i < h255portnum ;i++){
            //取最小的port为媒体端口
            std::string h255port   = get_n_field_value_from_interesting_fields(edt, "h245.tsapIdentifier",i);
            const char  *pPort   = h255port.c_str();
            if(port != 0){
              port = strtol(pPort, NULL, 10)> port? port : strtol(pPort, NULL, 10);
              continue;
            }
            port = strtol(pPort, NULL, 10);
        }

        std::string server_ip = get_n_field_value_from_interesting_fields(edt, "ip.src",-1);
        std::string cli_ip = get_n_field_value_from_interesting_fields(edt, "ip.dst",-1);

        auto tuple = std::make_pair(server_ip, cli_ip);
        auto tuple_resv = std::make_pair(cli_ip,server_ip);

        auto key = std::make_pair(tuple, "audio");
        auto key_resv = std::make_pair(tuple_resv,"audio");

        auto mapFindIter_src = RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.find(key);
        auto mapFindIter_dst = RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.find(key_resv);
        if (mapFindIter_src == RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.end())
        {   // 未预先保存过该 323 相关的 port 信息
            rtpPort.port_src = port;
            RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.emplace(key, rtpPort);
            RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.emplace(key_resv, rtpPort);
        }else {
           RtpPortInfo rtpPortFind = mapFindIter_src->second;
           if(rtpPortFind.port_src == port){
            return;
           }
            rtpPortFind.port_dst = port;
            RtpMediaInfo minfo;
            minfo.Q931CalledPartyNumber = voip_trailer_.Q931CalledPartyNumber;
            minfo.Q931CallingPartyNumber = voip_trailer_.Q931CallingPartyNumber;
            RtpPortInfo  rtpPortResv;
            rtpPortResv . port_dst = rtpPortFind.port_src;
            rtpPortResv . port_src = rtpPortFind.port_dst;
            std::string payload_type =  get_n_field_value_from_interesting_fields(edt, "h245.audioData",-1);

            if (payload_type.find("g711Alaw") !=std::string::npos) {
              rtpPortFind.payload_type = PT_PCMA;
              rtpPortResv.payload_type = PT_PCMA;
            } else if (payload_type.find("g711Ulaw") !=std::string::npos) {
              rtpPortFind.payload_type = PT_PCMU;
              rtpPortResv.payload_type = PT_PCMU;
            } else if (payload_type.find("g722") !=std::string::npos) {
              rtpPortFind.payload_type = PT_G722;
              rtpPortResv.payload_type = PT_G722;
            } else if (payload_type.find("g7231") !=std::string::npos) {
              rtpPortFind.payload_type = PT_G723;
              rtpPortResv.payload_type = PT_G723;
            } else if (payload_type.find("g728") !=std::string::npos) {
              rtpPortFind.payload_type = PT_G728;
              rtpPortResv.payload_type = PT_G728;
            } else if (payload_type.find("g729") !=std::string::npos) {
              rtpPortFind.payload_type = PT_G729;
              rtpPortResv.payload_type = PT_G729;
            }
            //正反向插入map
            RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.emplace(rtpPortFind,minfo);
            RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.emplace(rtpPortResv,minfo);
            // 建立了 port 与 info 关联后， server 不再需要;
            RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.erase(key);
            RtpStreamKeeper::getInstance()->map_serverIp2rtpPortInfo_.erase(key_resv);
        }
    }
}

void VoipStreamKeeper::dissectTrailer(epan_dissect_t *edt)
{

    field_info *pinfo      = nullptr;

    if (get_first_field_info_from_interesting_fields(edt, "hwl.tag") != nullptr)
    {
        voip_trailer_.trailer_type = HW;

        voip_trailer_.imei         = get_first_field_value_from_interesting_fields(edt, "hwl.imei");

        voip_trailer_.msisdn       = get_first_field_value_from_interesting_fields(edt, "hwl.msisdn");

        voip_trailer_.imsi         = get_first_field_value_from_interesting_fields(edt, "hwl.imsi");

        voip_trailer_.tac          = get_first_field_value_from_interesting_fields(edt, "hwl.tac");

        voip_trailer_.teid         = get_first_field_value_from_interesting_fields(edt, "hwl.TEID");
    }
    else if(get_first_field_info_from_interesting_fields(edt, "rtl.teid") != nullptr)
    {
        voip_trailer_.trailer_type = RT;

        voip_trailer_.imei         = get_first_field_value_from_interesting_fields(edt, "rtl.imei");

        voip_trailer_.msisdn       = get_first_field_value_from_interesting_fields(edt, "rtl.msisdn");

        voip_trailer_.imsi         = get_first_field_value_from_interesting_fields(edt, "rtl.imei");

        voip_trailer_.tac          = get_first_field_value_from_interesting_fields(edt, "rtl.tac");

        voip_trailer_.teid         = get_first_field_value_from_interesting_fields(edt, "rtl.teid");
    }
    else
    {
        voip_trailer_.trailer_type = 0;

        voip_trailer_.imei         = "";

        voip_trailer_.msisdn       = "";

        voip_trailer_.imsi         = "";

        voip_trailer_.tac          = "";

        voip_trailer_.teid         = "";
    }

    voip_trailer_.rt_plmnid    = get_first_field_value_from_interesting_fields(edt, "rtl.plmnid");

    voip_trailer_.rt_uli       = get_first_field_value_from_interesting_fields(edt, "rtl.uli");

    voip_trailer_.rt_outsrc_ip = get_first_field_value_from_interesting_fields(edt, "rtl.outer_src");

    voip_trailer_.rt_outdst_ip = get_first_field_value_from_interesting_fields(edt, "rtl.outer_dst");
        
    voip_trailer_.rt_basetype  = get_first_field_value_from_interesting_fields(edt, "rtl.basetype");


    voip_trailer_.hw_cellid    = get_first_field_value_from_interesting_fields(edt, "hwl.cellid");

    pinfo                      = get_first_field_info_from_interesting_fields(edt, "hwl.netType"); 

    uint32_t ivalue            = atoi(get_first_field_value_from_interesting_fields(edt, "hwl.ecgi").c_str());
    if (ivalue != 0)
    {
        char tmp_str[11];

        snprintf(tmp_str, 11,"0x%08x", ivalue);
        voip_trailer_.hw_ecgi      = std::string(tmp_str);
    }

    voip_trailer_.hw_sac       = get_first_field_value_from_interesting_fields(edt, "hwl.sac");

    voip_trailer_.hw_lac       = get_first_field_value_from_interesting_fields(edt, "hwl.lac");

    voip_trailer_.hw_meid      = get_first_field_value_from_interesting_fields(edt, "hwl.MEID");
    
    voip_trailer_.hw_bsid      = get_first_field_value_from_interesting_fields(edt, "hwl.bsid");

    pinfo                      = get_first_field_info_from_interesting_fields(edt, "hwl.netType");

    voip_trailer_.hw_nettype   = transform_for_field_value(edt, pinfo); 

    pinfo                      = get_first_field_info_from_interesting_fields(edt, "hwl.carrierOperator");

    voip_trailer_.hw_operator  = transform_for_field_value(edt, pinfo);

    voip_trailer_.hw_apn       = get_first_field_value_from_interesting_fields(edt, "hwl.apn");

    voip_trailer_.hw_esn       = get_first_field_value_from_interesting_fields(edt, "hwl.esn");

    voip_trailer_.hw_accont    = get_first_field_value_from_interesting_fields(edt, "hwl.account");

    voip_trailer_.hw_gre_key   = get_first_field_value_from_interesting_fields(edt, "hwl.GREkey");

    voip_trailer_.hw_ncode     = get_first_field_value_from_interesting_fields(edt, "hwl.ncode");

    voip_trailer_.hw_ecgi_mnc  = get_first_field_value_from_interesting_fields(edt, "hwl.ecgi_mnc");
    
    voip_trailer_.hw_tai       = get_first_field_value_from_interesting_fields(edt, "hwl.tai");

    voip_trailer_.src_ip       = choose_for_two_type_field("ip.src", "ipv6.src" )(edt, NULL,-1);

    voip_trailer_.dst_ip       = choose_for_two_type_field("ip.dst", "ipv6.dst" )(edt, NULL,-1);

    voip_trailer_.src_port     = choose_for_two_type_field("tcp.srcport", "udp.srcport" )(edt, NULL,-1);

    voip_trailer_.dst_port     = choose_for_two_type_field("tcp.dstport", "udp.dstport" )(edt, NULL,-1);

    voip_trailer_.ttl          = choose_for_two_type_field("ip.ttl", "ipv6.hlim")(edt, NULL,-1);

    voip_trailer_.proto        = choose_for_two_type_field("ip.proto", "esp.protocol")(edt, NULL,-1);

    pinfo                      = get_first_field_info_from_interesting_fields(edt, "frame.time");

    if (fvalue_type_ftenum(const_cast<fvalue_t *>(&pinfo->value)) != FT_ABSOLUTE_TIME)
    {
        voip_trailer_.capdate  = "error";
    }
    else
    {
        voip_trailer_.capdate  = unixTime2Str(pinfo->value.value.time.secs);
    }
    std::string frame_num_str = get_first_field_value_from_interesting_fields(edt, "frame.number");
    const char  *pframe_num  = frame_num_str.c_str();
    uint32_t     frame_num    = strtol(pframe_num, NULL, 10);
    voip_trailer_.cleanAll();

}
void VoipStreamKeeper::setGCHFieldExtrPointer(GCHFieldsExtractor *pointer)
{
    m_gch_extr = pointer;
}