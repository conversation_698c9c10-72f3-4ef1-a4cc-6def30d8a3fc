/****************************************************************************************
 * 文 件 名 : yaEty_pcap_scanner.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-06-24
* 编    码 : root      '2018-06-24
* 修    改 : zhangsx   '2018-11-12
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_pcap_scanner.h"
#include "yaEty_utils.h"
#include "yaEty_target_info.h"

std::string& ltrim(std::string &s)
{
    return s.erase(0, s.find_first_not_of(" "));
}

std::string& rtrim(std::string& s)
{
    return s.erase(s.find_last_not_of(" ") + 1);
}

std::string& trim(std::string &s)
{
    return ltrim(rtrim(s));
}

PcapScanner::PcapScanner(const std::vector<std::string> &dirList,
                         const std::string &strWritingSuffix,
                         const std::string &strIgnoreSuffixList,
                         const std::string &strProcMarkSuffix) 
    : dirList_(dirList)
    , strWritingSuffix_(strWritingSuffix)
    , uTotalWritingFileSize_(0)
    , strProcMarkSuffix_(strProcMarkSuffix)
{
    for (std::string::size_type start = 0, pos = 0;
         pos != std::string::npos;
         start = pos + 1)
    {
        pos = strIgnoreSuffixList.find(',', start);
        std::string ignoreSuffix = strIgnoreSuffixList.substr(start,
                                                              std::string::npos == pos
                                                              ? std::string::npos : pos - start);
        ignoreSuffix = trim(ignoreSuffix);
        vecIgnoreSuffixs_.push_back(ignoreSuffix);
    }

    // 将 gch common fields 文件类型也加入忽略列表
    vecIgnoreSuffixs_.push_back(std::string(GCHFieldsExtractor::get_file_ext()));

    file_manager.setPath(RECORD_PATH);                    //添加记录文件读取操作
    file_manager.loadRecord();

    if (CFG->ForceProcess())
    {
        file_manager.clear();
    }
}

PcapScanner::~PcapScanner()
{
    
}


int PcapScanner::shouldProcessFile(const char *fileName, bool bIsDir)
{
    if (bIsDir)
    {   // 不处理目录
        return 0;
    }
    
    std::string strFileName = fileName;
    int lSts = 0;
    struct stat s;

    lSts = lstat(fileName, &s);
    CHECK_NOR_EXIT(lSts < 0, lSts, "stat file %s error\n", fileName);
    /* is a writing file ? */
    if (strEndwith(strFileName, strWritingSuffix_))
    {   // 累计 writing file size

        uTotalWritingFileSize_ += s.st_size;
        return 0;
    }
    /* ignore the file */
    for (const auto &ignore : vecIgnoreSuffixs_)
    {
        if (strEndwith(strFileName, ignore))
        {
            return 0;
        }
    } 
    
    if (!CFG->FilenameRename())                                                                        //add by zhangsx 2018-11-13
    {
        if (file_manager.select_file(strFileName) == 0)
        {
            return 0;
        }
        else
        {
            file_manager.add_file(strFileName);
        }
    }
    if (strFileName.find("unknown_") != std::string::npos) {
        // unknown_1716256602_0_0_00002_20240521095658.pcap
        auto tag = strFileName.find_last_of('_');
        if (tag != std::string::npos) {
            std::string time_str = strFileName.substr(tag + 1, 14);
            uint64_t    timestamp = atoll(time_str.c_str());
            pcapQueue_[timestamp] = strFileName;
        }
        /* push to queue */
    }else{
        struct timespec last_change_time =  s.st_ctim;
        pcapQueue_[last_change_time.tv_sec] = strFileName;
    }
    return 0;
}

int PcapScanner::scan()
{
    if (getPcapFileCnt() > 0)
    {   // 还有内容，不进行实际扫描
        return 0;
    }

    uTotalWritingFileSize_ = 0;
    for (const auto &dir : dirList_)
    {
        forDirEntry(dir.c_str(),
                    std::bind(&PcapScanner::shouldProcessFile,
                              this, std::placeholders::_1, std::placeholders::_2),
                    5);             /* 最多扫描 5 层 */
    }
    file_manager.saveRecord();                                  //添加保存记录文件操作
    return 0;
}

int PcapScanner::getPcapFileCnt()
{
    return pcapQueue_.size();
}

uint64_t PcapScanner::getTotalWritingFileSize()
{
    return uTotalWritingFileSize_;
}

int  PcapScanner::foreachPcapFile(std::function<int (const char *)> funProc)
{
    while (!pcapQueue_.empty())
    {
        funProc(pcapQueue_.begin()->second.c_str());
        pcapQueue_.erase(pcapQueue_.begin());
    }
    
    return 0;
}

int PcapScanner::getNextPcapFile(std::string &strFileName)
{
    if (pcapQueue_.empty())
    {
        return -1;
    }

    int         lSts                = 0;
    std::string strOriginalFileName = pcapQueue_.begin()->second;
    pcapQueue_.erase(pcapQueue_.begin());

    if (!CFG->FilenameRename())    //如果是保存模式，无需重命名                                                        //add by zhangsx 2018-11-13
    {
        strFileName = strOriginalFileName;
        return 0;
    }
    // 重命名，防止下次 scan 又扫到它，重复分配
    strFileName = strOriginalFileName + strProcMarkSuffix_;
    lSts        = rename(strOriginalFileName.c_str(), strFileName.c_str());
    CHECK_NOR_EXIT(lSts < 0, lSts, "rename error:%s -> %s\n", strOriginalFileName.c_str(), strFileName.c_str());

    // printf("pid:%d, new file %s\n", getpid(), strFileName.c_str());
    return 0;
}
