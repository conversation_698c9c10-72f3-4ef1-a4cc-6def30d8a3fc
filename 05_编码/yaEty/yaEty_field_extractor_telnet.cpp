/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_telnet.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-09-05
* 编    码 : zhangsx      '2018-09-05
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <iconv.h>

#include <algorithm>
#include <iostream>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"


#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_telnet.h"

#include "yaEty_content_reassembly_share_mem.h"
#include "yaEty_field_extractor_telnet.h"


#define TELNET_ITEM_GROUP(grp,   NO)                                        \
        F_D_ITEM(grp "_" NO                    , ""                                    , eMT_direct,           "",        NULL)

#define TELNET_ITEM_GROUPS_5(grp, a, b, c, d, e)  \
        TELNET_ITEM_GROUP(grp, a),                \
        TELNET_ITEM_GROUP(grp, b),                \
        TELNET_ITEM_GROUP(grp, c),                \
        TELNET_ITEM_GROUP(grp, d),                \
        TELNET_ITEM_GROUP(grp, e)


/*****************************************************************
*Function    :transform_field_for_C2S
*Description :Telnet C2S
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
        
static std::string transform_field_for_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{

    pFinfo = get_first_field_info_from_interesting_fields(edt, "tcp.srcport");
    unsigned int && src_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pFinfo->value)));


    pFinfo = get_first_field_info_from_interesting_fields(edt, "tcp.dstport");
    unsigned int && dst_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pFinfo->value)));

    return src_port > dst_port ? "C2S" : "S2C";

}

/*****************************************************************
*Function    :transform_field_for_bin_from_realdata
*Description :Telnet data
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
        
static std::string transform_field_for_bin_from_realdata(epan_dissect_t *edt, const field_info *pFinfo)
{
    // 将 | 进行替换 ===!! TBL_SEP FOR URI !!===
    std::string strRes;
    char *pFormated = format_text(NULL,
        tvb_get_ptr(edt->tvb, pFinfo->start,pFinfo->length), 
                        pFinfo->length) ;                                       //这里获取telnet.data原始数据的地址和长度，以直接转换
    strRes = pFormated;
    wmem_free(NULL, pFormated);

    // 增加对 '|' 的转换
    std::replace(strRes.begin(), strRes.end(), '|', '_');
    return strRes;
}




static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // 10 个 RTL 标签头
    F_D_ITEM_RTL_10(),

    // 27 个通用字段头
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    /* 通用特定字段(由协议本身确定) */
    F_D_ITEM("SrcPort"                     , "tcp.srcport"                           , eMT_direct,           "",        NULL),
    F_D_ITEM("DstPort"                     , "tcp.dstport"                           , eMT_direct,           "",        NULL),
    F_D_ITEM("C2S"                         , ""                                      , eMT_fromEdt,          "",        transform_field_for_C2S),
    F_D_ITEM("Proto"                       , "ip.proto"                              , eMT_direct,           "",        NULL),
    F_D_ITEM("TTL"                         , "ip.ttl"                                , eMT_direct,           "",        NULL),
    /* 协议独有定义字段 */
    F_D_ITEM("TelnetData"                  , "telnet.data"                           , eMT_transform | eMT_lastplain,           "",        transform_field_for_bin_from_realdata),// 处理 field 中的二进制
    TELNET_ITEM_GROUPS_5("Negotiations", "01", "02", "03", "04", "05"),                                                      //  这里处理
    TELNET_ITEM_GROUPS_5("Negotiations", "06", "07", "08", "09", "10")
};


ProtoFieldExtractorTelnet::ProtoFieldExtractorTelnet() :ProtoFieldExtractor("TELNET", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}

/*****************************************************************
*Function    :ProtoFieldExtractorTelnet::ExtractSpecialFields
*Description :Telnet Negotiations
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :true
*Others      :由telnet.cmd和telnet.subcmd拼合组成
*****************************************************************/

bool ProtoFieldExtractorTelnet::ExtractSpecialFields(epan_dissect_t * edt, RecordWriter * pWriter)
{
    field_info     *pfinfoCmd = NULL;
    field_info     *pfinfoSubcmd = NULL;
    
    int             lCmdCnt = 0;
    int             lSubcmdCnt = 0;

    int             field_cmd = 0;
    int             field_subcmd = 0;

    ProtoFieldDesc *pProtoFieldDesc = NULL;
    GPtrArray      *finfos_cmd = NULL;
    GPtrArray      *finfos_subcmd = NULL;
    const char     *pString = NULL;
    
    std::string     strCmd;
    std::string     strSubcmd;

    field_cmd = proto_registrar_get_id_byname("telnet.cmd");
    //field_cmd = proto_registrar_get_id_byname("text");
    finfos_cmd = proto_get_finfo_ptr_array(edt->tree, field_cmd);
    if (NULL == finfos_cmd)
    {   // 没有这组值，或者 index 出错？
        goto DEFAULT;
    }

    field_subcmd = proto_registrar_get_id_byname("telnet.subcmd");
    finfos_subcmd = proto_get_finfo_ptr_array(edt->tree, field_subcmd);
    if (NULL == finfos_subcmd)
    {   // 没有这组值，或者 index 出错？
        goto DEFAULT;
    }

    for (int i = 0; i < MIN( g_ptr_array_len(finfos_cmd), this->GetFieldsCount()); i++, nFieldIndexToExtract++)
    {
        pProtoFieldDesc = GetProtoFieldDescByIndex(nFieldIndexToExtract);
        //ExtractTelnetGroupItemOf(edt, pWriter, lCmdCntExtracted, 5, NULL);
        
        pfinfoCmd = (field_info *)g_ptr_array_index(finfos_cmd, lCmdCnt);
                
        if (NULL != pfinfoCmd)
        {
            lCmdCnt++;
            
            int lValue = fvalue_get_uinteger(const_cast<fvalue_t *>(&pfinfoCmd->value));          //telnet协议中字段对应值为整数
            pString = try_val_to_str(lValue, (const value_string *)(pfinfoCmd->hfinfo->strings)); //提取对应字符串
            
            if(NULL != pString )
            {
                strCmd = std::string(pString);
            }
            else
            {
                strCmd = "";
            }
            if(ety_ws_get_node_field_value(pfinfoCmd, edt) == "240")
            {
                pWriter->writeRecordField(pProtoFieldDesc->etyFieldName, strCmd);
                continue;
            }
            
        }
        else
        {
            strCmd = "";
        }
        
        if(!(lSubcmdCnt < g_ptr_array_len(finfos_subcmd)))
        {
            strSubcmd = "";
        }
        else
        {
            pfinfoSubcmd = (field_info *)g_ptr_array_index(finfos_subcmd, lSubcmdCnt);
            
            if (NULL != pfinfoSubcmd)
            {
                lSubcmdCnt++;
                
                if(NULL != pfinfoSubcmd->rep)
                {
                    std::string && t_str = std::string(pfinfoSubcmd->rep->representation);//只能从pfinfoSubcmd->rep->representation中提取
                    std::size_t && t_pos = t_str.find_first_of(":");
                    strSubcmd = t_str.substr(t_pos + 1);
                    //strSubcmd = std::string(pfinfoSubcmd->rep->representation+sizeof("Subcommand:")); //只能从pfinfoSubcmd->rep->representation中提取
                }
                else
                {
                    strSubcmd = "";
                }
                
            }
            else
            {
                strSubcmd = "";
            }
        }
        
        pWriter->writeRecordField(pProtoFieldDesc->etyFieldName, strCmd+strSubcmd); 
    }

DEFAULT:
    std::string strValueToWrite;
    for (; nFieldIndexToExtract < this->GetFieldsCount();nFieldIndexToExtract++)
    {
        pProtoFieldDesc = GetProtoFieldDescByIndex(nFieldIndexToExtract);
        strValueToWrite = pProtoFieldDesc->defaultFieldValue;

        // 进行可能地转换
        if (ProtoFieldDesc_GetType(pProtoFieldDesc) == eMT_fromEdt
            && pProtoFieldDesc->funTransfrom != NULL)
        {
            strValueToWrite = pProtoFieldDesc->funTransfrom(edt, NULL);
            strValueToWrite = !strValueToWrite.empty() ? strValueToWrite : pProtoFieldDesc->defaultFieldValue;
        }

        if (ProtoFieldDesc_GetType(pProtoFieldDesc) == eMT_direct)
        {
            strValueToWrite = get_first_field_value_from_interesting_fields(edt, pProtoFieldDesc->wsFieldName);
        }

        pWriter->writeRecordField(pProtoFieldDesc->etyFieldName, strValueToWrite);
    }

    return true;
}
