/****************************************************************************************
 * 文 件 名 : yaEty_cap_file_processor.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-05-12
* 编    码 : root      '2018-05-12
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _YAETY_CAP_FILE_PROCESSOR_H_
#define _YAETY_CAP_FILE_PROCESSOR_H_

#include <functional>
#include <capchild/capture_session.h>
#include "yaEty_ws_utils.h"
#include "yaEty_field_extractor.h"

class CapFileProcessor
{
public:
    typedef std::function<bool (capture_file *, epan_dissect_t *)> funPktPostProc_t;

public:
    CapFileProcessor(const char *strFileName, int lTheId = -1);
    CapFileProcessor(const std::string &strFileName, int lTheId = -1);
    ~CapFileProcessor();

public:
    static int ProduceTasks();
    static int ReduceTasks(int procIndex);
    static int ConsumeTasks(int procIndex);
    static int StopTasks(bool isStop);

public:
    static int ScanLoop();
    static int RegistSignal(void);
    static bool ShouldStop(void);

public:
    int ProcessCap(bool bAddSuffixOnDone = false);

public:
    static std::string GetCurrentProcessingPcapFilename();
    static std::string GetCurrentProcessingFrameNum();

private:
    static void StopRunnig(int sig);

private: // ws and onXxx
    int  openCapFile();
    bool onPktDissectedByWs(capture_file *cfile, epan_dissect_t *edt, wtap_rec *whdr, const guchar *pd);
    int  onCapProcDone(int lProcDone, bool bAddSuffix = false);
    bool processPacketSinglePass(capture_file *cf, epan_dissect_t *edt, gint64 offset,
                                 wtap_rec *whdr,   const guchar *pd,    guint tap_flags _U_);
    bool shouldDumpThisPacket(const std::string &strMainProto);

private: // 内容还原
    int reassemblyContent();
    int initProtoContentReassembleyModule();
    int runContentReassemblyProgram(const std::string &strOutDir, const std::string &strPcapFile);

private: // 协议解析
    int  dissectProtocols();
    bool extractPkt(const char *pStrProtocol, IProtoFieldExtractor *pExt, epan_dissect_t *edt);
    void tryToExtractTunnelProtocol(epan_dissect_t *edt,      const std::string &strProtoName,
                                    TunnelProtoFieldExtractor *protoFieldExt);
    bool dumpPkt(const char *pStrProtocol, wtap_rec *whdr, const guchar *pd, epan_dissect_t *edt);

private:
    std::string     strCapFileName_;
    const  char *   pszCapFileName_;
    int             lTheId_;
    pid_t           xplico_pid;

private:  // ws
    capture_session  capSession_;
    capture_file     capFile_;
    funPktPostProc_t funPktPostProc_;
    gboolean         line_buffered;
    guint32          cum_bytes;
    frame_data       prev_cap_frame;
    frame_data       ref_frame;
    capture_session  global_capture_session;
    output_fields_t* output_fields;

private:
    static std::string cs_strFilename;
    static std::string cs_strFrameNum;
    static bool is_shouldStop;
};

#endif /* _YAETY_CAP_FILE_PROCESSOR_H_ */
