/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_rtp_stream.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2019-11-08
* 编    码 : 
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2019 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
****************************************************************************************/

#include "wsutil/utf8_entities.h"
#include "yaEty_field_extractor_rtp_stream.h"
#include "yaEty_voip_stream_keeper.h"
#include "yaEty_rtp_stream_keeper.h"
#include "yaEty_cap_file_processor.h"

#include <functional>
#include "yaEty_target_info.h"
#include <exception>
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/to_str.h"
#include "wsutil/str_util.h"

#include <errno.h>

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor_http.h"
#include "yaEty_cap_file_processor.h"
extern  std::map<std::string, std::map<std::string, std::string>> target_info_map;
struct common_line_info  *linfo = NULL;

static std::string trans_for_tempid166(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    char station_id[40] = {0};
    char file_id[40] = {0};
    const char *ptr  = (const char*)rtpsinfo_->rtp_info->rtp_stats.first_packet_mac_addr.data;
    snprintf(station_id,7,"%s", ptr);
    snprintf(file_id,4,"%s", ptr+6);

    std::string stationID = station_id;
    std::string fileID = file_id;
    std::string keyID;
    if(fileID == "144")
        keyID = "TempID172EWS" + stationID;
    else if (fileID == "172")
        keyID = "TempID172EN" + stationID;
    else if (fileID == "166")
        keyID = "TempID166" + stationID;

    if(linfo == NULL){
        auto find_iter = target_info_map.find(keyID);
        if (find_iter ==  target_info_map.end())
            return "";
        linfo  = new(struct common_line_info);
        std::map<std::string, std::string>  ele_map = find_iter->second;
    
        linfo->PTMC = ele_map["PTMC"];
        linfo->SlaveDID = ele_map["SlaveDID"];
        linfo->UserType = ele_map["UserType"];
        linfo->JXH = ele_map["JXH"];
        linfo->TargetCategory = ele_map["TargetCategory"];
        linfo->TempID172EWS = ele_map["TempID172EWS"];
        linfo->TempID172EN = ele_map["TempID172EN"];
        linfo->TempID166 = ele_map["TempID166"];
        return linfo->TempID166;
    }
        return "dst";
}

static std::string trans_for_rtl_msidsn(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->msisdn;
}

static std::string trans_for_rtl_imei(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->imei;
}

static std::string trans_for_rtl_imsi(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->imsi;
}

static std::string trans_for_rtl_tac(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->tac;
}

static std::string trans_for_rtl_teid(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->teid;
}


static std::string trans_for_rtl_plmnid(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->rt_plmnid;
}

static std::string trans_for_rtl_uli(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->rt_uli;
}

static std::string trans_for_rtl_bs(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->rt_basetype;
}

static std::string trans_for_rtl_outsrc(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->rt_outsrc_ip;
}

static std::string trans_for_rtl_outdst(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->rt_outdst_ip;
}

static std::string trans_for_hwl_operator(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_operator;
}

static std::string trans_for_hwl_nettype(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_nettype;
}

static std::string trans_for_hwl_ecgi(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_ecgi;
}

static std::string trans_for_hwl_esn(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_esn;
}

static std::string trans_for_hwl_lac(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_lac;
}

static std::string trans_for_hwl_sac(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_sac;
}

static std::string trans_for_hwl_meid(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_meid;
}

static std::string trans_for_hwl_cellid(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_cellid;
}

static std::string trans_for_hwl_apn(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_apn;
}

static std::string trans_for_hwl_mnc(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_ecgi_mnc;
}

static std::string trans_for_hwl_ncode(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_ncode;
}

static std::string trans_for_hwl_bsid(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_bsid;
}

static std::string trans_for_hwl_accont(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_accont;
}

static std::string trans_for_hwl_grekey(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_gre_key;
}

static std::string trans_for_hwl_tai(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->hw_tai;
}


static std::string trans_for_trans_proto(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->proto;
}

static std::string trans_for_trans_ttl(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return rtpsinfo_->trailer->ttl;
}

static std::string trans_for_trans_capdate(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo = (ya_rtp_stream_info_t *)edt;
    rtp_stream_info_t    *rtpInfo  = rtpsinfo->rtp_info;
    // time_t unixTime                = rtpInfo->start_fd->abs_ts.secs + rtpInfo->start_rel_time.secs;
    if(rtpInfo->start_fd!= NULL){
      time_t unixTime                = rtpInfo->start_fd->abs_ts.secs ;//+ rtpInfo->start_rel_time.secs;
      tm     tm                      = *localtime((time_t *)&unixTime);
      char timeBuff[20]              = { 0 };

      strftime(timeBuff, sizeof timeBuff, "%Y-%m-%d %H:%M:%S", &tm);

      return timeBuff;
    }
    return "";
}

static std::string trans_for_trans_dealdate(epan_dissect_t *edt, const field_info *pFinfo)
{
    return unixTime2Str(time(NULL));
}

static std::string trans_for_src_address(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;
    
    return std::string(alloced_uptr(address_to_display(NULL, &(rtp_info_->src_addr)), [](void *p) { wmem_free(NULL, p); }).get());
}

static std::string trans_for_dst_address(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;
    
    return std::string(alloced_uptr(address_to_display(NULL, &(rtp_info_->dest_addr)), [](void *p) { wmem_free(NULL, p); }).get());
}

static std::string trans_for_src_port(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;
    
    return std::to_string(rtp_info_->src_port);
}

static std::string trans_for_dst_port(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;
    
    return std::to_string(rtp_info_->dest_port);
}

static std::string trans_for_sip_from(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;
    RtpPortInfo rtpPort;

    rtpPort.port_src = rtp_info_->src_port;
    rtpPort.port_dst = rtp_info_->dest_port;
    rtpPort.payload_type = rtp_info_->payload_type;
    auto findPortIter_dst = RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.find(rtpPort);
    if (findPortIter_dst != RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.end()) 
    {
        return findPortIter_dst->second.sip_from;
    }else {
      rtpPort.port_src = rtp_info_->dest_port;
      rtpPort.port_dst = rtp_info_->src_port;
      auto findPortIter_src = RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.find(rtpPort);
      if (findPortIter_src != RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.end()) 
      {
        return  findPortIter_src->second.sip_from;
      }
    }
    return "";
}

static std::string trans_for_sip_to(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;
    RtpPortInfo rtpPort;

    rtpPort.port_src = rtp_info_->src_port;
    rtpPort.port_dst = rtp_info_->dest_port;
    rtpPort.payload_type = rtp_info_->payload_type;

    auto findPortIter_dst = RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.find(rtpPort);
    if (findPortIter_dst != RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.end()) 
    {
        RtpMediaInfo minfo = findPortIter_dst->second;
        return minfo.sip_to;
    }else {
      rtpPort.port_src = rtp_info_->dest_port;
      rtpPort.port_dst = rtp_info_->src_port;
      auto findPortIter_src = RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.find(rtpPort);
      if (findPortIter_src != RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.end()) 
      {
          return  findPortIter_src->second.sip_to;
      }
    }
    return "";
}

static std::string trans_for_rtp_ssrc(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    rtp_stream_info_t *   rtp_info_ = rtpsinfo_->rtp_info;
    char out_buff[11]               = { 0 };

    // strcpy(out_buff, "0x");
    //dword_to_hex(&out_buff[2], rtp_info_->ssrc);
    snprintf(out_buff,sizeof out_buff,"0x%x",rtp_info_->ssrc);
    return out_buff;
}

static std::string trans_for_rtp_payload(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;
    
    return std::to_string(rtp_info_->payload_type);
}

static std::string trans_for_rtp_pt(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
 
  if (rtpsinfo_->rtp_info->payload_type_name != NULL)
        return rtpsinfo_->rtp_info->payload_type_name;
  return "";


}

static std::string trans_for_rtp_sample_rate(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    
    return rtpsinfo_->sample_rate;
}

static std::string trans_for_rtp_count(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;
    
    return std::to_string(rtp_info_->packet_count);
}

static std::string trans_for_rtp_lost(epan_dissect_t *edt, const field_info *pFinfo)
{
    uint32_t expected = 0, lost_ = 0;
    
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt; 
    
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;
    if(rtp_info_->rtp_stats.stop_seq_nr > 0 ){
        expected = (rtp_info_->rtp_stats.stop_seq_nr + rtp_info_->rtp_stats.cycles * 65536)
                    - rtp_info_->rtp_stats.start_seq_nr + 1;
        
        lost_ = expected - rtp_info_->rtp_stats.total_nr;
        return std::to_string(lost_);
    }

    if(rtpsinfo_->loss_info_.streamPacketLossNum_ > 0){
      lost_ = rtpsinfo_->loss_info_.streamPacketLossNum_;
    }
    return std::to_string(lost_);
}

static std::string trans_for_rtp_max_delta(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt; 
    
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;

    return std::to_string(rtp_info_->rtp_stats.max_delta);
}

static std::string trans_for_rtp_max_jitter(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;

    return std::to_string(rtp_info_->rtp_stats.max_jitter);
}

static std::string trans_for_rtp_mean_jitter(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;

    return std::to_string(rtp_info_->rtp_stats.mean_jitter);
}

static std::string trans_for_rtp_status(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;

    return rtp_info_->problem ? "Problem" : "";
}

static std::string transform_field_for_filename(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    const std::string & rtp_name = rtpsinfo_->file_name;
    char buffer[1024] = {0};
    char cmd[1024] = {0};
    FILE *fp = NULL;
    std::string strAppDir        = getAppDir();

    if(rtp_name.empty())
        return "";
    if(rtp_name.find(".raw")!= std::string::npos){
      if(strstr(rtpsinfo_->rtp_info->payload_type_name,"H263")!=NULL)
      {
        std::string H263_file_name = rtp_name;
        H263_file_name.erase(H263_file_name.find(".raw"));
        H263_file_name += ".mkv";
        snprintf(cmd, sizeof(cmd), "ffmpeg -y -i %s -c:v copy %s", rtp_name.c_str(),H263_file_name.c_str());
        fp = popen(cmd, "r");
        if (fp != NULL)
        {
        pclose(fp);
        VoipStreamKeeper::getInstance()->insertRtpStreamFileToVector(H263_file_name);
        return H263_file_name.substr(H263_file_name.find("tmp_") +4);
        }
      } else if(strcmp(rtpsinfo_->rtp_info->payload_type_name,"g723")==0)
      {
        std::string G723_file_name = rtp_name;
        G723_file_name.erase(G723_file_name.find(".raw"));
        G723_file_name += ".wav";
        snprintf(cmd, sizeof(cmd), "ffmpeg -y -f g723_1 -ac 1 -y -i %s %s", rtp_name.c_str(),G723_file_name.c_str());
        fp = popen(cmd, "r");
        if (fp != NULL)
        {
        pclose(fp);
        // fgets(buffer, sizeof(buffer), fp);
        VoipStreamKeeper::getInstance()->insertRtpStreamFileToVector(G723_file_name);
        return G723_file_name.substr(G723_file_name.find("tmp_") +4);
        }
      } else if((rtpsinfo_->payload_name == "AMR"||strcmp(rtpsinfo_->rtp_info->payload_type_name,"AMR")==0||rtpsinfo_->payload_name == "AMR-WB")&&rtp_name.find("raw")!= std::string::npos)
      {
        std::string amr_file_name = rtp_name;
        if(rtpsinfo_->payload_name == "AMR-WB"){
          snprintf(cmd, sizeof(cmd), "python2 %samr.py -w -n 1 -a %s",strAppDir.c_str(), rtp_name.c_str());
        }else{
          snprintf(cmd, sizeof(cmd), "python2 %samr.py -n 1 -a %s",strAppDir.c_str(), rtp_name.c_str());
        }
        fp = popen(cmd, "r");
        if (fp != NULL)
        {
        fgets(buffer, sizeof(buffer), fp);
        pclose(fp);
        amr_file_name.erase(amr_file_name.find(".raw"));
        amr_file_name += ".amr";
        struct stat file_state;
        memset(&file_state ,0, sizeof(struct stat));
        if(0 != stat(amr_file_name.c_str(), &file_state) && file_state.st_size == 0)
        {
          return "";
        }
        VoipStreamKeeper::getInstance()->insertRtpStreamFileToVector(amr_file_name);
        return amr_file_name.substr(amr_file_name.find("tmp_") +4);
        }
      }else if(strstr(rtpsinfo_->rtp_info->payload_type_name,"g728")!=NULL)
      {
        std::string g728_file_name = rtp_name;
        g728_file_name.erase(g728_file_name.find(".raw"));
        snprintf(cmd, sizeof(cmd), "%sg728fp -little -nopostf dec %s %s.g728",strAppDir.c_str(), rtp_name.c_str(),g728_file_name.c_str());
        fp = popen(cmd, "r");
        if (fp != NULL)
        {
        fgets(buffer, sizeof(buffer), fp);
        pclose(fp);
        }
        sleep(1);
        snprintf(cmd, sizeof(cmd), "ffmpeg -y -f s16le -ar 8000 -ac 1 -i %s.g728 -c:a copy %s.wav", g728_file_name.c_str(),g728_file_name.c_str());
        fp = popen(cmd, "r");
        if (fp != NULL)
        {
        fgets(buffer, sizeof(buffer), fp);
        pclose(fp);
        }
        g728_file_name += ".wav";
        VoipStreamKeeper::getInstance()->insertRtpStreamFileToVector(g728_file_name);
        return g728_file_name.substr(g728_file_name.find("tmp_") +4);

      }
    }
    if(strstr(rtpsinfo_->rtp_info->payload_type_name,"h261")!=NULL)
      {
        std::string H261_file_name = rtp_name;
        H261_file_name.erase(H261_file_name.find(".h261"));
        H261_file_name += ".avi";
        snprintf(cmd, sizeof(cmd), "ffmpeg -y -i %s -c:v copy -f avi %s", rtp_name.c_str(),H261_file_name.c_str());
        fp = popen(cmd, "r");
        if (fp != NULL)
        {
        pclose(fp);
        // fgets(buffer, sizeof(buffer), fp);
        VoipStreamKeeper::getInstance()->insertRtpStreamFileToVector(H261_file_name);
        return H261_file_name.substr(H261_file_name.find("tmp_") +4);
        }
    }
    return rtp_name.substr(rtp_name.find("tmp_") +4);
}
static std::string  transform_field_for_data_type_num(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    const std::string & rtp_name = rtpsinfo_->file_name;
    if(rtpsinfo_->rtp_info->payload_type_name == NULL){
      return "";
    }
    if(rtpsinfo_->payload_name == "AMR"||strcmp(rtpsinfo_->rtp_info->payload_type_name,"AMR")==0||
        (strcmp(rtpsinfo_->rtp_info->payload_type_name,"g723")==0)||
        (strstr(rtpsinfo_->rtp_info->payload_type_name,"H263")==NULL&&
        rtp_name.find(".raw")!= std::string::npos)||
        rtp_name.find(".wav")!= std::string::npos){
            return std::to_string(2);
        }else
        {
            return std::to_string(7);
        }
}
static std::string transform_field_for_filesize(epan_dissect_t *edt, const field_info *pFinfo)
{
    struct stat file_state;
    memset(&file_state ,0, sizeof(struct stat));
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    if (rtpsinfo_->file_size > 0)
        return std::to_string(rtpsinfo_->file_size);

    stat(rtpsinfo_->file_name.c_str(), &file_state);

    if (file_state.st_size == 0)
    {
        const std::string & tmp = std::string(rtpsinfo_->file_name + ".writing");
        stat(tmp.c_str(), &file_state);
    }

    return std::to_string(file_state.st_size);
}

static std::string transform_field_for_from_dir_name(epan_dissect_t *edt, const field_info *pFinfo)
{
    const std::string & pcap_file_name = CapFileProcessor::GetCurrentProcessingPcapFilename();

    size_t dir_end = pcap_file_name.find_last_of('/');

    if (dir_end == std::string::npos)
        return "";

    // return std::string(pcap_file_name.substr(0, dir_end));
    return pcap_file_name;
}

static std::string transform_field_for_duration(epan_dissect_t *edt, const field_info *pFinfo)
{
    char strTimeBuf[30] = { 0 };

    ya_rtp_stream_info_t * rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;

    time_t callDuration = (rtp_info_->stop_rel_time.secs) - (rtp_info_->start_rel_time.secs);

    if (callDuration == 0 && !rtpsinfo_->sample_rate.empty())
    {
        struct stat file_state;
        memset(&file_state ,0, sizeof(struct stat));

        if (rtpsinfo_->file_size > 0)
            callDuration = rtpsinfo_->file_size / atoi(rtpsinfo_->sample_rate.c_str());
        else
        {
            stat(rtpsinfo_->file_name.c_str(), &file_state);

            if (file_state.st_size == 0)
            {
                const std::string & tmp = std::string(rtpsinfo_->file_name.substr(0, rtpsinfo_->file_name.find_first_of('.')) + ".writing");
                stat(tmp.c_str(), &file_state);
            }
        }

        callDuration = file_state.st_size / atoi(rtpsinfo_->sample_rate.c_str());
    }

    struct tm *tms = gmtime((time_t *)&callDuration);
    mktime(tms);
    strftime(strTimeBuf, sizeof strTimeBuf, "%H:%M:%S", tms);
    return strTimeBuf;
    //return ctime(&callDuration);
}

static std::string transform_field_for_line_no(epan_dissect_t *edt, const field_info *pFinfo)
{
    ya_rtp_stream_info_t * rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    return rtpsinfo_->line_no;
}

static std::string transform_field_for_packet_is_loss(epan_dissect_t *edt, const field_info *pFinfo){
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    if(rtpsinfo_->loss_info_.streamPacketLossNum_ > 0){
          return std::to_string(1);
      }
    return std::to_string(0);
}

static std::string transform_field_for_packet_loss_percent(epan_dissect_t *edt, const field_info *pFinfo){
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    if(rtpsinfo_->loss_info_.streamPacketLossNum_ > 0){
    int precent = 100 - (rtpsinfo_->loss_info_.streamPacketLossNum_ * 100 /
                              (rtpsinfo_->loss_info_.lastDequeuedPktNum_ - rtpsinfo_->loss_info_.streamPacketFirstNum));
    while(precent > 100){
		precent = precent/10;
	}
    auto precent_str = std::to_string(precent);
    precent_str += "%";
    return precent_str;
    }
    return "100%";

}
static std::string transform_field_for_packet_loss_num(epan_dissect_t *edt, const field_info *pFinfo){
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    if(rtpsinfo_->loss_info_.streamPacketLossNum_ > 0){
    return std::to_string(rtpsinfo_->loss_info_.streamPacketLossNum_ );
    }
    return std::to_string(0);

}


static std::string transform_field_for_packet_fill_zero_bytes(epan_dissect_t *edt, const field_info *pFinfo){
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    return std::to_string(rtpsinfo_->loss_info_.streamPacketLossBytes_);
}
static std::string transform_field_for_packet_fill_zero_time(epan_dissect_t *edt, const field_info *pFinfo){
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    if(rtpsinfo_->loss_info_.streamPacketLossBytes_> 0 &&rtpsinfo_->loss_info_.channels_>0&&rtpsinfo_ ->loss_info_.rtp_sample_rate_>0){
        float fill_zero_time = static_cast <float> (rtpsinfo_->loss_info_.streamPacketLossBytes_)/
                              static_cast <float> (rtpsinfo_->loss_info_.channels_)/
                              static_cast <float> (2) /
                              static_cast <float> (rtpsinfo_ ->loss_info_.rtp_sample_rate_);
        auto str = std::to_string(fill_zero_time);
        return str.substr(0,str.find(".")+2) + "s";
    }
    return "0s";
}
//异常帧数
static std::string transform_field_for_packet_unusual_num(epan_dissect_t *edt, const field_info *pFinfo){
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    if(rtpsinfo_->loss_info_.packet_total_num_ -rtpsinfo_->loss_info_.packet_normal_num_ > 0){
    return std::to_string(rtpsinfo_->loss_info_.packet_total_num_ -rtpsinfo_->loss_info_.packet_normal_num_);
    }
    return std::to_string(0);

    }
//总帧数
static std::string transform_field_for_packet_total_num(epan_dissect_t *edt, const field_info *pFinfo){
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    return std::to_string(rtpsinfo_->loss_info_.packet_total_num_);
    }
//正常帧数
static std::string transform_field_for_packet_normal_num(epan_dissect_t *edt, const field_info *pFinfo){
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    return std::to_string(rtpsinfo_->loss_info_.packet_normal_num_);

    }
//重复帧数量
static std::string transform_field_for_packet_repeat_num(epan_dissect_t *edt, const field_info *pFinfo){
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    return std::to_string(rtpsinfo_->loss_info_.packet_repeat_num_);
    }

static std::string transform_field_for_zj_tunnel_layer(epan_dissect_t *edt, const field_info *pFinfo){


    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    ya_flow_tunnel_info_t  * flow_tunnel_info = rtpsinfo_->flow_tunnel_info;

    return flow_tunnel_info->tunnel_layer;

}

static std::string transform_field_for_zj_tunnel_addr(epan_dissect_t *edt, const field_info *pFinfo){

    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    ya_flow_tunnel_info_t  * flow_tunnel_info = rtpsinfo_->flow_tunnel_info;

    return flow_tunnel_info->tunnel_str;

}
static std::string transform_field_for_file_creat_time(epan_dissect_t *edt, const field_info *pFinfo){
      ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
      return unixTime2Str(rtpsinfo_->file_creat_time);
}
static std::string transform_field_for_file_finish_time(epan_dissect_t *edt, const field_info *pFinfo){
    return unixTime2Str(time(NULL));
}
struct RtpPortInfo;
static std::string  transform_field_for_Q931CallingPartyNumber(epan_dissect_t *edt, const field_info *pFinfo){
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;
    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;

    if(rtpsinfo_->Q931CallingPartyNumber.size()!= 0){
      return rtpsinfo_->Q931CallingPartyNumber;
    }

    RtpPortInfo rtpPort;

    rtpPort.port_src = rtp_info_->src_port;
    rtpPort.port_dst = rtp_info_->dest_port;
    rtpPort.payload_type = rtp_info_->payload_type;

    auto findPortIter_dst = RtpStreamKeeper::getInstance()-> map_port2rtpMediaInfo_.find(rtpPort);
    if (findPortIter_dst != RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.end())
    {
        RtpMediaInfo minfo = findPortIter_dst->second;
        return minfo.Q931CallingPartyNumber;
    }else {
      rtpPort.port_src = rtp_info_->dest_port;
      rtpPort.port_dst = rtp_info_->src_port;
      auto findPortIter_src = RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.find(rtpPort);
      if (findPortIter_src != RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.end()) 
      {
          return findPortIter_src->second.Q931CallingPartyNumber;
      }
    }
    return "";
}
static std::string  transform_field_for_Q931CalledPartyNumber(epan_dissect_t *edt, const field_info *pFinfo){
    ya_rtp_stream_info_t *rtpsinfo_ = (ya_rtp_stream_info_t *)edt;

    rtp_stream_info_t * rtp_info_ = rtpsinfo_->rtp_info;

    if(rtpsinfo_->Q931CalledPartyNumber.size()!= 0){
      return rtpsinfo_->Q931CalledPartyNumber;
    }

    RtpPortInfo rtpPort;

    rtpPort.port_src = rtp_info_->src_port;
    rtpPort.port_dst = rtp_info_->dest_port;
    rtpPort.payload_type = rtp_info_->payload_type;

    auto findPortIter_dst = RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.find(rtpPort);
    if (findPortIter_dst != RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.end()) 
    {
        RtpMediaInfo minfo = findPortIter_dst->second;
        return minfo.Q931CalledPartyNumber;
    }else {
      rtpPort.port_src = rtp_info_->dest_port;
      rtpPort.port_dst = rtp_info_->src_port;
      auto findPortIter_src = RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.find(rtpPort);
      if (findPortIter_src != RtpStreamKeeper::getInstance()->map_port2rtpMediaInfo_.end()) 
      {
          return findPortIter_src->second.Q931CalledPartyNumber;
      }
    }
    return "";
}
static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_GCH_COMMON_FIELDS(),

    F_D_ITEM("TAGTYPE"                      , ""                                , eMT_fromEdt      , "",     trans_for_hwl_nettype),
    F_D_ITEM("OPERATOR_TYPE"                , ""                                , eMT_fromEdt      , "",     trans_for_hwl_operator),
    F_D_ITEM("HW_NCODE"                     , ""                                , eMT_fromEdt      , "",     trans_for_hwl_ncode),
    F_D_ITEM("HW_ACCOUNT"                   , ""                                , eMT_fromEdt      , "",     trans_for_hwl_accont),
    F_D_ITEM("HW_ESN"                       , ""                                , eMT_fromEdt      , "",     trans_for_hwl_esn),
    F_D_ITEM("HW_MEID"                      , ""                                , eMT_fromEdt      , "",     trans_for_hwl_meid),
    F_D_ITEM("HW_LAC"                       , ""                                , eMT_fromEdt      , "",     trans_for_hwl_lac),
    F_D_ITEM("HW_SAC"                       , ""                                , eMT_fromEdt      , "",     trans_for_hwl_sac),
    F_D_ITEM("HW_CI"                        , ""                                , eMT_fromEdt      , "",     trans_for_hwl_cellid),
    F_D_ITEM("HW_ECGI"                      , ""                                , eMT_fromEdt      , "",     trans_for_hwl_ecgi),
    F_D_ITEM("HW_BSID"                      , ""                                , eMT_fromEdt      , "",     trans_for_hwl_bsid),
    F_D_ITEM("HW_GRE_KEY"                   , ""                                , eMT_fromEdt      , "",     trans_for_hwl_grekey),
    F_D_ITEM("HW_TAI"                       , ""                                , eMT_fromEdt      , "",     trans_for_hwl_tai),
    F_D_ITEM("HW_ECGI_MNC"                  , ""                                , eMT_fromEdt      , "",     trans_for_hwl_mnc),
    F_D_ITEM("HW_APN"                       , ""                                , eMT_fromEdt      , "",     trans_for_hwl_apn),
    F_D_ITEM("RTL_TEID"                     , ""                                , eMT_fromEdt      , "",     trans_for_rtl_teid),
    F_D_ITEM("RTL_OUTTER_SRC"               , ""                                , eMT_fromEdt      , "",     trans_for_rtl_outsrc),
    F_D_ITEM("RTL_OUTTER_DST"               , ""                                , eMT_fromEdt      , "",     trans_for_rtl_outdst),
    F_D_ITEM("RTL_MSISDN"                   , ""                                , eMT_fromEdt      , "",     trans_for_rtl_msidsn),
    F_D_ITEM("RTL_IMEI"                     , ""                                , eMT_fromEdt      , "",     trans_for_rtl_imei),
    F_D_ITEM("RTL_IMSI"                     , ""                                , eMT_fromEdt      , "",     trans_for_rtl_imsi),
    F_D_ITEM("RTL_TAC"                      , ""                                , eMT_fromEdt      , "",     trans_for_rtl_tac),
    F_D_ITEM("RTL_PLMN_ID"                  , ""                                , eMT_fromEdt      , "",     trans_for_rtl_plmnid),
    F_D_ITEM("RTL_ULI"                      , ""                                , eMT_fromEdt      , "",     trans_for_rtl_uli),
    F_D_ITEM("RTL_BS"                       , ""                                , eMT_fromEdt      , "",     trans_for_rtl_bs),
    F_D_ITEM("DevNo"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("LineNo"                       , ""                                , eMT_fromEdt      , "",     transform_field_for_line_no),
    F_D_ITEM("LinkLayerType"                , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("isIPv6"                       , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("isMPLS"                       , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("nLabel"                       , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("innerLabel"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("otherLabel"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv1"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv2"                        , ""                                , eMT_fromEdt      , "0",    NULL),
    F_D_ITEM("resv3"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv4"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv5"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv6"                        , ""                                , eMT_fromEdt        , "",     transform_field_for_task_id),
    F_D_ITEM("resv7"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("resv8"                        , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("CapDate"                      , ""                                , eMT_fromEdt      , "",     trans_for_trans_capdate),
    F_D_ITEM("DealDate"                     , ""                                , eMT_fromEdt      , "",     trans_for_trans_dealdate),
    F_D_ITEM("SrcIp"                        , ""                                , eMT_fromEdt      , "",     trans_for_src_address),
    F_D_ITEM("SrcCountry"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("SrcArea"                      , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("SrcProvince"                  , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("SrcCity"                      , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("SrcCarrier"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("DstIp"                        , ""                                , eMT_fromEdt      , "",     trans_for_dst_address),
    F_D_ITEM("DstCountry"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("DstArea"                      , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("DstProvince"                  , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("DstCity"                      , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("DstCarrier"                   , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("SrcPort"                      , ""                                , eMT_fromEdt      , "",     trans_for_src_port),
    F_D_ITEM("DstPort"                      , ""                                , eMT_fromEdt      , "",     trans_for_dst_port),
    F_D_ITEM("C2S"                          , ""                                , eMT_fixed        , "",     NULL),
    F_D_ITEM("Proto"                        , ""                                , eMT_fromEdt      , "",     trans_for_trans_proto),
    F_D_ITEM("TTL"                          , ""                                , eMT_fromEdt      , "",     trans_for_trans_ttl),
    F_D_ITEM("From"                         , ""                                , eMT_fromEdt      , "",     trans_for_sip_from),
    F_D_ITEM("To"                           , ""                                , eMT_fromEdt      , "",     trans_for_sip_to),
    F_D_ITEM("SSRC"                         , ""                                , eMT_fromEdt      , "",     trans_for_rtp_ssrc),
    F_D_ITEM("Payload"                      , ""                                , eMT_fromEdt      , "",     trans_for_rtp_payload),
    F_D_ITEM("PayloadType"                  , ""                                , eMT_fromEdt      , "",     trans_for_rtp_pt),
    F_D_ITEM("Sample_Rate"                  , ""                                , eMT_fromEdt      , "",     trans_for_rtp_sample_rate),
    F_D_ITEM("Packets"                      , ""                                , eMT_fromEdt      , "",     trans_for_rtp_count),
    F_D_ITEM("Lost"                         , ""                                , eMT_fromEdt      , "",     trans_for_rtp_lost),
    F_D_ITEM("MaxDelta(ms)"                 , ""                                , eMT_fromEdt      , "",     trans_for_rtp_max_delta),
    F_D_ITEM("MaxJitter"                    , ""                                , eMT_fromEdt      , "",     trans_for_rtp_max_jitter),
    F_D_ITEM("MeanJitter"                   , ""                                , eMT_fromEdt      , "",     trans_for_rtp_mean_jitter),
    F_D_ITEM("Status"                       , ""                                , eMT_fromEdt      , "",     trans_for_rtp_status),
    F_D_ITEM("Duration"                     , ""                                , eMT_fromEdt      , "",     transform_field_for_duration),
    F_D_ITEM("FromDir"                      , ""                                , eMT_fromEdt      , "",     transform_field_for_from_dir_name),
    F_D_ITEM("FileName"                     , ""                                , eMT_fromEdt      , "",     transform_field_for_filename),
    F_D_ITEM("FileSize"                     , ""                                , eMT_fromEdt      , "",     transform_field_for_filesize),
    F_D_ITEM("Q931CallingPartyNumber"                     , ""                  , eMT_fromEdt      , "",     transform_field_for_Q931CallingPartyNumber),
    F_D_ITEM("Q931CalledPartyNumber"                     , ""                   , eMT_fromEdt      , "",     transform_field_for_Q931CalledPartyNumber),
    F_D_ITEM("PacketTotalNum"                     , ""                          , eMT_fromEdt      , "",     transform_field_for_packet_total_num),//总帧数
    F_D_ITEM("PacketNormalNum"                     , ""                         , eMT_fromEdt      , "",     transform_field_for_packet_normal_num),//正常帧数
    F_D_ITEM("PacketRepeatNum"                     , ""                         , eMT_fromEdt      , "",     transform_field_for_packet_repeat_num),//重复帧数量
    F_D_ITEM("PacketUnusualNum"                     , ""                         , eMT_fromEdt      , "",     transform_field_for_packet_unusual_num),//重复帧数量
    F_D_ITEM("PacketIsLoss"                 , ""                                , eMT_fromEdt      , "",     transform_field_for_packet_is_loss),//是否丢帧
    F_D_ITEM("PacketLossNum"                , ""                                , eMT_fromEdt      , "",     transform_field_for_packet_loss_num),//丢帧数量（排序后等同于异常帧）
    F_D_ITEM("PacketCompletePercent"            , ""                            , eMT_fromEdt      , "",     transform_field_for_packet_loss_percent),//完整度
    F_D_ITEM("PacketFillZeroBytes"          , ""                                , eMT_fromEdt      , "",     transform_field_for_packet_fill_zero_bytes),//填充静帧字节数
    F_D_ITEM("PacketFillZeroTime"           , ""                                , eMT_fromEdt      , "",     transform_field_for_packet_fill_zero_time),//填充静帧时长
    F_D_ITEM("DataTypeNum"                , ""                                , eMT_fromEdt      , "",     transform_field_for_data_type_num),
    F_D_ITEM("FileCreatTime"                , ""                                , eMT_fromEdt      , "",     transform_field_for_file_creat_time),
    F_D_ITEM("FileFinishTime"                , ""                                , eMT_fromEdt      , "",     transform_field_for_file_finish_time)
};

ProtoFieldExtractorRtpStream::ProtoFieldExtractorRtpStream():ProtoFieldExtractor("RTP_STREAM", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}

bool ProtoFieldExtractorRtpStream::ExtractSpecialFields(epan_dissect_t * edt, RecordWriter * pWriter)
{
    return false;
}





