/****************************************************************************************
 * 文 件 名 : yaEty_rec_dumpper_cap.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-08-22
* 编    码 : root      '2018-08-22
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _YAETY_REC_DUMPPER_CAP_H_
#define _YAETY_REC_DUMPPER_CAP_H_

// stdlib
#include <linux/limits.h>
#include <stdint.h>
#include <string>
#include <vector>
#include <map>

// our header
#include "yaEty_rec_dumper.h"

// constants
static const int   DUMP_SNAPSHOT_LEN      = 8192;

// 用于父子进程通信的 pkt 消息结构
struct EPM_DumpPktContext
{
    char     protoName[20];
    char     classifyFieldValue[50];
    wtap_rec rec;
    uint8_t  buf[DUMP_SNAPSHOT_LEN];     // len 为 rec.rec_header.packet_header.caplen
};

// helper struct
struct DumpContext
{
    char         fileNameBuf[PATH_MAX];         // dump 文件名
    char         fileNameBufWriting[PATH_MAX];  // dump 文件 writing 文件名
    int          index;                         // 该 dumper 的索引号，用来和 hash 结果对应
    int          dumpedFrameCnt;                // 该 dump 文件当前已经写入的 pkt cnt
    wtap_dumper  *pDumper;                      // wtap_dumper 对象指针，对应一个 dump pcap 文件
};
#define DUMP_CONTEXT_ZERO { {0}, {0}, 0, 0, NULL}

// CapRecordDumpper
class CapRecordDumper : public RecordDumper
{
public:
    CapRecordDumper(const std::string &strProtoName,
                    const std::string &strClassifyField  = "ip.src");

    ~CapRecordDumper();

public: // static method
    static void             Register();
    static bool             shouldDumpPktOfProto(const std::string &strProto);
    static CapRecordDumper *getDumperOf(const std::string &strProtoName);
    static void             revertAllDumpers();

public: // interface
    virtual bool            dump(const wtap_rec *rec, const uint8_t *buf, const char *pClassifyFieldValue) override;

    std::string  getClassifyField()
    {
         return strClassifyField_;
    }

private:
    DumpContext *getDumpContextOf(const char *pClassifyFieldValue);

    bool         generateDumpFileName(DumpContext *pDumpCtx, int index);

    DumpContext *createDumper(int index);

    bool         destroyDumper(DumpContext *pDumpCtx);

    void         destroyDumpers();

    std::string  getDumpFileSuffix();

    int getThreadId();

private:
    static int   registerDumper(const std::string &strProtoName, CapRecordDumper *pDumper);
    static int   unregisterDumper(const std::string &strProtoName);
    static void  configDumpers();

private:
    std::string             strProtoName_;
    std::string             strClassifyField_;
    std::vector<DumpContext> vecDumpers_;

private:
    int         dump_err_;
    char        *dump_err_info_;

private: // static members
    static int                                      cs_lPktsPerFile;
    static int                                      cs_lClassifyToNFile;
    static std::string                              cs_strDumpFileDir;
    static std::map<std::string, CapRecordDumper *> cs_mapDumper;
    static std::map<std::string, std::string>       cs_enabledProtoDumper; /* map, key means a proto packet should dump to disk and classified by value */
};

#endif /* _YAETY_REC_DUMPPER_CAP_H_ */
