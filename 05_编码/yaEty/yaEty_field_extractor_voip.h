#pragma once

#include <set>

#include <ui/rtp_stream.h>
#include "yaEty_field_extractor.h"
#include "config.h"
#include "epan/epan_dissect.h"
#include "yaEty_stream_keeper_def.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor_def.h"
#include "yaEty_content_reassembly_share_mem.h"

// ssrc, src_port(from _rtp_stream_info), dst_port(from _rtp_stream_info);
typedef std::tuple<uint32_t, uint16_t, uint16_t> voip_key_t;

static inline voip_key_t fromRtpStreamInfo(const rtp_stream_info_t *p)
{
    return std::make_tuple(p->ssrc, p->src_port, p->dest_port);
}
struct compare_voip_key
{
    bool operator()(const voip_key_t k1 , const voip_key_t k2){
      if(std::get<1>(k1) != std::get<1>(k2)  && std::get<1>(k1) != std::get<2>(k2)){
          return std::get<1>(k1) < std::get<1>(k2);
      }
      if(std::get<2>(k1) != std::get<2>(k2)  && std::get<2>(k1) != std::get<1>(k2)){
          return std::get<2>(k1) < std::get<2>(k2);
      }
      if(std::get<0>(k1) != std::get<0>(k2)){
          return std::get<0>(k1) < std::get<0>(k2);
      }

      return false;
    }
};

struct compare_rsi
{
    bool operator()(const rtp_stream_info_t* k1 , const rtp_stream_info_t* k2){
      if(k1 ==NULL || k2 == NULL){
        return true;
      }
      if(k1->src_port != k2->src_port && k1->src_port != k2->dest_port){
          return k1->src_port < k2->src_port;
      }
      if(k1->dest_port != k2->dest_port && k1->dest_port != k2->src_port){
          return k1->dest_port < k2->dest_port;
      }
      if(k1->ssrc != k2->ssrc){
          return k1->ssrc < k2->ssrc;
      }

      return false;
    }
};
class ProtoFieldExtractorVoip:public ProtoFieldExtractor
{
public:
    ProtoFieldExtractorVoip();
    bool ExtractSpecialFields(epan_dissect_t * edt, RecordWriter * pWriter);
    int GetDebugFieldCount() override
    {
        return 1;
    }

public:
    static uint32_t    AddVoipSsrc(rtp_stream_info_t *rtp_stream);
    static uint32_t    AddVoipRtpStream(uint32_t num, rtp_stream_info_t* p_rtp_stream);
    static void        CleanVoipSsrc();
    static void        CleanVoipRtpStream();

  public:
    static std::set<rtp_stream_info_t *> voip_ssrc;
    // rsi -> call_num
    static std::map<rtp_stream_info_t *, uint32_t,compare_rsi> voip_rtp_stream;

};
