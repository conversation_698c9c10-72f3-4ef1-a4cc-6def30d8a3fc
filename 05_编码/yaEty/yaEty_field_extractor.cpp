/****************************************************************************************
 * 文 件 名 : yaEty_proto_field_mapper.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-14
* 编    码 : zhengsw      '2018-05-14
* 修    改 : zhangsx      '2018-11-14
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <stdio.h>
#include <string.h>

#include <string>
#include <algorithm>

#include "epan/strutil.h"
#include "yaEty_field_extractor.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer.h"
#include "yaEty_config.h"
#include "yaEty_cap_file_processor.h"

#include "yaEty_field_extractor_http.h"
#include "yaEty_field_extractor_bgp.h"
#include "yaEty_field_extractor_dns.h"
#include "yaEty_field_extractor_ftp.h"
#include "yaEty_field_extractor_smtp.h"
#include "yaEty_field_extractor_pop.h"
#include "yaEty_field_extractor_imap.h"
#include "yaEty_field_extractor_radius.h"
#include "yaEty_field_extractor_sip.h"
#include "yaEty_field_extractor_rtp.h"
#include "yaEty_field_extractor_esp.h"
#include "yaEty_field_extractor_l2tp.h"
#include "yaEty_field_extractor_pptp.h"
#include "yaEty_field_extractor_wxf.h"
#include "yaEty_field_extractor_dhcp.h"
#include "yaEty_field_extractor_telnet.h"
#include "yaEty_field_extractor_ssh.h"
#include "yaEty_field_extractor_mysql.h"
#include "yaEty_field_extractor_tds.h"
#include "yaEty_field_extractor_dtls.h"
#include "yaEty_field_extractor_rip.h"
#include "yaEty_field_extractor_snmp.h"
#include "yaEty_field_extractor_megaco.h"
#include "yaEty_field_extractor_rdp.h"
#include "yaEty_field_extractor_diameter.h"
#include "yaEty_field_extractor_eigrp.h"
#include "yaEty_field_extractor_ospf.h"
#include "yaEty_field_extractor_classicstun.h"
#include "yaEty_field_extractor_stun.h"
#include "yaEty_field_extractor_ah.h"
#include "yaEty_field_extractor_mgcp.h"
#include "yaEty_field_extractor_ldap.h"
#include "yaEty_field_extractor_gre.h"
#include "yaEty_field_extractor_gtp_control.h"
#include "yaEty_field_extractor_isakmp.h"
#include "yaEty_field_extractor_ssl_n.h"
#include "yaEty_field_extractor_tftp.h"
#include "yaEty_field_extractor_iax.h"
#include "yaEty_field_extractor_sdp_l5.h"
#include "yaEty_field_extractor_wxa.h"
#include "yaEty_field_extractor_sms.h"

template <typename T>
typename ProtoFieldExtractorMap<T>::name2ExtratorMap_t ProtoFieldExtractorMap<T>::s_protoFieldMM;

std::function<bool(const char *)> ProtoFieldExtractorBase::cs_funCheckProtoEnable;
// ========================================================================================================================

std::string transform_field_for_absolute_time(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (fvalue_type_ftenum(const_cast<fvalue_t *>(&pFinfo->value)) != FT_ABSOLUTE_TIME)
    {
        return "error";
    }

    return unixTime2Str(pFinfo->value.value.time.secs);
}

static std::string transform_field_for_cap_filename(epan_dissect_t *edt, const field_info *pFinfo)
{
    return CapFileProcessor::GetCurrentProcessingPcapFilename();
}

std::string transform_field_for_task_id(epan_dissect_t *edt, const field_info *pFinfo)
{
    return CFG->GetTaskID();
}

// 对 pStr 进行字符替换，返回一个新的字符串，内存不用回收
std::string strReplace(const char *pStr, char from, char to)
{
    std::string str = pStr;
    std::replace(str.begin(), str.end(), from, to);

    return str;
}

// 处理 field 中出现的 "|"
std::string transform_field_for_may_occur_tbl_sep(epan_dissect_t *edt, const field_info *pFinfo)
{
    // 将 | 进行替换 ===!! TBL_SEP FOR URI !!===
    return strReplace(pFinfo->value.value.string, '|', '_');
}

// 处理 field 中的二进制
std::string transform_field_for_may_occur_bin(epan_dissect_t *edt, const field_info *pFinfo)
{   // TODO: MEM_AUTO
    // 将 | 进行替换 ===!! TBL_SEP FOR URI !!===
    std::string strRes;
    char *pFormated = format_text(NULL,
                                  (const guchar *)pFinfo->value.value.string,
                                  pFinfo->length );
    strRes = pFormated;
    wmem_free(NULL, pFormated);

    // 增加对 '|' 的转换
    std::replace(strRes.begin(), strRes.end(), '|', '_');
    return strRes;
}

// 确保 hash(src, dst) == hash(dst, src)
size_t hash_proto_5_tuple(uint32_t ipSrc, uint32_t ipDst, uint16_t portSrc, uint16_t portDst, uint8_t proto)
{   // hash 时没有"源目"，只有"大小"
    uint32_t ipLt   = ipSrc;
    uint32_t ipGt   = ipDst;
    uint16_t portLt = portSrc;
    uint16_t portGt = portDst;

    if (ipSrc > ipDst)
    {
        ipLt = ipDst;
        ipGt = ipSrc;
    }

    if (portSrc > portDst)
    {
        portLt = portDst;
        portGt = portSrc;
    }

    return ((size_t)ipLt * 59)    ^
           ((size_t)ipGt)         ^
           ((size_t)portLt << 16) ^
           ((size_t)portGt)       ^
           ((size_t)proto);
}

std::string transform_field_for_hash_code(epan_dissect_t *edt, const field_info *pFinfo)
{
    field_info   *finfo     = NULL;

    // 从 edt 中收集五元组信息
    finfo = get_first_field_info_from_interesting_fields(edt, "ip.src");
    if (NULL == finfo)
    {   // 看来是 ipv6，暂时不支持
        return "";
    }

    uint32_t ipSrc   = 0;
    uint32_t ipDst   = 0;
    uint16_t portSrc = 0;
    uint16_t portDst = 0;
    uint8_t  proto   = 0;

    ipSrc = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.dst");
    ipDst = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.proto");
    proto = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "tcp.srcport");
    if (NULL == finfo)
    {   // 看来不是 tcp，udp 等通过五元组标定上下行并不合适,不处理
        return "";
    }

    portSrc = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "tcp.dstport");
    portDst = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    size_t hash_code = hash_proto_5_tuple(ipSrc, ipDst, portSrc, portDst, proto);
    return std::to_string(hash_code);
}



choose_for_two_type_field::choose_for_two_type_field(const char * field_name1, const char * field_name2)
    :_field_name1(field_name1), _field_name2(field_name2)
{
}

std::string choose_for_two_type_field::operator()(epan_dissect_t *edt, const field_info *pFinfo)
{
    GPtrArray *finfos = nullptr;
    std::string strRst = "";
    
    int field_id1 = proto_registrar_get_id_byname(_field_name1);
    int field_id2 = proto_registrar_get_id_byname(_field_name2);
   
    finfos = proto_get_finfo_ptr_array(edt->tree, field_id1) != nullptr ? proto_get_finfo_ptr_array(edt->tree, field_id1) : proto_get_finfo_ptr_array(edt->tree, field_id2);
    
    if (nullptr == finfos)
    {
        return "";
    }
    
    int finfos_cnt = g_ptr_array_len(finfos);
    
    if (finfos_cnt == 0)
    {
        return "";
    }
    
    for (int i = 0; i < finfos_cnt; ++ i)
    {
        field_info * finfo = (field_info *)g_ptr_array_index(finfos, i);
        
        if (i > 0)
        {
            strRst += ",";
        }
        strRst +=ety_ws_get_node_field_value(finfo, edt);
    }
    
    return strRst;
}
std::string choose_for_two_type_field::operator()(epan_dissect_t *edt, const field_info *pFinfo,int n)
{
    GPtrArray *finfos = nullptr;
    std::string strRst = "";

    int field_id1 = proto_registrar_get_id_byname(_field_name1);
    int field_id2 = proto_registrar_get_id_byname(_field_name2);

    finfos = proto_get_finfo_ptr_array(edt->tree, field_id1) != nullptr ? proto_get_finfo_ptr_array(edt->tree, field_id1) : proto_get_finfo_ptr_array(edt->tree, field_id2);

    if (nullptr == finfos)
    {
        return "";
    }

    int finfos_cnt = g_ptr_array_len(finfos);

    if (finfos_cnt == 0)
    {
        return "";
    }
    if(n == -1){
        field_info * finfo = (field_info *)g_ptr_array_index(finfos, finfos_cnt-1);
        strRst +=ety_ws_get_node_field_value(finfo, edt);
        return strRst;

    }
    finfos_cnt = finfos_cnt > n ? n:finfos_cnt;
    for (int i = 0; i < finfos_cnt; ++ i)
    {
        field_info * finfo = (field_info *)g_ptr_array_index(finfos, i);
        
        if (i > 0)
        {
            strRst += ",";
        }
        strRst +=ety_ws_get_node_field_value(finfo, edt);
    }

    return strRst;
}

string_format_limit_length::string_format_limit_length(uint32_t raw_str_len_lmt, uint32_t out_str_len_lmt)
    :_raw_str_len_lmt(raw_str_len_lmt), _out_str_len_lmt(out_str_len_lmt)
{
}

std::string string_format_limit_length::operator()(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (NULL == pFinfo)
        return "";
    
    field_info * pfinfo = const_cast<field_info *>(pFinfo);
    const std::string & tmp_rst = ety_ws_get_node_field_value(pfinfo, edt);
    
    return tmp_rst.size() < _raw_str_len_lmt ? tmp_rst : tmp_rst.substr(0, _out_str_len_lmt) + "...";
}


ProtoFieldExtractorBase:: ProtoFieldExtractorBase(const char *strProtoName)
{

}

ProtoFieldExtractorBase::ProtoFieldExtractorBase(const char *strProtoName, const char *strProtoFieldDescFile)
    : strProtoName_(strProtoName)
    , strProtoFieldDescFile_(strProtoFieldDescFile)
{
    // ParseProtoFileDescFile_fake();
}

ProtoFieldExtractorBase::ProtoFieldExtractorBase(const char *strProtoName, ProtoFieldDesc *pFieldMapItemArray, int arrayLen)
    : strProtoName_(strProtoName)
    , strProtoFieldDescFile_("NONE")
{
    if (NULL == pFieldMapItemArray
        )
    {
        // warning ...
        return ;
    }

    AddFieldDescFromArray(pFieldMapItemArray, arrayLen);
}

ProtoFieldExtractorBase::~ProtoFieldExtractorBase()
{

}

int ProtoFieldExtractorBase::AddFieldDescFromArray( ProtoFieldDesc *pFieldMapItemArray, int arrayLen)
{
    ProtoFieldDesc *pMapItem = NULL;

    for (int i = 0; i < arrayLen; i++)
    {
        pMapItem = &pFieldMapItemArray[i];
        if (NULL == pMapItem)
        {  // 错位？？
            continue;
        }

        AddFieldDescItem(pMapItem);
    }

    return 0;
}

int ProtoFieldExtractorBase::AddFieldDescItem(ProtoFieldDesc *pItem)
{
    if ((pItem->eMappedType & eMT_delayField) == eMT_delayField)
    {
        pItem->wsFieldName.setUpFieldName();
    }
    vecProtoFieldDesc_.push_back(pItem);
    mapEtyName2FieldDesc_.emplace(pItem->etyFieldName, pItem);

    return 0;
}

int ProtoFieldExtractorBase::ParseProtoFieldDescFile()
{
    return 0;
}

int ProtoFieldExtractorBase::addProtoFieldDesc(const char *etyName, const char *wsName)
{
    // mapEtyName2FieldDescIndex_.emplace(etyName, vecProtoFieldDesc_.size());
    // vecProtoFieldDesc_.push_back(ProtoFieldDesc(etyName, wsName));
}


int ProtoFieldExtractorBase::AddDebugFieldsDesc()
{
    // debug mode 下增加一些额外字段
    if (CFG->GetValueOf<bool>("debug_mode"))
    {
        static ProtoFieldDesc s_moreProtoFieldDescArray[] =
        {    // 额外添加 pcap 文件名与 framenumber
            F_D_ITEM("__FromPcap"       , NULL,           eMT_fromEdt,    "none",            transform_field_for_cap_filename),
            F_D_ITEM("__FrameNumber"    , "frame.number", eMT_direct,     "",                NULL),
        };

        // AddFieldDescFromArray(s_moreProtoFieldDescArray, dimen_of(s_moreProtoFieldDescArray));
        AddFieldDescFromArray(s_moreProtoFieldDescArray, GetDebugFieldCount());
    }

    return 0;
}

int ProtoFieldExtractorBase::ParseProtoFileDescFile_fake()
{
    // addProtoFieldDesc("SrcIp", "ip.src");
    // addProtoFieldDesc("DstIp", "ip.dst");

    return 0;
}

int ProtoFieldExtractorBase::GetFieldsCount()
{
    return vecProtoFieldDesc_.size();
}

ProtoFieldDesc *ProtoFieldExtractorBase::GetProtoFieldDescOf(const char *pstrEtyFieldName)
{
    // TODO: some checks
    return mapEtyName2FieldDesc_[pstrEtyFieldName];
}
ProtoFieldDesc *ProtoFieldExtractorBase::GetProtoFieldDescByIndex(uint index)
{
    // TODO: some checks
    return vecProtoFieldDesc_[index];
}

std::string ProtoFieldExtractorBase::GetWsFieldNameOf(const char *strEtyFieldName)
{
    return mapEtyName2FieldDesc_[strEtyFieldName]->wsFieldName;
}

std::string ProtoFieldExtractorBase::GetWsFieldNameByIndex(uint index)
{
    return vecProtoFieldDesc_[index]->wsFieldName;
}

std::string ProtoFieldExtractorBase::GetEtyFieldNameByIndex(uint index)
{
    if (index >= vecProtoFieldDesc_.size())
    {
        return "index used to call GetEtyFieldNameByIndex out of range.";
    }

    return vecProtoFieldDesc_[index]->etyFieldName;
}

static void
PktPostProc_field_generic(epan_dissect_t *edt, ProtoFieldDesc *pProtoFieldDesc, RecordWriter *pWriter)
{
    int         field_id         = 0;
    int         finfos_cnt       = 0;
    field_info *finfo            = NULL;
    GPtrArray  *finfos           = NULL;
    std::string strFinalFieldValue;
    std::string strRes;

    switch (ProtoFieldDesc_GetType(pProtoFieldDesc))
    {
    case eMT_fixed:
    {
        pWriter->writeRecordField(pProtoFieldDesc->etyFieldName, pProtoFieldDesc->defaultFieldValue);
        break;
    }

    case eMT_fromEdt:
    {
        if (NULL == pProtoFieldDesc->funTransfrom)
        {
            strFinalFieldValue = pProtoFieldDesc->defaultFieldValue;
        }
        else
        {   // 进行转换
            strFinalFieldValue = pProtoFieldDesc->funTransfrom(edt, NULL);
        }

        pWriter->writeRecordField(pProtoFieldDesc->etyFieldName, strFinalFieldValue);
        break;
    }

    case eMT_direct:
    case eMT_transform:
    {
        field_id           = proto_registrar_get_id_byname(pProtoFieldDesc->wsFieldName);
        finfos             = proto_get_finfo_ptr_array(edt->tree, field_id);

        if (NULL == finfos)
        {   // 虽然是映射，可惜没有找到解析的结果，转出默认值吧
            pWriter->writeRecordField(pProtoFieldDesc->etyFieldName, pProtoFieldDesc->defaultFieldValue);
            //printf("field %s not found.\n", pProtoFieldDesc->wsFieldName);
            break;              // 提前跳出
        }

        finfos_cnt = g_ptr_array_len(finfos);
        if (finfos_cnt == 0)
        {
            break;
        }

        const char *strSep = "";
        int         i      = 0;
        do
        {
            finfo = (field_info *)g_ptr_array_index(finfos, i);

            if (ProtoFieldDesc_GetType(pProtoFieldDesc) == eMT_direct)
            {  // 可直接映射
                strFinalFieldValue = ety_ws_get_node_field_value(finfo, edt);
            }
            else if (ProtoFieldDesc_GetType(pProtoFieldDesc) == eMT_transform
                     && pProtoFieldDesc->funTransfrom)
            {  // 需要从 finfo 进行转换
                strFinalFieldValue = pProtoFieldDesc->funTransfrom(edt, finfo);
                strFinalFieldValue = !strFinalFieldValue.empty() ? strFinalFieldValue : pProtoFieldDesc->defaultFieldValue;
            }

            (strRes += strSep) += strFinalFieldValue;
          // 在有超过 1 个 item 时需要进行分隔，变换分隔符
        } while((++i < finfos_cnt) && (strSep = ","));

        strFinalFieldValue = strRes;
        pWriter->writeRecordField(pProtoFieldDesc->etyFieldName, strFinalFieldValue);

        break;
    }
    }
}

bool ProtoFieldExtractorBase::ExtractPlainFields(epan_dissect_t *edt, RecordWriter *pWriter)
{
    ProtoFieldDesc *pFieldDesc = NULL;
    int             field_cnt  = GetFieldsCount();

    for (int i = 0; i < field_cnt; i++)
    {
        pFieldDesc = this->GetProtoFieldDescByIndex(i);

        // generic
        PktPostProc_field_generic(edt, pFieldDesc, pWriter);

        // is it a last plain field?
        if ((pFieldDesc->eMappedType & eMT_lastplain) == eMT_lastplain)
        {
            nFieldIndexToExtract = i + 1;
            break;
        }
    }

    return true;
}

bool ProtoFieldExtractorBase::ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter)
{
    return true;
}

int ProtoFieldExtractorBase::GetDebugFieldCount()
{
    return 2;
}

bool ProtoFieldExtractorBase::ShouldExtractThisFrame(epan_dissect_t *edit)
{
/*  
    if (strstr(CFG->GetValueOf<CSTR>("banned proto"),this->strProtoName_.c_str()) != nullptr)
    {
        return false;
    }
//*/
    return true;

}

bool ProtoFieldExtractorBase::CanExtractFromThisProtocol(const std::string &strProtoName)
{
    auto slashIter = strProtoName.find('/');
    if (std::string::npos != slashIter)
    {
        return strProtoName.compare(0, slashIter, strProtoName_) == 0;
    }

    // 只留下主协议名，eg: "SIP/SDP" -> "SIP"
    
    return strProtoName_ == strProtoName;
}

IProtoFieldExtractor* ProtoFieldExtractorBase::FindProtoFieldExtractorOf(const char *protoName)
{
    IProtoFieldExtractor *pExtractor = ProtoFieldExtractor::GetProtoFieldExtractorOf(protoName);
    
    if (NULL == pExtractor)
    {
        pExtractor = TunnelProtoFieldExtractor::GetProtoFieldExtractorOf(protoName);
    }

    if (NULL == pExtractor)
    {
        pExtractor = SubFormatFieldExtractor::GetProtoFieldExtractorOf(protoName);
    }

    return pExtractor;
}

int ProtoFieldExtractor::SetupDebugFieldDesc()
{
    ProtoFieldExtractor::ForeachFieldExtractor([](const std::string &strProtoName, ProtoFieldExtractor *protoFieldExt)
     {
         protoFieldExt->AddDebugFieldsDesc();
     }
    );
}

int ProtoFieldExtractor::RegisterFieldExtractors()
{
    // HTTP_ENABLE

    SetCheckProtoEnableFun([](const char *protoName)
    {
        return strstr(CFG->GetValueOf<CSTR>("unused_proto"), protoName) == nullptr;
    });

    static ProtoFieldExtractorHttp          s_pfm_http;
    static ProtoFieldExtractorDns           s_pfe_dns;
    static ProtoFieldExtractorFtp           s_pfm_ftp;
    static ProtoFieldExtractorSmtp          s_pfm_smtp;
    static ProtoFieldExtractorPop           s_pfm_pop;
    static ProtoFieldExtractorImap          s_pfm_imap;
    static ProtoFieldExtractorEsp           s_pfm_esp;
    static ProtoFieldExtractorRtp           s_pfm_rtp;
    static ProtoFieldExtractorRadius        s_pfm_radius;
    static ProtoFieldExtractorSip           s_pfm_sip;
    static ProtoFieldExtractorL2tp          s_pfm_l2tp;
    static ProtoFieldExtractorPptp          s_pfm_Pptp;
    static ProtoFieldExtractorWxf           s_pfm_wxf;
    static ProtoFieldExtractorDhcp          s_pfm_dhcp;
    static ProtoFieldExtractorTelnet        s_pfm_telnet;
    static ProtoFieldExtractorBgp           s_pfm_bgp;
    static ProtoFieldExtractorSSH           s_pfm_ssh;
    static ProtoFieldExtractorMysql         s_pfm_mysql;
    static ProtoFieldExtractorTds           s_pfm_tds;
    static ProtoFieldExtractorDtls          s_pfm_dtls;
    static ProtoFieldExtractorRip           s_pfm_rip;
    static ProtoFieldExtractorSnmp          s_pfm_snmp;
    static ProtoFieldExtractorMegaco        s_pfm_megaco;
    static ProtoFieldExtractorRdp           s_pfm_rdp;
    static ProtoFieldExtractorDiameter      s_pfm_diameter;
    static ProtoFieldExtractorEigrp         s_pfm_eigrp;
    static ProtoFieldExtractorOspf          s_pfm_ospf;
    static ProtoFieldExtractorClassicstun   s_pfm_classicstun;
    static ProtoFieldExtractorStun          s_pfm_stun;
    static ProtoFieldExtractorAh            s_pfm_ah;
    static ProtoFieldExtractorMgcp          s_pfm_mgcp;
    static ProtoFieldExtractorLdap          s_pfm_ldap;
    static ProtoFieldExtractorGre           s_pfm_gre;
    static ProtoFieldExtractorGtp_control   s_pfm_gtp;
    static ProtoFieldExtractorIsakmp        s_pfm_isakmp;
    static ProtoFieldExtractorSsl_n         s_pfm_ssl_n;
    static ProtoFieldExtractorTftp          s_pfm_tftp;
    static ProtoFieldExtractorIax           s_pfm_iax;
    static ProtoFieldExtractorSdp_l5        s_pfm_sdpl5;
    static ProtoFieldExtractorWxa           s_pfm_wxa;
    static ProtoFieldExtractorSMS           s_pfm_sms;

}

int ProtoFieldExtractorBase::SetCheckProtoEnableFun(std::function<bool(const char *)> fun)
{
    cs_funCheckProtoEnable = fun;
}
