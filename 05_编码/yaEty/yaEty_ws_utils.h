/****************************************************************************************
 * 文 件 名 : yaEty.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-05-12
* 编    码 : root      '2018-05-12
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _YAETY_WS_UTILS_H_
#define _YAETY_WS_UTILS_H_

#include "config.h"

#include <wsutil/filesystem.h>
#include <epan/tap.h>
#include <epan/epan_dissect.h>
#include <epan/column.h>
#include <epan/epan.h>                  // for epan_dissect_t
#include <capchild/capture_session.h>   // for capture_session
#include "cfile.h"                      // for capture_file
#include "file.h"                       // for cf_status_t

#include <string>

class wsEnvionment
{
public:
    wsEnvionment(char *appName,          int (*function_addr)(int, char **),
                 const char *pszOptPref, hf_ref_type hfRefType = HF_REF_TYPE_DIRECT);
    ~wsEnvionment();

private:
    void setWsPreference(const char *pszDissectOptions);
};

#include "c_lang_linkage_start.h"

/* proto fields */
const char *get_protocol_name_from_proto_tree(proto_tree *pProtoTree);

std::string get_main_protocol_name_from_proto_tree(proto_tree *pProtoTree);

std::string ety_ws_get_node_field_value(field_info* fi, epan_dissect_t* edt);

field_info *get_nth_field_info_from_interesting_fields(epan_dissect *edt, const char *wsFieldName, int n);

field_info *get_first_field_info_from_interesting_fields(epan_dissect *edt, const char *wsFieldName);

std::string get_first_field_value_from_interesting_fields(epan_dissect *edt, const char *wsFieldName);
std::string get_n_field_value_from_interesting_fields(epan_dissect *edt, const char *wsFieldName,int n);
int get_nth_field_info_from_interesting_fields_count(epan_dissect *edt, const char *wsFieldName);

uint32_t fvalue_get_ip_host_addr(fvalue_t *fv);

char *ws_bytes_to_str(wmem_allocator_t *scope, const guint8 *bd, int bd_len);

char  *ws_tvb_bytes_to_str(wmem_allocator_t *allocator, tvbuff_t *tvb,
                           const gint offset, const gint len);

/* capture_file */
cf_status_t cf_open(capture_file *cf, const char *fname, unsigned int type, gboolean is_tempfile, int *err);

void cf_close(capture_file *cf);

void capture_session_init(capture_session *cap_session, capture_file *cf);

void reset_epan_mem(capture_file *cf,epan_dissect_t *edt, gboolean tree, gboolean visual);

#include "c_lang_linkage_end.h"
#endif /* _YAETY_WS_UTILS_H_ */
