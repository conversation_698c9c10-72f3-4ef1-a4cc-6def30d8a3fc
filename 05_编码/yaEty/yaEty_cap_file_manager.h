#pragma once

/****************************************************************************************
* 文 件 名 : yaEty_cap_file_manager.h
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx    '2018-11-07
* 编    码 : zhangsx    '2018-11-07
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_config.h"
#include "yaEty_utils.h"
#include <stdio.h>

#include <vector>
#include <deque>
#include <unordered_set> 
#include <unordered_map> 
#include <iostream>
#include <fstream>
#include <string>

#include "rapidjson/rapidjson.h"
#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/reader.h"
#include "rapidjson/stringbuffer.h"

#if _WINDOWS
#include <direct.h>
#endif

//class CapFileNode
//{
//public:
//    CapFileNode();
//
//    bool isProcessed(std::string);                                                                      //查询文件是否经过处理
// // void addProcessed(std::string);                                                                     //添加到目录
//
//public:
//    bool isLeaf;                                                                                        //是文件或空目录则为真，否则为假
//    bool isDir;                                                                                         //是目录则为真，否则为假
//    std::unordered_map< std::string, std::unordered_set< CapFileNode * > >process_record;
//};

class CapFileTree
{
public:
    CapFileTree();
    int loadRecord(void);
    int saveRecord(void);
    void setPath(std::string);
    void setPath(const char *);
    void add_file(std::string, std::string);
    void add_file(std::string);
    int size();
    void clear();
    int select_file(std::string, std::string);
    int select_file(std::string);
    std::string getPath(void);


private:
    
    std::string getCanonicalPath(const char *);

    std::string getFileDirFromPath(const char *);

    std::string getFileNameFromPath(const char *);
    //std::unordered_map< std::string, std::unordered_set< CapFileNode * > >process_record;
    std::unordered_map< std::string, std::unordered_set< std::string > >process_record;

    std::string record_filepath;
    
};






