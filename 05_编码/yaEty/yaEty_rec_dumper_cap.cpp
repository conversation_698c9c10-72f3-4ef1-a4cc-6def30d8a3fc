/****************************************************************************************
 * 文 件 名 : yaEty_rec_dumper_cap.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-08-24
* 编    码 : root      '2018-08-24
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_rec_dumper_cap.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_config.h"
#include "yaEty_child_process.h"

#include <algorithm>

// constants
static const int   CAP_RECORD_CNT_PERFILE = 5000;
static const int   CAP_OUT_FILES_CNT      = 10;
static const char *DUMP_WRITING_SUFFIX    = ".writing";

// static objects
int                                       CapRecordDumper::cs_lPktsPerFile     = CAP_RECORD_CNT_PERFILE;
int                                       CapRecordDumper::cs_lClassifyToNFile = CAP_OUT_FILES_CNT;
std::string                               CapRecordDumper::cs_strDumpFileDir;
std::map<std::string, CapRecordDumper *>  CapRecordDumper::cs_mapDumper;
std::map<std::string, std::string>        CapRecordDumper::cs_enabledProtoDumper;

// implementations
CapRecordDumper::CapRecordDumper(const std::string &strProtoName,
                                 const std::string &strClassifyField  /* = "" */)
    : strProtoName_(strProtoName)
    , strClassifyField_(strClassifyField)
    , vecDumpers_(CapRecordDumper::cs_lClassifyToNFile, DUMP_CONTEXT_ZERO)
    , dump_err_(0)
    , dump_err_info_(NULL)
{
    registerDumper(strProtoName_, this);

    // 更新为配置中指明的 classify field
    if (shouldDumpPktOfProto(strProtoName_))
    {
        strClassifyField_ = cs_enabledProtoDumper[strProtoName_];
    }
}

CapRecordDumper::~CapRecordDumper()
{
    destroyDumpers();
    unregisterDumper(strProtoName_);
}

void CapRecordDumper::destroyDumpers()
{
    for (auto & dumperCtx : vecDumpers_)
    {
        destroyDumper(&dumperCtx);
    }
}
bool CapRecordDumper::generateDumpFileName(DumpContext *pDumpCtx, int index)
{
    // done:    20180226173528_11233_HTTP_02.2tbl.pcap
    // writing: 20180226173528_11233_HTTP_02.2tbl.pcap.writing
    char *pFileName = pDumpCtx->fileNameBuf;
    time_t unixTime = time(0);
    tm     tm       = *localtime((time_t *)&unixTime);

    // 补全目录
    pFileName += sprintf(pFileName, "%s/", cs_strDumpFileDir.c_str());

    // date string
    pFileName += strftime(pFileName, sizeof pDumpCtx->fileNameBuf, "%Y%m%d%H%M%S", &tm);

    // etc
    pFileName += sprintf(pFileName, "_%05d_%s_%03d_%02d%s",
                         hundredThousandthSecond(),
                         strProtoName_.c_str(),
                         index,
                         getThreadId(),
                         getDumpFileSuffix().c_str());

    // writing file name
    sprintf(pDumpCtx->fileNameBufWriting, "%s%s", pDumpCtx->fileNameBuf, DUMP_WRITING_SUFFIX);

    return true;
}

bool CapRecordDumper::dump(const wtap_rec *pRec, const uint8_t *pPktBuf, const char *pClassifyFieldValue)
{
    bool         bSts        = false;
    DumpContext *pDumperCtx  = getDumpContextOf(pClassifyFieldValue);
    if (nullptr == pDumperCtx)
    {
        return false;
    }
    int          dumperIndex = pDumperCtx->index;

    bSts = wtap_dump(pDumperCtx->pDumper, pRec, pPktBuf, &dump_err_, &dump_err_info_);
    if (bSts == false)
    {
        return false;
    }

    // 累计 frame cnt
    pDumperCtx->dumpedFrameCnt++;

    // 每写入 100 个 pkt 进行一下 flush
    if (pDumperCtx->dumpedFrameCnt % 100 == 0)
    {
        wtap_dump_flush(pDumperCtx->pDumper);
    }

    // 是否达到上限
    if (pDumperCtx->dumpedFrameCnt >= cs_lPktsPerFile)
    {   // 销毁该 dumper, 重新创建一个新 dumper
        destroyDumper(pDumperCtx);
        createDumper(dumperIndex);
    }

    return true;
}

std::string CapRecordDumper::getDumpFileSuffix()
{
    return CFG->GetValueOf<CSTR>("filename_not_dump_suffix");
}

// 简单地将字符串的所有字符给“异或”起来
static inline
int dumpHash(const char *pszFieldValue)
{
    const char *pChar = pszFieldValue;
    int         res   = 0;

    while (pChar && *pChar)
    {
        res ^= *pChar++;
    }

    // printf("dumpHash:%s -> %d\n", pszFieldValue, res);

    return res;
}

DumpContext * CapRecordDumper::getDumpContextOf(const char *pClassifyFieldValue)
{
    int         hashCode      = dumpHash(pClassifyFieldValue);
    int         dumperIndex   = hashCode % cs_lClassifyToNFile;

    if (NULL != vecDumpers_[dumperIndex].pDumper)
    {
        return &vecDumpers_[dumperIndex];
    }

    return createDumper(dumperIndex);
}

DumpContext * CapRecordDumper::createDumper(int index)
{
    DumpContext *pDumperCtx = &vecDumpers_[index];
    wtap_dumper *pDumper    = NULL;

    // 生成文件名
    generateDumpFileName(pDumperCtx, index);

    // 创建 wtap_dump 对象
    pDumper = wtap_dump_open(pDumperCtx->fileNameBufWriting,
                              WTAP_FILE_TYPE_SUBTYPE_PCAP, 		// WTAP_FILE_TYPE_SUBTYPE_PCAP
                              WTAP_ENCAP_ETHERNET,				// WTAP_ENCAP_ETHERNET
                              DUMP_SNAPSHOT_LEN,        		// 8192
                              FALSE,                            // compressed
                              &dump_err_);

    if (NULL == pDumper)
    {
        return NULL;
    }

    pDumperCtx->dumpedFrameCnt = 0;
    pDumperCtx->index          = index;
    pDumperCtx->pDumper        = pDumper;
    return pDumperCtx;
}

bool CapRecordDumper::destroyDumper(DumpContext *pDumpCtx)
{
    if (NULL == pDumpCtx
        || NULL == pDumpCtx->pDumper)
    {
        return false;
    }

#if 0
    printf("destroy dumper proto:%s index:%d dumped pkt:%d int proc %d\n",
           strProtoName_.c_str(),
           pDumpCtx->index,
           pDumpCtx->dumpedFrameCnt,
           getThreadId());
#endif

    // close
    wtap_dump_close(pDumpCtx->pDumper, &dump_err_);
    pDumpCtx->pDumper        = NULL;
    pDumpCtx->dumpedFrameCnt = 0;

    // rename
    rename(pDumpCtx->fileNameBufWriting, pDumpCtx->fileNameBuf);
    return true;
}

int CapRecordDumper::registerDumper(const std::string &strProtoName, CapRecordDumper *pDumper)
{
    cs_mapDumper.emplace(strProtoName, pDumper);
    return 0;
}

int CapRecordDumper::unregisterDumper(const std::string &strProtoName)
{
    cs_mapDumper.erase(strProtoName);
    return 0;
}

CapRecordDumper  *CapRecordDumper::getDumperOf(const std::string &strProtoName)
{
    if (cs_mapDumper.end() == cs_mapDumper.find(strProtoName))
    {
        return NULL;
    }

    return cs_mapDumper[strProtoName];
}

bool CapRecordDumper::shouldDumpPktOfProto(const std::string &strProto)
{
    return cs_enabledProtoDumper.find(strProto) != cs_enabledProtoDumper.end();
}

int CapRecordDumper::getThreadId()
{
    if (CFG->GetValueOf<int>("child_proc_num") == 0)
    {   // 未开启子进程时，
        return 42;
    }

    return CPKPER->GetChildProcIndex();
}

 void CapRecordDumper::revertAllDumpers()
 {
     for (auto &item : cs_mapDumper)
     {
         item.second->destroyDumpers();
     }
 }

void CapRecordDumper::configDumpers()
{
    // config cs_strDumpFileDir
    cs_strDumpFileDir = CFG->GetValueOf<CSTR>("dump_to_dir");

    strReplace(cs_strDumpFileDir, "<in>", CFG->GetInputDirList()[0]);
//  strReplace(cs_strDumpFileDir, "<out>", CFG->GetTblsDir());
    strReplace(cs_strDumpFileDir, "<out>", CFG->GetDumpDir());

    // config cs_lPktsPerFile and cs_lClassifyToNFile
    cs_lPktsPerFile          = CFG->GetValueOf<int>("dump_pkts_per_file");
    cs_lClassifyToNFile      = CFG->GetValueOf<int>("dump_classify_to_n_file");

    // config enabled protos
    const char *    pcszEnabledDumpProto = CFG->GetValueOf<CSTR>("need_to_dump_proto_list");
    alloced_uptr    pszOpts(strdup(pcszEnabledDumpProto), free);
    char            *pTokenOrigin = pszOpts.get();
    char            *pszProto     = NULL;
    char            *pszField     = NULL;

    do
    {
        pszProto = strtok(pTokenOrigin, ":");
        pszField = strtok(NULL,         ",");

        if (NULL == pszProto
            || NULL == pszField)
        {
           break;
        }

        cs_enabledProtoDumper.emplace(pszProto, pszField);
    } while (!(pTokenOrigin = NULL));
}

void CapRecordDumper::Register()
{
    // 配置 dump 输出目录，需要 dump 的协议
    configDumpers();

    static CapRecordDumper      ms_httpWriter     ("HTTP");
    static CapRecordDumper      ms_dnsWriter      ("DNS");
    static CapRecordDumper      ms_ftpWriter      ("FTP");
    static CapRecordDumper      ms_smtpWriter     ("IMF");
    static CapRecordDumper      ms_popWriter      ("POP");
    static CapRecordDumper      ms_imapWriter     ("IMAP");
    static CapRecordDumper      ms_radiusWriter   ("RADIUS");
    static CapRecordDumper      ms_sipWriter      ("SIP");
    static CapRecordDumper      ms_rtpWriter      ("RTP");
    static CapRecordDumper      ms_espWriter      ("ESP");
    static CapRecordDumper      ms_l2tpWriter     ("L2TP");
    static CapRecordDumper      ms_pptpWriter     ("PPTP");
    static CapRecordDumper      ms_wxfWriter      ("WXF");
    static CapRecordDumper      ms_wxaWriter      ("WXA");
}
