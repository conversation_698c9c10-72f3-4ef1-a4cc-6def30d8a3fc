#pragma once

#include <set>

#include <ui/rtp_stream.h>
#include "yaEty_field_extractor.h"
#include "config.h"
#include "epan/epan_dissect.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor_def.h"
#include "yaEty_content_reassembly_share_mem.h"

class ProtoFieldExtractorRtpStream:public ProtoFieldExtractor
{
public:
    ProtoFieldExtractorRtpStream();
    bool ExtractSpecialFields(epan_dissect_t * edt, RecordWriter * pWriter);
    int GetDebugFieldCount() override
    {
        return 1;
    }

};
