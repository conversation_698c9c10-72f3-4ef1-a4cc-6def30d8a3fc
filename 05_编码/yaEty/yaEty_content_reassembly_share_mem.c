/****************************************************************************************
 * 文 件 名 : yaEty_content_reassembly_share_mem.c
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : liugh      '2018-05-21
* 编    码 : liugh      '2018-05-21
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include<unistd.h>
#include<stdlib.h>
#include<stdio.h>
#include<errno.h>
#include<fcntl.h>
#include<string.h>
#include<sys/file.h>
#include<sys/wait.h>
#include<sys/mman.h>
#include<sys/ipc.h>
#include<sys/shm.h>
#include<sys/types.h>

#define bool char
#include "yaEty_content_reassembly_share_mem.h"


static unsigned int crc_table[256];
http_picture *g_share_http;
mail_data  *g_share_mail;
ftp_data  *g_share_ftp;
voip_data    *g_share_voip;

share_shmid  g_shmid;


#if 0
bool DispAttachShareMem(void)
{
	int shmid=0;
	g_share_http=NULL;
	g_share_mail=NULL;
	
	shmid=shmget( COMM_PICTURE_MEM_ID, 0, 0 );
	if(shmid<0)
	{
		printf("##shmget http attach share memory failed!\n");
		goto error;
	}
	g_share_http  = ( http_picture *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_http)//#1
	{
		printf("##shmat http attach share memory failed!\n");
		goto error;
	}

	shmid=shmget( COMM_MAIL_MEM_ID, 0, 0 );
	if(shmid<0)
	{
		printf("##shmget email attach share memory failed!\n");
		goto error;
	}
	g_share_mail  = ( mail_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_mail)//#2
	{
		printf("##shmat  email attach share memory failed!\n");
		goto error;
	}

	shmid=shmget( COMM_FTP_MEM_ID, 0, 0 );
	if(shmid<0)
	{
		printf("##shmget ftp attach share memory failed!\n");
		goto error;
	}
	g_share_ftp  = ( ftp_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_ftp)//#3
	{
		printf("##shmat  ftp attach share memory failed!\n");
		goto error;
	}


	shmid=shmget( COMM_VOIP_MEM_ID, 0, 0 );
	if(shmid<0)
	{
		printf("##shmget voip attach share memory failed!\n");
		goto error;
	}
	g_share_voip  = ( voip_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_voip)//#4
	{
		printf("##shmat  voip attach share memory failed!\n");
		goto error;
	}

	return 1;
error:
	return 0;
}


bool DispInitShareMem(void)
{
	int shmid=0;
	
	shmid=shmget( COMM_PICTURE_MEM_ID, HTTP_PICTURE_LIST_SIZE*HASH_SIZE*2, ( IPC_CREAT|0666 ) );
	if(shmid<0){
		printf("##shmget create share http memory failed!\n");
		goto error;
	}
	g_share_http = (http_picture *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_http)//#1
	{
		printf("##shmat attach share http memory failed!\n");
		goto error;
	}

    shmid=shmget( COMM_MAIL_MEM_ID, MAIL_DATA_LIST_SIZE*HASH_SIZE*2, ( IPC_CREAT|0666 ) );
	if(shmid<0){
		printf("##shmget create emails share memory failed!\n");
		goto error;
	}
	g_share_mail = (mail_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_mail)//#2
	{
		printf("##shmat attach share email memory failed!\n");
		goto error;
	}

	shmid=shmget( COMM_FTP_MEM_ID, FTP_DATA_LIST_SIZE*HASH_SIZE*2, ( IPC_CREAT|0666 ) );
	if(shmid<0){
		printf("##shmget create ftp share memory failed!\n");
		goto error;
	}
	g_share_ftp = (ftp_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_ftp)//#3
	{
		printf("##shmat attach share ftp memory failed!\n");
		goto error;
	}


	shmid=shmget( COMM_VOIP_MEM_ID, VOIP_DATA_LIST_SIZE*HASH_SIZE*2, ( IPC_CREAT|0666 ) );
	if(shmid<0){
		printf("##shmget create voip share memory failed!\n");
		goto error;
	}
	g_share_voip = (voip_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_voip)//#3
	{
		printf("##shmat attach share voip memory failed!\n");
		goto error;
	}
	
    memset((char *)g_share_http,0,HTTP_PICTURE_LIST_SIZE*HASH_SIZE*2);
	memset((char *)g_share_mail,0,MAIL_DATA_LIST_SIZE*HASH_SIZE*2);
	memset((char *)g_share_ftp,0,FTP_DATA_LIST_SIZE*HASH_SIZE*2);
	memset((char *)g_share_voip,0,VOIP_DATA_LIST_SIZE*HASH_SIZE*2);
    
                                                                                                   
	CreateCrcTable(); 

	return 1;
error:
	return 0;
}
#else

bool DispInitShareMem(unsigned int pid)
{
	int shmid=0;
	key_t http_key=0;
	key_t mail_key=0;
	key_t ftp_key=0;
	key_t voip_key=0;

	http_key=pid;
	mail_key=http_key+MAX_FORK_NUMBER ;
	ftp_key=mail_key+MAX_FORK_NUMBER ;
	voip_key=ftp_key+MAX_FORK_NUMBER ;
	
	shmid=shmget( http_key, HTTP_PICTURE_LIST_SIZE*HASH_SIZE*2, ( IPC_CREAT|0666 ) );
	if(shmid<0){
		printf("##shmget create share http memory failed!\n");
		goto error;
	}
	g_share_http = (http_picture *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_http)//#1
	{
		printf("##shmat attach share http memory failed!\n");
		goto error;
	}

    shmid=shmget( mail_key, MAIL_DATA_LIST_SIZE*HASH_SIZE*2, ( IPC_CREAT|0666 ) );
	if(shmid<0){
		printf("##shmget create emails share memory failed!\n");
		goto error;
	}
	g_share_mail = (mail_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_mail)//#2
	{
		printf("##shmat attach share email memory failed!\n");
		goto error;
	}

	shmid=shmget( ftp_key, FTP_DATA_LIST_SIZE*HASH_SIZE*2, ( IPC_CREAT|0666 ) );
	if(shmid<0){
		printf("##shmget create ftp share memory failed!\n");
		goto error;
	}
	g_share_ftp = (ftp_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_ftp)//#3
	{
		printf("##shmat attach share ftp memory failed!\n");
		goto error;
	}


	shmid=shmget( voip_key, VOIP_DATA_LIST_SIZE*HASH_SIZE*2, ( IPC_CREAT|0666 ) );
	if(shmid<0){
		printf("##shmget create voip share memory failed!\n");
		goto error;
	}
	g_share_voip = (voip_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_voip)//#3
	{
		printf("##shmat attach share voip memory failed!\n");
		goto error;
	}
	
    memset((char *)g_share_http,0,HTTP_PICTURE_LIST_SIZE*HASH_SIZE*2);
	memset((char *)g_share_mail,0,MAIL_DATA_LIST_SIZE*HASH_SIZE*2);
	memset((char *)g_share_ftp,0,FTP_DATA_LIST_SIZE*HASH_SIZE*2);
	memset((char *)g_share_voip,0,VOIP_DATA_LIST_SIZE*HASH_SIZE*2);
    
                                                                                                   
	CreateCrcTable(); 

	return 1;
error:
	return 0;
}


bool DispAttachShareMem(unsigned int pid)
{
	int shmid=0;
	g_share_http=NULL;
	g_share_mail=NULL;
	g_share_ftp=NULL;
	g_share_voip=NULL;

	key_t http_key=0;
	key_t mail_key=0;
	key_t ftp_key=0;
	key_t voip_key=0;

	http_key=pid;
	mail_key=http_key+MAX_FORK_NUMBER ;
	ftp_key=mail_key+MAX_FORK_NUMBER ;
	voip_key=ftp_key+MAX_FORK_NUMBER ;


	
	shmid=shmget( http_key, 0, 0 );
	if(shmid<0)
	{
		printf("##shmget http attach share memory failed!\n");
		goto error;
	}
	g_shmid.http_shmid=shmid;
	g_share_http  = ( http_picture *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_http)//#1
	{
		printf("##shmat http attach share memory failed!\n");
		goto error;
	}
	
	shmid=shmget( mail_key, 0, 0 );
	if(shmid<0)
	{
		printf("##shmget email attach share memory failed!\n");
		goto error;
	}
	g_shmid.mail_shmid=shmid;
	g_share_mail  = ( mail_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_mail)//#2
	{
		printf("##shmat  email attach share memory failed!\n");
		goto error;
	}

	shmid=shmget( ftp_key, 0, 0 );
	if(shmid<0)
	{
		printf("##shmget ftp attach share memory failed!\n");
		goto error;
	}
	g_shmid.ftp_shmid=shmid;
	g_share_ftp  = ( ftp_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_ftp)//#3
	{
		printf("##shmat  ftp attach share memory failed!\n");
		goto error;
	}


	shmid=shmget( voip_key, 0, 0 );
	if(shmid<0)
	{
		printf("##shmget voip attach share memory failed!\n");
		goto error;
	}
	g_shmid.voip_shmid=shmid;
	g_share_voip  = ( voip_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_voip)//#4
	{
		printf("##shmat  voip attach share memory failed!\n");
		goto error;
	}


	return 1;
error:
	return 0;
}

#endif


int  DispDelShareMem(unsigned int pid)
{
    key_t http_key=0;
	key_t mail_key=0;
	key_t ftp_key=0;
	key_t voip_key=0;
	int   shmid=0;
	char  cmd[512]={0};

    printf("childred pid=%d\n",pid);
	http_key=pid;
	mail_key=http_key+MAX_FORK_NUMBER ;
	ftp_key=mail_key+MAX_FORK_NUMBER ;
	voip_key=ftp_key+MAX_FORK_NUMBER ;

#if 1
	shmid=shmget( http_key, 0, 0 );
	if(shmid>=0){
		snprintf(cmd,512,"ipcrm -M 0x%x",http_key);
		system(cmd);
	}

	memset(cmd,0,512);
	shmid=shmget( mail_key, 0, 0 );
	if(shmid>=0){
		snprintf(cmd,512,"ipcrm -M 0x%x",mail_key);
		system(cmd);
	}

	memset(cmd,0,512);
	shmid=shmget( ftp_key, 0, 0 );
	if(shmid>=0){
		snprintf(cmd,512,"ipcrm -M 0x%x",ftp_key);
		system(cmd);
	}

	memset(cmd,0,512);
	shmid=shmget( voip_key, 0, 0 );
	if(shmid>=0){
		snprintf(cmd,512,"ipcrm -M 0x%x",voip_key);
		system(cmd);
	}

#else

	memset(cmd,0,512);
	snprintf(cmd,512,"ipcrm -M 0x%x",http_key);
	system(cmd);

	memset(cmd,0,512);
	snprintf(cmd,512,"ipcrm -M 0x%x",mail_key);
	system(cmd);


	memset(cmd,0,512);
	snprintf(cmd,512,"ipcrm -M 0x%x",ftp_key);
	system(cmd);
	

	memset(cmd,0,512);
	snprintf(cmd,512,"ipcrm -M 0x%x",voip_key);
	system(cmd);
#endif

	return 0;
}


/*
*if hash localtion flag is 0, then insert data 
*if hash localtion flag is 1, and 5tuple is same then insert picture path to sub
*if hash localtion flag is 1, and 5tuple is different then find conflict memory
*/
unsigned int DispGetFreeMemDB(data_info info, unsigned int hashval, int type, int *path_flag)
{
	unsigned int mem_index=0;
	int i;
	switch(type){
	case MEM_TYPE_HTTP_PICTURE:
		if(g_share_http[hashval].flag==0){
			mem_index=hashval;
			*path_flag=0;
		}else if(g_share_http[hashval].flag==1){
			if(g_share_http[hashval].dst_ip==info.dst_ip && g_share_http[hashval].dst_port==info.dst_port
			&& g_share_http[hashval].src_ip==info.src_ip && g_share_http[hashval].src_port==info.src_port){
				mem_index=hashval;
				*path_flag=1;
			}else{
				for(i=HASH_SIZE;i<HASH_SIZE*2;i++){
					if(g_share_http[i].flag==0){
						mem_index=i;
					}
				}
			}
		}
		break;
	case MEM_TYPE_EMAIL_DATA:
		if(g_share_mail[hashval].flag==0){
			mem_index=hashval;
			*path_flag=0;
		}else if(g_share_mail[hashval].flag==1){
			if(g_share_mail[hashval].dst_ip==info.dst_ip && g_share_mail[hashval].dst_port==info.dst_port
			&& g_share_mail[hashval].src_ip==info.src_ip && g_share_mail[hashval].src_port==info.src_port){
				mem_index=hashval;
				*path_flag=1;
			}else{
				for(i=HASH_SIZE;i<HASH_SIZE*2;i++){
					if(g_share_mail[i].flag==0){
						mem_index=i;
					}
				}
			}
		}
		break;
	case MEM_TYPE_FTP_DATA:
		if(g_share_ftp[hashval].flag==0){
			mem_index=hashval;
			*path_flag=0;
		}else if(g_share_ftp[hashval].flag==1){
			if(g_share_ftp[hashval].dst_ip==info.dst_ip && g_share_ftp[hashval].dst_port==info.dst_port
			&& g_share_ftp[hashval].src_ip==info.src_ip && g_share_ftp[hashval].src_port==info.src_port){
				mem_index=hashval;
				*path_flag=1;
			}else{
				for(i=HASH_SIZE;i<HASH_SIZE*2;i++){
					if(g_share_ftp[i].flag==0){
						mem_index=i;
					}
				}
			}
		}
		break;
	case MEM_TYPE_VOIP_DATA:
		if(g_share_voip[hashval].flag==0){
			mem_index=hashval;
			*path_flag=0;
		}else if(g_share_voip[hashval].flag==1){
			if(g_share_voip[hashval].dst_ip==info.dst_ip && g_share_voip[hashval].dst_port==info.dst_port
			&& g_share_voip[hashval].src_ip==info.src_ip && g_share_voip[hashval].src_port==info.src_port){
				mem_index=hashval;
				*path_flag=1;
			}else{
				for(i=HASH_SIZE;i<HASH_SIZE*2;i++){
					if(g_share_voip[i].flag==0){
						mem_index=i;
					}
				}
			}
		}
		break;
	default:
		mem_index=0;
		break;
	}

	return mem_index;
}



bool DispSetHashdb(data_info info, int type)
{
	http_picture *npq1=NULL;
	mail_data    *npq2=NULL;
	ftp_data     *npq3=NULL;
	unsigned int index=0;
	unsigned int hashval=0;
	unsigned int cur=0;
	int path_flag=0;
	int i;
	
    PktInfo ptf;
	ptf.dst_ip=info.dst_ip;
	ptf.dst_port=info.dst_port;
	ptf.src_ip=info.src_ip;
	ptf.src_port=info.src_port;
	ptf.proto=6;
	
	hashval=Hash(ptf);
    index=DispGetFreeMemDB(info,hashval,type,&path_flag);
	switch(type){
	case MEM_TYPE_HTTP_PICTURE:
		if(index == hashval){
			npq1=&g_share_http[index];
			if(0==path_flag){
				npq1->flag=1;
				npq1->next=0;
				npq1->dst_ip=info.dst_ip;
				npq1->dst_port=info.dst_port;
				npq1->src_ip=info.src_ip;
				npq1->src_port=info.src_port;
				
				if(npq1->pci_number<PICTURE_GATHER_NUMBER){
					npq1->pic_gather[npq1->pci_number].flag=info.type;
					strncpy(npq1->pic_gather[npq1->pci_number++].path,info.path,strlen(info.path));
				}
			}else if(1==path_flag){
				path_flag=0;
				if(npq1->pci_number<PICTURE_GATHER_NUMBER){	
					npq1->pic_gather[npq1->pci_number].flag=info.type;
					strncpy(npq1->pic_gather[npq1->pci_number++].path,info.path,strlen(info.path));
				}		
			}
		}else{
			cur =hashval;
			while(g_share_http[cur].next!=0)
				cur=g_share_http[cur].next;
			g_share_http[cur].next=index;
			npq1= &g_share_http[index];
			npq1->flag=1;
			npq1->next=0;
			npq1->dst_ip=info.dst_ip;
			npq1->dst_port=info.dst_port;
			npq1->src_ip=info.src_ip;
			npq1->src_port=info.src_port;
			if(npq1->pci_number<PICTURE_GATHER_NUMBER){
				npq1->pic_gather[npq1->pci_number].flag=info.type;
				strncpy(npq1->pic_gather[npq1->pci_number++].path,info.path,strlen(info.path));
			}
		}
        break;
	case MEM_TYPE_EMAIL_DATA:
		if(index == hashval){
			npq2=&g_share_mail[index];
			if(0==path_flag){
				npq2->flag=1;
				npq2->next=0;
				npq2->dst_ip=info.dst_ip;
				npq2->dst_port=info.dst_port;
				npq2->src_ip=info.src_ip;
				npq2->src_port=info.src_port;
				
				if(npq2->mail_number<MAIL_GATHER_NUMBER){
					npq2->mail_gather[npq2->mail_number].flag=info.type;
					strncpy(npq2->mail_gather[npq2->mail_number++].path,info.path,strlen(info.path));
				}
			}else if(1==path_flag){
				path_flag=0;
				if(npq2->mail_number<MAIL_GATHER_NUMBER){	
					npq2->mail_gather[npq2->mail_number].flag=info.type;
					strncpy(npq2->mail_gather[npq2->mail_number++].path,info.path,strlen(info.path));
				}		
			}
		}else{
			cur =hashval;
			while(g_share_mail[cur].next!=0)
				cur=g_share_mail[cur].next;
			g_share_mail[cur].next=index;
			npq2= &g_share_mail[index];
			npq2->flag=1;
			npq2->next=0;
			npq2->dst_ip=info.dst_ip;
			npq2->dst_port=info.dst_port;
			npq2->src_ip=info.src_ip;
			npq2->src_port=info.src_port;
			if(npq2->mail_number<MAIL_GATHER_NUMBER){
				npq2->mail_gather[npq2->mail_number].flag=info.type;
				strncpy(npq2->mail_gather[npq2->mail_number++].path,info.path,strlen(info.path));
			}
		}
        break;

	case MEM_TYPE_FTP_DATA:
		if(index == hashval){
			npq3=&g_share_ftp[index];
			if(0==path_flag){
				npq3->flag=1;
				npq3->next=0;
				npq3->dst_ip=info.dst_ip;
				npq3->dst_port=info.dst_port;
				npq3->src_ip=info.src_ip;
				npq3->src_port=info.src_port;
				
				if(npq3->file_number<FTP_GATHER_NUMBER){
					npq3->ftp_gather[npq3->file_number].flag=info.type;
					strncpy(npq3->ftp_gather[npq3->file_number++].path,info.path,strlen(info.path));
				}
			}else if(1==path_flag){
				path_flag=0;
				if(npq3->file_number<FTP_GATHER_NUMBER){	
					npq3->ftp_gather[npq3->file_number].flag=info.type;
					strncpy(npq3->ftp_gather[npq3->file_number++].path,info.path,strlen(info.path));
				}		
			}
		}else{
			cur =hashval;
			while(g_share_ftp[cur].next!=0)
				cur=g_share_ftp[cur].next;
			g_share_ftp[cur].next=index;
			npq3= &g_share_ftp[index];
			npq3->flag=1;
			npq3->next=0;
			npq3->dst_ip=info.dst_ip;
			npq3->dst_port=info.dst_port;
			npq3->src_ip=info.src_ip;
			npq3->src_port=info.src_port;
			if(npq3->file_number<FTP_GATHER_NUMBER){
				npq3->ftp_gather[npq3->file_number].flag=info.type;
				strncpy(npq3->ftp_gather[npq3->file_number++].path,info.path,strlen(info.path));
			}
		}
        break;
	default:
		break;
	return 0;
	}

	return 1;
}

bool DispUdpSetHashdb(data_info info, int type)
{
	voip_data    *npq1=NULL;
	unsigned int index=0;
	unsigned int hashval=0;
	unsigned int cur=0;
	int path_flag=0;
	int i;
	
    PktInfo ptf;
	ptf.dst_ip=info.dst_ip;
	ptf.dst_port=info.dst_port;
	ptf.src_ip=info.src_ip;
	ptf.src_port=info.src_port;
	ptf.proto=17;
	
	hashval=Hash(ptf);
    index=DispGetFreeMemDB(info,hashval,type,&path_flag);
	switch(type){
	case MEM_TYPE_VOIP_DATA :
		if(index == hashval){
			npq1=&g_share_voip[index];
			if(0==path_flag){
				npq1->flag=1;
				npq1->next=0;
				npq1->dst_ip=info.dst_ip;
				npq1->dst_port=info.dst_port;
				npq1->src_ip=info.src_ip;
				npq1->src_port=info.src_port;
				
				if(npq1->voip_number<VOIP_GATHER_NUMBER){
					npq1->voip_gather[npq1->voip_number].flag=info.type;
					strncpy(npq1->voip_gather[npq1->voip_number++].path,info.path,strlen(info.path));
				}
			}else if(1==path_flag){
				path_flag=0;
				if(npq1->voip_number<VOIP_GATHER_NUMBER){	
					npq1->voip_gather[npq1->voip_number].flag=info.type;
					strncpy(npq1->voip_gather[npq1->voip_number++].path,info.path,strlen(info.path));
				}		
			}
		}else{
			cur =hashval;
			while(g_share_voip[cur].next!=0)
				cur=g_share_voip[cur].next;
			g_share_voip[cur].next=index;
			npq1= &g_share_voip[index];
			npq1->flag=1;
			npq1->next=0;
			npq1->dst_ip=info.dst_ip;
			npq1->dst_port=info.dst_port;
			npq1->src_ip=info.src_ip;
			npq1->src_port=info.src_port;
			if(npq1->voip_number<VOIP_GATHER_NUMBER){
				npq1->voip_gather[npq1->voip_number].flag=info.type;
				strncpy(npq1->voip_gather[npq1->voip_number++].path,info.path,strlen(info.path));
			}
		}
        break;
	default:
		break;
	return 0;
	}

	return 1;
}



void *DispGetHashdb(PktInfo ptf, int type)
{
	http_picture *npq1=NULL;
	mail_data    *npq2=NULL;
	ftp_data     *npq3=NULL;
	voip_data    *npq4=NULL;
	switch(type){
	case MEM_TYPE_HTTP_PICTURE:
		if(NULL == g_share_http){
			return NULL;
		}
		npq1=(http_picture *)&g_share_http[Hash(ptf)];
		while(npq1->next!=0){
			if(ptf.dst_ip==npq1->dst_ip && ptf.dst_port==npq1->dst_port &&
			   ptf.src_ip==npq1->src_ip && ptf.src_port==npq1->src_port){
				return npq1;
			}
			npq1=(http_picture *)&g_share_http[npq1->next];
		}
		if(ptf.dst_ip==npq1->dst_ip && ptf.dst_port==npq1->dst_port &&
		   ptf.src_ip==npq1->src_ip && ptf.src_port==npq1->src_port){
			return npq1;
		}
		break;
	case MEM_TYPE_EMAIL_DATA:
		if(NULL == g_share_mail){
			return NULL;
		}
		npq2=(mail_data *)&g_share_mail[Hash(ptf)];
		while(npq2->next!=0){
			if(ptf.dst_ip==npq2->dst_ip && ptf.dst_port==npq2->dst_port &&
			   ptf.src_ip==npq2->src_ip && ptf.src_port==npq2->src_port){
				return npq2;
			}
			npq2=(mail_data *)&g_share_mail[npq2->next];
		}
		if(ptf.dst_ip==npq2->dst_ip && ptf.dst_port==npq2->dst_port &&
		   ptf.src_ip==npq2->src_ip && ptf.src_port==npq2->src_port){
			return npq2;
		}
		break;
	case MEM_TYPE_FTP_DATA:
		if(NULL == g_share_ftp){
			return NULL;
		}
		npq3=(ftp_data *)&g_share_ftp[Hash(ptf)];
		while(npq3->next!=0){
			if(ptf.dst_ip==npq3->dst_ip && ptf.dst_port==npq3->dst_port &&
			   ptf.src_ip==npq3->src_ip && ptf.src_port==npq3->src_port){
				return npq3;
			}
			npq3=(ftp_data *)&g_share_ftp[npq3->next];
		}
		if(ptf.dst_ip==npq3->dst_ip && ptf.dst_port==npq3->dst_port &&
		   ptf.src_ip==npq3->src_ip && ptf.src_port==npq3->src_port){
			return npq3;
		}
		break;
	case MEM_TYPE_VOIP_DATA:
		if(NULL == g_share_voip){
			return NULL;
		}
		npq4=(voip_data *)&g_share_voip[Hash(ptf)];
		while(npq4->next!=0){
			if(ptf.dst_ip==npq4->dst_ip && ptf.dst_port==npq4->dst_port &&
			   ptf.src_ip==npq4->src_ip && ptf.src_port==npq4->src_port){
				return npq4;
			}
			npq4=(voip_data *)&g_share_voip[npq4->next];
		}
		if(ptf.dst_ip==npq4->dst_ip && ptf.dst_port==npq4->dst_port &&
		   ptf.src_ip==npq4->src_ip && ptf.src_port==npq4->src_port){
			return npq4;
		}
		break;
	default:
		break;
	}

	return NULL;
}



unsigned int GetSumPoly(unsigned char data)
{
	unsigned int sum_poly = data;
	int j;
	sum_poly <<= 24;
	for(j = 0; j < 8; j++)
	{
		int hi = sum_poly&0x80000000; 
		sum_poly <<= 1;
		if(hi) sum_poly = sum_poly^POLY;
	}
	return sum_poly;
}

void CreateCrcTable(void)  
{
	int i;
	for(i = 0; i < 256; i++){
		crc_table[i] = GetSumPoly(i&0xFF);
	}
}

unsigned int CRC20Key(unsigned char* data, int len)
{
	int i;
	unsigned int reg = 0xFFFFFFFF;
	for(i = 0; i < len; i++)
	{
		reg = (reg<<8) ^ crc_table[(reg>>24)&0xFF ^ data[i]];
	}
	return (reg&0XFFFFF);
}

int Hash( PktInfo ptf )
{
    char c;
    int i;
    int hval = 0;
	
    hval=CRC20Key((unsigned char *)&ptf,sizeof(PktInfo));

    return (hval % HASH_SIZE);
}



/*just for test*/
int content_reassembly_share_mem_test()
{
	bool rel=0;
	int i,j;
	int count;

#if 0
	http_picture *get_data=NULL;

	CreateCrcTable();

	rel=DispAttachShareMem();
	if(!rel){
		printf("init share mem error !\n");
		exit(-1);
	}
#endif
	
	for(i=0;i<HASH_SIZE*2;i++){
		#if 0
		if(g_share_http[i].flag==1){
			printf("\n===============================\n");
			printf("src_ip:%x\n",g_share_http[i].src_ip);
			printf("dst_ip:%x\n",g_share_http[i].dst_ip);
			printf("src_port:%d\n",g_share_http[i].src_port);
			printf("dst_port:%d\n",g_share_http[i].dst_port);
			for(j=0;j<g_share_http[i].pci_number;j++){
				printf("\ttype:%d, picture:%s\n",g_share_http[i].pic_gather[j].flag,g_share_http[i].pic_gather[j].path);
			}
		}
	    #endif
		if(g_share_ftp[i].flag==1){
			printf("\n================  ftp  ===============\n");
			printf("src_ip:%x\n",g_share_ftp[i].src_ip);
			printf("dst_ip:%x\n",g_share_ftp[i].dst_ip);
			printf("src_port:%d\n",g_share_ftp[i].src_port);
			printf("dst_port:%d\n",g_share_ftp[i].dst_port);
			for(j=0;j<g_share_ftp[i].file_number;j++){
				printf("\tfiles:%s\n",g_share_ftp[i].ftp_gather[j].path);
			}
		}

		
		if(g_share_mail[i].flag==1){
			printf("\n================   email ===============\n");
			printf("src_ip:%x\n",g_share_mail[i].src_ip);
			printf("dst_ip:%x\n",g_share_mail[i].dst_ip);
			printf("src_port:%d\n",g_share_mail[i].src_port);
			printf("dst_port:%d\n",g_share_mail[i].dst_port);
			for(j=0;j<g_share_mail[i].mail_number;j++){
				printf("\temails:%s\n",g_share_mail[i].mail_gather[j].path);
			}
			
		}

	}


#if 0
	PktInfo ptf;
	ptf.src_ip=0xac10141e;
	ptf.dst_ip=0x3ad77623;
	ptf.src_port=63240;
	ptf.dst_port=80;
	ptf.proto=6;

	get_data=(http_picture *)DispGetHashdb(ptf, MEM_TYPE_HTTP_PICTURE);
	if(get_data!=NULL){
		printf("\n\n******************************* search result ***************************************\n");
		printf("[debug lgh] find the infomation\n");
		printf("src_ip:%x\n",get_data->src_ip);
		printf("dst_ip:%x\n",get_data->dst_ip);
		printf("src_port:%d\n",get_data->src_port);
		printf("dst_port:%d\n",get_data->dst_port);
		for(j=0;j<PICTURE_GATHER_NUMBER;j++){
			if(get_data->pic_gather[j].flag==1)
				printf("\tpicture:%s\n",get_data->pic_gather[j].path);
		}
	}
#endif

	return 0;

}

