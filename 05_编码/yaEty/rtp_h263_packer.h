#ifndef RTP_H263_PACKER_H
#define RTP_H263_PACKER_H

#include <vector>
#include <stdint.h>

enum H263NaluType_e {
    EM_H263_no_PSC               = 0,  //  帧首部
    EM_H263_PSC                  = 1,  //  帧中间分片
};

typedef int (*onGotH263Nalu_callback_t)(H263NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata);

// rtp h263 解包器
// 输入为 rtp 负载，输出为 nalu(回调)
class RtpH263Unpacker
{
public:
    RtpH263Unpacker(onGotH263Nalu_callback_t callback, void *userdata);

public:
    int enqueueRtpPayload(uint8_t *rtpPayload, int len);

private:
    onGotH263Nalu_callback_t onGotNalu_func_;
    void *userdata_;
};

#endif /* RTP_H263_UNPACKER_H */
