/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_stun.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-10-29
* 编    码 : zhangsx      '2018-10-29
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include "config.h"
#include "epan/epan_dissect.h"

#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_stun.h"
#include "yaEty_content_reassembly_share_mem.h"

struct choose_for_stun_field
{
public:
    choose_for_stun_field(const char * field_name1, const char * field_name2)
        :_field_name1(field_name1),_field_name2(field_name2)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info *pfinfo1 = get_first_field_info_from_interesting_fields(edt,_field_name1);

        field_info *pfinfo2 = get_first_field_info_from_interesting_fields(edt, _field_name2);
        if (pfinfo1)
        {
            return ety_ws_get_node_field_value(pfinfo1, edt);
        }
        if (pfinfo2)
        {
            return ety_ws_get_node_field_value(pfinfo2, edt);
        }
    }
public:
    const char * _field_name1;
    const char * _field_name2;
};

static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort"                      , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("udp.srcport","udp.dstport")),
    F_D_ITEM("DstPort"                      , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("udp.srcport","udp.dstport")),
    F_D_ITEM("C2S"                          , ""                                , eMT_direct,       "",     NULL),
    F_D_ITEM("Proto"                        , "ip.proto"                        , eMT_direct,       "",     NULL),
    F_D_ITEM("TTL"                          , "ip.ttl"                          , eMT_direct,       "",     NULL),
    F_D_ITEM("Message_type"                 , "stun.type"                       , eMT_direct,       "",     NULL),
    F_D_ITEM("Address_type"                 , "stun.attr.type"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("Source_address"               , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Source_port"                  , "stun.att.port"                   , eMT_direct,       "",     NULL),
    F_D_ITEM("Changed_server"               , "stun.att.change-ip"              , eMT_direct,       "",     NULL),
    F_D_ITEM("Changed_port"                 , "stun.att.change-port"            , eMT_direct,       "",     NULL),
    F_D_ITEM("Response_address"             , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Response_port"                , "stun.att.port"                   , eMT_direct,       "",     NULL),
    F_D_ITEM("Reflected_from"               , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Reflected_port"               , "stun.att.port"                   , eMT_direct,       "",     NULL),
    F_D_ITEM("Mapped_address"               , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Mapped_port"                  , "stun.att.port"                   , eMT_direct,       "",     NULL),
    F_D_ITEM("Alternate_server"             , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Alternate_port"               , "stun.att.port"                   , eMT_direct,       "",     NULL),
    F_D_ITEM("Response_origin_address"      , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Response_origin_port"         , "stun.att.port"                   , eMT_direct,       "",     NULL),
    F_D_ITEM("Other_address"                , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Other_port"                   , "stun.att.port"                   , eMT_direct,       "",     NULL),
    F_D_ITEM("Xor_mapped_address"           , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Xor_mapped_port"              , "stun.att.port-xord"              , eMT_direct,       "",     NULL),
    F_D_ITEM("Xor_peer_address"             , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Xor_peer_port"                , "stun.att.port-xord"              , eMT_direct,       "",     NULL),
    F_D_ITEM("Xor_relayed_address"          , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Xor_relayed_port"             , "stun.att.port-xord"              , eMT_direct,       "",     NULL),
    F_D_ITEM("Xor_response_address"         , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Xor_response_port"            , "stun.att.port-xord"              , eMT_direct,       "",     NULL),
    F_D_ITEM("Xor_reflected_from"           , ""                                , eMT_fromEdt,      "",     choose_for_stun_field("stun.att.ipv4","stun.att.ipv6")),
    F_D_ITEM("Xor_reflected_port"           , "stun.att.port-xord"              , eMT_direct,       "",     NULL),
    F_D_ITEM("Username"                     , "stun.att.username"               , eMT_direct,       "",     NULL),
    F_D_ITEM("Password"                     , "stun.att.password"               , eMT_direct,       "",     NULL),
    F_D_ITEM("Message_integrity"            , "stun.att.hmac"                   , eMT_direct,       "",     NULL),
    F_D_ITEM("Realm"                        , ""                                , eMT_direct,       "",     NULL),
    F_D_ITEM("Reservation_token"            , "stun.att.token"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("Priority"                     , "stun.att.priority"               , eMT_direct,       "",     NULL),
    F_D_ITEM("Software"                     , "stun.att.software"               , eMT_direct,       "",     NULL),
    F_D_ITEM("Cache_timeout"                , "stun.att.cache-timeout"          , eMT_direct,       "",     NULL),
    F_D_ITEM("Tie_breaker"                  , "stun.att.tie-breaker"            , eMT_direct,       "",     NULL),
    F_D_ITEM("Channelnum"                   , "stun.att.channelnum"             , eMT_direct,       "",     NULL),
    F_D_ITEM("Bandwidth"                    , "stun.port.bandwidth"             , eMT_direct,       "",     NULL),
    F_D_ITEM("Lifetime"                     , "stun.att.lifetime"               , eMT_direct,       "",     NULL),
};

ProtoFieldExtractorStun::ProtoFieldExtractorStun() :ProtoFieldExtractor("STUN", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}
