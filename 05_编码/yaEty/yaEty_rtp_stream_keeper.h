/****************************************************************************************
 * 文 件 名 : yaEty_rtp_stream_keeper.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 : 
 * 功    能 : 
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-03-13
* 编    码 : root      '2019-03-13
* 修    改 : 
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _YAETY_RTP_STREAM_KEEPER_H_
#define _YAETY_RTP_STREAM_KEEPER_H_

#include "yaEty_rtp_stream.h"

#include <memory>
#include <map>
#include <thread>
#include <functional>
#include <condition_variable>
struct RtpPortInfo
{
public:
    uint32_t      port_src = 0;
    uint32_t      port_dst = 0;
    int           payload_type = 0;
    bool operator==(const RtpPortInfo &info_) const 
    {
        return this->port_dst == info_.port_dst && this->port_src == info_.port_src&&this->payload_type == info_.payload_type;
    }
    friend bool operator<(const RtpPortInfo &k1,const RtpPortInfo &k2);
};
inline bool operator<(const RtpPortInfo &k1,const RtpPortInfo &k2){
  if(k1.port_dst!=k2.port_dst)
    return k1.port_dst<k2.port_dst;
  if( k1.port_src<k2.port_src){
    return k1.port_src<k2.port_src;
  }
  if( k1.payload_type<k2.payload_type){
    return k1.payload_type<k2.payload_type;
  }
  return false;
}

struct RtpMediaInfo
{
    uint8_t      rtpPayloadType;
    RtpMediaType mediaType;
    uint8_t      pack_mode;
    std::string  byteH264PPS;
    std::string  byteH264SPS;
    std::string Q931CallingPartyNumber;
    std::string Q931CalledPartyNumber;
    std::string sip_from;
    std::string sip_to;

    void cleanAll(){
    rtpPayloadType = 0;
    mediaType = RtpMediaType::unknown;
    pack_mode = 0;
    byteH264PPS.clear();
    byteH264SPS.clear();
    Q931CallingPartyNumber.clear();
    Q931CalledPartyNumber.clear();
    }
};

class RtpStreamKeeper
{
public:
    static RtpStreamKeeper* getInstance()
    {
        static RtpStreamKeeper fs_instance;
        return &fs_instance;
    }

public: // rtp stream management
    static int onTapEthPacket(void *arg, packet_info *pinfo, epan_dissect_t *edt, const void *arg2);

    static int onTapRtpPacket(void *arg, packet_info *pinfo, epan_dissect_t *edt, const void *arg2);

    static int onTapRtspPacket(void *arg, packet_info *pinfo, epan_dissect_t *edt, const void *arg2);

public:
    void onPcapProcessDone();

    RtpMediaType getRtpMediaType(rtp_stream_info_t *info);

    bool anyStreamHasPktToDecode();

private:
    RtpStreamKeeper();
    ~RtpStreamKeeper();

private:
    int processRtpPacket(packet_info *pinfo, epan_dissect_t *edt, const void *arg2);

    int processRtspPacket(packet_info *pinfo, epan_dissect_t *edt, const void *arg2);
    
    int enqueueRtpPacketToStream(packet_info *pinfo, epan_dissect_t *edt, const void *arg2);

    int wakeUpStreamProcThread();

    int updateDissectedPktNum(packet_info *pinfo);

    int tryToDestroyStream(const RtpStreamInfo &info, const RtpStream &rtpStream);

    std::string dissectFlowTrailer(epan_dissect_t *edt);
private: // rtp packet decode and write to disk management
    int checkStreams(bool _is_file_changed);

public: // thread
    int run();
    int stop();

private:
    int registerTapListener();
    int unregisterTapListener();

private:// rtp stream
    std::map<RtpStreamInfo, std::shared_ptr<RtpStream>>     map_streams_;
    std::mutex                                              mtx_streams_;

private:
    uint64_t                               totalDissectedPktCnt_;
    int64_t                                lastDissectedPktNum_;
    
private:// thread to decode rtp packet
    bool                    bRunFlag_ = true;
    std::mutex              mtxCond_;
    std::condition_variable cond_;
    std::thread             rtpStreamProcThread_;
    time_t                  last_decode_time;   //上一次有数据包的时间
private:
    std::map<std::string, RtpMediaInfo> map_serverIp2rtpMediaInfo_;
    std::map<uint32_t, RtpMediaInfo>    map_ssrc2rtpMediaInfo_;

public:
    std::map<std::pair<std::pair<std::string,std::string>,std::string>,RtpPortInfo>  map_serverIp2rtpPortInfo_;
    // <<ip_src,ip_dst>,media_type>
    std::map<RtpPortInfo, RtpMediaInfo>    map_port2rtpMediaInfo_;
};

#endif /* _YAETY_RTP_STREAM_KEEPER_H_ */
