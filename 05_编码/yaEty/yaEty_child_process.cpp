/****************************************************************************************
 * 文 件 名 : yaEty_child_process.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-07-05
* 编    码 : root      '2018-07-05
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_child_process.h"
#include "yaEty_utils.h"
#include "yaEty_target_info.h"

#include <sys/types.h>
#include <sys/socket.h>
#include <unistd.h>
#include <stdlib.h>
#include <poll.h>

ChildProcKeeper *ChildProcKeeper::s_pInstance;

ChildProcKeeper::ChildProcKeeper(int procNum, childProcFunc_t fun)
    : pid_(getpid())
    , pIndex_(-1)
    , pNum_(procNum)
    , childProcFun_(fun)
    , socketPairArrayPtr_(new int[pNum_][2])
    , pollfdArrayPtr_(new pollfd[pNum_])
{
    // printf("parent pid: %d\n", pid_);
}

ChildProcKeeper::~ChildProcKeeper()
{
    if (pIndex_ != -1)
    {   // 子进程，只需要关闭自己的 1
        close(socketPairArrayPtr_[pIndex_][1]);
        return;
    }

    // 父进程，关掉所有的 0
    for (int i = 0; i < pNum_; i++)
    {
        close(socketPairArrayPtr_[i][0]);
    }
}

int ChildProcKeeper::onChildBornInChildExecution(int childIndex)
{   // 此时已在子进程内执行
    pIndex_ = childIndex;
    pid_    = getpid();

    for (int i = 0; i < pNum_; i++)
    {   // 子进程关掉所有的 0,
        close(socketPairArrayPtr_[i][0]);

        // 1 只留下自己的
        if (i != pIndex_)
        {
            close(socketPairArrayPtr_[i][1]);
        }
    }

#if 0
    // gch 公共字段信息改为由文件传递, 数据库线程无需启用
   /* 启动public.signal5_1_target信息管理线程 */
   pthread_t tid;
   int ret = 0;
   char conn_info[256]="user=postgres password=postgres hostaddr=************** port=5432 sslmode=disable dbname=TS";
   ret=pthread_create(&tid, NULL, signal5_target_info_manager_from_db, (void*)conn_info);
   if(0!=ret){
       printf("create signal5_1_target manager  thread failed!\n");
       exit(-1);
   }
#endif
    // printf("new child, pid: %d\n", pid_);

    return 0;
}

int ChildProcKeeper::onChildBornInParentExecution(int childIndex)
{   // 每创建一个 child, 关闭连接它的 1
    close(socketPairArrayPtr_[childIndex][1]);
    return 0;
}

int ChildProcKeeper::CreateProcess()
{
    int lSts = 0;

    // 创建用于通信的 socketpairs
    for (int i = 0; i < pNum_; i++)
    {
        lSts = socketpair(AF_UNIX, SOCK_DGRAM, 0, socketPairArrayPtr_[i]);
        CHECK_NOR_EXIT(lSts < 0, lSts, "create socketpair error.\n");

        // 将 socketpair 0 添加到 pollfd 中
        pollfdArrayPtr_[i].fd     = socketPairArrayPtr_[i][0];
        pollfdArrayPtr_[i].events = POLLIN;
    }

    // 创建 child processes
    for (int i = 0; i < pNum_; i++)
    {
        pid_t pid = fork();
        CHECK_NOR_EXIT(pid < 0, pid, "create child process error.\n");

        if (0 == pid)
        {
            onChildBornInChildExecution(i);

            // call child proc fun
            childProcFun_(i);
            exit(0);                     // 子进程函数处理完后，退出
        }
        else
        {
            onChildBornInParentExecution(i);
            continue;
        }
    }

    return 0;
}

int ChildProcKeeper::SendMsgToChild(int procIndex, const char *pBuff, int len)
{   // 必然在父进程
    return write(socketPairArrayPtr_[procIndex][0], pBuff, len);
}

int ChildProcKeeper::RecvMsgFromParent(char *pBuff, int buffLen)
{
    int lSts                = 0;
    char buff[EtyMsgLenMax] = { 0 };

    lSts = read(socketPairArrayPtr_[pIndex_][1], buff, buffLen);
    if (lSts < 0)
    {
        goto OUT;
    }

OUT:
    return lSts;
}

int ChildProcKeeper::SendMsgToParent(int lMsgCode, const char *pBuff, int len)
{
    int            lSts     = 0;
    char buff[EtyMsgLenMax] = { 0 };
    ST_EtyProcMsg *pMsg     = (ST_EtyProcMsg *)buff;

    pMsg->lMsg       = lMsgCode;
    pMsg->payloadLen = len;
    memcpy(pMsg->payload, pBuff, len);

    /* 发送请求消息 */
    lSts = write(socketPairArrayPtr_[pIndex_][1], pMsg, sizeof (ST_EtyProcMsg) + pMsg->payloadLen);
    CHECK_NOR_EXIT(lSts < 0, lSts, "write to socketpair error.\n");

    return lSts;
}

int ChildProcKeeper::AskParentForTask(std::string &strFileName)
{
    int            lSts     = 0;
    char buff[EtyMsgLenMax] = { 0 };
    ST_EtyProcMsg *pMsg     = (ST_EtyProcMsg *)buff;

    pMsg->lMsg       = EPM_AskForTask;
    pMsg->payloadLen = 0;

    /* 发送请求消息 */
    lSts = write(socketPairArrayPtr_[pIndex_][1], pMsg, sizeof (ST_EtyProcMsg) + pMsg->payloadLen);
    CHECK_NOR_EXIT(lSts < 0, lSts, "write to socketpair error.\n");

    /* 读取消息，获取任务 */
    lSts = read(socketPairArrayPtr_[pIndex_][1], buff, sizeof buff);
    CHECK_NOR_EXIT(lSts < 0, lSts, "read from socketpair error.\n");

    /* 授予了任务 */
    if (EPM_GrantTask == pMsg->lMsg)
    {
        strFileName = pMsg->payload;
        // printf("child process %d 从 parent 处申请到任务：%s\n", pIndex_, pMsg->payload);
    }

OUT:
    return pMsg->lMsg;
}

int ChildProcKeeper::GrantTaskToChild(int procIndex, std::string &strFileName)
{
    int            lSts     = 0;
    char buff[EtyMsgLenMax] = { 0 };
    ST_EtyProcMsg *pMsg     = (ST_EtyProcMsg *)buff;

    pMsg->lMsg       = EPM_GrantTask;
    pMsg->payloadLen = strFileName.length() + 1;
    strcpy(pMsg->payload, strFileName.c_str());

    lSts = write(socketPairArrayPtr_[procIndex][0], pMsg, sizeof (ST_EtyProcMsg) + pMsg->payloadLen);
    CHECK_NOR_EXIT(lSts < 0, lSts, "write to socketpair error.\n");

    // printf("向 child process %d 分派任务：%s\n", procIndex, strFileName.c_str());

    return lSts;
}

void ChildProcKeeper::CreateInstance(int procNum, childProcFunc_t fun)
{
    static ChildProcKeeper s_instance(procNum, fun);
    s_pInstance = &s_instance;
}

ChildProcKeeper *ChildProcKeeper::GetInstance()
{
    return s_pInstance;
}

int ChildProcKeeper::pollChildren(int timeoutInMs, childMsgProcFunc_t func)
{
    int            nPollIn  = 0;
    int            lSts     = 0;
    char buff[EtyMsgLenMax] = { 0 };
    ST_EtyProcMsg *pMsg     = (ST_EtyProcMsg *)buff;

    nPollIn = poll(pollfdArrayPtr_.get(), pNum_, timeoutInMs);
    if (nPollIn < 0)
    {   // poll 出错
        return -1;
    }

    if (nPollIn == 0)
    {   // poll 超时
        return 0;
    }

    for (int i = 0; i < pNum_; i++)
    {
        if (!(pollfdArrayPtr_[i].revents & POLLIN))
        {
            continue;
        }

        // child proc i 发来消息
        lSts = read(socketPairArrayPtr_[i][0], buff, sizeof buff);
        if (lSts < 0)
        {
            continue;
        }

        // 处理消息
        func(i, pMsg, lSts);
    }

    return 0;
}

int ChildProcKeeper::BroadcastMsgToChildren(const char *pBuff, int len)
{
    for (int i = 0; i < pNum_; i++)
    {
        SendMsgToChild(i, pBuff, len);
    }

    return 0;
}
