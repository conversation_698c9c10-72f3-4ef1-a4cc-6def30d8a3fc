/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_rip.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-10-08
* 编    码 : zhangsx      '2018-10-08
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ipv4.h"
#include "epan/strutil.h"

#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_rip.h"
#include "yaEty_content_reassembly_share_mem.h"

static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    F_D_ITEM("SrcPort"                      , "udp.srcport"                     , eMT_direct,       "",       NULL),
    F_D_ITEM("DstPort"                      , "udp.dstport"                     , eMT_direct,       "",       NULL),
    F_D_ITEM("C2S"                          , ""                                , eMT_fixed,        "",       NULL),
    F_D_ITEM("Proto"                        , "ip.proto"                        , eMT_direct,       "",       NULL),
    F_D_ITEM("TTL"                          , "ip.ttl"                          , eMT_direct,       "",       NULL),

    F_D_ITEM("Version"                      , "rip.version"                     , eMT_direct,       "",       NULL),
    F_D_ITEM("RouteDstIp"                   , "rip.ip"                          , eMT_direct,       "",       NULL),
    F_D_ITEM("SubnetMask"                   , "rip.netmask"                     , eMT_direct,       "",       NULL),
    F_D_ITEM("NextHop"                      , "rip.next_hop"                    , eMT_direct,       "",       NULL),
    F_D_ITEM("Metric"                       , "rip.metric"                      , eMT_direct,       "",       NULL),
};

ProtoFieldExtractorRip::ProtoFieldExtractorRip() :MatchPrefixProtoFieldExtractor("RIP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}
