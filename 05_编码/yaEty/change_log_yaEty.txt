yaEty_pkg_1.0.323.4429_1027 by 张庆学 at 2023.10.27
1) 增加 获取并填充 voip, rtp_stream tbl GCH 公共字段头


yaEty_pkg_1.0.32.4429_0904 by 郑思文 at 2023.09.04
1) voip tbl 中的 Json_of_rtp 字段输出为正规 json array 形式，为空时输出"[]";

yaEty_pkg_1.0.31.4429_0901 by 郑思文 at 2023.09.01
1) 修复当一条 rtp stream 中首个 rtp 包因为过短而解码失败，但积累更多时可以解码时将该流存储为 raw 文件的问题;
2) yaEty.conf 配置文件中增加 "voip_tbl_dir" 配置项，单独配置 voip tbl 的输出目录, 用来与 mixAudio 配合,
   让 mixAudio 输出到 /tmp/content/tbls/voip 目录中，方便 ETL.jar 统一处理 /tmp/content/tbls 目录中的协议;

yaEty_pkg_1.0.30.4429_0830 by 郑思文 at 2023.08.30
1) 修复 voip CapDate 在没有 rtp 时 voip 输出 CapDate 为 197x 的问题;
2) 修复 voip 与 rtp_stream DealDate 相差 8 小时问题;
3) 修复 voip 的 Hw_ecgi 字段在没有移动信息时也总输出 0x080 问题;
4) 修复 voip 中的 LineNo 输出为 ["",""] 问题;
5) voip Json_of_rtp 字段中不再输出 Lost, MaxDelta 等统计信息(代码中并未实现);
6) 修复 rtp_stream 中 FileSize 大小与实际还原大小不一致问题;

yaEty_pkg_1.0.29.4429_0829 by 郑思文: at 2023.08.29
1) 修复 voip CapData 为 197x 年问题;
2) 修复 voip 与 rtp_stream FromDir 输出错误问题;
3) 修复 rtp_stream 中 FileSize 偶尔为 0 问题;
4) 修复在不开启子进程时遇到多个输入 pcap 文件中 rtp stream ssrc 均相同情况下
   输出的所有 rtp_stream 记录中还原文件总为第一个文件问题;
5) 修复 rtp/g711 无法还原问题;

1.0.28.4429_0828 by 郑思文 at 2023.08.28
本次更新内容:
1) voip 协议在 tbl 最后预留一个空白字段，供 mixAudio 程序填入合并后文件名;

1.0.27.4429_0824 by 郑思文 at 2023.08.24
本次更新内容:
1) 修复 rtp_stream 与 voip 协议在没有遇到 sip 流量时无法填充 CapDate 问题, 并且统一为包内时间;
2) voip 协议增加 gangch target info 共性字段;
3) rtp_stream, voip 中的 ssrc 字段以及还原出的 .wav 与 .h264 文件名中出现的 ssrc 统一为 hex 形式;

1.0.26.4429_0818 by 郑思文 at 2023.08.18
本次更新内容:
1) 增加对 rtsp + rtp/h264 视频还原的支持(输出为 .h264 文件);
   要求 rtp payload 为动态类型，并且 rtsp/sdp 中有 rtpmap 将动态类型映射到 h264;

1.0.25.4428_0817 by 郑思文 at 2023.08.17
本次更新内容:
1) yaEty.conf 中补充关于 task_id 的配置示例;

1.0.25.4427_0817 by 郑思文 at 2023.08.17
本次更新内容:
1) 修复 sdp 这种‘子协议’(sip/sdp, rtsp/sdp) 无法被配置文件中的 unsued_proto 禁用的问题(tbl 与 字段表);
2) 更新 yaEty.conf 文件中各个协议的 unused_proto 写法，正确名称为: CLASSIC-STUN, MySQL, GSM SMS;

1.0.25.4426_0811 by 郑思文 at 2023.08.11
本次更新内容:
1) 调整命令行参数语法, 增加 "-T <tbl outdir>" 选项，不再将最后一个参数作为输出目录;
   当命令行不指定 -T 参数时使用配置文件中的 tbl_dir 配置，如果 tbl_dir 没有配置默认使用 /tmp/tbls/yaEty;
2) 命令行参数增加 "-C <content outdir>" 选项，支持在命令行配置 content 输出目录;
   当命令行不指定 -C 参数时使用配置文件中的 content_dir 配置, 如果 content_dir 没有配置默认使用 /tmp/tbls/yaEty/content;
3) 默认不执行内容还原，即默认总是配置了 "-n";

1.0.24.4426_0809 by 黄知伟 at 2023.08.09
更新内容: (专用于 gangch 项目 voip 还原)
1) 为 rtp_stream 协议增加 target 信息，目前字段均为空值进行占位，
   后续需要连接数据库查询后填充有效数据；新增字段如下:
 "signalType" "dFwdBeginTime" "dFwdEndTime" "dRetBeginTime" "dRetEndTime" "sIMSI" "sIMEI" "sTMSI" "dStartTime" "dFinishTime" "nDiscWay" "nRSpotBeamNo" "nNSpotBeamNo" "UserType" "TARGETTYPE" "JXH" "TargetCategory" "stationID" "sendTime" "PTMC" "SlaveDID" "TempID172EWS" "TempID172EN" "TempID166" "SourcePath";
2) 为 rtp_stream 协议增加 SrcProvince, DstProvince 字段;
3) 为 rtp_stream 协议增加 DealDate 字段，表示实际处理时间，不论被处理数据是何时产生的;
4) 增加 service 服务文件;

yaEty_pkg_1.0.23.4425_0623
本次更新内容：
1) dhcp协议解析增加了hostname和request_ip的输出项，对应字段表添加了两个新字段HostName和RequestIP

yaEty_pkg_1.0.23.4406_0408
本次更新内容：
1) 配置文件增加了line_no项和line_le项，以增加对线路号解析的支持(仅限voip和rtp_stream)
   具体格式及含义：
   line_no = 1                                # 是否解析线路号 0为否，1为是 仅对rtp_stream和voip生效。
   line_le = 1                                # 线路号是否按小端模式解析 为0 则按大端模式解析 为1 则按小端模式解析

yaEty_pkg_1.0.23.4391_1223
本次更新内容：
1) 修改tbl中抓包时间输出格式(去掉毫秒)和还原文件输出路径输出格式(将绝对路径改为相对路径)
2) 添加终止方式SIGUSR1，yaEty在收到信号后会再扫描一遍输入路径后停止运行。

yaEty_pkg_1.0.23.4376_1204
本次更新内容：
1) 添加配置文件读取任务id文件，再从任务id文件中读取任务id以供使用的功能
2) 添加从命令行设置任务id的功能，此方式优先级要高于其他配置任务id方式

yaEty_pkg_1.0.23.4374_1202
本次更新内容：
1）配置文件增加了task_id项，此参数会被添加至tbl文件名的最后项(后缀之前)

yaEty_pkg_1.0.23.4366_1022
本次更新仅需更新可执行文件
本次更新内容：
1）rtp_stream tbl增加了输出文件的时长，大小，以及输入源路径
2）voip tbl增加了输入源路径

yaEty_pkg_1.0.23.4363_1013
本次更新仅需更新可执行文件
本次更新内容：
1）增加tbl字段输出长度限制，并用于wxf的filedata字段

yaEty_pkg_1.0.23.4349_0904
1）修复jsc标签bug，支持跳过未知的tlv且支持了新类型的tlv解析。
2）恢复tbl默认单文件tbl量为2000。
3）修复配置输出路径优先级高于指令指定输出路径优先级的bug。

yaEty_pkg_1.0.23.4335_0717
本次更新仅需更新可执行文件
本次更新内容：
1）支持了对voip中rtp payload type 为speex的rtp数据(nb,wb,uwb)的还原支持，现在程序处理rtp
 payload type为speex的rtp数据时会直接生成音频文件(.wav)了。
 
yaEty_pkg_1.0.23.4333_0715
本次更新仅需更新可执行文件
本次更新内容：
1）重新支持了rt标签的解析，配置trailer_type = rt即可生效。
2）支持了对voip中rtp payload type 为iLBC的rtp数据的还原支持，现在程序处理rtp
 payload type为iLBC的rtp数据时会直接生成音频文件(.wav)了。

yaEty_pkg_1.0.23.4324_0616
本次更新内容：
1）支持了jsc标签解析 为开启此功能，需在原有配置文件中新加一项：
    trailer_type = jsc
以支持jsc标签的解析，将jsc换为hw后即可支持对hw标签的解析，不过暂不支持rt标签

yaEty_pkg_1.0.23.4324_0615
1）添加了对GTP-U中数据的解析支持

yaEty_0.31_1220
1）修复了恒为标签解析的bug.
2）修复了yaEty不能正常调用xplico的bug

yaEty_0.30_1130
1）voip和rtp_stream的tbl补充了公共字段
2）配置项dissect_options增加额外的选项值(esp.enable_null_encryption_decode_heuristic:true), 以支持volte数据的解析
3）增加了tbl条数上限配置项(tbl_limits)，当tbl输出满足上限时就会换一个文件输出tbl

yaEty_0.29_1111
1）添加额外的rtp_stream信息输出，以供在voip tbl中无有效数据时入库

yaEty_0.28_1018:
1）添加对恒为标签格式解析的支持;
2）更新WXA解析模式
3）修复之前一系列的bug，包括程序处理pcap不完整，程序工作模式不正常等。

yaEty_0.27_0531:
1）添加新的配置选项(content_file_lower_limit)，程序默认会删除还原出来体积小于配置值的voip还原文件(.wav, .raw)。
2）添加配置输出路径功能(field_dir, tbl_dir, content_dir)，在这三项均配置后，程序会将对应内容输出至指定的目录。
3）修复之前一系列的bug，包括程序进行voip还原文件大小不稳定，程序本身崩溃，程序不支持多进程voip还原等

yaEty_0.26_0401:
1）添加对voip解析的支持，可以输出voip行为的tbl并尝试解出音频(当前支持的格式:G711, G722，G726, G729)。
2）不支持的格式会被汇聚处理成.raw文件(对rtp payload部分原始数据的聚合)。

yaEty_0.25_0313:
1）为http下行tbl中添加了http.host的输出
2）修复了http解析的一个bug和l2tp的一个内存泄漏的bug

yaEty_0.24_0225:
1）添加packet长度输出，使用原有resv1字段
2）resv字段使用情况：
	resv1：每帧长度
	resv2：未使用
	resv3：宜昌数据标签-mac
	resv4：宜昌数据标签-username
	resv5：未使用
	resv6：tcp会话id
	resv7：源mac地址
	resv8：目的mac地址
	
yaEty_0.23_1227:
1）添加 gre、gtp、isakmp、ospf、stun、tftp、iax 协议的解析;
2）增加对西安rtl标签解析的支持;

yaEty_0.22_1128:
1）添加 tds、diameter、eigrp、ospf、stun、classicstun、ah、bgp、mgcp、ldap协议的解析;
2）修复 rtp 协议解析时由于端口引起的崩溃问题；
3）在程序目录下增加 80211_keys 文件用来配置 wifi 解析时用到的 ssid 密码;
4）增加对 ipv6 的支持；
5）添加 -r 命令支持解析 pcap 时不进行 .ext 重命名；

yaEty_0.21_1019:
1）修复 xplico 还原时崩溃问题(videosnarf 引起)；
2）修复 sip、ftp 等协议中出现的崩溃问题，五元组中获取端口时可能失败问题容错；
3）修复微信文件传输协议中 localname 显示为乱码问题，支持中文显示；
4）提升对微信文件传输协议的识别能力，如 tcp payload 中直接出现的 lklv 结构等场景；
5）提升http 协议中的 content 内容(包含下行页面)的输出能力，支持 chunk编码后又经过 gzip 压缩的内容
   解析处理；
6）新增 telnet、bgp、ssh、`snmp、mysql、dtls、rip、rdp、megaco 协议解析；

yaEty_0.20_0828:
 1）增强对于 rtp 协议的识别：支持 rtp_udp, rtp_stun, rtp_rtsp 等；
 2）添加在解析时针对特定协议进行 dump 为 pcap 文件的支持，
    以此实现对 rtp 协议的“按照 ssrc 聚合为 pcap 后一举还(jian)原(mie)” 功能,
    配置见 yaEty.cof 'dump' 部分;
 3) 升级 wireshark 框架为 2.6.2 版本；
 4）添加 dhcp 协议的解析；
 5）post 解析模块修复内存泄漏问题；

yaEty_0.16_0806:
 1）新增加对微信文件消息的解析，输出为 wxf 协议，支持 http/tcp 80/tcp 8080/tcp 443 形式;
 2）为所以 tcp 上下行数据建立关联，属于同一会话的上下行数据拥有相同的 resv6 字段值(由五元组对称 hash 得到);
 3）添加 sip 与 voip 还原出的内容的关联，关联在 sip "x-id" 字段;
 4）在调试模式下为所有协议表增加  __FromPcap 与 __FrameNumber 指明数据来源，调试模式由 yaEty.conf 中 debug_mode 开启;
 5）http 协议中 Proxy-Authorization UserAgent Set-Cookie Referer Location 等字段中可能出现'|' 导致 tbl 入库时错位，
    将其转换为 '_';

yaEty_0.15_0711:
 1) 修复解析与内容还原组件之间共享内存销毁方式，之前版本实际无法有效清理共享内存
    造成共享内存残留.

yaEty_0.14_0710:
 1）修复打包时未打进 radius 协议解析配置文件问题;

yaEty_0.14_0707:
 1) 支持多进程模式，由 yaEty.conf 配置文件中变量 "child_proc_num"(默认为 10) 决定并发子进程数量;
 2) 修复输入目录中存在非 pcap 文件时的崩溃问题，遇到此类不支持文件会为其添加后缀并不处理，后缀
    由配置文件中 "rename_suffix_err" 变量控制;

yaEty_0.13_0703:
 1) 添加对 rtp 协议的解析与内容还原，还原在 RtpStream 字段中;
 2) 添加对 esp 协议的解析;
 3) 添加对 l2tp 协议的解析;
 4) 添加对 pptp-control 协议的解析;
 5) 修复 smtp 协议还原出路径“累加”问题;
 6) 对 http 协议中还原出的视频流文件路径，添加.mp4 后缀;
 7) 为 ftp,esp,rtp,smtp,pop,imap 等协议添加 rtl 标签;
 8) 添加对宜昌固网标签的解析，解析出的 "不明6字节" 与 username 分别存放在 resv3 与 resv4 中;
 9) install.sh 安装脚本支持 -l 参数进行“当前目录”安装;

yaEty_0.12:
 1) 对目前所有支持的协议添加 mac 地址的解析，源目mac 地址分别存放在 resv7 和 resv8 中;
 2) http post 内容(v51) 中出现的“优化输出”字样移除;
 3) http 还原出的内容列表(v61) 中增加类型字段，如：
    [
     {type:1,path:"/tmp/05/build/run/content/http/172.16.20.30/http_rs_body_1529894468_0x7fd334004d90_674.jpg"},
     {type:1,path:"/tmp/05/build/run/content/http/172.16.20.30/http_rs_body_1529894468_0x7fd3340057e0_706.jpg"},
     {type:1,path:"/tmp/05/build/run/content/http/172.16.20.30/http_rs_body_1529894468_0x7fd334005250_729.jpg"},
    ]
    其中 type 取值：1 为图片，2 为视频;
 4) 命令行参数支持多个输入目录，并支持对这些目录的递归遍历(不再只处理一层，最多处理5层)，如：
    ./yaEty dir1 dir2 dir3 outDir              # 最后一个参数作为输出目录，之前的全部当作输入目录
 5) 默认支持增长目录，会对所有输入目录进行监测，处理完成后会继续扫描，如果有新文件会继续处理；
    如果没有新文件，但检测 .writing 文件增长，则会继续扫描，直到连续3次发现目录没有增长才会退出,
    相关参数都可在 yaEty.conf 中进行配置;
 6) 默认为处理过的文件添加 ".done" 后缀，在 yaEty.conf 中可以决定处理后是删除文件还下添加后缀;
 7) 添加 radius 协议的解析;
 8) 添加 sip 协议的解析;

yaEty_0.11:
 1) 修复 http post 解析时由于内容过长导致的崩溃问题;
 2) 在安装脚本中增加清理旧版本遗留下的共享内存段动作;
 3) 添加 ftp 与 smtp 协议的解析;
 4) 添加 ftp 与 smtp 协议的内容还原并关联，ftp 还原出的文件路径存放在
    ftp tbl 记录中的 Ftp_data 字段中；smtp 还原出的文件路径存放在 smtp
    tbl 记录的 ExtMailHdrValue0 字段中;

yaEty_0.10:
  1）http post 内容输出可见内容比例提升，不可见内容不再输出其16进制形式;
  2）内容还原模块加载so 库路径策略调整，不再依赖配置文件，即不用再执行安装也正常运行;

yaEty_0.9:
  1) 添加对 dns 协议的解析；
  2) 修复内容还原组件遇到有些 pcap 包解析时可能出现的崩溃问题；

yaEty_0.8:
  1) 添加对 post 内容的解析，输出到 V51 字段中，目前有不可见字符的内容会输出将16进制形式；
  2) 修复内容还原组件遇到有些 pcap 包解析时可能出现的崩溃问题；

yaEty_0.7:
  1) 整合内容还原功能，http 中还原出的图片路径会以 json 字符串形式存放在相应 tbl
     记录的 V61 字段中，json 内容为图片的绝对路径组成的 json array;
  2) 调整输出目录组织方式，如果命令行调用时给出输出路径为 out (sudo ./yaEty ~/pcaps/h2 out):
     out                                                           # 输出根目录
     ├── content                                                   # 内容还原目录
     │   └── http                                                  # 内容还原协议目录
     │       └── ************                                      # 按源ip 组织整理的内容目录
     │           └── http_rs_body_1526978506_0x7fb608009380_32.gif # 还原出的图片
     ├── field                                                     # tbl 文件的字段表文件目录
     │   └── http_n.txt                                            # 具体协议的 tbl 字段表文件
     └── tbls                                                      # tbl 输出目录
         └── http_n                                                # 按协议名组织的 tbl 目录
             └── 20180522164146_93968_http_n_042.tbl               # tbl 文件
  3) 添加对 http 协议中头域数量的解析，存放到 H_A_NUMBER 和 H_X_NUMBER 中；
  4) 修复 URI 中出现"|"时导致 tbl 字段错位问题，将其替换为 "_";

yaEty_0.6:
  1) 添加对 http 头域的解析，输出到 K00/V00 开始的 kv 对中;

yaEty_0.5:
  1) 修复日期格式问题，形如“2018-05-16 11:53:02；
  2）修复 C2S 字段判定问题;
  3) 默认在当前目录下产生 field 目录，存放协议的字段描述文件；
  4）新增加命令行参数：yaEty <pcap dir> [tbl out dir]；
  5）增加更多 http 字段解析，如：Referer, Last-Modified 等；
