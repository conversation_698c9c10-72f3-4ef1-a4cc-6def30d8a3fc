﻿/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_ssh.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-09-04
* 编    码 : zhangsx      '2018-09-04
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include <iconv.h>

#include <algorithm>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"

#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_ssh.h"
#include "yaEty_content_reassembly_share_mem.h"

/*****************************************************************
*Function    :transform_field_for_C2S
*Description :ssh C2S
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :std::string
*Others      :none
*****************************************************************/

static std::string transform_field_for_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{

    pFinfo = get_first_field_info_from_interesting_fields(edt, "tcp.srcport");
    unsigned int && ssh_s = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pFinfo->value)));
    
    
    pFinfo = get_first_field_info_from_interesting_fields(edt, "tcp.dstport");
    unsigned int && ssh_d = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pFinfo->value)));
    
    return ssh_s > ssh_d ? "C2S" : "S2C";

}

/*****************************************************************
*Function    :transform_field_for_field_length
*Description :get field length
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_field_length(epan_dissect_t *edt, const field_info *pFinfo)
{

    return (NULL != pFinfo) ? std::to_string(pFinfo->length) : "";
}


/*****************************************************************
*Data        : ms_protoFieldDescArray
*Description : Protocol field match in wireshark
*Format      :( "Need Field" ,"match wireshark field", apply data  
*Others      : none
*****************************************************************/

static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // 10 个 RTL 标签头
    F_D_ITEM_RTL_10(),

    // 27 个通用字段头
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    /* 通用特定字段(由协议本身确定) */
    F_D_ITEM("SrcPort"                              , "tcp.srcport"                                             , eMT_direct,           "",          NULL),
    F_D_ITEM("DstPort"                              , "tcp.dstport"                                             , eMT_direct,           "",          NULL),
    F_D_ITEM("C2S"                                  , ""                                                        , eMT_fromEdt,          "",          transform_field_for_C2S),
    F_D_ITEM("Proto"                                , "ip.proto"                                                , eMT_direct,           "",          NULL),
    F_D_ITEM("TTL"                                  , "ip.ttl"                                                  , eMT_direct,           "",          NULL),
    /* ssh协议体 */                                                                                                                             
    F_D_ITEM("SSHVersion"                           , "ssh.protocol"                                            , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexInit_PacketLength"           , "ssh.packet_length"                                       , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexInit_PadingLength"           , "ssh.padding_length"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexInit_MessageCode"            , "ssh.message_code"                                        , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexInit_Cookie"                 , "ssh.cookie"                                              , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_kex_algorithmsLen"         , "ssh.kex_algorithms_length"                               , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexInit_kex_algorithms"         , "ssh.kex_algorithms"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_svrhostkey_algLen"         , "ssh.server_host_key_algorithms_length"                   , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_svrhostkey_alg"            , "ssh.server_host_key_algorithms"                          , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_EncryptAlg_C2SLen"         , "ssh.encrytion_algorithms_client_to_server_length"        , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_EncryptAlgC2S"             , "ssh.encrytion_algorithms_client_to_server"               , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_EncryptAlgS2CLen"          , "ssh.encrytion_algorithms_server_to_client_length"        , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_EncryptAlgS2C"             , "ssh.encrytion_algorithms_server_to_client"               , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_MacAlgC2SLen"              , "ssh.mac_algorithms_client_to_server_length"              , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_MacAlgC2S"                 , "ssh.mac_algorithms_client_to_server"                     , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_MacAlgS2CLen"              , "ssh.mac_algorithms_server_to_client_length"              , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_MacAlgS2C"                 , "ssh.mac_algorithms_server_to_client"                     , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_CompAlgC2SLen"             , "ssh.compression_algorithms_client_to_server_length"      , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_CompAlgC2S"                , "ssh.compression_algorithms_client_to_server"             , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_CompAlgS2CLen"             , "ssh.compression_algorithms_server_to_client_length"      , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_CompAlgS2C"                , "ssh.compression_algorithms_server_to_client"             , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_languageC2SLen"            , "ssh.language_client_to_server_length"                    , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_languageC2S"               , "ssh.language_client_to_server"                           , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_languageS2CLen"            , "ssh.language_server_to_client_length"                    , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_languageS2C"               , "ssh.language_server_to_client"                           , eMT_direct,           "",          NULL),
    F_D_ITEM("CltKexInit_kex_FirstPkt_fw"           , "ssh.first_kex_packet_follows"                            , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexInit_reserved"               , "ssh.kex.reserved"                                        , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexInit_Pading"                 , "ssh.padding_string"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexInit_PacketLength"           , "ssh.packet_length"                                       , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexInit_PadingLength"           , "ssh.padding_length"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexInit_MessageCode"            , "ssh.message_code"                                        , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexInit_Cookie"                 , "ssh.cookie"                                              , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_kex_algorithmsLen"         , "ssh.kex_algorithms_length"                               , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexInit_kex_algorithms"         , "ssh.kex_algorithms"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_svrhostkey_algLen"         , "ssh.server_host_key_algorithms_length"                   , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_svrhostkey_alg"            , "ssh.server_host_key_algorithms"                          , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_EncryptAlg_C2SLen"         , "ssh.encrytion_algorithms_client_to_server_length"        , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_EncryptAlgC2S"             , "ssh.encrytion_algorithms_client_to_server"               , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_EncryptAlgS2CLen"          , "ssh.encrytion_algorithms_server_to_client_length"        , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_EncryptAlgS2C"             , "ssh.encrytion_algorithms_server_to_client"               , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_MacAlgC2SLen"              , "ssh.mac_algorithms_client_to_server_length"              , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_MacAlgC2S"                 , "ssh.mac_algorithms_client_to_server"                     , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_MacAlgS2CLen"              , "ssh.mac_algorithms_server_to_client_length"              , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_MacAlgS2C"                 , "ssh.mac_algorithms_server_to_client"                     , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_CompAlgC2SLen"             , "ssh.compression_algorithms_client_to_server_length"      , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_CompAlgC2S"                , "ssh.compression_algorithms_client_to_server"             , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_CompAlgS2CLen"             , "ssh.compression_algorithms_server_to_client_length"      , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_CompAlgS2C"                , "ssh.compression_algorithms_server_to_client"             , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_languageC2SLen"            , "ssh.language_client_to_server_length"                    , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_languageC2S"               , "ssh.language_client_to_server"                           , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_languageS2CLen"            , "ssh.language_server_to_client_length"                    , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_languageS2C"               , "ssh.language_server_to_client"                           , eMT_direct,           "",          NULL),
    F_D_ITEM("SrvKexInit_kex_FirstPkt_fw"           , "ssh.session.key"                                         , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexInit_reserved"               , "ssh.kex.reserved"                                        , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexInit_Pading"                 , "ssh.padding_string"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexDHINIT_PacketLength"         , "ssh.packet_length"                                       , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexDHINIT_PadingLength"         , "ssh.padding_length"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexDHINIT_MessageCode"          , "ssh.message_code"                                        , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexDHINIT_e_Length"             , "ssh.dh.e"                                                , eMT_transform,        "",          transform_field_for_field_length),
    F_D_ITEM("ClientKexDHINIT_e"                    , "ssh.dh.e"                                                , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexDHINIT_Pading"               , "ssh.padding_string"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexGEX_REQ_PacketLen"           , "ssh.native_lanman"                                       , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexGEX_REQ_PadingLen"           , "ssh.primary_domain"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexGEX_REQ_MsgCode"             , "ssh.cmd"                                                 , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexGEX_REQUEST_min"             , "ssh.andxoffset"                                          , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexGEX_REQUEST_nbits"           , "ssh.connect.flag"                                        , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexGEX_REQUEST_max"             , "ssh.pwlen"                                               , eMT_direct,           "",          NULL),
    F_D_ITEM("ClientKexGEX_REQUEST_Pading"          , "ssh.password"                                            , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexGEX_GROUP_PacketLen"         , "ssh.path"                                                , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexGEX_GROUP_PadingLen"         , "ssh.service"                                             , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexGEX_GROUP_MsgCode"           , "ssh.cmd"                                                 , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexGEX_GROUP_p_Length"          , "ssh.dh_gex.p"                                            , eMT_transform,        "",          transform_field_for_field_length),
    F_D_ITEM("ServerKexGEX_GROUP_p"                 , "ssh.dh_gex.p"                                            , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexGEX_GROUP_g_Length"          , "ssh.dh_gex.g"                                            , eMT_transform,        "",          NULL),
    F_D_ITEM("ServerKexGEX_GROUP_g"                 , "ssh.dh_gex.g"                                            , eMT_direct,           "",          transform_field_for_field_length),
    F_D_ITEM("ServerKexGEX_GROUP_Pading"            , "ssh.padding_string"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexDHReply_PacketLen"           , "ssh.packet_length"                                       , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexDHReply_PadingLen"           , "ssh.padding_length"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexDHReply_MessageCode"         , "ssh.message_code"                                        , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexDHReply_PubKeyLen"           , "ssh.ecdh.q_s"                                            , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexDHReply_PubKey"              , "ssh.ecdh.q_s_length"                                     , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexDHReply_f_Length"            , "ssh.dh.f"                                                , eMT_transform,        "",          transform_field_for_field_length),
    F_D_ITEM("ServerKexDHReply_f"                   , "ssh.dh.f"                                                , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexDHReply_Sig_Len"             , "ssh.kex.h_sig_length"                                    , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexDHReply_Signature"           , "ssh.kex.h_sig"                                           , eMT_direct,           "",          NULL),
    F_D_ITEM("ServerKexDHReply_Pading"              , "ssh.padding_string"                                      , eMT_direct,           "",          NULL),
    F_D_ITEM("MsgNewKeys"                           , "ssh.first_kex_packet_follows"                            , eMT_direct,           "",          NULL),

};


ProtoFieldExtractorSSH::ProtoFieldExtractorSSH():ProtoFieldExtractor("SSH", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}

bool ProtoFieldExtractorSSH::ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter)
{
    return true;
}



