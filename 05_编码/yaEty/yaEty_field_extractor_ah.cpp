/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_ah.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-11-07
* 编    码 : zhangsx      '2018-11-07
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include"config.h"
#include"epan/epan_dissect.h"
#include"yaEty_utils.h"
#include"yaEty_ws_utils.h"
#include"yaEty_rec_writer_tbl.h"
#include"yaEty_field_extractor.h"
#include"yaEty_field_extractor_ah.h"
#include"yaEty_content_reassembly_share_mem.h"

/*****************************************************************
*Function    :transform_field_for_field_length
*Description :get field length
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_field_length(epan_dissect_t *edt, const field_info *pFinfo)
{

    return (NULL != pFinfo) ? std::to_string(pFinfo->length) : "";
}

/*****************************************************************
*Function    :choose_for_ah_field
*Description :从两个字段中选出不为空的一项以供填入结果
*Input       :epan_dissect_t, field_info, const char*, const char*
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

struct choose_for_ah_field
{
public:
    choose_for_ah_field(const char * field_name1, const char * field_name2)
        :_field_name1(field_name1), _field_name2(field_name2)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info *pfinfo1 = get_first_field_info_from_interesting_fields(edt, _field_name1);

        field_info *pfinfo2 = get_first_field_info_from_interesting_fields(edt, _field_name2);
        if (pfinfo1)
        {
            return ety_ws_get_node_field_value(pfinfo1, edt);
        }
        if (pfinfo2)
        {
            return ety_ws_get_node_field_value(pfinfo2, edt);
        }
        return "";
    }
public:
    const char * _field_name1;
    const char * _field_name2;
};
/*****************************************************************
*Function    :transform_field_for_ah
*Description :ah 获取协议中数组的每一项字段值
*Input       :epan_dissect_t, field_info, const char *, int
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
struct transform_field_for_ah
{
public:
    transform_field_for_ah(int num, const char * field_name)
        :_num(num), _field_name(field_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info *pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _num);

        if (nullptr == pfinfo)
        {
            return "";
        }

        std::string && strRst = ety_ws_get_node_field_value(pfinfo, edt);

        return strRst;
    }
public:
    int _num;
    const char *_field_name;
};

static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort"                  , ""                        , eMT_fixed,        "",     NULL),
    F_D_ITEM("DstPort"                  , ""                        , eMT_fixed,        "",     NULL),
    F_D_ITEM("C2S"                      , ""                        , eMT_fixed,        "",     NULL),
    F_D_ITEM("Proto"                    , "ip.proto"                , eMT_direct,       "",     NULL),
    F_D_ITEM("TTL"                      , "ip.ttl"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("AH_NextHeader"            , "ah.next_header"          , eMT_direct,       "",     NULL),
    F_D_ITEM("AH_SPI"                   , "ah.spi"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("AH_Sequence"              , "ah.sequence"             , eMT_direct,       "",     NULL),
    F_D_ITEM("AH_ICV_Length"            , "ah.icv"                  , eMT_transform,    "",     transform_field_for_field_length),
    F_D_ITEM("AH_ICV"                   , "ah.icv"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("InnerIPSrc"               , ""                        , eMT_fromEdt,      "",     transform_field_for_ah(1,"ip.src")),
    F_D_ITEM("InnerIPDst"               , ""                        , eMT_fromEdt,      "",     transform_field_for_ah(1,"ip.dsp")),
    F_D_ITEM("InnerIPProto"             , ""                        , eMT_fromEdt,      "",     transform_field_for_ah(1,"ip.proto")),
    F_D_ITEM("InnerSrcPort"             , ""                        , eMT_fromEdt,      "",     choose_for_ah_field("tcp.srcport","udp.srcport")),
    F_D_ITEM("InnerDstPort"             , ""                        , eMT_fromEdt,      "",     choose_for_ah_field("tcp.dstport","udp.dstport")),
};

ProtoFieldExtractorAh::ProtoFieldExtractorAh():ProtoFieldExtractor("AH", ms_protoFieldDescArray,dimen_of(ms_protoFieldDescArray))
{
}
