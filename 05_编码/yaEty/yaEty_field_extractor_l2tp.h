/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_l2tp.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-17
* 编    码 : licl         '2018-06-26
* 修    改 :.
*************.***************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _YAETY_FIELD_EXTRACTOR_L2TP_H_
#define _YAETY_FIELD_EXTRACTOR_L2TP_H_

#include "yaEty_field_extractor.h"

class ProtoFieldExtractorL2tp : public TunnelProtoFieldExtractor
//class ProtoFieldExtractorL2tp : public ProtoFieldExtractor
{
public:
    ProtoFieldExtractorL2tp();

public:
    //virtual bool ShouldExtractThisFrame(epan_dissect_t *edit);
    //virtual bool ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter);
};

#endif /* _YAETY_FIELD_EXTRACTOR_L2tp_H_ */
