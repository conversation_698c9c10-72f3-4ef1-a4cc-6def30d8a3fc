/****************************************************************************************
 * 文 件 名 : yaEty_proto_field_info.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-14
* 编    码 : zhengsw      '2018-05-14
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _YAETY_PROTO_FIELD_H_
#define _YAETY_PROTO_FIELD_H_

#include <string>

class ProtoFieldsInfo
{
public:
    ProtoFieldsInfo(const char *strProtoName);
    ~ProtoFieldsInfo();

public:
    int SetProtoFieldValueByIndex(uint index);
    ProtoField *GetProtoFieldByIndex(uint index);

private:
    std::string              strProtoName_;
    std::vector<void *>      vecFields_;
    ProtoFieldDescKeeper     *pProtoFieldDescKeeper_;
};

#endif /* _YAETY_PROTO_FIELD_H_ */
