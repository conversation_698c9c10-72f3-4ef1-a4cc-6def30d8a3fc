﻿/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_snmp.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-10-08
* 编    码 : zhangsx      '2018-10-08
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include <algorithm>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_snmp.h"
#include "yaEty_content_reassembly_share_mem.h"

//*
#define SNMP_FIELD_PROPERTY(NO)       \
        F_D_ITEM("O_Name"#NO                     , ""                                , eMT_fromEdt,       "",     transform_for_snmp_property((NO),"snmp.name")),\
        F_D_ITEM("O_Syntax"#NO                   , ""                                , eMT_fromEdt,       "",     transform_for_snmp_property_syntax((NO),"snmp.name")),\
        F_D_ITEM("O_Value"#NO                    , ""                                , eMT_fromEdt,       "",     transform_for_snmp_property_value((NO),"snmp.name"))
/*/
#define SNMP_FIELD_PROPERTY(NO)       \
        F_D_ITEM("O_Name"#NO                     , "snmp.name"                       , eMT_fromEdt,       "",     transform_for_snmp_property((NO),"snmp.name")),\
        F_D_ITEM("O_Syntax"#NO                   , ""                                , eMT_fixed,         "",     NULL),\
        F_D_ITEM("O_Value"#NO                    , "snmp.value"                      , eMT_fixed,         "",     NULL)
//*/

#define SNMP_GROUP_PROPERTY(a,b,c,d,e)    \
        SNMP_FIELD_PROPERTY(a),           \
        SNMP_FIELD_PROPERTY(b),           \
        SNMP_FIELD_PROPERTY(c),           \
        SNMP_FIELD_PROPERTY(d),           \
        SNMP_FIELD_PROPERTY(e)

static std::string transform_field_for_spfield(epan_dissect_t *edt, const field_info *pFinfo);

/*****************************************************************
*Function    :transform_for_snmp_property
*Description :获取snmp协议中各属性名称
*Input       :epan_dissect_t， field_info, int, const char *
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
struct transform_for_snmp_property
{
public:
    transform_for_snmp_property(int num, const char * field_name)
        : _num(num), _field_name(field_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        int field_id = proto_registrar_get_id_byname(_field_name);

        GPtrArray *finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);

        if (NULL == finfos_array)
        {   // 没有这组值，或者 index 出错？
            header_field_info *hfinfo = proto_registrar_get_byname(_field_name);
            if (!hfinfo)
            {
                return "";
            }
            if (hfinfo->same_name_prev_id > -1)
            {
                field_id = hfinfo->same_name_prev_id;
                finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);
            }
            else
            {
                return "";
            }
        }

        if (!(_num < g_ptr_array_len(finfos_array)))
        {
            return "";
        }

        field_info *pfinfo = (field_info *)g_ptr_array_index(finfos_array, _num);

        std::string && strRst = ety_ws_get_node_field_value(pfinfo, edt);

        return strRst;
    }

    int _num;
    const char *_field_name;
};

/*****************************************************************
*Function    :transform_for_snmp_property_syntax
*Description :获取snmp协议中各属性数据类型
*Input       :epan_dissect_t， field_info, int, const char *
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
struct transform_for_snmp_property_syntax : public transform_for_snmp_property
{
public:
    transform_for_snmp_property_syntax(int num, const char * field_name)
        : transform_for_snmp_property(num,field_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        proto_node *pnode = NULL;//用来获取传入参数对应结点的下一个结点(类型不确定)

        int field_id = proto_registrar_get_id_byname(_field_name);

        GPtrArray *finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);

        if (NULL == finfos_array)
        {   // 没有这组值，或者 index 出错？
            header_field_info *hfinfo = proto_registrar_get_byname(_field_name);
            if (NULL == hfinfo)
            {
                return "";
            }
            if (hfinfo->same_name_prev_id > -1)
            {
                field_id = hfinfo->same_name_prev_id;
                finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);
            }
            else
            {
                return "";
            }
        }

        if (!(_num < g_ptr_array_len(finfos_array)))
        {
            return "";
        }

        field_info *pfinfo = (field_info *)g_ptr_array_index(finfos_array, _num);

        if (pfinfo->pnode)
        {
            pnode = pfinfo->pnode;
            if (pnode->next)
            {
                pfinfo = pnode->next->finfo;
            }
        }

        return transform_field_for_spfield(edt, pfinfo);
    }
};

/*****************************************************************
*Function    :transform_for_snmp_property_value
*Description :获取snmp协议中各属性值
*Input       :epan_dissect_t, field_info, int, const char *
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
struct transform_for_snmp_property_value : public transform_for_snmp_property
{
public:
    transform_for_snmp_property_value(int num, const char * field_name)
        : transform_for_snmp_property(num, field_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        proto_node *pnode = NULL;

        int field_id = proto_registrar_get_id_byname(_field_name);

        GPtrArray *finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);

        if (NULL == finfos_array)
        {   // 没有这组值，或者 index 出错？
            header_field_info *hfinfo = proto_registrar_get_byname(_field_name);
            if (NULL == hfinfo)
            {
                return "";
            }
            if (hfinfo->same_name_prev_id > -1)
            {
                field_id = hfinfo->same_name_prev_id;
                finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);
            }
            else
            {
                return "";
            }
        }

        if (!(_num < g_ptr_array_len(finfos_array)))
        {
            return "";
        }

        field_info *pfinfo = (field_info *)g_ptr_array_index(finfos_array, _num);

        if (pfinfo->pnode)
        {
            pnode = pfinfo->pnode;
            if (pnode->next)
            {
                pfinfo = pnode->next->finfo;
            }
        }
        
        std::string && strRst = ety_ws_get_node_field_value(pfinfo, edt);

        return strRst;
    }
};

/*****************************************************************
*Function    :transform_field_for_PDU_Type
*Description :snmp type (从源码所在文件内的局部变量中获取)
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_PDU_Type(epan_dissect_t *edt, const field_info *pFinfo)
{
    int tds_type = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));

    const char *pString = try_val_to_str(tds_type, (const value_string *)(pFinfo->hfinfo->strings));

    if (NULL == pString)
    {
        return "";
    }

    return std::string(pString);
}

/*****************************************************************
*Function    :transform_field_for_spfield
*Description :获取字段值对应的字符串
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_spfield(epan_dissect_t *edt, const field_info *pFinfo)
{
    std::string strRes = "";
    if (NULL != pFinfo && NULL != pFinfo->hfinfo)
    {
        std::string && t_str = std::string(pFinfo->hfinfo->name);

        if (t_str.find('('))
        {
            strRes = std::string(&t_str.c_str()[t_str.find_first_of('(')], &t_str.c_str()[t_str.find_first_of(')')]);
            std::size_t && t_pos = strRes.find_first_of("(");
            strRes = strRes.substr(t_pos + 1);
        }
        
    }

    return strRes;
}


static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort"                      , "udp.srcport"                         , eMT_direct,       "",      NULL),
    F_D_ITEM("DstPort"                      , "udp.dstport"                         , eMT_direct,       "",      NULL),
    F_D_ITEM("C2S"                          , ""                                    , eMT_fixed,        "",      NULL),
    F_D_ITEM("Proto"                        , "ip.proto"                            , eMT_direct,       "",      NULL),
    F_D_ITEM("TTL"                          , "ip.ttl"                              , eMT_direct,       "",      NULL),
                                                                                                         
    F_D_ITEM("Version"                      , "snmp.version"                        , eMT_direct,       "",      NULL),
    F_D_ITEM("Community"                    , "snmp.community"                      , eMT_direct,       "",      NULL),
    F_D_ITEM("PDU_Type"                     , "snmp.data"                           , eMT_transform,    "",      transform_field_for_PDU_Type),
    F_D_ITEM("Request_ID"                   , "snmp.request_id"                     , eMT_direct,       "",      NULL),
    F_D_ITEM("Error"                        , "snmp.error_status"                   , eMT_direct,       "",      NULL),
    F_D_ITEM("Error_Index"                  , "snmp.error_index"                    , eMT_direct,       "",      NULL),
    F_D_ITEM("enterprise"                   , "snmp.enterprise"                     , eMT_direct,       "",      NULL),
    F_D_ITEM("agent_addr"                   , "snmp.agent_addr"                     , eMT_direct,       "",      NULL),
    F_D_ITEM("generic_trap"                 , "snmp.generic_trap"                   , eMT_direct,       "",      NULL),
    F_D_ITEM("specific_trap"                , "snmp.specific_trap"                  , eMT_direct,       "",      NULL),
    F_D_ITEM("time_stamp"                   , "snmp.time_stamp"                     , eMT_direct,       "",      NULL),
    F_D_ITEM("identifier"                   , "snmp.identity"                       , eMT_direct,       "",      NULL),
    F_D_ITEM("context_name"                 , "snmp.contextName"                    , eMT_direct,       "",      NULL),
    F_D_ITEM("context_engine"               , "snmp.contextEngineID"                , eMT_direct,       "",      NULL),
    F_D_ITEM("msg_digest"                   , ""                                    , eMT_fixed,        "",      NULL),
    F_D_ITEM("msg_salt"                     , ""                                    , eMT_fixed,        "",      NULL),
    F_D_ITEM("user_secname"                 , "snmp.msgUserName"                    , eMT_direct,       "",      NULL),
    F_D_ITEM("user_auth_proto"              , ""                                    , eMT_fixed,        "",      NULL),
    F_D_ITEM("user_auth_key"                , ""                                    , eMT_fixed,        "",      NULL),
    F_D_ITEM("user_priv_proto"              , ""                                    , eMT_fixed,        "",      NULL),
    F_D_ITEM("user_priv_key"                , ""                                    , eMT_fixed,        "",      NULL),
    F_D_ITEM("engine_boots"                 , "snmp.msgAuthoritativeEngineBoots"    , eMT_direct,       "",      NULL),
    F_D_ITEM("engine_time"                  , "snmp.msgAuthoritativeEngineTime"     , eMT_direct,       "",      NULL),
    F_D_ITEM("engine_max_msg_size"          , "snmp.msgMaxSize"                     , eMT_direct,       "",      NULL),
    F_D_ITEM("engine_id"                    , "snmp.msgAuthoritativeEngineID"       , eMT_direct,       "",      NULL),
    F_D_ITEM("Total_OID"                    , "snmp.value.oid"                      , eMT_direct,       "",      NULL),
    SNMP_GROUP_PROPERTY(0,1,2,3,4),
    SNMP_GROUP_PROPERTY(5,6,7,8,9),
    SNMP_GROUP_PROPERTY(10,11,12,13,14),
    SNMP_GROUP_PROPERTY(15,16,17,18,19),
    SNMP_GROUP_PROPERTY(20,21,22,23,24),
    SNMP_GROUP_PROPERTY(25,26,27,28,29),
    SNMP_FIELD_PROPERTY(30),
    SNMP_FIELD_PROPERTY(31)
};

ProtoFieldExtractorSnmp::ProtoFieldExtractorSnmp() :ProtoFieldExtractor("SNMP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}
