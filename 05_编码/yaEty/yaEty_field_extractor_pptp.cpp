/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_radius.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-14
* 编    码 : licl         '2018-06-21
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <iconv.h>

#include <algorithm>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_pptp.h"
#include <epan/value_string.h>

#define CNTRL_REQ       0x01
#define CNTRL_REPLY     0x02
#define STOP_REQ        0x03
#define STOP_REPLY      0x04
#define ECHO_REQ        0x05
#define ECHO_REPLY      0x06
#define OUT_REQ         0x07
#define OUT_REPLY       0x08
#define IN_REQ          0x09
#define IN_REPLY        0x0A
#define IN_CONNECTED    0x0B
#define CLEAR_REQ       0x0C
#define DISC_NOTIFY     0x0D
#define ERROR_NOTIFY    0x0E
#define SET_LINK        0x0F

/*****************************************************************
*Function    :transformFieldForMessageType
*Description :转换PPTP MessageType
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :std::string
*Others      :none
*****************************************************************/
std::string transformFieldForMapValue2String(epan_dissect_t *edt, const field_info *pFinfo)
{
    int lValue = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));
    const char *pString = try_val_to_str(lValue, (const value_string *)(pFinfo->hfinfo->strings));
    if(NULL == pString)
    {
        return "";
    }
    std::string aString(pString);
    return  aString;
}

/*****************************************************************
*Function    :transformFieldForPPTPVersion
*Description :转换PPTP version
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :std::string
*Others      :none
*****************************************************************/
std::string transformFieldForPPTPVersion(epan_dissect_t *edt, const field_info *pFinfo)
{
    char szVersion[200];
    if (fvalue_type_ftenum(const_cast<fvalue_t *>(&pFinfo->value)) != FT_UINT16)
    {
        return "";
    }
    if(NULL != pFinfo->rep)
    {
        std::string aString(pFinfo->rep->representation);
        return aString;
    }
    return "";
}

/*****************************************************************
*Function    :transform_field_for_C2S
*Description :PPTP C2S
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :std::string
*Others      :none
*****************************************************************/
std::string transformFieldForC2S(epan_dissect_t *edt, const field_info *pFinfo)
{

    if (fvalue_type_ftenum(const_cast<fvalue_t *>(&pFinfo->value)) != FT_UINT16)
    {
        return "";
    }

    int Type = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));
    switch (Type)
    {
        case CNTRL_REQ:
        case STOP_REQ:
        case ECHO_REQ:
        case OUT_REQ:
        case IN_REQ:
        case CLEAR_REQ:
            return "C2S";
        case CNTRL_REPLY:
        case STOP_REPLY:
        case ECHO_REPLY:
        case OUT_REPLY:
        case IN_REPLY:
            return "S2C";
        default:
            return "";
    }
}

static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // 10 个 RTL 标签头
    F_D_ITEM_RTL_10(),

    // 27 个通用字段头
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    F_D_ITEM("SrcPort"                            , "tcp.srcport"                                , eMT_direct,    "",      NULL),
    F_D_ITEM("DstPort"                            , "tcp.dstport"                                , eMT_direct,    "",      NULL),
    F_D_ITEM("C2S"                                , "pptp.control_message_type"                  , eMT_transform, "",      transformFieldForC2S),
    F_D_ITEM("Proto"                              , "ip.proto"                                   , eMT_direct,    "",      NULL),
    F_D_ITEM("TTL"                                , "ip.ttl"                                     , eMT_direct,    "",      NULL),
    F_D_ITEM("pptpVersion"                        , "pptp.protocol_version"                      , eMT_transform, "",      transformFieldForPPTPVersion),
    F_D_ITEM("controlMessageType"                 , "pptp.control_message_type"                  , eMT_transform, "",      transformFieldForMapValue2String),

    F_D_ITEM("sccrqFramingCapabilities"           , "pptp.framing_capabilities"                  , eMT_transform, "",      transformFieldForMapValue2String),
    F_D_ITEM("sccrqBearerCapabilities"            , "pptp.bearer_capabilities"                   , eMT_transform, "",      transformFieldForMapValue2String),
    F_D_ITEM("sccrqMaximumChannels"               , "pptp.maximum_channels"                      , eMT_direct,    "",      NULL),
    F_D_ITEM("sccrqFirmwareRevision"              , "pptp.firmware_revision"                     , eMT_direct,    "",      NULL),
    F_D_ITEM("sccrqHostName"                      , "pptp.host_name"                             , eMT_direct,    "",      NULL),
    F_D_ITEM("sccrqVendorName"                    , "pptp.vendor_name"                           , eMT_direct,    "",      NULL),

    F_D_ITEM("sccrpControlResult"                 , "pptp.control_result"                        , eMT_transform, "",      transformFieldForMapValue2String),
    F_D_ITEM("sccrpError"                         , "pptp.error"                                 , eMT_transform, "",      transformFieldForMapValue2String),
    F_D_ITEM("sccrpFramingCapabilities"           , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("sccrpBearerCapabilities"            , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("sccrpMaximumChannels"               , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("sccrpFirmwareRevision"              , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("sccrpHostName"                      , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("sccrpVendorName"                    , NULL                                         , eMT_fixed,     "",      NULL),

    F_D_ITEM("ocrqCallID"                         , "pptp.call_id"                               , eMT_direct,    "",      NULL),
    F_D_ITEM("ocrqCallSeqNum"                     , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("ocrqMinimumBps"                     , "pptp.minimum_bps"                           , eMT_direct,    "",      NULL),
    F_D_ITEM("ocrqMaximumBps"                     , "pptp.maximum_bps"                           , eMT_direct,    "",      NULL),
    F_D_ITEM("ocrqBearerType"                     , "pptp.bearer_type"                           , eMT_transform, "",      transformFieldForMapValue2String),
    F_D_ITEM("ocrqFramingType"                    , "pptp.framing_type"                          , eMT_transform, "",      transformFieldForMapValue2String),
    F_D_ITEM("ocrqPktRecvWindowSize"              , "pptp.packet_receive_window_size"            , eMT_direct,    "",      NULL),
    F_D_ITEM("ocrqPktProcessingDelay"             , "pptp.packet_processing_delay"               , eMT_direct,    "",      NULL),
    F_D_ITEM("ocrqPhoneNumLen"                    , "pptp.phone_number_length"                   , eMT_direct,    "",      NULL),
    F_D_ITEM("ocrqPhoneNum"                       , "pptp.phone_number"                          , eMT_direct,    "",      NULL),
    F_D_ITEM("ocrqSubaddress"                     , "pptp.subaddress"                            , eMT_direct,    "",      NULL),
    F_D_ITEM("ocrpCallID"                         , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("ocrpPeerCallID"                     , "pptp.peer_call_id"                          , eMT_direct,    "",      NULL),

    F_D_ITEM("ocrpOutCallReqResult"               , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("ocrpOutCallError"                   , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("ocrpOutCallCause"                   , NULL                                         , eMT_fixed,     "",      NULL),

    F_D_ITEM("ocrp_pptp_connect_speed"            , "pptp.connect_speed"                         , eMT_direct,    "",      NULL),
    F_D_ITEM("ocrpPktRecvWindowSize"              , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("ocrpPktProcessingDelay"             , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("ocrpPhysicalChannelID"              , "pptp.physical_channel_id"                   , eMT_direct,    "",      NULL),

    F_D_ITEM("icrqCallID"                         , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("icrqCallSerialNumber"               , "pptp.call_serial_number"                    , eMT_direct,    "",      NULL),
    F_D_ITEM("icrqBearerType"                     , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("icrq_pptp_physical_channel_id"      , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("icrqDialedNumberLen"                , "pptp.dialed_number_length"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("icrqDialingNumberLen"               , "pptp.dialing_number_length"                 , eMT_direct,    "",      NULL),
    F_D_ITEM("icrqDialedNumber"                   , "pptp.dialed_number"                         , eMT_direct,    "",      NULL),
    F_D_ITEM("icrqDialingNumber"                  , "pptp.dialing_number"                        , eMT_direct,    "",      NULL),
    F_D_ITEM("icrqSubaddress"                     , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("icrpCallID"                         , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("icrpPeerCallID"                     , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("icrpIncomingCallReqResult"          , "pptp.in_result"                             , eMT_transform, "",      transformFieldForMapValue2String),
    F_D_ITEM("icrpIncomingCallReqError"           , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("icrpPktRecvWindowSize"              , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("icrpTransitDelay"                   , NULL                                         , eMT_fixed,     "",      NULL),
};

ProtoFieldExtractorPptp::ProtoFieldExtractorPptp():ProtoFieldExtractor("PPTP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray)) // 从 proto_register_protocol 可查看注册的是什么
{
}

