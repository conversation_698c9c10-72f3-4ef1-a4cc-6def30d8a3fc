/* frame_tvbuff.h
 * Implements a tvbuff for frame
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.
 */

#ifndef __FRAME_TVBUFF_H__
#define __FRAME_TVBUFF_H__
#include <wiretap/wtap.h>
#include <cfile.h>
#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */


void frame_tvbuff_set_capture_file(capture_file *pCapFile);

extern tvbuff_t *frame_tvbuff_new(const frame_data *fd, const guint8 *buf);

extern tvbuff_t *frame_tvbuff_new_buffer(const frame_data *fd, Buffer *buf);

extern tvbuff_t *file_tvbuff_new(const frame_data *fd, const guint8 *buf);

extern tvbuff_t *file_tvbuff_new_buffer(const frame_data *fd, Buffer *buf);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __FRAME_TVBUFF_H__ */

/*
 * Editor modelines  -  http://www.wireshark.org/tools/modelines.html
 *
 * Local variables:
 * c-basic-offset: 8
 * tab-width: 8
 * indent-tabs-mode: t
 * End:
 *
 * vi: set shiftwidth=8 tabstop=8 noexpandtab:
 * :indentSize=8:tabSize=8:noTabs=false:
 */
