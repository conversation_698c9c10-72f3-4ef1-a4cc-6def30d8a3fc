﻿/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_bgp.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-09-14
* 编    码 : zhangsx      '2018-09-14
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include <iostream>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"

#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_bgp.h"
#include "yaEty_content_reassembly_share_mem.h"


/*****************************************************************
*Function    :transform_field_for_next_hop_length
*Description :BGP next_hop length
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_next_hop_length(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (nullptr ==  pFinfo)
    {
        return "";
    }
    return std::to_string(pFinfo-> length);
}

/*****************************************************************
*Function    :transform_field_for_spfield
*Description :获取字段值对应的字符串
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_spfield(epan_dissect_t *edt, const field_info *pFinfo)
{
    std::string strRes = "";
    if (NULL != pFinfo->rep)
    {
        std::string && t_str = std::string(pFinfo->rep->representation);
        std::size_t && t_pos = t_str.find_first_of(":");
        strRes = t_str.substr(t_pos + 1);
    }

    return strRes;
}

/*****************************************************************
*Function    :bgp_as_groupby_order
*Description :将bgp协议as字段信息按组划分
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
struct bgp_as_groupby_order
{
public:
    bgp_as_groupby_order(const int type, const char* group_name, const char* field_name)
    :_type(type),_field_name(field_name), _group_name(group_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)//pFinfo是最终要显示的数据来源
    {
        field_info* p_finfo         = nullptr;
        field_info* d_finfo         = nullptr;
        GPtrArray * finfos_array    = nullptr;
        proto_node* p_node         = nullptr;

        std::string rst = "";
        
        std::vector<proto_node *> field_state;
     
        //获取类型数组结点
        int field_id = proto_registrar_get_id_byname(_group_name);
        if (field_id > -1)
        {
            finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);
        }

        if (nullptr == finfos_array)
        {
            return "";
        }

        int parent_size = g_ptr_array_len(finfos_array);
        //获取完毕，接下来是匹配类型
        for (int i = 0; i < parent_size; ++i)
        {
            p_finfo = (field_info*)g_ptr_array_index(finfos_array, i);

            int&& temp_type = fvalue_get_uinteger(const_cast<fvalue_t *>(&p_finfo->value));

            if (temp_type == _type || temp_type == 2)
            {
                if (p_finfo->pnode->next != nullptr)
                {
                    field_state.push_back(p_finfo->pnode->next->next);//将类型符合的输出元素父节点状态记录入vector.
                }
            }
        }
        //匹配记录完毕，接下来准备比对以便调整输出格式
        if (field_state.size() == 0)
        {
            return "";
        }

        field_id = proto_registrar_get_id_byname(_field_name);
        
        if (field_id > -1)
        {
            finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);
        }

        if (nullptr == finfos_array)
        {
            return "";
        }

        int group_size = g_ptr_array_len(finfos_array);
        
        for (int i = 0; i < field_state.size(); ++i)
        {
            if (field_state[i] == nullptr)
            {
                rst += "0";
            }
            else
            {
                for (p_node = field_state[i]->first_child->next->next; p_node != nullptr; p_node = p_node->next)
                {
                    std::string && str = ety_ws_get_node_field_value(p_node->finfo, edt);

                    rst += str;

                    if (p_node != field_state[i]->last_child && str != "")
                    {
                        rst += ",";
                    }
                }
            }
            if (i < field_state.size() - 1)
            {
                rst += "#";
            }
        }

        return rst;
    }

public:
    const int _type;
    const char* _field_name;
    const char* _group_name;
};




/*****************************************************************
*Function    :bgp_nexthop_groupby_order
*Description :将bgp协议nexthop字段信息按组划分
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
struct bgp_nexthop_groupby_order
{
public:
    bgp_nexthop_groupby_order(const int type, const char* group_name, const char* field_name)
        :_type(type), _field_name(field_name), _group_name(group_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)//pFinfo是最终要显示的数据来源
    {
        field_info* p_finfo = nullptr;
        field_info* d_finfo = nullptr;
        GPtrArray * finfos_array = nullptr;
        proto_node* p_node = nullptr;

        std::string rst = "";

        std::vector<proto_node *> field_state;

        //获取类型数组结点
        int field_id = proto_registrar_get_id_byname(_group_name);
        if (field_id > -1)
        {
            finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);
        }

        if (nullptr == finfos_array)
        {
            return "";
        }

        int parent_size = g_ptr_array_len(finfos_array);
        //获取完毕，接下来是匹配类型
        for (int i = 0; i < parent_size; ++i)
        {
            p_finfo = (field_info*)g_ptr_array_index(finfos_array, i);

            int&& temp_type = fvalue_get_uinteger(const_cast<fvalue_t *>(&p_finfo->value));

            if (temp_type == _type)
            {
                if (p_finfo->pnode->next != nullptr)
                {
                    field_state.push_back(p_finfo->pnode->next->next);//将类型符合的输出元素父节点状态记录入vector.
                }
            }
        }
        //匹配记录完毕，接下来准备比对以便调整输出格式
        if (field_state.size() == 0)
        {
            return "";
        }

        field_id = proto_registrar_get_id_byname(_field_name);

        if (field_id > -1)
        {
            finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);
        }

        if (nullptr == finfos_array)
        {
            return "";
        }

        int group_size = g_ptr_array_len(finfos_array);

        for (int i = 0; i < field_state.size(); ++i)
        {
            if (field_state[i] == nullptr)
            {
                rst += "0";
            }
            else
            {
                for (p_node = field_state[i]; p_node != nullptr; p_node = p_node->next)
                {
                    std::string && str = ety_ws_get_node_field_value(p_node->finfo, edt);

                    rst += str;

                    if (p_node != field_state[i]->parent->last_child && str != "")
                    {
                        std::cout << str << std::endl;
                        rst += ",";
                    }
                }
            }
            if (i < field_state.size() - 1)
            {
                rst += "#";
            }
        }

        return rst;
    }

public:
    const int _type;
    const char* _field_name;
    const char* _group_name;
};

/*****************************************************************
*Function    :bgp_son_message_groupby_order
*Description :将bgp协议字段信息按组划分
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

struct bgp_son_message_groupby_order
{
public:
    bgp_son_message_groupby_order(const char * field_name) 
    :_field_name(field_name)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        std::string rst = "";
        std::string strDiv = "";

        field_info* p_finfo = nullptr;
        GPtrArray * finfos_array = nullptr;
        proto_node* p_node = nullptr;                           //记录父节点信息用

        int field_id = proto_registrar_get_id_byname(_field_name);

        if (field_id > -1)
        {
            finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);
        }

        if (nullptr == finfos_array)
        {
            return rst;
        }

        int group_size = g_ptr_array_len(finfos_array);

        if (!(group_size > 0))
        {
            return rst;
        }

        for (int i = 0; i < group_size; ++i)
        {
            p_finfo = (field_info*)g_ptr_array_index(finfos_array, i);

            
            
            if( !(p_finfo != nullptr && p_finfo->pnode->parent != nullptr && p_finfo->pnode->parent->parent != nullptr) )
            {
                break;
            }

            if (p_finfo->pnode->parent->parent != p_node )
            {
                if (p_node != nullptr)
                {
                    strDiv = "#";
                }
                
                p_node = p_finfo->pnode->parent->parent;
            }
            else
            {
                if (p_node != nullptr)
                {
                    strDiv = ",";
                };
            }
            rst += strDiv;
			
            rst += ety_ws_get_node_field_value(p_finfo, edt);
        }
        return rst;
    }
public:
    const char * _field_name;
};

static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    // 10 个 RTL 标签头
    F_D_ITEM_RTL_10(),

    // 27 个通用字段头
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    /* 通用特定字段(由协议本身确定) */
    F_D_ITEM("SrcPort"                             , "tcp.srcport"                                          , eMT_direct,           "",        NULL),
    F_D_ITEM("DstPort"                             , "tcp.dstport"                                          , eMT_direct,           "",        NULL),
    F_D_ITEM("C2S"                                 , ""                                                     , eMT_fixed,            "",        NULL),
    F_D_ITEM("Proto"                               , "ip.proto"                                             , eMT_direct,           "",        NULL),
    F_D_ITEM("TTL"                                 , "ip.ttl"                                               , eMT_direct,           "",        NULL),
    /* 协议独有定义字段 */
    F_D_ITEM("Version"                             , "bgp.open.version"                                     , eMT_direct,           "",        NULL),
    F_D_ITEM("MsgType"                             , "bgp.type"                                             , eMT_direct,           "",        NULL),
    F_D_ITEM("MyAS"                                , "bgp.open.myas"                                        , eMT_direct,           "",        NULL),
    F_D_ITEM("Identifier"                          , "bgp.open.identifier"                                  , eMT_direct,           "",        NULL),
    F_D_ITEM("Authentication"                      , "bgp.open.opt.param.auth"                              , eMT_direct,           "",        NULL),
    F_D_ITEM("Capabilities"                        , "bgp.cap"                                              , eMT_direct,           "",        NULL),
    F_D_ITEM("WithdrawnRoutesLength"               , "bgp.update.withdrawn_routes.length"                   , eMT_direct,           "",        NULL),
    F_D_ITEM("WithdrawnRoutes"                     , "bgp.withdrawn_prefix"                                 , eMT_direct,           "",        NULL),
    F_D_ITEM("ORIGIN"                              , "bgp.update.path_attribute.origin"                     , eMT_direct,           "",        NULL),
    F_D_ITEM("AS_PATH"                             , ""                                                     , eMT_fromEdt,          "",        bgp_as_groupby_order(2, "bgp.update.path_attribute.type_code", "bgp.update.path_attribute.as_path_segment.as2") ),
//  F_D_ITEM("NEXT_HOP"                            , ""                  									, eMT_fromEdt,          "",        bgp_message_groupby_order( "bgp.update.path_attribute.next_hop")),
    F_D_ITEM("NEXT_HOP"                            , ""                  									, eMT_fromEdt,          "",        bgp_nexthop_groupby_order(3, "bgp.update.path_attribute.type_code", "bgp.update.path_attribute.next_hop")),
    F_D_ITEM("MED"                                 , "bgp.update.path_attribute.multi_exit_disc"            , eMT_direct,           "",        NULL),
    F_D_ITEM("LOCAL_PREF"                          , "bgp.update.path_attribute.local_pref"                 , eMT_direct,           "",        NULL),
    F_D_ITEM("ATOMIC_AGGREGATE"                    , "bgp.update.path_attribute.aggregator_origin"          , eMT_direct,           "",        NULL),
    F_D_ITEM("AGGREGATOR"                          , "bgp.update.path_attribute.aggregator_as"              , eMT_direct,           "",        NULL),
    F_D_ITEM("COMMUNITIES"                         , "bgp.update.path_attribute.communities"                , eMT_transform,        "",        transform_field_for_spfield),
    F_D_ITEM("ORIGINATOR_ID"                       , "bgp.update.path_attribute.originator_id"              , eMT_direct,           "",        NULL),
    F_D_ITEM("CLUSTER_LIST"                        , "bgp.path_attribute.cluster_list"                      , eMT_transform,        "",        transform_field_for_spfield),
    F_D_ITEM("MP_REACH_NLRI_AF"                    , "bgp.update.path_attribute.mp_reach_nlri.afi"          , eMT_direct,           "",        NULL),
    F_D_ITEM("MP_REACH_NLRI_SAF"                   , "bgp.update.path_attribute.mp_reach_nlri.afi"          , eMT_direct,           "",        NULL),
    F_D_ITEM("MP_REACH_NLRI_nexthopLen"            , "bgp.update.path_attribute.mp_reach_nlri.next_hop"     , eMT_transform,        "",        transform_field_for_next_hop_length),
    F_D_ITEM("MP_REACH_NLRI_nexthop"               , "bgp.update.path_attribute.mp_reach_nlri.next_hop"     , eMT_direct,           "",        NULL),
    F_D_ITEM("MP_REACH_NLRI_snpaNum"               , "bgp.update.path_attribute.mp_reach_nlri.nbr_snpa"     , eMT_direct,           "",        NULL),
    F_D_ITEM("MP_REACH_NLRI_snpa"                  , "bgp.update.path_attribute.mp_reach_nlri.snpa"         , eMT_direct,           "",        NULL),
    F_D_ITEM("MP_REACH_NLRI"                       , "bgp.mp_reach_nlri_ipv4_prefix"                        , eMT_direct,           "",        NULL),
    F_D_ITEM("MP_UNREACH_NLRI_AF"                  , "bgp.update.path_attribute.mp_unreach_nlri.afi"        , eMT_direct,           "",        NULL),
    F_D_ITEM("MP_UNREACH_NLRI_SAF"                 , "bgp.update.path_attribute.mp_unreach_nlri.afi"        , eMT_direct,           "",        NULL),
    F_D_ITEM("MP_UNREACH_NLRI"                     , "bgp.mp_unreach_nlri_ipv4_prefix"                      , eMT_direct,           "",        NULL),
    F_D_ITEM("EXTENDED_COMMUNITIES"                , "bgp.ext_communities"                                  , eMT_direct,           "",        NULL),
    F_D_ITEM("AS4_PATH"                            , ""                                                     , eMT_fromEdt,          "",        bgp_as_groupby_order(17, "bgp.update.path_attribute.type_code", "bgp.update.path_attribute.as_path_segment.as4") ),
    F_D_ITEM("AS4_AGGREGATOR"                      , "bgp.update.path_attribute.aggregator_as"              , eMT_direct,           "",        NULL),
    F_D_ITEM("SAFI_SPECIFIC_ATTRIBUTE"             , ""                                                     , eMT_fixed,            "",        NULL),
    F_D_ITEM("TUNNEL_ENCAPSULATION_ATTRIBUTE"      , ""                                                     , eMT_fixed,            "",        NULL),
    /*修改原本SAFI and Tunnel Encapsulation Attribute*/
    F_D_ITEM("TUNNEL_ENCAPSULATION_TLV_LEN"        , "bgp.update.encaps_tunnel_tlv_len"                     , eMT_direct,           "",        NULL),
    F_D_ITEM("TUNNEL_ENCAPSULATION_TLV_TYPE"       , "bgp.update.encaps_tunnel_tlv_type"                    , eMT_direct,           "",        NULL),
    F_D_ITEM("TUNNEL_ENCAPSULATION_SUBTLV_LEN"     , "bgp.update.encaps_tunnel_tlv_sublen"                  , eMT_direct,           "",        NULL),
    F_D_ITEM("TUNNEL_ENCAPSULATION_SUBTLV_TYPE"    , "bgp.update.encaps_tunnel_subtlv_type"                 , eMT_direct,           "",        NULL),
    F_D_ITEM("TUNNEL_ENCAPSULATION_SUBTLV_SESS_ID" , "bgp.update.encaps_tunnel_tlv_subtlv_session_id"       , eMT_direct,           "",        NULL),
    F_D_ITEM("TUNNEL_ENCAPSULATION_SUBTLV_COOKIE"  , "bgp.update.encaps_tunnel_tlv_subtlv_cookie"           , eMT_direct,           "",        NULL),
    F_D_ITEM("TUNNEL_ENCAPSULATION_SUBTLV_GRE_KEY" , "bgp.update.encaps_tunnel_tlv_subtlv_gre_key"          , eMT_direct,           "",        NULL),
    F_D_ITEM("TUNNEL_ENCAPSULATION_SUBTLV_COLOR_V" , "bgp.update.encaps_tunnel_tlv_subtlv_color_value"      , eMT_direct,           "",        NULL),
    F_D_ITEM("TUNNEL_ENCAPSULATION_SUBTLV_BLK_LEN" , "bgp.update.encaps_tunnel_tlv_subtlv_lb_block_length"  , eMT_direct,           "",        NULL),
    
    F_D_ITEM("PMSI_TUNNEL_ATTRIBUTE"               , "bgp.update.path_attribute.pmsi"                       , eMT_direct,           "",        NULL),
    F_D_ITEM("LINK_STATE_ATTRIBUTE"                , "bgp.update.path_attribute.link_state"                 , eMT_direct,           "",        NULL),
    F_D_ITEM("NLRI"                                , "bgp.update.nlri"                                      , eMT_direct,           "",        NULL),
    F_D_ITEM("PREFIX_LENGTH"                       , ""                                                     , eMT_fromEdt,          "",        bgp_son_message_groupby_order("bgp.prefix_length") ),
    F_D_ITEM("NLRI_PREFIX"                         , ""                                                     , eMT_fromEdt,          "",        bgp_son_message_groupby_order("bgp.nlri_prefix") ),
};


ProtoFieldExtractorBgp::ProtoFieldExtractorBgp() :ProtoFieldExtractor("BGP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}

bool ProtoFieldExtractorBgp::ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter)
{
    return true;
}
