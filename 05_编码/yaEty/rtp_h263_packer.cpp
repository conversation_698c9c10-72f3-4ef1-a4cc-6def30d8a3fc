#include "rtp_h263_packer.h"
#include <algorithm>
#include <arpa/inet.h>

struct H263Header
{
    H263Header(uint16_t h263Header)
        : data(h263Header)
    {
    }

    H263NaluType_e getType()
    {
      if(data == 4)
        return EM_H263_PSC;
      return EM_H263_no_PSC;
    }
    uint16_t data;
};


RtpH263Unpacker::RtpH263Unpacker(onGotH263Nalu_callback_t callback, void *userdata)
    : onGotNalu_func_(callback)
    , userdata_(userdata)
{
}


int RtpH263Unpacker::enqueueRtpPayload(uint8_t *rtpPayload, int len)
{
    if (rtpPayload == NULL) {
        return 0;
    }
    H263Header header(rtpPayload[0]);

    H263NaluType_e naluType  = header.getType();

    if (naluType == EM_H263_no_PSC)
    { // Single NALU
        onGotNalu_func_(naluType, rtpPayload +2 , len - 2, userdata_);
    }
    else if (naluType == EM_H263_PSC)
    { // STAP-A: Single-time aggregation
        rtpPayload[0] = 0x00;
        onGotNalu_func_(naluType, rtpPayload, len, userdata_);
    }
    else
    {
        // TODO: log error rtp nalu type
    }

   return 0;
}
