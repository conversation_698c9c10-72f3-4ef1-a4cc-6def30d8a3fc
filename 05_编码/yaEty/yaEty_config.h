/****************************************************************************************
 * 文 件 名 : yaEty_config.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-16
* 编    码 : zhengsw      '2018-05-16
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _YAETY_CONFIG_H_
#define _YAETY_CONFIG_H_

#include "c_lang_linkage_start.h"
#include <iniparser/iniparser.h>
#include "c_lang_linkage_end.h"

#include <string>
#include <vector>

#define CFG                     EtyConfig::GetInstance()

typedef const char *            CSTR;

class EtyConfig
{
public:
    static EtyConfig *GetInstance();

public:
    int ParseConfig(const char *configFile, int argc, char *argv[]);

    void showConfigSummary();

public:
    // 注意：
    // 当配置文件不存在，或者 parse 出错时，会返回相应类型的默认值：
    // int 为 0
    // const char * 为 NULL
    // bool 为 false
    template <typename T>
    T GetValueOf(const std::string &strConfigName)
        {
            if (NULL == ini_)
            {
                return T();
            }

            return GetValueOf_inner<T>(toGlobalConfigName(strConfigName));
        }

public:
    int ParseConfigFile(const std::string &strConfigFilePath);

    int ParseCmdLineOpts(int argc, char *argv[]);

public:
    const std::vector<std::string> &GetInputDirList()
        {
            return vecInputDirs_;
        }

    const std::string GetInputDirListStr()
        {
            std::string strList = vecInputDirs_[0];

            for (std::size_t i = 1; i < vecInputDirs_.size(); i++)
            {
                strList += "," + vecInputDirs_[i];
            }

            return strList;
        }

    void SetContentReassemblyDir(const std::string &strContentReassemDir)
    {
        strContentReassemblyDir_ = strContentReassemDir;
    }

    const std::string &GetContentReassemblyDir()
    {
        return strContentReassemblyDir_;
    }

    const std::string &GetAppPathContentReassembly()
    {
        return strAppPathContentReassembly_;
    }

    void SetTblsDir(const std::string &strTblsDir);
    const std::string &GetTblsDir();

    void SetFieldsDir(const std::string &strFieldsDir);
    const std::string &GetFieldsDir();

    void SetDumpDir(const std::string &strDumpsDir);
    const std::string &GetDumpDir();

    void SetTrailerType(const std::string &strTrailerType);
    const std::string &GetTrailerType();
    void SetFullZeroBytes();
    void SetTaskID(const std::string &strTaskID);
    const std::string &GetTaskID();

    void SetTaskIDByFile(const std::string &strTaskFile);
    void SetScanTimeoutRtpMapSecond();
    void EnableLineNo(const bool state);

    const uint32_t GetContentLimit()
    {
        return (const uint32_t)CFG->GetValueOf<int>("content_file_lower_limit");
    }

    const uint32_t GetTblTimeout()
    {
        return (const uint32_t)CFG->GetValueOf<int>("tbl_timeout");
    }
    const uint32_t GetScanTimeoutRtpMapSecond()
    {
         return ScanTimeoutRtpMapSecond_;
    }
    const int GetTblCountLimit()
    {
        constexpr static const int   RTXDR_RECORD_CNT_PERFILE = 2000;

        int ret = (const int)CFG->GetValueOf<int>("tbl_limits");

        if (ret == 0)
            ret = RTXDR_RECORD_CNT_PERFILE;

        return ret;
    }
    const uint32_t GetContentFileMaxSize()
    {
        return (const uint32_t)CFG->GetValueOf<int>("content_file_max_size");
    }
    const bool LineBeOrLe()
    {
        return CFG->GetValueOf<bool>("line_le");
    }

    bool MakeOutputDir();

    bool DoContentReassembly();

    bool FilenameRename()
    {
        return bFilenameRename_;
    }

    bool OnHelpMode()
    {
        return bHelpMode_;
    }
    
    bool ForceProcess()
    {
        return bProcessFileForce_;
    }

    bool AddTaskID()
    {
        return bAddTaskID_;
    }

    bool HasSetTaskFile()
    {
        return bSetTaskIDFile_;
    }

    bool AddLineNo()
    {
        return bAddLineNo_;
    }
    bool isFullZeroBytes(){
      return bFullZeroBytes_;
    }
    bool isCutFile(){
      return bCutFile_;
    }
    void SetProcessSingleFile(const char* file){
      singleProcessFileMode = 1;
      singleProcessFile +=file ;
    }
    bool GetProcessSingleMode()
    {
      return singleProcessFileMode;
    }
    std::string GetSingleProcessFile(){
      return singleProcessFile;
    }
private:
    EtyConfig();
    ~EtyConfig();

private:
    const std::string toGlobalConfigName(const std::string &strConfigName);
    template <typename T>
    T GetValueOf_inner(const std::string &strConfigName);
    int SetOutputDirFromConfig();

private:
    dictionary  *ini_;

private:
    std::string strConfigFilePath_;
    std::string strTblsDir_;
    std::string strContentReassemblyDir_;
    std::string strFieldsDir_;
    std::string strDumpDir_;
    std::string strReassembleDataDir_;
    std::string strAppPathContentReassembly_;
    std::string strTrailerType_;
    std::string strTaskID_;
    std::vector<std::string> vecInputDirs_;
    bool bHelpMode_;
    bool bDoContentReassembly_ = false;
    bool bFilenameRename_;
    bool bProcessFileForce_;
    bool bAddTaskID_ = false;
    bool bAddLineNo_ = false;
    bool bSetTaskIDFile_;
    bool bFullZeroBytes_ = false;
    bool bCutFile_ = false;
    int ScanTimeoutRtpMapSecond_;

    //单文件模式
    std::string singleProcessFile;
    bool singleProcessFileMode;
};

#endif /* _YAETY_CONFIG_H_ */
