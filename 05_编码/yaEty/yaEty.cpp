/****************************************************************************************
 * 文 件 名 : yaEty.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 : yaEty
 * 功    能 : main
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-11
* 编    码 : zhengsw      '2018-05-11
* 修    改 : zhangsx      '2019-01-24
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_config.h"
#include "yaEty_ws_utils.h"
#include "yaEty_utils.h"
#include "yaEty_field_extractor.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_rec_dumper_cap.h"
#include "yaEty_cap_file_processor.h"
#include "yaEty_child_process.h"
#include "yaEty_rtp_stream_keeper.h"
#include "yaEty_voip_stream_keeper.h"

#define CONFIG_FILE "./yaEty.conf"

int main(int argc, char *argv[])
{
    int   lSts    = 0;
    char *appName = argv[0];

    // cmdline opt
    lSts = CFG->ParseConfig(CONFIG_FILE, argc, argv);
    if (lSts < 0                // parse 出错
        || CFG->OnHelpMode())   // help 模式
    {   // -h 选项被打开，help msg 已经由 ParseCmdLineOpts 输出
        return lSts;
    }
    //由配置信息创建输出目录
    CFG->MakeOutputDir();

    CapFileProcessor::RegistSignal();

    // field extractors 注册, 被 TblRecordWriter 依赖，需要先行就绪
    ProtoFieldExtractor::RegisterFieldExtractors();

    ProtoFieldExtractor::SetupDebugFieldDesc();

    // tbl writer 注册
    TblRecordWriter::Register();

    // cap dumper 注册
    CapRecordDumper::Register();

    // wireshark 环境初始化
    wsEnvionment wsEnv(appName, main, CFG->GetValueOf<CSTR>("dissect_options"));

    // 输出 tbl field 文件
    TblRecordWriter::WriteTblFields(CFG->GetFieldsDir());

    // 不开启子进程
    if (CFG->GetValueOf<int>("child_proc_num") == 0)
    {
        // rtp stream keeper
        RtpStreamKeeper::getInstance();

        // voip info
        VoipStreamKeeper * p_voipStreamKeeper = VoipStreamKeeper::getInstance();

        CapFileProcessor::ScanLoop();

        delete VoipStreamKeeper::getInstance();
        return 0;
    }

    // 子进程管理器
    ChildProcKeeper::CreateInstance(CFG->GetValueOf<int>("child_proc_num"),
                                    &CapFileProcessor::ReduceTasks);

    // 开始分裂，子进程去执行 CapFileProcessor::ConsumeTasks
    CPKPER->CreateProcess();

    // 此为父进程执行点，开始扫描文件，创建任务并分发
    CapFileProcessor::ProduceTasks();

    delete VoipStreamKeeper::getInstance();
    return 0;
}
