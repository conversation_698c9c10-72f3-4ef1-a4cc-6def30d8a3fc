/****************************************************************************************
 * 文 件 名 : yaEty_rtp_stream.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-03-13
* 编    码 : root      '2019-03-13
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _YAETY_RTP_STREAM_H_
#define _YAETY_RTP_STREAM_H_

#include "yaEty_config.h"

#include <ui/rtp_media.h>
#include <ui/rtp_stream.h>
#include "rtp_h261_packer.h"
#include "rtp_h263_packer.h"
#include "rtp_h264_packer.h"
#include "yaEty_voip_stream_keeper.h"

#include <string>
#include <queue>
#include <memory>

enum class RtpMediaType
{
    unknown,
    audio,
    video_h264,
    video_h261,
    video_h263,
    raw,
};

typedef struct _rtp_stream_info rtp_stream_info_t;

#if 0
#define DEBUG_LOG(...)        printf(__VA_ARGS__)
#else
#define DEBUG_LOG(...)
#define DEBUG_LOG_(...)       printf(__VA_ARGS__)
#endif

class RtpStreamInfo
{
public:
    RtpStreamInfo(packet_info *pinfo, const struct _rtp_info * rtpInfo);
    RtpStreamInfo(const RtpStreamInfo &);
    ~RtpStreamInfo();

public:
    const rtp_stream_info_t &get() const
    {
        return info_;
    }

    bool operator < (const RtpStreamInfo &right) const;

private:
    int copyFrom(const rtp_stream_info_t &streamInfo);

private:
    rtp_stream_info_t info_;
};

class RtpPacket
{
public:
    RtpPacket(const packet_info *pinfo, const struct _rtp_info *pRtpInfo, double startRelTime);

    ~RtpPacket();

    rtp_packet_t *getPkt()
    {
        return &rtpPkt_;
    }

    uint32_t getPktTimestamp(){
        return pktTimestamp_;
    }
    uint32_t getPktSeq(){
        return pktSeq_;
    }
private:
    rtp_packet_t rtpPkt_;
    uint32_t     pktTimestamp_;
    uint32_t     pktSeq_;

};

class RtpStreamKeeper;

class RtpStream
{
public:
    RtpStream(const rtp_stream_info_t &rtpStreamInfo, packet_info *pinfo, RtpStreamKeeper *keeper);

    ~RtpStream();

    int enqueueRtpPacket(packet_info *pinfo, epan_dissect_t *edt, struct _rtp_info *rtpInfo);

    int tryToDecodeSomeRtpPackets();

    static bool rtpPktCanDecode(struct _rtp_info *info);

    bool hasPktToDecode()
    {
        return !queue_rtpPkt_.empty();
    }

    int getLastEnqueuedPktNum() const
    {
        return lastEnqueuedPktNum_;
    }
    uint8_t setErrFlag(){
      errFlag_ = 1;
    }
    uint8_t getErrFlag(){
      return errFlag_;
    }
    int onGotNalu_callback(uint8_t naluType, uint8_t *nalu, int naluLen);
    int onGotH261Nalu_callback(uint8_t naluType, uint8_t *nalu, int naluLen);
    int onGotH263Nalu_callback(uint8_t naluType, uint8_t *nalu, int naluLen);

    void setTrailerInfo(std::string TrailerInfo) {
      if (TrailerInfo_.size() != 0 && TrailerInfo.size() == 0) {
        // 第一次写过,后续再来的时候为空
        return;
      }
      if (TrailerInfo.size() == 0) {
        // 写入 n个'|'占位
        for (int i = 0; i < GCHFieldsExtractor::get_fields_cnt(); i++) TrailerInfo_ += '|';
      } else {
        // 更新
        TrailerInfo_ = TrailerInfo;
      }
    }
    const char *getTrailerInfo() {
      if (TrailerInfo_.size() > 0) {
        return TrailerInfo_.c_str();
      } else {
        return NULL;
      }
    }

private:
    int decodeRtpPacketsToAudio();
    int decodeRtpPacketsToVideo_h264();
    int decodeRtpPacketsToVideo_h261();
    int decodeRtpPacketsToVideo_h263();
    int outRawRtpPayload();

private: // write to file

    int writeToFile(void *buff, uint32_t len);

    int writeZeroToFile(uint32_t len);
    int writeWavHeader(unsigned sample_rate, unsigned channels, int sample_bytes_);

    int writeWavHeader(const char * file_name, unsigned file_size);

    int resample(rtp_packet_t *rtp_packet, unsigned sample_rate,     uint64_t resample_buff_len,
                 SAMPLE *decode_buff,      SAMPLE **ppResample_buff, char **write_buff);
    int writeFilePartTbl(int len);
private:
    double                       start_abs_offset_   = 0;
    double                       start_rel_time_     = 0;
    double                       stop_rel_time_      = 0;
    uint64_t                     lastEnqueuedPktNum_ = 0;
    uint32_t                     audio_out_rate_     = 0;
    uint32_t                     cur_in_rate         = 0;
    struct SpeexResamplerState_ *audio_resampler_    = NULL;
    const int                    sample_bytes_;
    ya_rtp_loss_info lossInfo_;
    struct _GHashTable *decoders_hash_ = NULL;
    uint32_t                     fileWrittenLen_;                         //已经写文件的大小
    int                          fileWrittenPart_    = 0;                          //已经写文件后缀
    int                          decode_flag_        = 0;
private:
    rtp_stream_info_t                      rtpStreamInfo_;
    RtpMediaType                           mediaType_;
    std::queue<std::shared_ptr<RtpPacket>> queue_rtpPkt_;
    std::vector<uint8_t>                   rawBuff_;                // 当未能判断出是否能解码时，将 raw data 进行缓存
    std::string                            fileName_;
    std::string                            dirName_;                //记得加配置用
    std::string                            taskID_ = "";            //配置用任务ID
    uint8_t                                errFlag_ = 0;
    std::string                             TrailerInfo_;
private:
    int openFileFp(int sampleRate);
    int closeFileFp();
    std::string      ContentFileName_;
    std::string      ContentFileNameWriting_;
    FILE             *pContentFile_ = NULL;

private: // rtp video
    RtpH264Unpacker  rtpH264Unpacker;
    RtpH263Unpacker  rtpH263Unpacker;
    RtpH261Unpacker  rtpH261Unpacker;

};

#endif /* _YAETY_RTP_STREAM_H_ */
