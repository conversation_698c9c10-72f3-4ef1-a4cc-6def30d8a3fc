/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_sms.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2020-12-08
* 编    码 : zhangsx      '2020-12-08
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2020 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ipv4.h"
#include "epan/strutil.h"

#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_sms.h"

static std::string transform_for_field_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (nullptr == pFinfo || pFinfo->length == 0 )
    {
        return "";
    }

    int lValue = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));          //协议中字段对应值为整数

    const char* pString = try_val_to_str(lValue, (const value_string *)(pFinfo->hfinfo->strings)); //提取对应字符串

    if (nullptr == pString)
    {
        return std::to_string(lValue);
    }
    else
    {
        return std::string(pString);
    }
}


static std::string transform_field_for_sms_time(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (get_first_field_info_from_interesting_fields(edt, "gsm_sms.scts.year") != nullptr)
    {
        std::string sms_year        = get_first_field_value_from_interesting_fields(edt, "gsm_sms.scts.year");
        std::string sms_month       = get_first_field_value_from_interesting_fields(edt, "gsm_sms.scts.month");
        std::string sms_day         = get_first_field_value_from_interesting_fields(edt, "gsm_sms.scts.day");
        std::string sms_hours       = get_first_field_value_from_interesting_fields(edt, "gsm_sms.scts.hour");
        std::string sms_minutes     = get_first_field_value_from_interesting_fields(edt, "gsm_sms.scts.minutes");
        std::string sms_seconds     = get_first_field_value_from_interesting_fields(edt, "gsm_sms.scts.seconds");

        return "20"+ sms_year + "-" + sms_month + "-" + sms_day + " " + sms_hours + ":"+sms_minutes +":"+sms_seconds;
    }
}

static std::string transform_field_for_sms_timezone(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (nullptr != pFinfo && nullptr != pFinfo->hfinfo)
        return std::string(pFinfo->rep->representation);
    return "";
}

static std::string transform_field_for_sms_charset(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (nullptr != pFinfo && nullptr != pFinfo->hfinfo)
    {   
        uint32_t tp_dcs_val = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));
        if (tp_dcs_val > 0)
        {
            field_info *pfinfo = get_first_field_info_from_interesting_fields(edt, "gsm_sms.dcs.character_set");
            return transform_for_field_value(edt, pfinfo);
        }

        if (tp_dcs_val == 0)
            return "GSM 7 bit default alphabet";
    }
    return "";
}


static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    F_D_ITEM("SrcPort"                      , ""                                , eMT_fixed,        "",       NULL),
    F_D_ITEM("DstPort"                      , ""                                , eMT_fixed,        "",       NULL),
    F_D_ITEM("C2S"                          , ""                                , eMT_fixed,        "",       NULL),
    F_D_ITEM("Proto"                        , ""                                , eMT_fixed,        "",       NULL),
    F_D_ITEM("TTL"                          , ""                                , eMT_fixed,        "",       NULL),

    F_D_ITEM("TP-Originating-Address"       , "gsm_sms.tp-oa"                   , eMT_direct,       "",       NULL),
    F_D_ITEM("Time"                         , ""                                , eMT_fromEdt,      "",       transform_field_for_sms_time),
    F_D_ITEM("TimeZone"                     , "gsm_sms.scts.timezone"           , eMT_transform,    "",       transform_field_for_sms_timezone),
    F_D_ITEM("Raw Character Set"            , "gsm_sms.tp-dcs"                  , eMT_transform,    "",       transform_field_for_sms_charset),
    F_D_ITEM("Message"                      , "gsm_sms.sms_text"                , eMT_direct,       "",       NULL),
};

ProtoFieldExtractorSMS::ProtoFieldExtractorSMS() :MatchPrefixProtoFieldExtractor("GSM SMS", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}