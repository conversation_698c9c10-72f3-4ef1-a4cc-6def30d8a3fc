/****************************************************************************************
 * 文 件 名 : yaEty_cap_file_extractor.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 : cap file extractor
 * 功    能 : pcap 文件 extractor
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-12
* 编    码 : zhengsw      '2018-05-12
* 修    改 : zhangsx      '2018-11-12
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_cap_file_processor.h"
#include "yaEty_ws_utils.h"

#include <algorithm>

#include "yaEty_frame_tvbuff_ws_2_6.h"
#include "yaEty_config.h"
#include "yaEty_utils.h"
#include "yaEty_field_extractor.h"
#include "yaEty_pcap_scanner.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_rec_dumper_cap.h"
#include "yaEty_child_process.h"
#include "yaEty_content_reassembly_share_mem.h"
#include "yaEty_voip_stream_keeper.h"
#include "yaEty_rtp_stream_keeper.h"
#include "yaEty_target_info.h"

#include <sys/types.h>
#include <sys/wait.h>

extern __thread const frame_data *ref;
extern __thread frame_data       *prev_dis;
extern __thread frame_data       *prev_cap;

// static
std::string CapFileProcessor::cs_strFilename;
std::string CapFileProcessor::cs_strFrameNum;
bool CapFileProcessor::is_shouldStop = true;

CapFileProcessor::CapFileProcessor(const char *capFileName, int lTheId)
    : strCapFileName_(capFileName)
    , pszCapFileName_(strCapFileName_.c_str())
    , lTheId_(lTheId)
    , line_buffered(FALSE)
    , cum_bytes(0)
    , output_fields(NULL)
{   // 更新当前处理的 pcap 文件名
    cs_strFilename = strCapFileName_;
}

CapFileProcessor::CapFileProcessor(const std::string &strFileName, int lTheId)
    : CapFileProcessor(strFileName.c_str(), lTheId)
{

}

CapFileProcessor::~CapFileProcessor()
{
    reset_tap_listeners();
    epan_free(capFile_.epan);
    cf_close(&capFile_);
}

int CapFileProcessor::openCapFile()
{
    int       err          = 0;
    int       in_file_type = WTAP_TYPE_AUTO;

    capture_session_init(&capSession_, &capFile_);
    cap_file_init(&capFile_);

    if (cf_open(&capFile_, pszCapFileName_, in_file_type, FALSE, &err) != CF_OK)
    {
        return -1;
    }

    frame_tvbuff_set_capture_file(&capFile_);

    // build column info，需要列中的 COL_PROTOTOL 识别 packet 协议类型
    build_column_format_array(&capFile_.cinfo, 10, TRUE);

    return 0;
}

bool CapFileProcessor::extractPkt(const char *pStrProtocol, IProtoFieldExtractor *pExt, epan_dissect_t *edt)
{
    // 获取该协议的 writer
    RecordWriter* pWriter = pExt->GetRecordWriter();
    if (NULL == pWriter)
    {   // log
        return false;
    }

    // 由字段提取器决定是否需要提取该帧
    if (!pExt->ShouldExtractThisFrame(edt))
    {
        return false;
    }

    // 解析平凡字段
    if(!pExt->ExtractPlainFields(edt, pWriter))
    {
        return false;
    }

    // 解析非凡字段
    pExt->ExtractSpecialFields(edt, pWriter);

    // 下一条记录
    pWriter->writeNextRecord();
    return true;
}

void CapFileProcessor::tryToExtractTunnelProtocol(epan_dissect_t *edt, const std::string &strProtoName, TunnelProtoFieldExtractor *protoFieldExt)
{
    std::string strProtoNameLower = strProtoName;
    std::transform(strProtoNameLower.begin(), strProtoNameLower.end(), strProtoNameLower.begin(), tolower);

    if (!proto_is_frame_protocol(edt->pi.layers, strProtoNameLower.c_str()))
    {
        return ;
    }

    extractPkt(strProtoName.c_str(), protoFieldExt, edt);
}

bool CapFileProcessor::onPktDissectedByWs(capture_file   *cfile,
                                          epan_dissect_t *edt,
                                          wtap_rec       *wRec,
                                          const guchar   *recBuff)
{
    IProtoFieldExtractor *pFieldExt = NULL;
    int                  field_cnt = 0;
    proto_tree          *pTree     = edt->tree;
    field_info          *pfinfo    = NULL;
    std::string          strProtocol;

    // 包号
    auto strFrameNum    = get_first_field_value_from_interesting_fields(edt, "frame.number");

    CFG->GetValueOf<bool>("log_verbose") ? printf("pkt [%s] was dissected.\n", strFrameNum.c_str()) : 0;

    // 检测包是否截断
    auto strFrameLen    = get_first_field_value_from_interesting_fields(edt, "frame.len");
    auto strFrameCapLen = get_first_field_value_from_interesting_fields(edt, "frame.cap_len");
    if (strFrameLen != strFrameCapLen)
    {
        CFG->GetValueOf<bool>("log_verbose") ? printf("pkt [%s] was truncated.\n", strFrameNum.c_str()) : 0;
        return false;
    }

    // 判断是否为坏包
    pfinfo = get_first_field_info_from_interesting_fields(edt, "_ws.malformed.expert");
    if (pfinfo)
    {   // TODO:log 坏包
        CFG->GetValueOf<bool>("log_verbose") ? printf("pkt [%s] was a malformed pkt.\n", strFrameNum.c_str()) : 0;
        return false;
    }

    // 更新当前处理的 frameNum
    cs_strFrameNum = strFrameNum;

    // 获取该包的协议种类
    strProtocol = get_main_protocol_name_from_proto_tree(pTree);

    // 是否需要对该 packet 进行 dump
    if (shouldDumpThisPacket(strProtocol))
    {
        return dumpPkt(strProtocol.c_str(),  wRec, recBuff, edt);
    }

    // 1) 判断是否存在隧道协议，并进行隧道协议相关字段提取
    TunnelProtoFieldExtractor::ForeachFieldExtractor(
        std::bind(&CapFileProcessor::tryToExtractTunnelProtocol, this, edt,
                  std::placeholders::_1, std::placeholders::_2));


    // 2) 获取主协议的字段提取器，并进行相关字段提取
    pFieldExt = ProtoFieldExtractor::GetProtoFieldExtractorOf(strProtocol.c_str());
    if (NULL == pFieldExt)
    {   // TODO:log
        return false;
    }

    if (extractPkt(strProtocol.c_str(), pFieldExt, edt) == false)                                                  //add by zhangsx 2018-12-19
    {
        return false;
    }

    // 3) 获取主协议中特定数据格式，并进行相关字段提取
    pFieldExt = SubFormatFieldExtractor::GetProtoFieldExtractorOf(strProtocol.c_str());                     
    if (NULL == pFieldExt)
    {   // TODO:log
        return false;
    }

  
    return extractPkt(strProtocol.c_str(), pFieldExt, edt);
}

#define wth provider.wth

int CapFileProcessor::dissectProtocols()
{
    capture_file *cf                = &capFile_;
    gboolean      out_file_name_res = FALSE;
    char *        save_file         = NULL;
    int           out_file_type     = 0;
    int           max_packet_count  = 0;
    gint64        max_byte_count    = 0;

    int             lSts              = 0;
    gchar          *err_info          = NULL;
    int             err               = 0;
    guint32         framenum          = 0;
    gint64          data_offset       = 0;
    guint           tap_flags         = 0;
    epan_dissect_t *edt               = NULL;
    gboolean        success           = TRUE;

    /* get info from file  */
    wtap_file_get_idb_info(cf->wth);
    wtap_file_encap(cf->wth);

    /* init rec and new a epan_dissect */
    edt = epan_dissect_new(cf->epan, TRUE, TRUE);

    /* read pkt and process */
    while (wtap_read(cf->wth, &err, &err_info, &data_offset))
    {
        framenum++;
        /* yaEty_debug("yaEty: processing packet #%d\n", framenum); */

        /* 归零 */
        reset_epan_mem(cf, edt, TRUE, TRUE);

        /* 包处理 */
        lSts = processPacketSinglePass(cf,
                                       edt,
                                       data_offset,
                                       wtap_get_rec(cf->wth),
                                       wtap_get_buf_ptr(cf->wth),
                                       tap_flags);

        if (lSts)
        {

        }

        if ( (--max_packet_count == 0)
             || (max_byte_count != 0
                 && data_offset >= max_byte_count))
        {
            // yaEty_debug("yaEty: max_packet_count (%d) or "
            //              "max_byte_count (%" G_GINT64_MODIFIER "d/%" G_GINT64_MODIFIER "d) reached",
            //              max_packet_count, data_offset, max_byte_count);
            err = 0; /* This is not an error */
            break;
        }
    }

    if (edt)
    {
        epan_dissect_free(edt);
        edt = NULL;
    }

    draw_tap_listeners(TRUE);
    wtap_close(cf->wth);
    cf->wth = NULL;

    return 0;
}

bool
CapFileProcessor::processPacketSinglePass(capture_file *cf, epan_dissect_t *edt, gint64 offset,
                                           wtap_rec *whdr, const guchar *pd,
                                          guint tap_flags _U_)
{
  frame_data   fdata;
  gboolean     passed = TRUE;

  cf->count++;

  frame_data_init(&fdata, cf->count, whdr, offset, cum_bytes);

  if (edt)
  {
      /* This is the first pass, so prime the epan_dissect_t with the
       * hfids postdissectors want on the first pass.
       */
    prime_epan_dissect_with_postdissector_wanted_hfids(edt);

    frame_data_set_before_dissect(&fdata, &cf->elapsed_time,
                                  &ref, prev_dis);

    if (ref == &fdata) {
      ref_frame = fdata;
      ref = &ref_frame;
    }

    /* == dissecting is here */
    epan_dissect_run_with_taps(edt, cf->cd_t, whdr,
                               frame_tvbuff_new(&cf->provider, &fdata, pd),
                               &fdata, &cf->cinfo);
  }

  if (passed)
  {
      frame_data_set_after_dissect(&fdata, &cum_bytes);
      cf->provider.prev_cap = cf->provider.prev_dis = frame_data_sequence_add(cf->provider.frames, &fdata);

      g_assert(edt);

      onPktDissectedByWs(cf, edt, whdr, pd);

      if (line_buffered)
          fflush(stdout);

      if (ferror(stdout))
      {
        exit(2);
      }
  }

  prev_cap_frame = fdata;
  prev_cap = &prev_cap_frame;

  if (edt)
  {
     epan_dissect_reset(edt);
     frame_data_destroy(&fdata);
  }
  return passed;
}

int CapFileProcessor::ProcessCap(bool bAddSuffixOnDone)
{
    int lSts = 0;
    // std::unique_ptr<GCHFieldsExtractor> gch;

    // 提示信息
    printf("processing %s with the id:%d ...\n", pszCapFileName_, lTheId_);


    struct stat file_state;
    memset(&file_state ,0, sizeof(struct stat));
    if(0 != stat(pszCapFileName_, &file_state) && file_state.st_size == 0)
    {
      printf("processing file not exit or empty %s ...\n", pszCapFileName_);
      return 0;
    }
    //先进行重命名
    // 需要添加后缀，可能因为处理失败，或者即使成功了也要求添加后缀
    std::string  strFileName;
    const char  *pszAddSuffix        = NULL;

    pszAddSuffix = CFG->GetValueOf<CSTR>("rename_suffix");

    // 重命名，防止下次 scan 又扫到它，重复分配
    strFileName = strCapFileName_ + pszAddSuffix;
    lSts        = rename(strCapFileName_.c_str(), strFileName.c_str());
    CHECK_NOR_EXIT(lSts < 0, lSts, "rename error:%s -> %s\n", strCapFileName_.c_str(), strFileName.c_str());

    printf("process one file start (with theId: %d): %s -> %s.\n", lTheId_, pszCapFileName_, strFileName.c_str());

    //更新处理的文件名
    pszCapFileName_ = strFileName.c_str();
    // 打开文件
    lSts = openCapFile();
    if (lSts < 0)
    {
        printf("open file %s error, can not dissect it.\n", pszCapFileName_);
        goto DONE;
    }

    // gch.reset(new GCHFieldsExtractor(strCapFileName_));
    // VoipStreamKeeper::getInstance()->setGCHFieldExtrPointer(gch.get());

    // 内容还原
    reassemblyContent();

    // 协议解析
    dissectProtocols();


DONE:
    // VoipStreamKeeper::getInstance()->setGCHFieldExtrPointer(nullptr);
    VoipStreamKeeper::getInstance()->doVoipClean();
    onCapProcDone(lSts, bAddSuffixOnDone);
    return 0;
}

int CapFileProcessor::reassemblyContent()
{
    int lSts = 0;
    if (CFG->DoContentReassembly())
    {
        // 内容还原
        lSts = runContentReassemblyProgram(CFG->GetContentReassemblyDir(), pszCapFileName_);
        CHECK_NOR_EXIT(lSts < 0, lSts, "content reassembly failed.\n");

        // init 共享内存
        lSts = initProtoContentReassembleyModule();
        CHECK_NOR_EXIT(lSts < 0, lSts, "init share mem error, %s\n", strerror(errno));
    }

    return 0;
}

int CapFileProcessor::initProtoContentReassembleyModule()
{
    static __thread bool s_init_already = false;
    bool                 bSts           = false;

    if (s_init_already)
    {
        return 0;
    }

	CreateCrcTable();

	bSts = DispAttachShareMem(getpid());
	if(!bSts)
    {
        return -1;
	}

    s_init_already = true;
    return 0;
}

int CapFileProcessor::runContentReassemblyProgram(const std::string &strOutDir, const std::string &strPcapFile)
{
    int         lSts             = 0;
    char buffCrCmdline[PATH_MAX] = { 0 };
    std::string strAppDir        = getAppDir();

    pid_t fpid;
    int   childPid;
    char  configDir[512] = {0};
    char  xp_buf[64]     = {0};

    // 确保输出目录存在
    lSts = ensureDirExist(strOutDir.c_str());
    CHECK_NOR_EXIT(lSts < 0, lSts, "");

    // 生成命令行字符串
    /*
    sprintf(buffCrCmdline, "%s -c %s%s -o %s -m pcap -f %s",
            CFG->GetAppPathContentReassembly().c_str(),
            strAppDir.c_str(), "xplico/config/xplico_cli_nc.cfg",
            CFG->GetContentReassemblyDir().c_str(),
            strPcapFile.c_str()
        );*/

    snprintf(xp_buf,64,"%d",getpid());
    sprintf(configDir,"%s%s",strAppDir.c_str(),"xplico/config/xplico_cli_nc.cfg");
    fpid=fork();
    if(fpid<0){
         printf("[debug liugh]create xplico fork error\n");
    }else if(fpid==0){
        if(execl(CFG->GetAppPathContentReassembly().c_str(),"xplico","-c",configDir,
                 "-o",CFG->GetContentReassemblyDir().c_str(),
                 "-p",xp_buf,
                 "-m","pcap","-f", strPcapFile.c_str(),NULL)<0){
            printf("execute xplico error \n");
            exit(-1);
        }
    }else{
        xplico_pid=wait(&childPid);
    }

    return 0;
}

int CapFileProcessor::onCapProcDone(int lProcDone, bool bAddSuffix)
{
    int lSts = 0;

    // 处理完后是否删除文件
    if (CFG->GetValueOf<bool>("del_pcap_after_process"))
    {
        lSts = remove(pszCapFileName_);
        CHECK_NOR_EXIT(lSts, lSts, "remove file error:%s\n", pszCapFileName_);

        printf("process one file done: pcap %s deleted.", pszCapFileName_);
        return 0;
    }

    // 正常处理，明确指明不添加后缀，则放过
    if (lProcDone == 0 && !bAddSuffix)
    {
        printf("process one file done (with theId: %d): %s.\n", lTheId_, pszCapFileName_);
        return 0;
    }
    if (!CFG->FilenameRename())
    {
        printf("process one file done (with theId: %d): %s.\n", lTheId_, pszCapFileName_);
        return 0;
    }
    // 需要添加后缀，可能因为处理失败，或者即使成功了也要求添加后缀
    std::string  strFileName;
    const char  *pszAddSuffix        = NULL;

    // 决定添加何种后缀
    if (lProcDone < 0)
    {
        pszAddSuffix = CFG->GetValueOf<CSTR>("rename_suffix_err");

        // 重命名，防止下次 scan 又扫到它，重复分配
        strFileName = strCapFileName_ + pszAddSuffix;
        lSts        = rename(strCapFileName_.c_str(), strFileName.c_str());
        CHECK_NOR_EXIT(lSts < 0, lSts, "rename error:%s -> %s\n", strCapFileName_.c_str(), strFileName.c_str());

        printf("process one file err (with theId: %d): %s -> %s.\n", lTheId_, pszCapFileName_, strFileName.c_str());
    }

    return 0;
}

int CapFileProcessor::ScanLoop()
{
    // 扫描 pcap 文件
    int      scanTryTimesMax          = CFG->GetValueOf<int>("scan_retry_times");
    int      scanIntervalOnIdle       = CFG->GetValueOf<int>("scan_retry_iterval_in_second");
    int      scanTryTimes             = scanTryTimesMax;
    uint64_t totalWritingFileSizeLast = 0;

    PcapScanner scanner(CFG->GetInputDirList(),
                        CFG->GetValueOf<CSTR>("writing_suffix"),
                        CFG->GetValueOf<CSTR>("scan_ignore_suffixs"),
                        CFG->GetValueOf<CSTR>("rename_suffix"));

    if (!(scanTryTimesMax > -1))
    {
        CapFileProcessor::StopTasks(false);
    }

    if(CFG->GetProcessSingleMode()){
        CapFileProcessor proc(CFG->GetSingleProcessFile());
        proc.ProcessCap(true);           // 在处理完文件后添加后缀，防止再次处理
        return 0;
    }
    do
    {
        if (CapFileProcessor::ShouldStop() && scanTryTimesMax == -1)
        {
            scanTryTimesMax = 1;
        }
        // 扫描 pcap 文件
        scanner.scan();

        // 是否有文件需要处理？
        if (scanner.getPcapFileCnt() > 0)
        {   // 存在需要处理的 pcap 文件
            scanTryTimes = scanTryTimesMax;                                  // 一旦发现有 pcap 或者有 writing 文件，拆除倒计时

            //处理一批，从文件中更新taskid
            if (CFG->AddTaskID()){
                CFG->SetTaskIDByFile(std::string(CFG->GetValueOf<CSTR>("task_file")));
                TblRecordWriter::updateInMapTblTaskID();
            }
            // 发现 pcap 文件则产生新的任务
            scanner.foreachPcapFile([](const char *pszPcapFileName)
                                        {
                                            CapFileProcessor proc(pszPcapFileName);
                                            proc.ProcessCap(true);           // 在处理完文件后添加后缀，防止再次处理
                                            return 0;
                                        });
            continue;           // 继续扫描
        }

        // 没有文件需要处理，是否需要扫描？
        if (!CFG->GetValueOf<bool>("scan_in_input_dir"))
        {   // 不让扫描，没有发现文件需要处理
            printf("there is no pcap file to process and scan_in_input_dir option is off，bye!\n");
            continue;
        }

        // 看来需要扫描，是否有 writing 文件增长？
        if (scanner.getTotalWritingFileSize() == totalWritingFileSizeLast)
        {   // writing 文件没有增长(变化)
            scanTryTimes--;                                                  // 连续一定次数都没有扫描到则结束扫描
            printf("warning : there is no pcap file to process "
                   "and writing file was not grown(total size is %ldbyte),\n"
                   " remain rescan times : %d, go to sleep.\n",
                   totalWritingFileSizeLast,
                   scanTryTimes);
            if (scanTryTimesMax - scanTryTimes == 1)
            {
                CapRecordDumper::revertAllDumpers();
            }
            TblRecordWriter::WriteTblsDone();
            //VoipStreamKeeper::getInstance()->writeVoipTblDone();
            sleep(scanIntervalOnIdle);
            continue;
        }

        // writing 文件在增长
        printf("there is no pcap file to process "
               "but writing file was growing(%ldbyte -> %ldbyte),\n"
               " ety will rescan later, go to sleep.\n",
               totalWritingFileSizeLast,
               scanner.getTotalWritingFileSize());
        totalWritingFileSizeLast = scanner.getTotalWritingFileSize();

        sleep(scanIntervalOnIdle);
    }
    while (CFG->GetValueOf<bool>("scan_in_input_dir") // 需要进行扫描
           && (scanTryTimes > 0) || (scanTryTimesMax == -1));                      // 还有可用的 scanTryTimes

    return 0;
}

int CapFileProcessor::ProduceTasks()
{
    // 扫描 pcap 文件
    int      work_mode                = CFG->GetValueOf<bool>("scan_in_input_dir");
    uint64_t totalWritingFileSizeLast = 0;
    int      childProcIndexToProc     = 0;
    int      childProcNum             = CPKPER->GetChildProcNum();
    int      scanTryTimesMax          = CFG->GetValueOf<int>("scan_retry_times");
    int      scanIntervalOnIdle       = CFG->GetValueOf<int>("scan_retry_iterval_in_second");

    if (!(scanTryTimesMax > -1))
    {
        CapFileProcessor::StopTasks(false);
    }

    int      scanTryTimes             = scanTryTimesMax > -1 ? scanTryTimesMax : 2;

    PcapScanner scanner(CFG->GetInputDirList(),
                        CFG->GetValueOf<CSTR>("writing_suffix"),
                        CFG->GetValueOf<CSTR>("scan_ignore_suffixs"),
                        CFG->GetValueOf<CSTR>("rename_suffix"));               
    do
    {
        if (CapFileProcessor::ShouldStop() && scanTryTimesMax == -1)
        {
            scanTryTimesMax = 1;
        }
        // 扫描 pcap 文件
        scanner.scan();

        // 是否有文件需要处理？
        if (scanner.getPcapFileCnt() > 0)
        {   // 存在需要处理的 pcap 文件
            scanTryTimes = CapFileProcessor::ShouldStop() ? scanTryTimesMax : 2;                                  // 一旦发现有 pcap 或者有 writing 文件，拆除倒计时
            //处理一批，从文件中更新taskid
            if (CFG->AddTaskID()){
                CFG->SetTaskIDByFile(std::string(CFG->GetValueOf<CSTR>("task_file")));
                TblRecordWriter::updateInMapTblTaskID();
            }
            // 发现 pcap 文件则产生新的任务
            CPKPER->pollChildren(1000, [&scanner](int childProcIndex, ST_EtyProcMsg *pMsg, int lMsgLen)
                                           {
                                               if (pMsg->lMsg != EPM_AskForTask)
                                               {
                                                   return lMsgLen;
                                               }

                                               std::string strFileName;
                                               int lSts = scanner.getNextPcapFile(strFileName);
                                               if (lSts < 0)
                                               {   // 已经没有文件了
                                                   return 0;
                                               }

                                               // 授予任务
                                               CPKPER->GrantTaskToChild(childProcIndex, strFileName);
                                               return lMsgLen;
                                           });

            continue;                          // 继续扫描
        }

        // 没有文件需要处理，是否需要扫描？
        if (!CFG->GetValueOf<bool>("scan_in_input_dir"))
        {   // 不让扫描，没有发现文件需要处理
            printf("there is no pcap file to process and scan_in_input_dir option is off，bye!\n");
            continue;
        }

        // 看来需要扫描，是否有 writing 文件增长？
        if (scanner.getTotalWritingFileSize() == totalWritingFileSizeLast)
        {   // writing 文件没有增长(变化)
            scanTryTimes--;                                                  // 连续一定次数都没有扫描到则结束扫描
            /*printf("warning : there is no pcap file to process "
                   "and writing file was not grown(total size is %ldbyte),\n"
                   " remain rescan times : %d, go to sleep.\n",
                   totalWritingFileSizeLast,
                   scanTryTimes);*/

            if (1 == scanTryTimes)
            {   // 快没有机会了，告诉 child 们 让dumper 们快写盘好重新解析
                ST_EtyProcMsg doneMsg = { 0 };
                doneMsg.lMsg          = EPM_AlmostNoTask;

                printf("向 children 广播消息，就快要没有任务了.%d\n");
                CPKPER->BroadcastMsgToChildren((char *)&doneMsg, sizeof doneMsg);
            }
            //TblRecordWriter::WriteTblsDone();
            scanTryTimes = CapFileProcessor::ShouldStop() ? scanTryTimes : 2;
           // VoipStreamKeeper::getInstance()->writeVoipTblDone();
            sleep(scanIntervalOnIdle);
            continue;
        }

        // writing 文件在增长
        scanTryTimes = CapFileProcessor::ShouldStop() ? scanTryTimes : 2;
        printf("there is no pcap file to process "
               "but writing file was growing(%ldbyte -> %ldbyte),\n"
               " ety will rescan later, go to sleep.times :%d\n",
               totalWritingFileSizeLast,
               scanner.getTotalWritingFileSize(),
               scanTryTimes);

        totalWritingFileSizeLast = scanner.getTotalWritingFileSize();
        sleep(scanIntervalOnIdle);
    }
    while ( work_mode > 0 // 需要进行扫描
           && scanTryTimes > 0);                      // 还有可用的 scanTryTimes

    // 看来文件都已经处理完了，告诉 child 们散了吧
    ST_EtyProcMsg doneMsg = { 0 };
    doneMsg.lMsg          = EPM_NoTask;

    printf("向 children 广播消息，已经没有任务.\n");
    CPKPER->BroadcastMsgToChildren((char *)&doneMsg, sizeof doneMsg);

    // wait children
    int childPid = 0;
    for (; ;)
    {
        childPid = waitpid(-1, NULL, WNOHANG);
        if (childPid < 0)
        {   // 已经没有子进程了
            printf("children all exited, parent will exit, bye!\n");
            break;
        }

        if (0 == childPid)
        {   // 没有任何子进程才死去，继续
            // printf("no child status changed.\n");
            sleep(1);
            continue;
        }

        printf("parent detect: child %d exited.\n", childPid);

    }

    return 0;
}

int CapFileProcessor::ReduceTasks(int procIndex)
{
    VoipStreamKeeper * p_voipStreamKeeper = VoipStreamKeeper::getInstance();

    return ConsumeTasks(procIndex);
}

int CapFileProcessor::ConsumeTasks(int procIndex)
{
    int lSts = 0;

    while (true)
    {
        //printf("child proc %d ask for task\n", procIndex);

        // 积极地向 parent 领取任务
        std::string strFileName;
        lSts = CPKPER->AskParentForTask(strFileName);
        if (lSts == EPM_NoTask)
        {   // 没有任务了
            printf("child proc %d :recv 'NoTask' msg, I'm going to exit.\n", procIndex);
            break;
        }
        else if (lSts == EPM_AlmostNoTask)
        {
            printf("child proc %d :recv 'AlmostNoTask' msg, I'm going to revert dumpers.\n", procIndex);
            CapRecordDumper::revertAllDumpers();
            continue;
        }
        else if (lSts < 0)
        {
            printf("child proc %d :parent process exited, there is no task and I'm going to exit too.\n", procIndex);
            break;
        }

        // 开始工作
        CapFileProcessor capProc(strFileName, procIndex);
        capProc.ProcessCap(false);                          // 在处理完文件后不用添加后缀，由 Scanner 处得到时它已经添加了后缀
        TblRecordWriter::WriteTblsDone();
    }

	if (CFG->DoContentReassembly()){
        DispDelShareMem(getpid());  // delete share memory
	}

    //delete VoipStreamKeeper::getInstance();
    return lSts;
}

std::string CapFileProcessor::GetCurrentProcessingPcapFilename()
{
    return cs_strFilename;
}

std::string CapFileProcessor::GetCurrentProcessingFrameNum()
{
    return cs_strFrameNum;
}

bool CapFileProcessor::shouldDumpThisPacket(const std::string &strMainProto)
{
    // 1) 若当前文件名为 xxx.pcap2tbl 则一定不进行 dump，必须得输出 tbl
    // 2) 若配置了该协议需要进行 dump 则由协议判断
    static std::string strSuffixNotDump         = CFG->GetValueOf<CSTR>("filename_not_dump_suffix");
    static std::string strSuffixNotDumpRenamed  = strSuffixNotDump + CFG->GetValueOf<CSTR>("rename_suffix");

    if (strEndwith(cs_strFilename, strSuffixNotDump)
        || strEndwith(cs_strFilename, strSuffixNotDumpRenamed))
    {
        return false;
    }

    // CapRecordDumper 配置决定某类协议是否需要进行 dump
    return CapRecordDumper::shouldDumpPktOfProto(strMainProto);
}

bool CapFileProcessor::dumpPkt(const char *pStrProtocol, wtap_rec *whdr, const guchar *pd, epan_dissect_t *edt)
{
    // get dumper
    CapRecordDumper *pDumper =  CapRecordDumper::getDumperOf(pStrProtocol);
    if (NULL == pDumper)
    {
        return false;
    }

    // get classify fieldvalue
    std::string strFieldValue = get_first_field_value_from_interesting_fields(edt, pDumper->getClassifyField().c_str());

    pDumper->dump(whdr, pd, strFieldValue.c_str());
    return true;
}


int CapFileProcessor::StopTasks(bool isStop)
{
    CapFileProcessor::is_shouldStop = isStop;
    return 0;
}

bool CapFileProcessor::ShouldStop()
{
    return CapFileProcessor::is_shouldStop;
}

void CapFileProcessor::StopRunnig(int sig)
{
    CapFileProcessor::StopTasks(true);
}

int CapFileProcessor::RegistSignal()
{
    signal(SIGUSR1, CapFileProcessor::StopRunnig);
    return 0;
}
