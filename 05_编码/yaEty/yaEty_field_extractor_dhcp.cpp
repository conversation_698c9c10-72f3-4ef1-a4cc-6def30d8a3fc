/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_dhcp.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-08-17
* 编    码 : zhangsx      '2018-08-17
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <iconv.h>

#include <algorithm>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_dhcp.h"
#include "yaEty_content_reassembly_share_mem.h"

/*****************************************************************
*Function    :transform_field_for_C2S
*Description :DHCP C2S
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :std::string
*Others      :none
*****************************************************************/

static std::string transform_field_for_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{

    std::string magic_info = get_first_field_value_from_interesting_fields(edt, "bootp.type");

    if (magic_info == "")
    {
        return "";
    }

    if (magic_info == "1")
    {
        return "C2S";
    }
    if (magic_info == "2")
    {
        return "S2C";
    }

}


/*****************************************************************
*Function    :transform_field_for_magic
*Description :DHCP or BOOTP
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_field_for_magic(epan_dissect_t *edt, const field_info *pFinfo)
{

    std::string magic_info = get_first_field_value_from_interesting_fields(edt, "bootp.cookie");

    if (magic_info == "")
    {
        return "";
    }

    if (magic_info == "************")
    {
        return "DHCP";
    }
    else
    {
        return "BOOTP";
    }

}


static std::string transform_field_for_dhcp(epan_dissect_t *edt, const field_info *pFinfo)
{

    std::string dhcp_type = get_first_field_value_from_interesting_fields(edt, "bootp.option.dhcp");

    if (dhcp_type == "")
    {
        return "";
    }


}


static std::string transformFieldForMapValue2String(epan_dissect_t *edt, const field_info *pFinfo)
{
    int lValue = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));
    const char *pString = try_val_to_str(lValue, (const value_string *)(pFinfo->hfinfo->strings));
    if (NULL == pString)
    {
        return "";
    }
    std::string aString(pString);
    return  aString;
}


static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // 10 个 RTL 标签头
    F_D_ITEM_RTL_10(),

    // 27 个通用字段头
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    /* DHCP字段 */
    F_D_ITEM("SrcPort"                     , "udp.srcport"                           , eMT_direct,           "",        NULL),
    F_D_ITEM("DstPort"                     , "udp.dstport"                           , eMT_direct,           "",        NULL),
    F_D_ITEM("C2S"                         , "bootp.type"                            , eMT_transform,        "",        transform_field_for_C2S),
    F_D_ITEM("Proto"                       , "ip.proto"                              , eMT_direct,           "",        NULL),
    F_D_ITEM("TTL"                         , "ip.ttl"                                , eMT_direct,           "",        NULL),

    F_D_ITEM("MessageType"                 , "bootp.type"                            , eMT_transform,        "",        transformFieldForMapValue2String),//
    F_D_ITEM("HardwareType"                , "bootp.hw.type"                         , eMT_transform,        "",        transformFieldForMapValue2String),//
    F_D_ITEM("HardwareAddrLen"             , "bootp.hw.len"                          , eMT_direct,           "",        NULL),//
    F_D_ITEM("Hops"                        , "bootp.hops"                            , eMT_direct,           "",        NULL),//
    F_D_ITEM("TransID"                     , "bootp.id"                              , eMT_direct,           "",        NULL),//
    F_D_ITEM("Seconds"                     , "bootp.secs"                            , eMT_direct,           "",        NULL),//
    F_D_ITEM("Flags"                       , "bootp.flags"                           , eMT_direct,           "",        NULL),//
    F_D_ITEM("ClientIPaddr"                , "bootp.ip.client"                       , eMT_direct,           "",        NULL),//
    F_D_ITEM("YourIPaddr"                  , "bootp.ip.your"                         , eMT_direct,           "",        NULL),//
    F_D_ITEM("ServerIPaddr"                , "bootp.ip.server"                       , eMT_direct,           "",        NULL),//
    F_D_ITEM("RelayAgentIPaddr"            , "bootp.ip.relay"                        , eMT_direct,           "",        NULL),//
    F_D_ITEM("ClientMacAddr"               , "bootp.hw.mac_addr"                     , eMT_direct,           "",        NULL),//
    F_D_ITEM("Servername"                  , "bootp.server"                          , eMT_direct,           "",        NULL),//
    F_D_ITEM("BootFileName"                , "bootp.file"                            , eMT_direct,           "",        NULL),//
    F_D_ITEM("Magic"                       , "bootp.cookie"                          , eMT_transform,        "",        transform_field_for_magic),//
 // F_D_ITEM("OptionTag"                   , "bootp.option.type"                     , eMT_transform,        "",        NULL),//
 // F_D_ITEM("OptionLength"                , "bootp.option.length"                   , eMT_direct,           "",        NULL),//
    F_D_ITEM("DHCP"                        , "bootp.option.dhcp"                     , eMT_transform,        "",        transformFieldForMapValue2String),//53
    F_D_ITEM("ClientMACAddress"            , "bootp.hw.mac_addr"                     , eMT_direct,           "",        NULL),//61
    F_D_ITEM("DHCPServerIdentifier"        , "bootp.option.dhcp_server_id"           , eMT_direct,           "",        NULL),//54
    F_D_ITEM("ParameterRequestItem"        , "bootp.option.request_list_item"        , eMT_direct,           "",        NULL),//55
    F_D_ITEM("SubnetMask"                  , "bootp.option.subnet_mask"              , eMT_direct,           "",        NULL),//1
    F_D_ITEM("HostName"                    , "bootp.option.hostname"                 , eMT_direct,           "",        NULL),//12
    F_D_ITEM("RequestIP"                   , "bootp.option.requested_ip_address"     , eMT_direct,           "",        NULL),//50
    F_D_ITEM("RenewalTimeValue"            , "bootp.option.renewal_time_value"       , eMT_direct,           "",        NULL),//58
    F_D_ITEM("RebindingTimeValue"          , "bootp.option.rebinding_time_value"     , eMT_direct,           "",        NULL),//59
    F_D_ITEM("IpAddressLeaseTime"          , "bootp.option.ip_address_lease_time"    , eMT_direct,           "",        NULL),//51
    F_D_ITEM("DHCPServerIdentifier"        , "bootp.option.dhcp_server_id"           , eMT_direct,           "",        NULL),//54
    F_D_ITEM("END"                         , "bootp.option.end"                      , eMT_direct,           "",        NULL),//255
};


ProtoFieldExtractorDhcp::ProtoFieldExtractorDhcp() :ProtoFieldExtractor("DHCP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}
