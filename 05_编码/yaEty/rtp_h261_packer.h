#ifndef RTP_H261_PACKER_H
#define RTP_H261_PACKER_H

#include <vector>
#include <stdint.h>

enum H261NaluType_e {
    EM_H261_no_PSC               = 0,  //  帧首部
    EM_H261_PSC                  = 1,  //  帧中间分片
};

typedef int (*onGotH261Nalu_callback_t)(H261NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata);

// rtp h261 解包器
// 输入为 rtp 负载，输出为 nalu(回调)
class RtpH261Unpacker
{
public:
    RtpH261Unpacker(onGotH261Nalu_callback_t callback, void *userdata);

public:
    int enqueueRtpPayload(uint8_t *rtpPayload, int len);

private:
    onGotH261Nalu_callback_t onGotNalu_func_;
    void *userdata_;
};

#endif /* RTP_H261_UNPACKER_H */
