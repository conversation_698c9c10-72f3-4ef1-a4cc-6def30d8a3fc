#pragma once

#define RT (1)
#define HW (2)

struct _trailer
{
public:
    uint16_t trailer_type;
    std::string imsi;
    std::string imei;
    std::string msisdn;
    std::string teid;
    std::string tac;

    /* hw special field */
    std::string hw_ecgi;
    std::string hw_sac;
    std::string hw_lac;
    std::string hw_meid;
    std::string hw_ecgi_mnc;
    std::string hw_gre_key;
    std::string hw_bsid;
    std::string hw_cellid;
    std::string hw_operator;
    std::string hw_nettype;
    std::string hw_apn;
    std::string hw_accont;
    std::string hw_ncode;
    std::string hw_esn;
    std::string hw_tai;

    /* rt special field */
    std::string rt_plmnid;
    std::string rt_uli;
    std::string rt_basetype;
    std::string rt_outsrc_ip;
    std::string rt_outdst_ip;

    std::string src_ip;
    std::string dst_ip;
    std::string src_port;
    std::string dst_port;

    std::string proto;
    std::string ttl;

    std::string capdate;
    std::string Q931CallingPartyNumber;
    std::string Q931CalledPartyNumber;
    std::string Linfo_buf;
public:
    void cleanAll()
    {
        trailer_type = 0;
        imsi.clear();
        imei.clear();
        msisdn.clear();
        teid.clear();
        tac.clear();
        hw_ecgi.clear();
        hw_sac.clear();
        hw_cellid.clear();
        hw_operator.clear();
        hw_nettype.clear();
        hw_apn.clear();
        hw_bsid.clear();
        hw_ncode.clear();
        hw_esn.clear();
        hw_ecgi_mnc.clear();
        hw_gre_key.clear();
        hw_accont.clear();
        hw_tai.clear();
        rt_plmnid.clear();
        rt_uli.clear();
        rt_basetype.clear();
        rt_outsrc_ip.clear();
        rt_outdst_ip.clear();
        src_ip.clear();
        dst_ip.clear();
        src_port.clear();
        dst_port.clear();
        proto.clear();
        ttl.clear();
        capdate.clear();
        // Q931CallingPartyNumber.clear();
        // Q931CalledPartyNumber.clear();
    }
};

typedef _trailer ya_trailer_t;


typedef struct _flow_tunnel_info{
    std::string magic_code;
    std::string tunnel_len;
    std::string tunnel_layer;
    std::string tunnel_str_len;
    std::string tunnel_str;
    public:
    void cleanAll()
    {
    magic_code.clear();
    tunnel_len.clear();
    tunnel_layer.clear();
    tunnel_str_len.clear();
    tunnel_str.clear();
    }
}ya_flow_tunnel_info_t ;

