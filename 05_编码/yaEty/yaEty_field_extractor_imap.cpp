/****************************************************************************************
 * 文 件 名 : yaEty_proto_field_desc_imap.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : liugh      '2018-06-11
* 编    码 : liugh      '2018-06-11
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include <algorithm>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_imap.h"
#include "yaEty_content_reassembly_share_mem.h"

static std::string transform_field_for_imap_response(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (fvalue_type_ftenum(const_cast<fvalue_t *>(&pFinfo->value)) != FT_BOOLEAN)
    {
        return "error";
    }

    return fvalue_get_uinteger64(const_cast<fvalue_t *>(&pFinfo->value)) ? "S2C" : "C2S";
}

static std::string transform_field_for_imap_data(epan_dissect_t *edt, const field_info *pFinfo)
{
    field_info   *finfo     = NULL;
    mail_data    *get_mail = NULL;
    PktInfo       ptf;

    // 从 edt 中收集五元组信息
    finfo = get_first_field_info_from_interesting_fields(edt, "ip.src");
	if(finfo==NULL){return "";}
    ptf.src_ip = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.dst");
	if(finfo==NULL){return "";}
    ptf.dst_ip = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.proto");
	if(finfo==NULL){return "";}
    ptf.proto = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "tcp.srcport");
	if(finfo==NULL){return "";}
    ptf.src_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "tcp.dstport");
	if(finfo==NULL){return "";}
    ptf.dst_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    // hash 获取其还原出的内容文件 path list.
    get_mail = (mail_data *)DispGetHashdb(ptf, MEM_TYPE_EMAIL_DATA);
    if (NULL == get_mail)
    {
        return "";
    }

    std::string strJsonFileList;

    // 输出为 json 数组
    Json::Value jArrayPath;
    Json::StreamWriterBuilder jBuilder;
    jBuilder["commentStyle"] = "None";
    jBuilder["indentation"]  = "";

    for(int i = 0; i < get_mail->mail_number; i++){
         jArrayPath.append(Json::Value(get_mail->mail_gather[i].path));
    }

    strJsonFileList = Json::writeString(jBuilder, jArrayPath);
    return strJsonFileList;
}

static std::string transform_multi_cmmon_imap_extractor_value(epan_dissect_t *edt, const field_info *pFinfo, const char *cmd)
{
    if(cmd==NULL){return "";}

    field_info     *finfo         = NULL;
    GPtrArray      *finfos        = NULL;
    int             field_id      = 0;
    int             finfos_cnt    = 0;
    int             fields_cnt    = 0;

    std::string strRes;

    field_id   = proto_registrar_get_id_byname(cmd);
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);

    if(finfos==NULL ){
        return "";
    }

    finfos_cnt = g_ptr_array_len(finfos);
    for(int i=0;i<finfos_cnt;i++)
    {
        // 获取需要写入的头域值
        finfo = (field_info *)g_ptr_array_index(finfos, i);
        strRes = ety_ws_get_node_field_value(finfo, edt);
    }
    //strRes.replace(0,3,"\r\n\t");
    int pos=0;
    while((pos=strRes.find("\r\n\t"))!=-1){
        strRes.erase(pos,3);
    }
    return strRes;
}

static std::string transform_multi_imap_cc_extractor_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    return transform_multi_cmmon_imap_extractor_value(edt, pFinfo, "imf.cc");
}


static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // RTL tags fields
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort",                 "tcp.srcport",                eMT_direct,        "",                NULL),
    F_D_ITEM("DstPort",                 "tcp.dstport",                eMT_direct,        "",                NULL),
    F_D_ITEM("C2S",                     NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Proto",                   "ip.proto",                   eMT_direct,        "",                NULL),
    F_D_ITEM("TTL",                     "ip.ttl",                     eMT_direct,        "",                NULL),

    F_D_ITEM("Tag",                     "imap.request_tag",           eMT_direct,        "",                NULL),
    F_D_ITEM("MsgID",                   "imap.request.command.uid",   eMT_direct,        "",                NULL),
    F_D_ITEM("Cmd",                     "imap.request.command",       eMT_direct,        "",                NULL),
    F_D_ITEM("Cmd_Resp",                "imap.response",              eMT_direct,        "",                NULL),
    F_D_ITEM("Date",                    "imf.date",                   eMT_direct,        "",                NULL),
    F_D_ITEM("Date-warning",            NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("From",                    "imf.from",                   eMT_direct,        "",                NULL),
    F_D_ITEM("Sender",                  "imf.sender",                 eMT_direct,        "",                NULL),
    F_D_ITEM("Reply-To",                "imf.reply_to",               eMT_direct,        "",                NULL),
    F_D_ITEM("To",                      "imf.to",                     eMT_direct,        "",                NULL),
    F_D_ITEM("Cc",                      NULL,                         eMT_fromEdt,       "",                transform_multi_imap_cc_extractor_value),
    F_D_ITEM("Bcc",                     "imf.bcc",                    eMT_direct ,       "",                NULL),
    F_D_ITEM("Message-ID",              "imf.message_id",             eMT_direct,        "",                NULL),
    F_D_ITEM("In-Reply-To",             "imf.in_reply_to",            eMT_direct,        "",                NULL),
    F_D_ITEM("References",              "imf.references",             eMT_direct,        "",                NULL),
    F_D_ITEM("Subject",                 "imf.subject",                eMT_direct,        "",                NULL),
    F_D_ITEM("Comments",                "imf.comments",               eMT_direct,        "",                NULL),
    F_D_ITEM("Keywords",                "imf.keywords",               eMT_direct,        "",                NULL),
    F_D_ITEM("Followup-to",             NULL,                         eMT_fixed,         "",                NULL),

    F_D_ITEM("Resent-Date",             "imf.resent.date",            eMT_direct,        "",                NULL),
    F_D_ITEM("Resent-From",             "imf.resent.from",            eMT_direct,        "",                NULL),
    F_D_ITEM("Resent-Sender",           "imf.resent.sender",          eMT_direct,        "",                NULL),
    F_D_ITEM("Resent-To",               "imf.resent.to",              eMT_direct,        "",                NULL),
    F_D_ITEM("Resent-Cc",               "imf.resent.cc",              eMT_direct,        "",                NULL),
    F_D_ITEM("Resent-Bcc",              "imf.resent.bcc",             eMT_direct,        "",                NULL),
    F_D_ITEM("Resent-Reply_To",         NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Resent-Message_ID",       "imf.resent.message_id",      eMT_direct,        "",                NULL),

    F_D_ITEM("Return-Path",             "imf.return_path",            eMT_direct,        "",                NULL),
    F_D_ITEM("Received",                "imf.received",               eMT_direct,        "",                NULL),
    F_D_ITEM("Encrypted",               NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM(ETY_DIS_NOT_TO,            NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM(ETY_DIS_NOT_OPT,           NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Accept-Language",         NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Original-Message_ID",     NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("PICS-Label",              NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Encoding",                WS_CON_TRA_ECODING,           eMT_direct,        "",                NULL),

    F_D_ITEM("List-Archive",            NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("List-Help",               NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("List-ID",                 NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("List-Owner",              NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("List-Post",               NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("List-Subscribe",          NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("List-Unsubscribe",        NULL,                         eMT_fixed,         "",                NULL),

    F_D_ITEM("Message-Context",         "imf.message_text",           eMT_direct,        "",                NULL),
    F_D_ITEM("DL-Expansion-History",    "imf.dl_expansion_history",   eMT_direct,        "",                NULL),
    F_D_ITEM("Alternate-Recipient",     NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM(ETY_ORI_ENC_INF_T,         WS_ORI_ENC_INF_T,             eMT_direct,        "",                NULL),
    F_D_ITEM("Content-Return",          NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Generate-Delivery-Report",NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM(ETY_PRE_NON_REP,           NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Obsoletes,DB_FT_STRING",  NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Supersedes",              "imf.supersedes",             eMT_direct,        "",                NULL),
    F_D_ITEM("Content-Identifier",      "imf.content.id",             eMT_direct,        "",                NULL),
    F_D_ITEM("Delivery-Date",           "imf.delivery_date",          eMT_direct,        "",                NULL),
    F_D_ITEM("Expiry-Date",             "imf.ext.expiry-date",        eMT_direct,        "",                NULL),
    F_D_ITEM("Expires",                 "imf.expires",                eMT_direct,        "",                NULL),
    F_D_ITEM("Reply-By",                "imf.reply_by",               eMT_direct,        "",                NULL),
    F_D_ITEM("Importance",              "imf.importance",             eMT_direct,        "",                NULL),
    F_D_ITEM("Incomplete_Copy",         "imf.incomplete_copy",        eMT_direct,        "",                NULL),
    F_D_ITEM("Priority",                "imf.priority",               eMT_direct,        "",                NULL),
    F_D_ITEM("Sensitivity",             "imf.sensitivity",            eMT_direct,        "",                NULL),
    F_D_ITEM("Language",                "imf.content_language",       eMT_direct,        "",                NULL),
    F_D_ITEM("Conversion",              "imf.conversion",             eMT_direct,        "",                NULL),
    F_D_ITEM("Conversion-With-Loss",    "imf.conversion_with_loss",   eMT_direct,        "",                NULL),
    F_D_ITEM("Message-Type",            "imf.message_type",           eMT_direct,        "",                NULL),
    F_D_ITEM("Autosubmitted",           "imf.autosubmitted",          eMT_direct,        "",                NULL),
    F_D_ITEM("Autoforwarded",           "imf.autoforwarded",          eMT_direct,        "",                NULL),
    F_D_ITEM(ETY_DIS_X4_IP_EXT,         WS_DIS_X4_IP_EXT,             eMT_direct,        "",                NULL),
    F_D_ITEM(ETY_DIS_X4_MT_EXT,         WS_DIS_X4_MT_EXT,             eMT_direct,        "",                NULL),
    F_D_ITEM("Disclose-Recipients",     NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Deferred-Delivery",       "imf.deferred_delivery",      eMT_direct,        "",                NULL),
    F_D_ITEM("Latest-Delivery-Time",    "imf.latest_delivery_time",   eMT_direct,        "",                NULL),
    F_D_ITEM(ETY_ORI_RE_ADR,            WS_ORI_RE_ADR,                eMT_direct,        "",                NULL),

    F_D_ITEM("X400-Content-Identifier", "imf.x400_content_identifier",eMT_direct,        "",                NULL),
    F_D_ITEM("X400-Content-Return",     NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("X400-Content-Type",       "imf.x400_content_type",      eMT_direct,        "",                NULL),
    F_D_ITEM("X400-MTS-Identifier",     "imf.x400_mts_identifier",    eMT_direct,        "",                NULL),
    F_D_ITEM("X400-Originator",         "imf.x400_originator",        eMT_direct,        "",                NULL),
    F_D_ITEM("X400-Received",           "imf.x400_received",          eMT_direct,        "",                NULL),
    F_D_ITEM("X400-Recipients",         "imf.x400_recipients",        eMT_direct,        "",                NULL),
    F_D_ITEM("X400-Trace",              NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("MIME-Version",            "imf.mime_version",           eMT_direct,        "",                NULL),

    F_D_ITEM("Content-ID",              "imf.content.id",             eMT_direct,        "",                NULL),
    F_D_ITEM("Content-Description",     "imf.content.description",    eMT_direct,        "",                NULL),
    F_D_ITEM(ETY_CON_TRA_ENC,           WS_CON_TRA_ENC,               eMT_direct,        "",                NULL),
    F_D_ITEM("Content-Type",            "imf.content.type.type",      eMT_direct,        "",                NULL),
    F_D_ITEM("Content-Base",            NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Content-Location",        NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Content-features",        NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Content-Disposition",     NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Content-Language",        "imf.content_language",       eMT_direct,        "",                NULL),
    F_D_ITEM("Content-Alternative",     NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Content-MD5",             NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("Content-Duration",        NULL,                         eMT_fixed | eMT_lastplain,         "",NULL),

    F_D_ITEM("ExtMailHdrCount",         NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("ExtMailHdrName0",         NULL,                         eMT_fixed,         "",                NULL),
    F_D_ITEM("ExtMailHdrValue0",        "",                           eMT_fromEdt,       "",                transform_field_for_imap_data),
    F_D_ITEM_MAIL_NAME_VALUE("1", eMT_fixed),
    F_D_ITEM_MAIL_NAME_VALUE("2", eMT_fixed),
    F_D_ITEM_MAIL_NAME_VALUE("3", eMT_fixed),
    F_D_ITEM_MAIL_NAME_VALUE("4", eMT_fixed),
    F_D_ITEM_MAIL_NAME_VALUE_5("5", "6", "7", "8", "9", eMT_fixed),
    F_D_ITEM_MAIL_NAME_VALUE_10("10", "11", "12", "13", "14", "15", "16", "17", "18", "19", eMT_fixed),


};


ProtoFieldExtractorImap::ProtoFieldExtractorImap()
    : ProtoFieldExtractor("IMAP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}

bool ProtoFieldExtractorImap::ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter)
{
    ProtoFieldDesc *pFieldDescName = NULL;
    int             field_id       = 0;
    int             fields_cnt     = 0;
    std::string     strValue;

    // 继续输出后续的 name value, 尽管可能它们都为空
    fields_cnt = this->GetFieldsCount();
    for (int i = nFieldIndexToExtract; i < fields_cnt; i++)
    {
        pFieldDescName = this->GetProtoFieldDescByIndex(i);
        strValue = pFieldDescName->defaultFieldValue;

        // 进行可能地转换
        if (ProtoFieldDesc_GetType(pFieldDescName) == eMT_fromEdt
            && pFieldDescName->funTransfrom != NULL)
        {
            strValue = pFieldDescName->funTransfrom(edt, NULL);
            strValue = !strValue.empty() ? strValue : pFieldDescName->defaultFieldValue;
        }

        //naem value 对的解析中暂时用不到 eMT_direct
        if (ProtoFieldDesc_GetType(pFieldDescName) == eMT_direct)
        {
            strValue = get_first_field_value_from_interesting_fields(edt, pFieldDescName->wsFieldName);
        }

        // 输出
        pWriter->writeRecordField(pFieldDescName->etyFieldName, strValue);
    }

    nFieldIndexToExtract = 0;

    return TRUE;
}
