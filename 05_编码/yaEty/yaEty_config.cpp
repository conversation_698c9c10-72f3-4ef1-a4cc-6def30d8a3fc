/****************************************************************************************
 * 文 件 名 : yaEty_config.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-16
* 编    码 : zhengsw      '2018-05-16
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_config.h"
#include "yaEty_utils.h"
#include "version.h"

#include <getopt.h>
#include <iostream>

#define APP_NAME "yaEty"

EtyConfig::EtyConfig()
    : ini_(NULL)
    , strConfigFilePath_("./yaEty.conf")
    , strTblsDir_(getAppDir() + "tbls")
    , strContentReassemblyDir_(getAppDir() + "content")
    , strFieldsDir_(getAppDir() + "field")
    , strDumpDir_(getAppDir() + "dump")
//  , logRecordDir_(getAppDir() + "log")
    , strAppPathContentReassembly_(getAppDir() + "xplico/xplico")
    , bDoContentReassembly_(true)         // do not do it!!!
    , bHelpMode_(false)
    , bFilenameRename_(true)
    , bProcessFileForce_(false)
{
}

EtyConfig::~EtyConfig()
{

}

EtyConfig *EtyConfig::GetInstance()
{
    static EtyConfig s_config;
    return &s_config;
}

int EtyConfig::ParseConfigFile(const std::string &strConfigFilePath)
{
    // 如果是 "./" 打头的需要进行调整为从程序所在文件开始
    std::string configFilePath = strConfigFilePath;
    if (0 == configFilePath.compare(0, 2, "./"))
    {
        configFilePath = getAppDir() + configFilePath;
    }

    const char    *ini_name  = configFilePath.c_str();

    ini_ = iniparser_load(ini_name);
    if (ini_ == NULL)
    {
        fprintf(stderr, "parse config file error: %s\n", ini_name);
        return -1 ;
    }

    return 0;
}

void EtyConfig::SetTblsDir(const std::string &strTblsDir)
{
    strTblsDir_ = strTblsDir;
}

const std::string &EtyConfig::GetTblsDir()
{
    return strTblsDir_;
}

void EtyConfig::SetFieldsDir(const std::string &strFieldsDir)
{
    strFieldsDir_ = strFieldsDir;
}

const std::string &EtyConfig::GetFieldsDir()
{
    return strFieldsDir_;
}

void EtyConfig::SetDumpDir(const std::string &strDumpDir)
{
    strDumpDir_ = strDumpDir;
}

const std::string &EtyConfig::GetDumpDir()
{
    return strDumpDir_;
}

void EtyConfig::SetTrailerType(const std::string &strTrailerType)
{
    strTrailerType_ = strTrailerType;
    if(strTrailerType_.empty())
      strTrailerType_ = "rt";
}

void EtyConfig::SetScanTimeoutRtpMapSecond()
{
    ScanTimeoutRtpMapSecond_ = CFG->GetValueOf<int>("scan_timeout_rtp_map_second") == 0?3600:CFG->GetValueOf<int>("scan_timeout_rtp_map_second") ;
}
const std::string &EtyConfig::GetTrailerType()
{
    return strTrailerType_;
}
void EtyConfig::SetFullZeroBytes()
{
    bFullZeroBytes_ =  CFG->GetValueOf<bool>("full_zero_bytes");
    bCutFile_ =   CFG->GetValueOf<bool>("cut_file");
}
void EtyConfig::SetTaskID(const std::string &strTaskID)
{
    //if(strTaskID_.empty() || strTaskID_ == "0")    
        strTaskID_ = strTaskID;

    if(strTaskID_.empty())
        strTaskID_ = "0";

    if (!bAddTaskID_ && !strTaskID.empty())
        bAddTaskID_ = true;
}

const std::string &EtyConfig::GetTaskID()
{
    return strTaskID_;
}

void EtyConfig::SetTaskIDByFile(const std::string &strTaskFile)
{
    char * content = NULL;
    size_t real_len = 0,len = 0;;
    ssize_t read = 0;

    if (strTaskFile.empty())
    {
        bSetTaskIDFile_ = false;
        return;
    }

    FILE *fp = fopen(strTaskFile.c_str(), "r");
    if (fp == NULL)
    {
        std::cout << "fopen error:" << strerror(errno) << std::endl;
        bSetTaskIDFile_ = false;
        return;
    }

    if ((read = getline(&content, &real_len, fp)) != -1)
    {
        for (len = 0; content[len] != '\n'; ++ len)
        {
            if (!isgraph(content[len]))
                break;
        }
        content[len] = '\0';
        bSetTaskIDFile_ = true;
        this->SetTaskID(std::string(content, len));
    }
    free(content);
    content = NULL;
}

void EtyConfig::EnableLineNo(bool isAddLineNo)
{
    if (bAddLineNo_ != true)
        bAddLineNo_ = isAddLineNo;
}

int EtyConfig::ParseConfig(const char * configFile, int argc, char *argv[])
{
    int lSts = 0;

    // 解析配置文件
    lSts = ParseConfigFile(configFile);
    CHECK_NOR_EXIT(lSts < 0, lSts, "parse config file %s error.\n", configFile);

    // 从配置文件加载输出配置
    SetOutputDirFromConfig();

    // 解析命令行选项与参数
    lSts = ParseCmdLineOpts(argc, argv);
    CHECK_NOR_EXIT(lSts < 0, lSts, "parse cmdline options error.\n");

    // 是否 help mode
    if (OnHelpMode())
    {
        return lSts;
    }

    // todo: 各目录权限检测

    // 显示配置概要信息
    showConfigSummary();
    return 0;
 }

int EtyConfig::ParseCmdLineOpts(int argc, char *argv[])
{
#   define OPT_STRING  ":nhvrflt:T:C:i:"
#   define HELP_STRING " [-nhvrf] [-t <task id>] [-T <tbl_out_dir>] [-C <content_out_dir>] <pcap dir ...>\n"    \
                       " -n : no content reassembly\n"              \
                       " -h : show this help info\n"                \
                       " -v : show version info\n"                  \
                       " -r : record process state\n"               \
                       " -f : process files processed in record\n"  \
                       " -l : enable line number parse"             \
                       " -i : single file mode"

    static const char          optstring[]    = OPT_STRING;
    static const struct option long_options[] =
      {
          {"no-reassemble", no_argument,       NULL, 'n'},
          {"help",          no_argument,       NULL, 'h'},
          {"version",       no_argument,       NULL, 'v'},
          {"rename",        no_argument,       NULL, 'r'},
          {"force",         no_argument,       NULL, 'f'},
          {"lineno",        no_argument,       NULL, 'l'},
          {"taskid",        required_argument, NULL, 't'},
          {"tbldir",        required_argument, NULL, 'T'},
          {"contentdir",    required_argument, NULL, 'C'},
          {0, 0, 0, 0 }
      };

    int opt                                   = 0;
    const char *appName                       = APP_NAME;

    // 解析命令行选项与参数
    while ((opt = getopt_long(argc, argv, optstring, long_options, NULL)) != -1)
    {
        switch (opt)
        {
            case 'n':
            {
                bDoContentReassembly_ = false;
                printf("ety will not do content reassemble\n");
                break;
            }
            case 'h':
            {
                bHelpMode_ = true;
                printf("%s %s\n", appName, HELP_STRING);
                return 0;
            }
            case 'v':
            {
                bHelpMode_ = true;
                printf("%s %s\n", appName, PROJECT_VERSION_DATE_STR);
                return 0;
            }
            case 'r':
            {
                bFilenameRename_ = false;
                printf("file name will not be renamed.\n");
                break;
            }
            case 'f':
            {
                bProcessFileForce_ = true;
                printf("process will ignore record.\n");
                break;
            }
            case 'l':
            {
                bAddLineNo_ = true;
                break;
            }
            case 't':
            {
                if (optarg != nullptr)
                    CFG->SetTaskID(std::string(optarg));
                break;
            }
            case 'T':
            {
                if (optarg != nullptr)
                {
                    const char  *pOutDir      = optarg;
                    std::string  strDirPrefix = "";
                    if (pOutDir[0] != '/')
                    {
                        strDirPrefix = getAppDir();
                    }

                    CFG->SetTblsDir(strDirPrefix + pOutDir);
                }
                break;
            }
            case 'C':
            {
                if (optarg != nullptr)
                {
                    const char  *pOutDir      = optarg;
                    std::string  strDirPrefix = "";
                    if (pOutDir[0] != '/')
                    {
                        strDirPrefix = getAppDir();
                    }

                    CFG->SetContentReassemblyDir(strDirPrefix + pOutDir);
                }
                break;
            }
            case 'i':
            {
                if (optarg != nullptr)
                {
                    const char  *ProcessSingleFile      = optarg;
                    std::string  strDirPrefix = "";
                    CFG->SetProcessSingleFile(ProcessSingleFile);
                }
                break;
            }
            case '?':
            {
                printf("invalid option : %s\n", optarg);
                break;
            }
            case ':':
            default:
                break;
        }
    }

    // 调整后再没有选项，只有 oprand (文件名，路径之类)
    // 对于输入 "./yaEty -t 123 -n /root/pcaps/voip/yaEty /root/pcaps/voip/yaEty /root/pcaps/voip/rtp out"
    // argc 为 5
    // argv 为 "-n /root/pcaps/voip/yaEty /root/pcaps/voip/yaEty /root/pcaps/voip/rtp out"
    // argv[0] 为最后一个选项，这是为了与没有任何选项场景一致，如 "yaEty /root/pcaps/voip/yaEty /root/pcaps/voip/yaEty /root/pcaps/voip/rtp out"
    // argv[1] 始终为第一个参数
    argc = argc - (optind -1);
    argv = &argv[optind - 1];

    // 判断是否需要先进行 merge 操作
    bool         bMergeSmallPcaps = CFG->GetValueOf<bool>("merge_small_pcaps");
    const char * mergeOutDirName  = CFG->GetValueOf<CSTR>("merge_out_dir_name");

    /* 准备输入目录与输出目录 */
    if (argc < 2&& 0== CFG->GetProcessSingleMode())
    {
        printf("need input pcap dir, eg: %s <inDir>\n", appName);
        return -2;
    }

    // 收集输入目录, 所有剩余参数均作为输入目录
    for (int i = 1; i < argc; i++)
    {
        vecInputDirs_.push_back(argv[i]);
    }

    CFG->SetTrailerType(std::string(CFG->GetValueOf<CSTR>("trailer_type")));
    CFG->SetTaskID(std::string(CFG->GetValueOf<CSTR>("task_id")));
    CFG->SetTaskIDByFile(std::string(CFG->GetValueOf<CSTR>("task_file")));
    CFG->EnableLineNo(CFG->GetValueOf<bool>("line_no"));

    return 0;
}

void EtyConfig::showConfigSummary()
{
    // 是否进行内容还原
    bool bContentReassembly = CFG->DoContentReassembly();
    if(CFG->GetProcessSingleMode() == 1){
          // in and out
            printf(" pcap input : %s\n"
           " tbls out   : %s\n"
           " field out  : %s\n",
           CFG->GetSingleProcessFile().c_str(),
           CFG->GetTblsDir().c_str(),
           CFG->GetFieldsDir().c_str()
           );
    }else{
    // in and out
    printf(" pcap input : %s\n"
           " tbls out   : %s\n"
           " field out  : %s\n",
           CFG->GetInputDirListStr().c_str(),
           CFG->GetTblsDir().c_str(),
           CFG->GetFieldsDir().c_str()
           );
    }
    printf("content out : %s\n", CFG->GetContentReassemblyDir().c_str());
}

bool EtyConfig::DoContentReassembly()
{
    return bDoContentReassembly_;
}

bool EtyConfig::MakeOutputDir()
{
    (ensureDirExist(strFieldsDir_.c_str()) == 0);

    (ensureDirExist(strTblsDir_.c_str()) == 0);
    
    if (CFG->bDoContentReassembly_)
    {
        (ensureDirExist(strContentReassemblyDir_.c_str()) == 0);
    }
    auto tmp = CFG->GetValueOf<CSTR>("dump_to_dir");
    if(strlen(tmp)>= 5 &&memcmp(tmp,"<out>",5) == 0)
    {
        (ensureDirExist(strDumpDir_.c_str()) == 0);
    }
    return true;
}

const std::string EtyConfig::toGlobalConfigName(const std::string &strConfigName)
    {
        return ":" + strConfigName;
    }

int EtyConfig::SetOutputDirFromConfig()
{
    const char *field_dir   = CFG->GetValueOf<CSTR>("field_dir");
    const char *tbl_dir     = CFG->GetValueOf<CSTR>("tbl_dir");
    const char *content_dir = CFG->GetValueOf<CSTR>("content_dir");
    const char *dump_dir    = CFG->GetValueOf<CSTR>("dump_dir");

    if (strlen(field_dir) > 0)
    {
        CFG->SetFieldsDir(field_dir);
    }

    if (strlen(tbl_dir) > 0)
    {
        CFG->SetTblsDir(tbl_dir);
    }

    if (strlen(content_dir) > 0)
    {
        CFG->SetContentReassemblyDir(content_dir);
    }

    if (strlen(dump_dir))
    {
        CFG->SetDumpDir(dump_dir);
    }
    CFG->SetFullZeroBytes();
    CFG->SetScanTimeoutRtpMapSecond();
    return 0;
}

template <>
int EtyConfig::GetValueOf_inner(const std::string &strConfigName)
{
    return iniparser_getint(ini_, strConfigName.c_str(), 0);
}

template <>
CSTR EtyConfig::GetValueOf_inner(const std::string &strConfigName)
{
    return iniparser_getstring(ini_, strConfigName.c_str(), "");
}

template <>
bool EtyConfig::GetValueOf_inner(const std::string &strConfigName)
{
    return iniparser_getboolean(ini_, strConfigName.c_str(), 0) == 1;
}
