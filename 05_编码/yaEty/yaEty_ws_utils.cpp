/****************************************************************************************
 * 文 件 名 : yaEty_utils.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 : utils
 * 功    能 : 功能组件
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-12
* 编    码 : zhengsw   '2018-05-12
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <stdio.h>
#include <locale.h>
#include <unistd.h>
#include <glib.h>

#include "config.h"

#include "yaEty_utils.h"
#ifdef WS_2_4
   #include <epan/epan-int.h>
#endif
#include <epan/addr_resolv.h>
#include <epan/epan_dissect.h>
#include <epan/ftypes/ftypes-int.h>
#include <wsutil/cmdarg_err.h>
#include <wsutil/privileges.h>
#include <epan/stat_tap_ui.h>
#include <codecs/codecs.h>
#include <epan/timestamp.h>

#include "extcap.h"

#include "ui/failure_message.h"
#include "yaEty_ws_utils.h"

// global variables
__thread const frame_data *ref      = NULL;
__thread frame_data       *prev_dis = NULL;
__thread frame_data       *prev_cap = NULL;

// static variables
static __thread frame_data        prev_dis_frame;
static __thread frame_data        prev_cap_frame;
static __thread gboolean          epan_auto_reset       = FALSE;
static __thread guint32           epan_auto_reset_count = 0;

static void
failure_warning_message(const char *msg_format, va_list ap)
{
  fprintf(stderr, "yaEty: ");
  vfprintf(stderr, msg_format, ap);
  fprintf(stderr, "\n");
}

static void
failure_message_cont(const char *msg_format, va_list ap)
{
  vfprintf(stderr, msg_format, ap);
  fprintf(stderr, "\n");
}

wsEnvionment::wsEnvionment(char *appName, int (*function_addr)(int, char **),
                           const char *pszPrefList, hf_ref_type hfRefType /* = HF_REF_TYPE_DIRECT */)
{
    char *init_progfile_dir_error = NULL;

    // 设定报告错误函数
    cmdarg_err_init(failure_warning_message, failure_message_cont);

    /* 基础组件初始化 */
    init_process_policies();

    // 初始化路径相关变量
    init_progfile_dir_error = init_progfile_dir(appName, function_addr);
    if (init_progfile_dir_error != NULL)
    {
        fprintf(stderr,
                "yaEty: Can't get pathname of directory containing the yaEty program: %s.\n",
                init_progfile_dir_error);
        g_free(init_progfile_dir_error);
    }

#ifdef WS_2_4
    wtap_init();
#else
    wtap_init(true);
#endif

    codecs_init();

    /* 设置 field 默认访问方式为 DIRECT，
     * 可直接从 protodata 的 interesting hashtable 访问 */
    proto_set_field_ref_type_default_to(hfRefType);

    /* 注册协议解析器 */
    if (!epan_init(register_all_protocols, register_all_protocol_handoffs, NULL, NULL))
    {

    }

    // 加载配置，使得如 radius 目录配置文件会被使用
    epan_load_settings();

    start_requested_stats();

    timestamp_set_type(TS_RELATIVE);
    timestamp_set_precision(TS_PREC_AUTO);
    timestamp_set_seconds_type(TS_SECONDS_DEFAULT);

    // 设置 ws 首选项
    setWsPreference(pszPrefList);
}

wsEnvionment::~wsEnvionment()
{
    codecs_cleanup();
    epan_cleanup();
    extcap_cleanup();

    wtap_cleanup();
}

void wsEnvionment::setWsPreference(const char *pszDissectOptions)
{
    char *pErr = NULL;

    // ws 在不足 60 字节的 packet 中，如果出现了 trailer，优先补齐为 60
    // 填充的部分即为 "eth.padding"，而 eth.trailer 的长度则相应地减少了一些
    // 关闭这项功能，多余部分全部视作 trailer.
    char bufOptPadding[] = "eth.assume_padding:false";
    prefs_set_pref(bufOptPadding, &pErr);

    // 根据配置，配置首选项
    alloced_uptr pszOpts(strdup(pszDissectOptions), free);
    char *pTokenOrigin = pszOpts.get();
    char *pszOpt       = NULL;
    do
    {
        pszOpt = strtok(pTokenOrigin, ",");
        if (NULL == pszOpt)
        {
            break;
        }

        prefs_set_pref(pszOpt, &pErr);
    } while (!(pTokenOrigin = NULL));
}

std::string ety_ws_get_node_field_value(field_info* fi, epan_dissect_t* edt)
{
    alloced_uptr uptrValue(nullptr, g_free);
    uptrValue.reset(get_node_field_value(fi, edt));

    return uptrValue.get();
}

std::string get_main_protocol_name_from_proto_tree(proto_tree *pProtoTree)
{
    const char  *protoFullName    = get_protocol_name_from_proto_tree(pProtoTree);
    std::string  strProtoFullName = protoFullName;
#if 0
    // 是否含有子协议
    auto slashIter = strProtoFullName.find('/');
    if (std::string::npos == slashIter)
    {
        return strProtoFullName;
    }

    // 只留下主协议名，eg: "SIP/SDP" -> "SIP"
    strProtoFullName.erase(slashIter);
#endif
    return strProtoFullName;
}

const char *get_protocol_name_from_proto_tree(proto_tree *pProtoTree)
{
    // 从 column info 中的 COL_PROTOCOL 列中获取出 协议名
    // 当心：HTTP/XML
    const char *pStrProtoName = NULL;

    if (NULL == pProtoTree)
    {
        return NULL;
    }

    pStrProtoName = col_get_text(pProtoTree->tree_data->pinfo->cinfo, COL_PROTOCOL);
    return pStrProtoName;
}
int get_nth_field_info_from_interesting_fields_count(epan_dissect *edt, const char *wsFieldName)
{
    GPtrArray  *finfos      = NULL;
    int field_id   = proto_registrar_get_id_byname(wsFieldName);
    if (field_id < 0)
    {
        return -1;
    }
    header_field_info *hfinfo = proto_registrar_get_byname(wsFieldName);
    if(nullptr == hfinfo)
    {
      return -2;
    }

    finfos = proto_get_finfo_ptr_array(edt->tree, field_id);
    while (NULL == finfos && hfinfo->same_name_prev_id != -1)
    {
        hfinfo = proto_registrar_get_nth(hfinfo->same_name_prev_id);
        finfos = proto_get_finfo_ptr_array(edt->tree, hfinfo->id);
    }
    if (NULL == finfos)
    {
        return -2;
    }

    return g_ptr_array_len(finfos);
}

field_info *get_nth_field_info_from_interesting_fields(epan_dissect *edt, const char *wsFieldName, int n)
{
    int         field_id    = 0;
    int         finfos_cnt  = 0;
    field_info *finfo       = NULL;
    GPtrArray  *finfos      = NULL;
    const char *pFieldValue = NULL;

    field_id   = proto_registrar_get_id_byname(wsFieldName);
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);
    header_field_info *hfinfo = proto_registrar_get_byname(wsFieldName);
    if(nullptr == hfinfo)
    {
      return NULL;
    }
    while (NULL == finfos && hfinfo->same_name_prev_id != -1)
    {
        hfinfo = proto_registrar_get_nth(hfinfo->same_name_prev_id);
        finfos = proto_get_finfo_ptr_array(edt->tree, hfinfo->id);
    }

    if (NULL == finfos)
    {   // 虽然是映射，可惜没有找到解析的结果，转出默认值吧
        return NULL;
    }

    finfos_cnt = g_ptr_array_len(finfos);
    if (0 == finfos_cnt)
    {
        return NULL;
    }

    // negtive index, transform it to positive
    if (n < 0)
    {
        n = finfos_cnt + n;
    }

    // check if it was valid index
    if (n < 0 || n >= finfos_cnt)
    {
        return NULL;
    }

    finfo = (field_info *)g_ptr_array_index(finfos, n);
    return finfo;
}

field_info *get_first_field_info_from_interesting_fields(epan_dissect *edt, const char *wsFieldName)
{
    return get_nth_field_info_from_interesting_fields(edt, wsFieldName, 0);
}

field_info *get_n_field_info_from_interesting_fields(epan_dissect *edt, const char *wsFieldName,int n)
{
    return get_nth_field_info_from_interesting_fields(edt, wsFieldName, n);
}

std::string get_first_field_value_from_interesting_fields(epan_dissect *edt, const char *wsFieldName)
{
    field_info *finfo       = NULL;

    finfo = get_first_field_info_from_interesting_fields(edt, wsFieldName);
    if (NULL == finfo)
    {
        return "";
    }

    return ety_ws_get_node_field_value(finfo, edt);
}

std::string get_n_field_value_from_interesting_fields(epan_dissect *edt, const char *wsFieldName,int n)
{
    field_info *finfo       = NULL;

    finfo = get_n_field_info_from_interesting_fields(edt, wsFieldName,n);
    if (NULL == finfo)
    {
        return "";
    }

    return ety_ws_get_node_field_value(finfo, edt);
}
uint32_t fvalue_get_ip_host_addr(fvalue_t *fv)
{
    g_assert(fv->ftype->ftype == FT_IPv4);
    return fv->value.ipv4.addr;
    // ipv4_addr_and_mask *ipv4 = (ipv4_addr_and_mask *)fvalue_get(fv);
    // guint32             n_addr = ipv4->addr;                           // host order default

    // return n_addr;
}

char *
ws_bytes_to_str(wmem_allocator_t *scope, const guint8 *bd, int bd_len)
{
	gchar *cur;
	gchar *cur_ptr;

	if (!bd)
        return NULL;

	cur=(gchar *)wmem_alloc(scope, bd_len * 2 + 1);
	if (bd_len <= 0) { cur[0] = '\0'; return cur; }

	cur_ptr = bytes_to_hexstr(cur, bd, bd_len);
	*cur_ptr = '\0';				/* 1 byte */

	return cur;
}

gchar *ws_tvb_bytes_to_str(wmem_allocator_t *allocator, tvbuff_t *tvb,
                           const gint offset, const gint len)
{
	return ws_bytes_to_str(allocator, tvb_get_ptr(tvb, offset, len), len);
}

#ifdef WS_2_4
static const nstime_t *
yaEty_get_frame_ts(void *data, guint32 frame_num)
{
    capture_file *cf = (capture_file *) data;

    if (ref && ref->num == frame_num)
        return &ref->abs_ts;

    if (prev_dis && prev_dis->num == frame_num)
        return &prev_dis->abs_ts;

    if (prev_cap && prev_cap->num == frame_num)
        return &prev_cap->abs_ts;

    if (cf->frames) {
        frame_data *fd = frame_data_sequence_find(cf->frames, frame_num);

        return (fd) ? &fd->abs_ts : NULL;
    }

    return NULL;
}

#else
static const nstime_t *
yaEty_get_frame_ts(struct packet_provider_data *prov, guint32 frame_num)
{
  if (prov->ref && prov->ref->num == frame_num)
    return &prov->ref->abs_ts;

  if (prov->prev_dis && prov->prev_dis->num == frame_num)
    return &prov->prev_dis->abs_ts;

  if (prov->prev_cap && prov->prev_cap->num == frame_num)
    return &prov->prev_cap->abs_ts;

  if (prov->frames) {
     frame_data *fd = frame_data_sequence_find(prov->frames, frame_num);

     return (fd) ? &fd->abs_ts : NULL;
  }

  return NULL;
}
#endif

#ifdef WS_2_4
static epan_t *
yaEty_epan_new(capture_file *cf)
{
  epan_t *epan = epan_new();

  epan->data = cf;
  epan->get_frame_ts = yaEty_get_frame_ts;
  epan->get_interface_name = cap_file_get_interface_name;
  epan->get_interface_description = cap_file_get_interface_description;
  epan->get_user_comment = NULL;

  return epan;
}
#else
static epan_t *
yaEty_epan_new(capture_file *cf)
{
  static const struct packet_provider_funcs funcs = {
    yaEty_get_frame_ts,
    cap_file_provider_get_interface_name,
    cap_file_provider_get_interface_description,
    NULL,
  };

  return epan_new(&cf->provider, &funcs);
}
#endif

void reset_epan_mem(capture_file *cf,epan_dissect_t *edt, gboolean tree, gboolean visual)
{
  if (!epan_auto_reset || (cf->count < epan_auto_reset_count))
    return;

  fprintf(stderr, "resetting session.\n");

  epan_dissect_cleanup(edt);
  epan_free(cf->epan);

  cf->epan = yaEty_epan_new(cf);
  epan_dissect_init(edt, cf->epan, tree, visual);
  cf->count = 0;
}

#ifdef WS_2_4
cf_status_t cf_open(capture_file *cf, const char *fname, unsigned int type, gboolean is_tempfile, int *err)
{
  wtap  *wth;
  gchar *err_info;

  wth = wtap_open_offline(fname, type, err, &err_info, FALSE);
  if (wth == NULL)
    goto fail;

  /* The open succeeded.  Fill in the information for this file. */

  /* Create new epan session for dissection. */
  epan_free(cf->epan);
  cf->epan = yaEty_epan_new(cf);

  cf->wth = wth;
  cf->f_datalen = 0; /* not used, but set it anyway */

  /* Set the file name because we need it to set the follow stream filter.
     XXX - is that still true?  We need it for other reasons, though,
     in any case. */
  cf->filename = g_strdup(fname);

  /* Indicate whether it's a permanent or temporary file. */
  cf->is_tempfile = is_tempfile;

  /* No user changes yet. */
  cf->unsaved_changes = FALSE;

  cf->cd_t      = wtap_file_type_subtype(cf->wth);
  cf->open_type = type;
  cf->count     = 0;
  cf->drops_known = FALSE;
  cf->drops     = 0;
  cf->snap      = wtap_snapshot_length(cf->wth);
  nstime_set_zero(&cf->elapsed_time);
  cf->ref = NULL;
  cf->prev_dis = NULL;
  cf->prev_cap = NULL;
  cf->state = FILE_READ_IN_PROGRESS;

  wtap_set_cb_new_ipv4(cf->wth, add_ipv4_name);
  wtap_set_cb_new_ipv6(cf->wth, (wtap_new_ipv6_callback_t) add_ipv6_name);

  return CF_OK;

fail:
  cfile_open_failure_message("YaEty", fname, *err, err_info);
  return CF_ERROR;
}
#else
cf_status_t
cf_open(capture_file *cf, const char *fname, unsigned int type, gboolean is_tempfile, int *err)
{
  wtap  *wth;
  gchar *err_info;

  wth = wtap_open_offline(fname, type, err, &err_info, false);
  if (wth == NULL)
    goto fail;

  /* The open succeeded.  Fill in the information for this file. */

  /* Create new epan session for dissection. */
  epan_free(cf->epan);
  cf->epan = yaEty_epan_new(cf);

  cf->provider.wth = wth;
  cf->f_datalen = 0; /* not used, but set it anyway */

  /* Set the file name because we need it to set the follow stream filter.
     XXX - is that still true?  We need it for other reasons, though,
     in any case. */
  cf->filename = g_strdup(fname);

  /* Indicate whether it's a permanent or temporary file. */
  cf->is_tempfile = is_tempfile;

  /* No user changes yet. */
  cf->unsaved_changes = FALSE;

  cf->cd_t      = wtap_file_type_subtype(cf->provider.wth);
  cf->open_type = type;
  cf->count     = 0;
  cf->drops_known = FALSE;
  cf->drops     = 0;
  cf->snap      = wtap_snapshot_length(cf->provider.wth);
  
  cf->provider.frames = new_frame_data_sequence();
  nstime_set_zero(&cf->elapsed_time);
  cf->provider.ref = NULL;
  cf->provider.prev_dis = NULL;
  cf->provider.prev_cap = NULL;
  cf->cum_bytes = 0;

  cf->state = FILE_READ_IN_PROGRESS;

  wtap_set_cb_new_ipv4(cf->provider.wth, add_ipv4_name);
  wtap_set_cb_new_ipv6(cf->provider.wth, (wtap_new_ipv6_callback_t) add_ipv6_name);

  return CF_OK;

fail:
  cfile_open_failure_message("TShark", fname, *err, err_info);
  return CF_ERROR;
}
#endif

void cf_close(capture_file *cf)
{
  g_free(cf->filename);
}

void capture_session_init(capture_session *cap_session, capture_file *cf)
{
    cap_session->cf              = cf;
    cap_session->fork_child      = WS_INVALID_PID; /* invalid process handle */
    cap_session->state           = CAPTURE_STOPPED;
    cap_session->owner           = getuid();
    cap_session->group           = getgid();
    cap_session->count           = 0;
    cap_session->session_started = FALSE;
}


