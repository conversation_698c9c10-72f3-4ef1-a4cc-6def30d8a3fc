#include"config.h"
#include"epan/epan_dissect.h"

#include"yaEty_utils.h"
#include"yaEty_ws_utils.h"
#include"yaEty_rec_writer_tbl.h"
#include"yaEty_field_extractor.h"
#include"yaEty_field_extractor_iax.h"
#include"yaEty_content_reassembly_share_mem.h"

static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort"						, "udp.srcport"						, eMT_direct,		"",		NULL),
    F_D_ITEM("DstPort"						, "udp.dstport"						, eMT_direct,		"",		NULL),
    F_D_ITEM("C2S"							, ""								, eMT_fixed,		"",		NULL),
    F_D_ITEM("Proto"						, "ip.proto"						, eMT_direct,		"",		NULL),
    F_D_ITEM("TTL"							, "ip.ttl"							, eMT_direct,		"",		NULL),
    F_D_ITEM("IAX_PacketT"					, "iax2.packet_type"				, eMT_direct,		"",		NULL),
    F_D_ITEM("scallno"						, "iax2.src_call"					, eMT_direct,		"",		NULL),
    F_D_ITEM("dcallno"						, "iax2.dst_call"					, eMT_direct,		"",		NULL),
    F_D_ITEM("timestamp"					, "iax2.timestamp"					, eMT_direct,		"",		NULL),
    F_D_ITEM("oseqno"						, "iax2.oseqno"						, eMT_direct,		"",		NULL),
    F_D_ITEM("iseqno"						, "iax2.iseqno"						, eMT_direct,		"",		NULL),
    F_D_ITEM("FULL_FrameT"					, "iax2.type"						, eMT_direct,		"",		NULL),
    F_D_ITEM("IAX_Subclass"					, "iax2.subclass"					, eMT_direct,		"",		NULL),
    F_D_ITEM("calledParty"					, "iax2.iax.called_number"			, eMT_direct,		"",		NULL),
    F_D_ITEM("callingParty"					, "iax2.iax.calling_number"			, eMT_direct,		"",		NULL),
    F_D_ITEM("callingName"					, "iax2.iax.calling_name"			, eMT_direct,		"",		NULL),
    F_D_ITEM("username"						, "iax2.iax.username"				, eMT_direct,		"",		NULL),
    F_D_ITEM("password"						, "iax2.iax.password"				, eMT_direct,		"",		NULL),
    F_D_ITEM("codec"						, "iax2.iax.codecprefs"				, eMT_direct,		"",		NULL),
    F_D_ITEM("language"						, "iax2.iax.language"				, eMT_direct,		"",		NULL),
    F_D_ITEM("Authmethods"					, "iax2.iax.auth.methods"			, eMT_direct,		"",		NULL),
    F_D_ITEM("datetime"						, "iax2.iax.datetime"				, eMT_direct,		"",		NULL),
    F_D_ITEM("calling_number_Type"			, ""								, eMT_direct,		"",		NULL),
    F_D_ITEM("samplerate"					, ""								, eMT_direct,		"",		NULL),
    F_D_ITEM("encryption"					, "iax2.iax.encryption"				, eMT_direct,		"",		NULL),
    F_D_ITEM("payloadLen"					, "iax2.reassembled.length"			, eMT_direct,		"",		NULL),
};

ProtoFieldExtractorIax::ProtoFieldExtractorIax():MatchPrefixProtoFieldExtractor("IAX", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}
