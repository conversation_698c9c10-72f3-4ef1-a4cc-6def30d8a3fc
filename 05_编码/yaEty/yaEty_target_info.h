#pragma once
#include <libpq-fe.h>
#include "yaEty_field_extractor_def.h"

struct common_line_info
{
    std::string signalType;
//dFwdBeginTime、dFwdEndTime、dRetBeginTime、dRetEndTime、sIMSI、sIMEI、sTMSI、dStartTime、dFinishTime、nDiscWay、nRSpotBeamNo、nNSpotBeamNo、sFwdFileName、sRetFileName
    std::string dFwdBeginTime;
    std::string dFwdEndTime;
    std::string dRetBeginTime;
    std::string dRetEndTime;
    std::string sIMSI;
    std::string sIMEI;
    std::string sTMSI;
    std::string dStartTime;
    std::string dFinishTime;
    std::string nDiscWay;
    std::string nRSpotBeamNo;
    std::string nNSpotBeamNo;
    std::string sFwdFileName;   //正向待解析文件名
    std::string sRetFileName;   //正向待解析文件名
    std::string nID;
    std::string nSatNo;

    std::string UserType;
    std::string TARGETTYPE;
    std::string JXH;
    std::string TargetCategory;

    //signal5
    std::string filePath;
    std::string fileID;
    std::string stationID;
    std::string sendTime;
    std::string PTMC;
    std::string SlaveDID;
    std::string TempID172EWS;
    std::string TempID172EN;
    std::string TempID166;

    std::string SourcePath;
};

int dpi_get_fields_from_signal5_target_map(common_line_info *linfo);


int dpi_get_all_signal5_target_info(PGconn *conn_xhts);


void *signal5_target_info_manager_from_db(void *args);


/**
 * 港城路 2J 项目有公共字段, dpi_content 程序通过 文件 的方式传递这部分公共字段,
 * 具体过程如下:
 * 1. dpi_content 输出rtp pcap时, 同时输出附加的.linfo文件,
 *    文件名格式: <pcap>.linfo
 *    文件内容: 只包含公共字段 原始的tbl字符串.
 * 2. yaEty 解析pcap之前读取.linfo文件, 获取 公共字段 内容.
 * 3. 在提取字段时, 首先将 公共字段 作为原始内容写入每行tbl开头,  然后继续提取剩余字段.
 *
 * 注意:
 *  1. 公共字段 内容要与 dpi_content 定义一致, 同步修改
 */
class GCHFieldsExtractor
{
public:
    static int         get_fields_cnt() { return F_D_ITEM_GCH_COMMON_FIELDS_COUNT; }
    static const char *get_file_ext()   { return ".linfo"; }

public:
    GCHFieldsExtractor(const std::string &file_path);
    ~GCHFieldsExtractor();

public:
    bool            failed();
    const char*     get_value();

private:
    FILE            *fp_;
    std::string     file_path_;
    char            buff[10240];
};

