/****************************************************************************************
 * 文 件 名 : yaEty_proto_field_desc_http.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : liugh      '2018-06-25
* 编    码 : liugh      '2018-06-25
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include <algorithm>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_rtp.h"
#include "yaEty_content_reassembly_share_mem.h"


static std::string transform_field_for_voip_file_content(epan_dissect_t *edt, const field_info *pFinfo)
{
    field_info   *finfo     = NULL;
    voip_data    *voip_file = NULL;
    PktInfo       ptf;

    // 从 edt 中收集五元组信息
    finfo = get_first_field_info_from_interesting_fields(edt, "ip.src");
	if(finfo==NULL){return "";}
    ptf.src_ip = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.dst");
	if(finfo==NULL){return "";}
    ptf.dst_ip = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.proto");
	if(finfo==NULL){return "";}
    ptf.proto = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));
	
    
	if(ptf.proto==17){
		finfo = get_first_field_info_from_interesting_fields(edt, "udp.srcport");
	}else if(ptf.proto==6){
		finfo = get_first_field_info_from_interesting_fields(edt, "tcp.srcport");
	}else{
		return "";
	}
	if(NULL==finfo){return "";}
    ptf.src_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

	if(ptf.proto==17){
		finfo = get_first_field_info_from_interesting_fields(edt, "udp.dstport");
	}else if(ptf.proto==6){
		finfo = get_first_field_info_from_interesting_fields(edt, "tcp.dstport");
	}else{
		return "";
	}
	if(NULL==finfo){return "";}
    ptf.dst_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    // hash 获取其还原出的内容文件 path list.
    voip_file = (voip_data *)DispGetHashdb(ptf, MEM_TYPE_VOIP_DATA);
    if (NULL == voip_file)
    {
        return "";
    }

    std::string strJsonFileList;

    // 输出为 json 数组
    Json::Value jArrayPath;
    Json::StreamWriterBuilder jBuilder;
    jBuilder["commentStyle"] = "None";
    jBuilder["indentation"]  = "";

    for(int i = 0; i < voip_file->voip_number; i++){
         jArrayPath.append(Json::Value(voip_file->voip_gather[i].path));
	}

    strJsonFileList = Json::writeString(jBuilder, jArrayPath);
    return strJsonFileList;
}


static ProtoFieldDesc ms_protoFieldDescArray[] =
{
        // RTL tags fields
        F_D_ITEM_RTL_10(),
	F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
	F_D_ITEM("SrcPort",              "udp.srcport",                 eMT_direct,   "",                NULL),
	F_D_ITEM("DstPort",              "udp.dstport",                 eMT_direct,   "",                NULL),
	F_D_ITEM("C2S",                  "",                            eMT_fixed,    "",                NULL),
	F_D_ITEM("Proto",                "ip.proto",                    eMT_direct,   "",                NULL),
	F_D_ITEM("TTL",                  "ip.ttl",                      eMT_direct,   "",                NULL),


	F_D_ITEM("Version",             "rtp.version",                  eMT_direct,   "",                NULL),
	F_D_ITEM("Padding",             "rtp.padding",                  eMT_direct,   "",                NULL),
	F_D_ITEM("Extend",              "rtp.ext",                      eMT_direct,   "",                NULL),
	F_D_ITEM("CC",                  "rtp.cc",                       eMT_direct,   "",                NULL),
	F_D_ITEM("Marker",              "rtp.marker",                   eMT_direct,   "",                NULL),
	F_D_ITEM("Ptype",               "rtp.p_type",                   eMT_direct,   "",                NULL),
	F_D_ITEM("ExtendSeq",           "rtp.extseq",                   eMT_direct,   "",                NULL),

	F_D_ITEM("TimeStamp",           "rtp.timestamp",                eMT_direct,   "",                NULL),
	F_D_ITEM("Ssrc",                "rtp.ssrc",                     eMT_direct,   "",                NULL),
	F_D_ITEM("Profile",             "rtp.ext.profile",              eMT_direct,   "",                NULL),
	F_D_ITEM("ExtendLen",           "rtp.ext.len",                  eMT_direct,   "",                NULL),
	F_D_ITEM("CsrcItems",           "rtp.csrc.items",               eMT_direct,   "",                NULL),
	F_D_ITEM("CsrcItem",            "rtp.csrc.item",                eMT_direct,   "",                NULL),
	F_D_ITEM("HdrExtend",           "rtp.hdr_exts",                 eMT_direct,   "",                NULL),

	F_D_ITEM("Ed137s",              "rtp.ext.ed137s",               eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137",               "rtp.ext.ed137",                eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137PttType",        "rtp.ext.ed137.ptt_type",       eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137Squ",            "rtp.ext.ed137.squ",            eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137PttId",          "rtp.ext.ed137.ptt_id",         eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137Sct",            "rtp.ext.ed137.sct",            eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137X",              "rtp.ext.ed137.x",              eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137X-nu",           "rtp.ext.ed137.x-nu",           eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137FtType",         "rtp.ext.ed137.ft.type",        eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137FtLen",          "rtp.ext.ed137.ft.len",         eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137FtValue",        "rtp.ext.ed137.ft.value",       eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137Vf",             "rtp.ext.ed137.vf",             eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137FtBssQidx",      "rtp.ext.ed137.ft.bss.qidx",    eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137FtBssQidx",      "rtp.ext.ed137.ft.bss.qidx",    eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137FtBssQidx-ml",   "rtp.ext.ed137.ft.bss.qidx-ml", eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137FtBss-nu",       "rtp.ext.ed137.ft.bss-nu",      eMT_direct,   "",                NULL),


	F_D_ITEM("Ed137A",              "rtp.ext.ed137A",               eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137APttType",       "rtp.ext.ed137A.ptt_type",      eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137ASqu",           "rtp.ext.ed137A.squ",           eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137APttId",         "rtp.ext.ed137A.ptt_id",        eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137APm",            "rtp.ext.ed137A.pm",            eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137APtts",          "rtp.ext.ed137A.ptts",          eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137ASct",           "rtp.ext.ed137a.sct",           eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137AReserved",      "rtp.ext.ed137A.reserved",      eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137AX",             "rtp.ext.ed137A.x",             eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137AX-nu",          "rtp.ext.ed137A.x-nu",          eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137AFtType",        "rtp.ext.ed137A.ft.type",       eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137AFtLen",         "rtp.ext.ed137A.ft.len",        eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137AFtValue",       "rtp.ext.ed137A.ft.value",      eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137AFtSqiQidx",     "rtp.ext.ed137A.ft.sqi.qidx",   eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137AFtSqiQidx",     "rtp.ext.ed137A.ft.sqi.qidx",   eMT_direct,   "",                NULL),
	F_D_ITEM("Ed137AFtSqiQidx-ml",  "rtp.ext.ed137A.ft.sqi.qidx-ml",eMT_direct,   "",                NULL),

	F_D_ITEM("HdrExtend",           "rtp.hdr_ext",                  eMT_direct,   "",                NULL),
	//F_D_ITEM("Payload",             "rtp.payload",                  eMT_direct,   "",                NULL),
	F_D_ITEM("PaddingData",         "rtp.padding.data",             eMT_direct,   "",                NULL),
	F_D_ITEM("PaddingCount",        "rtp.padding.count",            eMT_direct,   "",                NULL),
	F_D_ITEM("Setup",               "rtp.setup",                    eMT_direct,   "",                NULL),
	F_D_ITEM("Setup-frame",         "rtp.setup-frame",              eMT_direct,   "",                NULL),
	F_D_ITEM("Setup-method",        "rtp.setup-method",             eMT_direct,   "",                NULL),
	F_D_ITEM("Fllow",               "rtp.follow",                   eMT_direct,   "",                NULL),
	F_D_ITEM("Timestamp-offset",    "rtp.timestamp-offset",         eMT_direct,   "",                NULL),
	F_D_ITEM("Block-length",        "rtp.block-length",             eMT_direct,   "",                NULL),

	F_D_ITEM("Rfc5285Id",           "rtp.ext.rfc5285.id",           eMT_direct,   "",                NULL),
	F_D_ITEM("Rfc5285Len",          "rtp.ext.rfc5285.len",          eMT_direct,   "",                NULL),
	F_D_ITEM("Rfc5285Appbits",      "rtp.ext.rfc5285.appbits",      eMT_direct,   "",                NULL),
	F_D_ITEM("Rfc5285Data",         "rtp.ext.rfc5285.data",         eMT_direct,   "",                NULL),
	F_D_ITEM("Rfc4571Len",          "rtp.rfc4571.len",              eMT_direct,   "",                NULL),

	F_D_ITEM("Fragments",           "rtp.fragments",                eMT_direct,   "",                NULL),
	F_D_ITEM("Fragment",            "rtp.fragment",                 eMT_direct,   "",                NULL),
	F_D_ITEM("FragOverlap",         "rtp.fragment.overlap",         eMT_direct,   "",                NULL),
	F_D_ITEM("FragOveerlapCon",     "rtp.fragment.overlap.conflict",eMT_direct,   "",                NULL),
	F_D_ITEM("FragMulipletails",    "rtp.fragment.multipletails",   eMT_direct,   "",                NULL),
	F_D_ITEM("FragToolongFrag",     "rtp.fragment.toolongfragment", eMT_direct,   "",                NULL),
	F_D_ITEM("FragError",           "rtp.fragment.error",           eMT_direct,   "",                NULL),
	F_D_ITEM("FragCount",           "rtp.fragment.count",           eMT_direct,   "",                NULL),

	F_D_ITEM("ReassembledIn",       "rtp.reassembled_in",           eMT_direct,   "",                NULL),
	F_D_ITEM("RtpStream",           "",                             eMT_fromEdt,  "",               transform_field_for_voip_file_content),
	F_D_ITEM("ReaLength",           "rtp.reassembled.length",       eMT_direct | eMT_lastplain,   "",NULL),

};

ProtoFieldExtractorRtp::ProtoFieldExtractorRtp()
    : ProtoFieldExtractor("RTP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}


bool ProtoFieldExtractorRtp::ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter)
{

	return TRUE;
}
