/****************************************************************************************
 * 文 件 名 : pkt_writer.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-03-16
* 编    码 : root      '2018-03-16
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _YAETY_REC_WRITER_H_
#define _YAETY_REC_WRITER_H_

class RecordWriter
{
public:
    virtual ~RecordWriter(){}

public: // interface
    virtual int   writeRecordField(const char *strFieldName, const char *strFieldValue) = 0;
    virtual int   writeRecordField(const char *strFieldName, const std::string &strFieldValue) = 0;
    virtual int   writeRawFields(const char *fieldsValue, int field_cnt) = 0;
    virtual int   writeNextRecord() = 0;
};

#endif /* _YAETY_REC_WRITER_H_ */
