/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_dns.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-24
* 编    码 : zhengsw      '2018-05-24
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_field_extractor_dns.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_ws_utils.h"
#include "yaEty_utils.h"

#define ITEM_DNS_SEP_OTHER(grp)                             \
        F_D_ITEM(grp "_Other", "", eMT_fixed,   "", NULL)

#define DNS_QUERY_ITEM_GROUP(grp,   NO)                                        \
        F_D_ITEM(grp "_name" NO ,"dns.qry.name",  eMT_fixed,   "",      NULL), \
        F_D_ITEM(grp "_type" NO ,"dns.qry.type",  eMT_fixed,   "",      NULL), \
        F_D_ITEM(grp "_cls"  NO ,"dns.qry.class", eMT_fixed,   "",      NULL)

#define DNS_QUERY_ITEM_GROUPS_4(grp, a, b, c, d) \
        DNS_QUERY_ITEM_GROUP(grp, a),            \
        DNS_QUERY_ITEM_GROUP(grp, b),            \
        DNS_QUERY_ITEM_GROUP(grp, c),            \
        DNS_QUERY_ITEM_GROUP(grp, d)

#define DNS_ANSWER_ITEM_GROUP(grp, NO)                                                \
        F_D_ITEM(grp "_type"      NO ,"dns.resp.type",  eMT_fixed,   "",      NULL), \
        F_D_ITEM(grp "_cls"       NO ,"dns.resp.class", eMT_fixed,   "",      NULL), \
        F_D_ITEM(grp "_name"      NO ,"dns.resp.name",  eMT_fixed,   "",      NULL), \
        F_D_ITEM(grp "_ttl"       NO ,"dns.resp.ttl",   eMT_fixed,   "",      NULL), \
        F_D_ITEM(grp "_len"       NO ,"dns.resp.len",   eMT_fixed,   "",      NULL), \
        F_D_ITEM(grp "_rdata"     NO ,"dns.cname",      eMT_fixed,   "",      NULL)

#define DNS_ANSWER_ITEM_GROUPS_4(grp, a, b, c, d)    \
        DNS_ANSWER_ITEM_GROUP(grp, a),               \
        DNS_ANSWER_ITEM_GROUP(grp, b),               \
        DNS_ANSWER_ITEM_GROUP(grp, c),               \
        DNS_ANSWER_ITEM_GROUP(grp, d)

static const    int QUERY_GRP_ITEM_CNT  = 3;
static const    int QUERY_CNT_MAX       = 8;
static const    int ANSWER_GRP_ITEM_CNT = 6;
static const    int ANSWER_CNT_MAX      = 16;

static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // RTL tags fields
    F_D_ITEM_RTL_10(),

    // common fields
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    // special fields
    F_D_ITEM("SrcPort" , "tcp.srcport", eMT_direct,    "",      NULL),
    F_D_ITEM("DstPort" , "tcp.dstport", eMT_direct,    "",      NULL),
    F_D_ITEM("C2S"     , "",            eMT_fixed,     "",      NULL),
    F_D_ITEM("Proto"   , "ip.proto",    eMT_direct,    "",      NULL),
    F_D_ITEM("TTL"     , "ip.ttl",      eMT_direct,    "",      NULL),

    // DNS fields
    F_D_ITEM("dns_length"        ,"dns.length",              eMT_direct,   "",      NULL),
    F_D_ITEM("identification"    ,"dns.id",                  eMT_direct,   "",      NULL),
    F_D_ITEM("response"          ,"dns.flags.response",      eMT_direct,   "",      NULL),
    F_D_ITEM("opcode"            ,"dns.flags.opcode",        eMT_direct,   "",      NULL),
    F_D_ITEM("auth_answer"       ,"dns.flags.authenticated", eMT_direct,   "",      NULL),
    F_D_ITEM("truncated"         ,"dns.flags.truncated",     eMT_direct,   "",      NULL),
    F_D_ITEM("recursion_req"     ,"dns.flags.recdesired",    eMT_direct,   "",      NULL),
    F_D_ITEM("recursion_avail"   ,"dns.flags.recavail",      eMT_direct,   "",      NULL),
    F_D_ITEM("return_code"       ,"dns.flags.rcode",         eMT_direct,   "",      NULL),
    F_D_ITEM("questions"         ,"dns.count.queries",       eMT_direct,   "",      NULL),
    F_D_ITEM("answer_RRs"        ,"dns.count.awsers",        eMT_direct,   "",      NULL),
    F_D_ITEM("authorization_RRs" ,"dns.count.auth_rr",       eMT_direct,   "",      NULL),
    F_D_ITEM("additional_RRs"    ,"dns.count.add_rr",        eMT_direct | eMT_lastplain,   "",      NULL),

    // Qd_name Qd_type Qd_class (00-07)
    // Qd_Other
    DNS_QUERY_ITEM_GROUPS_4("Qd", "00", "01", "02", "03"),
    DNS_QUERY_ITEM_GROUPS_4("Qd", "04", "05", "06", "07"),
    ITEM_DNS_SEP_OTHER("Qd"),

    // An_type An_cls An_name An_ttl An_len An_rdata (00-15)
    // An_Other
    DNS_ANSWER_ITEM_GROUPS_4("An", "00", "01", "02", "03"),
    DNS_ANSWER_ITEM_GROUPS_4("An", "04", "05", "06", "07"),
    DNS_ANSWER_ITEM_GROUPS_4("An", "08", "09", "10", "11"),
    DNS_ANSWER_ITEM_GROUPS_4("An", "12", "13", "14", "15"),
    ITEM_DNS_SEP_OTHER("An"),

    // An_type An_cls An_name An_ttl An_len An_rdata (00-15)
    // An_Other
    DNS_ANSWER_ITEM_GROUPS_4("Au", "00", "01", "02", "03"),
    DNS_ANSWER_ITEM_GROUPS_4("Au", "04", "05", "06", "07"),
    DNS_ANSWER_ITEM_GROUPS_4("Au", "08", "09", "10", "11"),
    DNS_ANSWER_ITEM_GROUPS_4("Au", "12", "13", "14", "15"),
    ITEM_DNS_SEP_OTHER("Au"),

    // Ad_type Ad_cls Ad_name Ad_ttl Ad_len Ad_rdata (00-15)
    // Ad_Other
    DNS_ANSWER_ITEM_GROUPS_4("Ad", "00", "01", "02", "03"),
    DNS_ANSWER_ITEM_GROUPS_4("Ad", "04", "05", "06", "07"),
    DNS_ANSWER_ITEM_GROUPS_4("Ad", "08", "09", "10", "11"),
    DNS_ANSWER_ITEM_GROUPS_4("Ad", "12", "13", "14", "15"),
    ITEM_DNS_SEP_OTHER("Ad"),
};

ProtoFieldExtractorDns::ProtoFieldExtractorDns()
    : ProtoFieldExtractor("DNS", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}

field_info * ProtoFieldExtractorDns::ExtractDnsQaGroupItemOf(epan_dissect_t *edt, RecordWriter *pWriter,
                                                             int groupIndex, int lItemDescIndex,
                                                             const char *pRealWsFieldName)
{
    int             field_id        = 0;
    ProtoFieldDesc *pProtoFieldDesc = NULL;
    field_info     *finfo           = NULL;
    GPtrArray      *finfos          = NULL;
    std::string    strWsFieldValue;

    pProtoFieldDesc = GetProtoFieldDescByIndex(lItemDescIndex);
    field_id   = proto_registrar_get_id_byname(pRealWsFieldName ? pRealWsFieldName : (const char *)pProtoFieldDesc->wsFieldName);
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);

    if (NULL == finfos
        || groupIndex >= g_ptr_array_len(finfos))
    {   // 没有这组值，或者 index 出错？
        pWriter->writeRecordField(pProtoFieldDesc->etyFieldName, pProtoFieldDesc->defaultFieldValue);
        return NULL;
    }

    finfo = (field_info *)g_ptr_array_index(finfos, groupIndex);
    strWsFieldValue = ety_ws_get_node_field_value(finfo, edt);

    pWriter->writeRecordField(pProtoFieldDesc->etyFieldName, strWsFieldValue);

    return finfo;
}

int ProtoFieldExtractorDns:: ExtractDnsQueryGroupNth(epan_dissect_t *edt, RecordWriter *pWriter,
                                                     int groupIndex, int groupItemCnt, int lGrpFirstDescIndex)
{
    for (int i = 0; i < groupItemCnt; i++)
    {
        ExtractDnsQaGroupItemOf(edt, pWriter, groupIndex, lGrpFirstDescIndex + i, NULL);
    }

    return groupItemCnt;
}

int ProtoFieldExtractorDns:: ExtractDnsAnswerGroupNth(epan_dissect_t *edt, RecordWriter *pWriter,
                                                     int groupIndex, int groupItemCnt, int lGrpFirstDescIndex)
{
    static const char *s_mapRdata[20] =
    {
                                 NULL,
    /* T_A              1  */    "dns.a",
    /* T_NS             2  */    NULL,
    /* T_MD             3  */    NULL,
    /* T_MF             4  */    NULL,
    /* T_CNAME          5  */    "dns.cname",
    };

    s_mapRdata[15] = "dns.mx.mail_exchange";

    // AnswerType, 用于判断需要拿 address 还是需要拿 cname 作为第五个字段
    field_info *finfoAnswerType =  ExtractDnsQaGroupItemOf(edt, pWriter, groupIndex,  lGrpFirstDescIndex, NULL);
	if(nullptr == finfoAnswerType)
	{
		return 0;
	}
    uint32_t    answerType = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfoAnswerType->value));

    // 后续 groupItemCnt - 1 个字段
    for (int i = 1; i < groupItemCnt - 1; i++)
    {
        ExtractDnsQaGroupItemOf(edt, pWriter, groupIndex, lGrpFirstDescIndex + i, NULL);
    }

    // 最后的 address 或者 cname. TODO: Rdata 的 type 没有补全，目前不支持的暂不输出，需要重新设计
    ExtractDnsQaGroupItemOf(edt, pWriter, groupIndex, lGrpFirstDescIndex + groupItemCnt - 1,
                            answerType >= dimen_of(s_mapRdata) ? NULL : s_mapRdata[answerType]);

    return groupItemCnt;
}

int ProtoFieldExtractorDns::OutputDefaultValueForSomeField(RecordWriter *pWriter, int fieldIndexStart, int fieldCnt)
{
    ProtoFieldDesc *pProtoFieldDesc = NULL;

    for (int i = 0; i < fieldCnt; i++)
    {
        pProtoFieldDesc = GetProtoFieldDescByIndex(fieldIndexStart + i);
        pWriter->writeRecordField(pProtoFieldDesc->etyFieldName, pProtoFieldDesc->defaultFieldValue);
    }

    return fieldCnt;
}

bool ProtoFieldExtractorDns::ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter)
{
    field_info *pfinfoQueryCnt      = NULL;
    field_info *pfinfoAnswerCnt     = NULL;
    int         lQueryCnt           = 0;
    int         lAnswerCnt          = 0;
    int         lQueryCntExtracted  = 0;
    int         lAnswerCntExtracted = 0;

    pfinfoQueryCnt = get_first_field_info_from_interesting_fields(edt, "dns.count.queries");
    lQueryCnt = pfinfoQueryCnt ? fvalue_get_uinteger(const_cast<fvalue_t *>(&pfinfoQueryCnt->value)) : 0;

    pfinfoAnswerCnt = get_first_field_info_from_interesting_fields(edt, "dns.count.answers");
    lAnswerCnt = pfinfoAnswerCnt ? fvalue_get_uinteger(const_cast<fvalue_t *>(&pfinfoAnswerCnt->value)) : 0;

    //   1) 解析 Qd
    // 1.a) 按照 dns.count.queries 解析 query
    for (; lQueryCntExtracted < MIN(lQueryCnt, QUERY_CNT_MAX); lQueryCntExtracted++)
    {
        nFieldIndexToExtract += ExtractDnsQueryGroupNth(edt, pWriter, lQueryCntExtracted,
                                                        QUERY_GRP_ITEM_CNT, nFieldIndexToExtract);
    }

    // 1.b) 剩下的输出 default value
    for (int i = lQueryCntExtracted; i < QUERY_CNT_MAX; i++)
    {
        nFieldIndexToExtract += OutputDefaultValueForSomeField(pWriter, nFieldIndexToExtract, QUERY_GRP_ITEM_CNT);
    }
    nFieldIndexToExtract += OutputDefaultValueForSomeField(pWriter, nFieldIndexToExtract, 1);

    //   2) 解析 An
    // 2.a) 按照 dns.count.awsers 解析 awser
    for (NULL;
         lAnswerCntExtracted < MIN(lAnswerCnt, ANSWER_CNT_MAX);
         lAnswerCntExtracted++)
    {
        nFieldIndexToExtract += ExtractDnsAnswerGroupNth(edt, pWriter, lAnswerCntExtracted,
                                                         ANSWER_GRP_ITEM_CNT, nFieldIndexToExtract);
    }

    // 剩余的全部输出默认值
    OutputDefaultValueForSomeField(pWriter, nFieldIndexToExtract,
                                   this->GetFieldsCount() - nFieldIndexToExtract);
    return true;
}
