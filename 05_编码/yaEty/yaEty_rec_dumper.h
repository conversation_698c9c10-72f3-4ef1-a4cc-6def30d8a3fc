/****************************************************************************************
 * 文 件 名 : yaEty_rec_dumpper.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-08-22
* 编    码 : root      '2018-08-22
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _YAETY_REC_DUMPPER_H_
#define _YAETY_REC_DUMPPER_H_

// 3rdlib
#include <wiretap/wtap.h>

// forward declaration
typedef struct epan_dissect epan_dissect_t;

class RecordDumper
{
public:
    ~RecordDumper(){}

public:
    virtual bool         dump(const wtap_rec *rec, const uint8_t *buf, const char *pClassifyFieldValue) = 0;
};

#endif /* _YAETY_REC_DUMPPER_H_ */
