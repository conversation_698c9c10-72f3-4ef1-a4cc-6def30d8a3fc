#ifndef RTP_H264_PACKER_H
#define RTP_H264_PACKER_H

#include <vector>
#include <stdint.h>

enum NaluType_e {
    NT_none                  = 0,  //  "Undefined"
    NT_slice_nIDR            = 1,  //  "NAL unit - Coded slice of a non-IDR picture"
    NT_slice_data_part_A     = 2,  //  "NAL unit - Coded slice data partition A"
    NT_slice_data_part_B     = 3,  //  "NAL unit - Coded slice data partition B"
    NT_slice_data_part_C     = 4,  //  "NAL unit - Coded slice data partition C"
    NT_slice_IDR             = 5,  //  "NAL unit - Coded slice of an IDR picture"
    NT_SEI                   = 6,  //  "NAL unit - Supplemental enhancement information (SEI)"
    NT_SPS                   = 7,  //  "NAL unit - Sequence parameter set"
    NT_PPS                   = 8,  //  "NAL unit - Picture parameter set"
    NT_access_unit_delimiter = 9,  //  "NAL unit - Access unit delimiter"
    NT_end_of_seq            = 10, //  "NAL unit - End of sequence"
    NT_end_of_stream         = 11, //  "NAL unit - End of stream"
    NT_filter_data           = 12, //  "NAL unit - Filler data"
    NT_SPS_ext               = 13, //  "NAL unit - Sequence parameter set extension"
    NT_prefix                = 14, //  "NAL unit - Prefix"
    NT_sub_SPS               = 15, //  "NAL unit - Subset sequence parameter set"
    NT_resv_16               = 16, //  "NAL unit - Reserved"
    NT_resv_17               = 17, //  "NAL unit - Reserved"
    NT_resv_18               = 18, //  "NAL unit - Reserved"
    NT_slice_aux             = 19, //  "NAL unit - Coded slice of an auxiliary coded picture without partitioning"
    NT_slice_ext             = 20, //  "NAL unit - Coded slice extension"
    NT_slice_ext_depth_view  = 21, //  "NAL unit - Coded slice extension for depth view components"
    NT_resv_22               = 22, //  "NAL unit - Reserved"
    NT_resv_23               = 23, //  "NAL unit - Reserved"
    NT_STAP_A                = 24, //  "Single-time aggregation packet A (STAP-A)"
    NT_STAP_B                = 25, //  "Single-time aggregation packet B (STAP-B)"
    NT_MTAP_16               = 26, //  "Multi-time aggregation packet 16 (MTAP16)"
    NT_MTAP_24               = 27, //  "Multi-time aggregation packet 24 (MTAP24)"
    NT_FU_A                  = 28, //  "Fragmentation unit A (FU-A)"
    NT_FU_B                  = 29, //  "Fragmentation unit B (FU-B)"
    NT_PACSI                 = 30, //  "NAL unit - Payload Content Scalability Information (PACSI)"
    NT_EAH                   = 31, //  "NAL unit - Extended NAL Header"
};

typedef int (*onGotNalu_callback_t)(NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata);

// rtp h264 解包器
// 输入为 rtp 负载，输出为 nalu(回调)
class RtpH264Unpacker
{
public:
    RtpH264Unpacker(onGotNalu_callback_t callback, void *userdata);

public:
    int enqueueRtpPayload(uint8_t *rtpPayload, int len);

private:
    int processSTAP_A(uint8_t *rtpPayload, int len);

    int processFU_A(uint8_t *rtpPayload, int len);

private:
    onGotNalu_callback_t onGotNalu_func_;
    void *userdata_;
    std::vector<uint8_t> naluBuff_;             // 存放一个 nulu 的 buff;
};

#endif /* RTP_H264_UNPACKER_H */
