/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_ssl_n.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-12-03
* 编    码 : zhangsx      '2018-12-03
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include"config.h"
#include"epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ftypes/ftypes.h"             // for field_info

#include <regex>
#include"yaEty_utils.h"
#include"yaEty_ws_utils.h"
#include"yaEty_rec_writer_tbl.h"
#include"yaEty_field_extractor.h"
#include"yaEty_field_extractor_ssl_n.h"
#include"yaEty_content_reassembly_share_mem.h"

/*****************************************************************
*Function    :transform_field_for_C2S
*Description :判断ssl是C2S还是S2C
*Input       :epan_dissect_t, field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_field_for_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{

    field_info* pfinfo = get_first_field_info_from_interesting_fields(edt, "tcp.srcport");
    unsigned int && ssl_s = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pfinfo->value)));


    pfinfo = get_first_field_info_from_interesting_fields(edt, "tcp.dstport");
    unsigned int && ssl_d = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pfinfo->value)));

    return ssl_s > ssl_d ? "C2S" : "S2C";

}


static std::string transform_for_field_length(epan_dissect_t *edt, const field_info *pFinfo)
{
    return (NULL != pFinfo) ? std::to_string(pFinfo->length) : "";
}
    
/*****************************************************************
*Function    :transform_ssl_field_num
*Description :获取ssl中指定字段的数目
*Input       :epan_dissect_t, field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_ssl_field_num(epan_dissect_t *edt, const field_info *pFinfo)
{
    int field_id = proto_registrar_get_id_byname("ssl.handshake.certificate");
    if (field_id == -1)
    {
        return "0";
    }

    GPtrArray *finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);


    return finfos_array ? std::to_string(g_ptr_array_len(finfos_array)) : "0";
}

/*****************************************************************
*Function    :transform_for_field_hex
*Description :获取ssl中指定字段的值的十六进制字符串
*Input       :epan_dissect_t, field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_for_field_hex(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (nullptr == pFinfo)
    {
        return "";
    }
    else
    {
        char * pHexStr = ws_tvb_bytes_to_str(NULL, pFinfo->ds_tvb, pFinfo->start, pFinfo->length);//这里从完整的合并包后产生的树上获取数据,wireshark无对应字段值
        std::string strRst = pHexStr;
        wmem_free(NULL, pHexStr);

        return strRst;
    }
}

/*****************************************************************
*Function    :transform_for_field_value
*Description :获取ssl中指定字段的值对应的文字描述的字符串
*Input       :epan_dissect_t, field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_for_field_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (nullptr == pFinfo || pFinfo->length == 0 )
    {
        return "";
    }

    int lValue = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));          //协议中字段对应值为整数

    const char* pString = try_val_to_str(lValue, (const value_string *)(pFinfo->hfinfo->strings)); //提取对应字符串

    if (nullptr == pString)
    {
        return std::to_string(lValue);
    }
    else
    {
        return std::string(pString);
    }
}

static std::string transform_for_field_value_ext(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (nullptr == pFinfo)
    {
        return "";
    }

    int lValue = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));          //协议中字段对应值为整数

    const char* pString = try_val_to_str_ext(lValue, ( value_string_ext *)(pFinfo->hfinfo->strings)); //提取对应字符串

    if (nullptr == pString)
    {
        return std::to_string(lValue);
    }
    else
    {
        return std::string(pString);
    }
}

static std::string transform_for_field_rep(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (nullptr == pFinfo)
    {
        return "";
    }

    std::string strRes = "";
    
    if (nullptr != pFinfo->rep)
    {
        std::string &&t_str = std::string(pFinfo->rep->representation);

        if (t_str.find('('))
        {
            int a = t_str.find_first_of('(');
            int b = t_str.find_first_of(')');
            if (a < b)
            {
                t_str = std::string(&std::move(t_str).c_str()[a], &std::move(t_str).c_str()[b]);
            }
            
            std::size_t && t_pos = t_str.find_first_of("(");
            strRes = t_str.substr(t_pos + 1);
        }
    }
    return strRes;
}

struct transform_ssl_field_match_types
{
public: 
    transform_ssl_field_match_types(int type, const char * field_name)
    :_type(type), _field_name(field_name)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        if (nullptr == pFinfo)
        {
            return "";
        }
        
        field_info* pfinfo = get_first_field_info_from_interesting_fields(edt, _field_name);
        
        if (nullptr == pfinfo)
        {
            return "";
        }
        
        if(_type !=  fvalue_get_uinteger(const_cast<fvalue_t *>(&pfinfo->value)))
        {
            return "";
        }
        else
        {
            return ety_ws_get_node_field_value(const_cast<field_info *>(pFinfo), edt);
        }
    }
    
public:
    const int _type;
    const char* _field_name;
};

struct get_nth_certificate_ssl_field
{
public:
    get_nth_certificate_ssl_field(int index, const char * field_name)
    :_index(index), _field_name(field_name)
    {
    }
    
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info* pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _index-1);
        
        if(nullptr == pfinfo)
        {
            return "";
        }
        else
        {
            return ety_ws_get_node_field_value(pfinfo, edt);
        }
    }
    
public:
    const int _index;
    const char* _field_name;
};  

struct get_nth_certificate_ssl_time : public get_nth_certificate_ssl_field
{
public:
    get_nth_certificate_ssl_time(int index, const char * field_name)
    :get_nth_certificate_ssl_field(index, field_name)
    {   
    }
    
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info* pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _index-1);
        
        if(nullptr == pfinfo)
        {
            return "";
        }
        
        if (pfinfo->pnode->first_child != nullptr)
        {
            pfinfo = pfinfo->pnode->first_child->finfo;
        }
        else
        {
            return "";
        }
        
        if(nullptr == pfinfo)
        {
            return "";
        }
        else
        {
            return ety_ws_get_node_field_value(pfinfo, edt);
        }   
    }   
};

struct get_nth_certificate_ssl_length : public get_nth_certificate_ssl_field
{
public:
    get_nth_certificate_ssl_length(int index, const char * field_name)
    :get_nth_certificate_ssl_field(index, field_name)
    {   
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info* pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _index-1);
        
        if(nullptr == pfinfo)
        {
            return "";
        }
        else
        {
            return transform_for_field_length(edt, pfinfo);
        }
    }
    
};

struct get_nth_certificate_ssl_hex : public get_nth_certificate_ssl_field
{
public:
    get_nth_certificate_ssl_hex(int index, const char * field_name)
    :get_nth_certificate_ssl_field(index, field_name)
    {   
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info* pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _index-1);
        
        if(nullptr == pfinfo)
        {
            return "";
        }
        else
        {
            char * pHexStr = ws_tvb_bytes_to_str(NULL, pfinfo->ds_tvb, pfinfo->start, pfinfo->length);//这里从完整的合并包后产生的树上获取数据,wireshark无对应字段值
            std::string strRst = pHexStr;
            wmem_free(NULL, pHexStr);

            return strRst;
        }
    }
    
};

struct get_nth_certificate_ssl_special : public get_nth_certificate_ssl_field
{
public:
    get_nth_certificate_ssl_special(int index, const char * field_name)
        :get_nth_certificate_ssl_field(index, field_name)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info* pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _index-1);

        if (nullptr == pfinfo || nullptr == pfinfo->pnode->first_child)
        {
            return "";
        }
        else
        {
            pfinfo = pfinfo->pnode->first_child->finfo;
            if (nullptr == pfinfo)
            {
                return "";
            }
            std::string strRes = "";
            /*if (nullptr != pfinfo->hfinfo)
            {
                std::string t_str = std::string(pfinfo->rep->representation);

                if (t_str.find('('))
                {
                    strRes = std::string(&t_str.c_str()[t_str.find_first_of('(')], &t_str.c_str()[t_str.find_first_of(')')]);
                    std::size_t && t_pos = strRes.find_first_of("(");
                    strRes = strRes.substr(t_pos + 1);
                }

            }*/
            if (nullptr == pfinfo->pnode)
            {
                return strRes;
            }

            for (proto_node *pnode = pfinfo->pnode->first_child ; pnode!= nullptr ; pnode = pnode->next)
            {
                std::string && t_str = std::string(pnode->finfo->rep->representation);
                std::string && p_str = std::string(&t_str.c_str()[t_str.find_first_of('(')], &t_str.c_str()[t_str.find_first_of(')')]);
                std::size_t && t_pos = p_str.find_first_of("(");
                strRes += p_str.substr(t_pos + 1);
                if (pnode != pfinfo->pnode->last_child)
                {
                    strRes += ",";                    
                }               
            }
            return strRes;
        }
    }

};

struct get_nth_certificate_ssl_dns : public get_nth_certificate_ssl_field
{
public:
    get_nth_certificate_ssl_dns(int index, const char* fieldname)
    :get_nth_certificate_ssl_field(index,fieldname)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info* pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _index - 1);

        if (nullptr == pfinfo || nullptr == pfinfo->pnode->first_child)
        {
            return "";
        }
        else
        {
            std::string strRes = "";         
         
            for (proto_node *pnode = pfinfo->pnode->first_child; pnode != nullptr; pnode = pnode->next)
            {
                if (nullptr == pnode->first_child)
                {
                    return "";
                }
                proto_node *dns_node = pnode->first_child;
                strRes += ety_ws_get_node_field_value(dns_node->finfo, edt);
                if (pnode != pfinfo->pnode->last_child)
                {
                    strRes += ",";
                }
            }
            return strRes;
        }
    }
};


struct transform_ssl_extension_match_types : public transform_ssl_field_match_types
{
public:
    transform_ssl_extension_match_types(int type, const char * field_name)
    :transform_ssl_field_match_types(type, field_name)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        std::string strRst = "";

        field_info* pfinfo = get_first_field_info_from_interesting_fields(edt, _field_name);

        if (_type != fvalue_get_uinteger(const_cast<fvalue_t *>(&pfinfo->value)))
        {
            return "";
        }

        if (nullptr == pFinfo)
        {
            return "";
        }

        strRst += transform_for_field_value(edt,pFinfo);
        strRst += ":";
        proto_node * pnode = pFinfo->pnode->parent;
        if ( pnode->last_child == pnode->first_child->next)
        {
            strRst.append("null");
        }
        else if (pnode->last_child->first_child != nullptr)
        {
            if (strcmp(pnode->last_child->first_child->finfo->hfinfo->abbrev, pnode->last_child->last_child->finfo->hfinfo->abbrev) == 0)
            {
                for (proto_node * ptr_node = pnode->last_child->first_child; ptr_node != nullptr; ptr_node = ptr_node->next)
                {
                    if (ptr_node->finfo->rep->representation != nullptr)
                    {
                        strRst += ptr_node->finfo->rep->representation;
                    }
                    else if ( ptr_node->finfo->value.ftype->ftype > FT_NONE && ptr_node->finfo->value.ftype->ftype < FT_UINT40 )
                    {
                        strRst += transform_for_field_value(edt, ptr_node->finfo);
                    }
                    else
                    {
                        strRst += ety_ws_get_node_field_value(ptr_node->finfo, edt);
                    }    
                                        
                    if (ptr_node != pnode->last_child->last_child)
                    {
                        strRst.append(",");
                    }
                }
            }
            else
            {
                strRst += ety_ws_get_node_field_value(pnode->last_child->last_child->finfo, edt);
            }
        }
        else
        {
            std::string && val = ety_ws_get_node_field_value(pnode->last_child->finfo, edt);
            if (val == "")
            {
                strRst.append("null");
            }
            else
            {
                strRst += val;
            }
        }

        return strRst;
    }

};

struct transform_ssl_value_match_types : public transform_ssl_field_match_types
{
    transform_ssl_value_match_types(int type, const char * field_name)
    :transform_ssl_field_match_types(type, field_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        if (nullptr == pFinfo)
        {
            return "";
        }

        field_info* pfinfo = get_first_field_info_from_interesting_fields(edt, _field_name);

        if (nullptr == pfinfo)
        {
            return "";
        }

        if (_type != fvalue_get_uinteger(const_cast<fvalue_t *>(&pfinfo->value)))
        {
            return "";
        }
        else
        {
            return transform_for_field_value(edt,pFinfo);
        }
    }
};

struct transform_ssl_value_ext_match_types : public transform_ssl_field_match_types
{
    transform_ssl_value_ext_match_types(int type, const char * field_name)
    :transform_ssl_field_match_types(type, field_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        if (nullptr == pFinfo)
        {
            return "";
        }

        field_info* pfinfo = get_first_field_info_from_interesting_fields(edt, _field_name);

        if (nullptr == pfinfo)
        {
            return "";
        }

        if (_type != fvalue_get_uinteger(const_cast<fvalue_t *>(&pfinfo->value)))
        {
            return "";
        }
        else
        {
            return transform_for_field_value_ext(edt,pFinfo);
        }
    }
};

struct transform_ssl_hex_match_types : public transform_ssl_field_match_types
{
    transform_ssl_hex_match_types(int type, const char * field_name)
    :transform_ssl_field_match_types(type, field_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        if (nullptr == pFinfo)
        {
            return "";
        }

        field_info* pfinfo = get_first_field_info_from_interesting_fields(edt, _field_name);

        if (nullptr == pfinfo)
        {
            return "";
        }

        if (_type != fvalue_get_uinteger(const_cast<fvalue_t *>(&pfinfo->value)))
        {
            return "";
        }
        else
        {
            /*
            std::string && tempRst = ety_ws_get_node_field_value(const_cast<field_info *>(pFinfo), edt);
            char * pHexStr = ws_bytes_to_str(NULL, (unsigned char*)const_cast<char *>(tempRst.c_str()), tempRst.length());
            std::string strRst = pHexStr;
            wmem_free(NULL, pHexStr);
            */
            char * pHexStr = ws_tvb_bytes_to_str(NULL, pFinfo->ds_tvb, pFinfo->start, pFinfo->length);//这里从完整的合并包后产生的树上获取数据,wireshark无对应字段值
            std::string strRst = pHexStr;
            wmem_free(NULL, pHexStr);

            return strRst;
        }
    }
};


struct transform_ssl_specical_match
{
public:
    transform_ssl_specical_match(const char* fieldname, std::string(*function)(epan_dissect_t *edt, const field_info *pFinfo))
    :_field_name(fieldname), _function(function)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        if (nullptr != get_first_field_info_from_interesting_fields(edt, _field_name))
        {
            return _function(edt, pFinfo);
        }
        return "";
    }
public:
    std::string(*_function)(epan_dissect_t *edt, const field_info *pFinfo);
    const char* _field_name;
};

struct transform_choose_for_two_type_field : public choose_for_two_type_field
{
    transform_choose_for_two_type_field(const char * fieldname1, const char * fieldname2, std::string(*function)(epan_dissect_t *edt, const field_info *pFinfo))
    :choose_for_two_type_field(fieldname1, fieldname2), _function(function)
    {
    }
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        GPtrArray *finfos = nullptr;
        std::string strRst = "";

        int field_id1 = proto_registrar_get_id_byname(_field_name1);
        int field_id2 = proto_registrar_get_id_byname(_field_name2);

        finfos = proto_get_finfo_ptr_array(edt->tree, field_id1) != nullptr ? proto_get_finfo_ptr_array(edt->tree, field_id1) : proto_get_finfo_ptr_array(edt->tree, field_id2);

        if (nullptr == finfos)
        {
            return "";
        }

        int finfos_cnt = g_ptr_array_len(finfos);

        if (finfos_cnt == 0)
        {
            return "";
        }

        for (int i = 0; i < finfos_cnt; ++i)
        {
            field_info * finfo = (field_info *)g_ptr_array_index(finfos, i);

            if (i > 0)
            {
                strRst += ",";
            }
            strRst += _function(edt, finfo);
        }

        return strRst;
    }

public:
    std::string(*_function)(epan_dissect_t *edt, const field_info *pFinfo);
};



#define SSL_N_CERT_CLASS(NO) \
    F_D_ITEM("Certificate_" #NO "Length"            , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_field( (NO), "ssl.handshake.certificate_length" ) ),    \
    F_D_ITEM("Certificate_" #NO "Info"              , ""                                    , eMT_fixed,        "",     NULL ),                                                                         \
    F_D_ITEM("Certificate_" #NO "Version"           , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_field( (NO), "x509af.version" ) ),                      \
    F_D_ITEM("Certificate_" #NO "Squence"           , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_hex( (NO), "ber.64bit_uint_as_bytes" ) ),               \
    F_D_ITEM("Certificate_" #NO "SquenceLength"     , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_length( (NO), "ber.64bit_uint_as_bytes" ) ),            \
    F_D_ITEM("Certificate_" #NO "Signature"         , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_field( (NO), "x509af.signature_element" ) ),            \
    F_D_ITEM("Certificate_" #NO "BeginTime"         , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_time( (NO), "x509af.notBefore" ) ),                     \
    F_D_ITEM("Certificate_" #NO "EndTime"           , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_time( (NO), "x509af.notAfter" ) ),                      \
    F_D_ITEM("Certificate_" #NO "Algorithm"         , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_field( (NO), "x509af.algorithm_element" ) ) ,           \
    F_D_ITEM("Certificate_" #NO "PubKey"            , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_hex( (NO), "x509af.subjectPublicKey" ) ),               \
    F_D_ITEM("Certificate_" #NO "PubKeyLength"      , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_length( (NO), "x509af.subjectPublicKey" ) ),            \
    F_D_ITEM("Certificate_" #NO "KeyUse"            , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_field( (NO), "x509ce.KeyUsage" ) ),                         \
    F_D_ITEM("Certificate_" #NO "DNSSNumber"        , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_field( (NO), "x509ce.GeneralNames" ) ),                  \
    F_D_ITEM("Certificate_" #NO "DNSS"              , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_dns( (NO), "x509ce.GeneralNames") ),                                                                         \
    F_D_ITEM("Certificate_" #NO "Issuernumber"      , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_length( (NO), "x509af.sc_issuer" ) ),                      \
    F_D_ITEM("Certificate_" #NO "Issuer"            , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_special( (NO), "x509af.sc_issuer" ) ),                \
    F_D_ITEM("Certificate_" #NO "Subjectnumber"     , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_length( (NO), "x509af.sc_subject" ) ),                     \
    F_D_ITEM("Certificate_" #NO "Subject"           , ""                                    , eMT_fromEdt,      "",     get_nth_certificate_ssl_special( (NO), "x509af.sc_subject" ) )

#define SSL_N_CERT_GROUP(a, b, c, d, e) \
    SSL_N_CERT_CLASS(a),                \
    SSL_N_CERT_CLASS(b),                \
    SSL_N_CERT_CLASS(c),                \
    SSL_N_CERT_CLASS(d),                \
    SSL_N_CERT_CLASS(e)



static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort"                          , "tcp.srcport"                                         , eMT_direct,       "",     NULL),
    F_D_ITEM("DstPort"                          , "tcp.dstport"                                         , eMT_direct,       "",     NULL),
    F_D_ITEM("C2S"                              , ""                                                    , eMT_fromEdt,      "",     transform_field_for_C2S),
    F_D_ITEM("Proto"                            , "ip.proto"                                            , eMT_direct,       "",     NULL),
    F_D_ITEM("TTL"                              , "ip.ttl"                                              , eMT_direct,       "",     NULL),
    F_D_ITEM("ContentType"                      , "ssl.record.content_type"                             , eMT_transform,    "",     transform_for_field_value),
    F_D_ITEM("Version"                          , "ssl.record.version"                                  , eMT_transform,    "",     transform_for_field_value),
    F_D_ITEM("RecordLayerLength"                , "ssl.record.length"                                   , eMT_direct,       "",     NULL),
    F_D_ITEM("ChangeCipherSpec"                 , "ssl.handshake.cipherspec"                            , eMT_direct,       "",     NULL),
    F_D_ITEM("AlertLevel"                       , "ssl.alert_message.level"                             , eMT_direct,       "",     NULL),
    F_D_ITEM("AlertDescription"                 , "ssl.alert_message.desc"                              , eMT_direct,       "",     NULL),
    F_D_ITEM("HandshakeType"                    , "ssl.handshake.type"                                  , eMT_transform,    "",     transform_for_field_value),
    F_D_ITEM("ClientHelloLength"                , "ssl.handshake.length"                                , eMT_transform,    "",     transform_ssl_field_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("ClientProtocolVersion"            , "ssl.handshake.version"                               , eMT_transform,    "",     transform_ssl_value_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("ClientGMTUnixTime"                , "ssl.handshake.random_time"                           , eMT_transform,    "",     transform_ssl_field_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("ClientRandomBytes"                , "ssl.handshake.random_bytes"                          , eMT_transform,    "",     transform_ssl_hex_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("ClientSessionIDLength"            , "ssl.handshake.session_id_length"                     , eMT_transform,    "",     transform_ssl_field_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("ClientSessionID"                  , "ssl.handshake.session_id"                            , eMT_transform,    "",     transform_ssl_hex_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("ClientCipherSuitesLength"         , "ssl.handshake.cipher_suites_length"                  , eMT_transform,    "",     transform_ssl_field_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("ClientCipherSuites"               , "ssl.handshake.ciphersuite"                           , eMT_transform,    "",     transform_ssl_value_ext_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("CltCompressionMethodsLen"         , "ssl.handshake.comp_methods_length"                   , eMT_transform,    "",     transform_ssl_field_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("ClientCompressionMethods"         , "ssl.handshake.comp_method"                           , eMT_transform,    "",     transform_ssl_field_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("ClientExtensionsLength"           , "ssl.handshake.extensions_length"                     , eMT_transform,    "",     transform_ssl_field_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("ClientExtensions"                 , "ssl.handshake.extension.type"                        , eMT_transform,    "",     transform_ssl_extension_match_types(1,"ssl.handshake.type")),
    F_D_ITEM("ServerHelloLength"                , "ssl.handshake.length"                                , eMT_transform,    "",     transform_ssl_field_match_types(2,"ssl.handshake.type")),
    F_D_ITEM("ServerProtocolVersion"            , "ssl.handshake.version"                               , eMT_transform,    "",     transform_ssl_value_match_types(2,"ssl.handshake.type")),
    F_D_ITEM("ServerGMTUnixTime"                , "ssl.handshake.random_time"                           , eMT_transform,    "",     transform_ssl_field_match_types(2,"ssl.handshake.type")),
    F_D_ITEM("ServerRandomBytes"                , "ssl.handshake.random_bytes"                          , eMT_transform,    "",     transform_ssl_hex_match_types(2,"ssl.handshake.type")),
    F_D_ITEM("ServerSessionIDLength"            , "ssl.handshake.session_id_length"                     , eMT_transform,    "",     transform_ssl_field_match_types(2,"ssl.handshake.type")),
    F_D_ITEM("ServerSessionID"                  , "ssl.handshake.session_id"                            , eMT_transform,    "",     transform_ssl_hex_match_types(2,"ssl.handshake.type")),
    F_D_ITEM("ServerCipherSuite"                , "ssl.handshake.ciphersuite"                           , eMT_transform,    "",     transform_ssl_value_ext_match_types(2,"ssl.handshake.type")),
    F_D_ITEM("ServerCompressionMethod"          , "ssl.handshake.comp_method"                           , eMT_transform,    "",     transform_ssl_field_match_types(2,"ssl.handshake.type")),
    F_D_ITEM("ServerExtensionsLength"           , "ssl.handshake.extensions_length"                     , eMT_transform,    "",     transform_ssl_field_match_types(2,"ssl.handshake.type")),
    F_D_ITEM("ServerExtensions"                 , "ssl.handshake.extension.type"                        , eMT_transform,    "",     transform_ssl_extension_match_types(2,"ssl.handshake.type")),
    F_D_ITEM("CertificatesLength"               , "ssl.handshake.certificates_length"                   , eMT_direct,       "",     NULL),
    F_D_ITEM("CertificatesNums"                 , ""                                                    , eMT_fromEdt,      "",     transform_ssl_field_num),
    SSL_N_CERT_GROUP(1, 2, 3, 4, 5),        
    SSL_N_CERT_GROUP(6, 7, 8, 9, 10),       
    F_D_ITEM("ServerKexLength"                  , ""                                                    , eMT_transform,    "",     transform_ssl_field_match_types(12,"ssl.handshake.type")),
    F_D_ITEM("ECDHCurveType"                    , "ssl.handshake.server_curve_type"                     , eMT_direct,       "",     NULL),
    F_D_ITEM("ECDHNamedCurve"                   , "ssl.handshake.server_named_curve"                    , eMT_direct,       "",     NULL),
    F_D_ITEM("ECDHPubKeyLength"                 , ""                                                    , eMT_fromEdt,      "",     choose_for_two_type_field("ssl.handshake.server_point_len", "ssl.handshake.client_point_len")),
    F_D_ITEM("ECDHPubkey"                       , ""                                                    , eMT_fromEdt,      "",     transform_choose_for_two_type_field("ssl.handshake.server_point", "ssl.handshake.client_point", transform_for_field_hex)),
    F_D_ITEM("ECDHSignatureHashAlgorithm"       , "ssl.handshake.sig_hash_hash"                         , eMT_transform,    "",     transform_for_field_value),
    F_D_ITEM("ECDHSignatureSigAlgorithm"        , "ssl.handshake.sig_hash_sig"                          , eMT_transform,    "",     transform_for_field_value),
    F_D_ITEM("ECDHSignatureLength"              , "ssl.handshake.sig_len"                               , eMT_direct,       "",     NULL),
    F_D_ITEM("ECDHSignature"                    , "ssl.handshake.sig"                                   , eMT_transform,    "",     transform_for_field_hex),
    F_D_ITEM("RSAModulusLength"                 , "ssl.handshake.modulus_len"                           , eMT_direct,       "",     NULL),
    F_D_ITEM("RSAModulus"                       , "ssl.handshake.modulus"                               , eMT_direct,       "",     NULL),
    F_D_ITEM("RSAExponentLength"                , "ssl.handshake.exponent_len"                          , eMT_direct,       "",     NULL),
    F_D_ITEM("RSAExponent"                      , "ssl.handshake.exponent"                              , eMT_direct,       "",     NULL),
    F_D_ITEM("RSASignatureHashAlgorithm"        , "ssl.handshake.sig_hash_hash"                         , eMT_direct,       "",     NULL),
    F_D_ITEM("RSASignatureSigAlgorithm"         , "ssl.handshake.sig_hash_sig"                          , eMT_direct,       "",     NULL),
    F_D_ITEM("RSASignatureLength"               , "ssl.handshake.sig_hash_alg_len"                      , eMT_direct,       "",     NULL),
    F_D_ITEM("RSASignature"                     , "ssl.handshake.sig_hash_alg"                          , eMT_direct,       "",     NULL),
    F_D_ITEM("DHEpLength"                       , "ssl.handshake.p_len"                                 , eMT_direct,       "",     NULL),
    F_D_ITEM("DHEp"                             , "ssl.handshake.p"                                     , eMT_direct,       "",     NULL),
    F_D_ITEM("DHEgLength"                       , "ssl.handshake.g_len"                                 , eMT_direct,       "",     NULL),
    F_D_ITEM("DHEg"                             , "ssl.handshake.g"                                     , eMT_direct,       "",     NULL),
    F_D_ITEM("DHEPubKeyLength"                  , ""                                                    , eMT_fromEdt,      "",     choose_for_two_type_field("ssl.handshake.ys_len", "ssl.handshake.yc_len")),
    F_D_ITEM("DHEPubkey"                        , ""                                                    , eMT_fromEdt,      "",     choose_for_two_type_field("ssl.handshake.ys", "ssl.handshake.yc")),
    F_D_ITEM("DHESignatureHashAlgorithm"        , ""                                                    , eMT_fixed,        "",     NULL),
    F_D_ITEM("DHESignatureSigAlgorithm"         , ""                                                    , eMT_fixed,        "",     NULL),
    F_D_ITEM("DHESignatureLength"               , "ssl.handshake.sig_len"                               , eMT_direct,       "",     NULL),
    F_D_ITEM("DHESignature"                     , "ssl.handshake.sig"                                   , eMT_transform,    "",     transform_ssl_specical_match("ssl.handshake.g",transform_for_field_hex)),
    F_D_ITEM("ServerKexData"                    , ""                                                    , eMT_fixed,        "",     NULL),
    F_D_ITEM("ClientKexLength"                  , "ssl.handshake.length"                                , eMT_transform,    "",     transform_ssl_field_match_types(16,"ssl.handshake.type")),
    F_D_ITEM("EncrypedPubkey"                   , "ssl.handshake.encrypted_key"                         , eMT_direct,       "",     NULL),
    F_D_ITEM("EncrypedPubkeyLength"             , "ssl.handshake.encrypted_key_length"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("CertificateRequestLength"         , "ssl.handshake.certificate_length"                    , eMT_direct,       "",     NULL),
    F_D_ITEM("ClientCertificateTypesCount"      , ""                                                    , eMT_fixed,        "",     NULL),
    F_D_ITEM("ClientCertificateTypes"           , ""                                                    , eMT_fixed,        "",     NULL),
    F_D_ITEM("DistinguishedNameLength"          , "x509if.RelativeDistinguishedName_item_element"       , eMT_transform,    "",     transform_for_field_length),
    F_D_ITEM("DistinguishedName"                , "x509if.RelativeDistinguishedName_item_element"       , eMT_transform,    "",     transform_for_field_rep),
    F_D_ITEM("CertificateVerifyLength"          , "ssl.handshake.client_cert_vrfy.sig_len"              , eMT_direct,       "0",    NULL),
    F_D_ITEM("ClientCertificateSignature"       , "ssl.handshake.extensions_supported_group"            , eMT_direct,       "",     NULL),
    F_D_ITEM("CltCertSignatureLength"           , "ssl.handshake.extensions_supported_groups_length"    , eMT_direct,       "0",    NULL),
    F_D_ITEM("ServerName"                       , "ssl.handshake.extensions_server_name"                , eMT_direct,       "",     NULL),
};

ProtoFieldExtractorSsl_n::ProtoFieldExtractorSsl_n():ProtoFieldExtractor("SSL/TLS", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{   
}

 bool ProtoFieldExtractorSsl_n :: CanExtractFromThisProtocol(const std::string &strProtoName) 
{
    return strBeginwith(strProtoName, "SSL") || strBeginwith(strProtoName, "TLS");
}

 bool ProtoFieldExtractorSsl_n::ShouldExtractThisFrame(epan_dissect_t *edt)
 {
     field_info* pfinfo = get_first_field_info_from_interesting_fields(edt, "tcp.srcport");
     
     return pfinfo ? true : false;
 }

