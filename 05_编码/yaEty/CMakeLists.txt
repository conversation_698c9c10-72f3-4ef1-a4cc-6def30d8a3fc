# cmake version
cmake_minimum_required(VERSION 3.14)

# project
project(yaEty LANGUAGES C CXX VERSION 1.0.34.4429)

# generate project version to version.h
set(CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR})

configure_file(version.h.in ${CMAKE_SOURCE_DIR}/version.h)

# debug build type
SET( CMAKE_BUILD_TYPE Debug)

# variables
set(WS_ROOT_DIR            ../wireshark_2.6.2)
set(WS_LIB_DIR             ${WS_ROOT_DIR}/build/run)
set(CMAKE_MODULE_PATH      ${CMAKE_SOURCE_DIR}/${WS_ROOT_DIR}/cmake/modules)
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/../run)

set(warn_flags " -Wall -Wextra                  \
                -Wno-missing-field-initializers \
                -Wno-unused-parameter           \
                -Wno-unused-variable            \
                -Wno-unused-function            \
                -Wno-unused-but-set-variable    \
                -Wno-cast-qual")                # 暂不使用 -Waddress-of-packed-member 打包成员地址

# C_FLAGS
set(CMAKE_C_FLAGS   "-std=gnu99  ${CMAKE_C_FLAGS} ${warn_flags}")
set(CMAKE_CXX_FLAGS "-std=c++11  ${CMAKE_CXX_FLAGS}  ${warn_flags}")

# SRC files
set(COMMON_SRC
    ${WS_ROOT_DIR}/cfile.c
    ${WS_ROOT_DIR}/extcap.c
#    ${WS_ROOT_DIR}/extcap_spawn.c
    ${WS_ROOT_DIR}/extcap_parser.c
)

# find GLIB2
find_package(GLIB2)
include_directories(${GLIB2_INCLUDE_DIRS})

# include dirs
include_directories(include
                    ${WS_ROOT_DIR}
                    )

# link dirs
link_directories(lib
                 ${WS_LIB_DIR}
                 )

set(ws_LIBS
    wireshark
    caputils
    wiretap
    wsutil
#    lua
    ui
    wscodecs
    bcg729
    opus
    siren
    sbc
    spandsp
    ilbc
    speex
    gmodule-2.0
    glib-2.0
    pcap
    gcrypt
    gpg-error
    dl
    z
    m
   )

set(yaEty_LIBS
    ${ws_LIBS}
    jsoncpp
    iniparser
    pq
    # -static-libasan
    # -fsanitize=address
)

set(yaEty_FILES
    yaEty.cpp
    yaEty_config.cpp
    yaEty_utils.cpp
    yaEty_ws_utils.cpp
    yaEty_parse_post.c
    yaEty_frame_tvbuff_ws_2_6.cpp
    yaEty_file_packet_provider.c
    yaEty_child_process.cpp
    yaEty_pcap_scanner.cpp
    yaEty_rec_writer_tbl.cpp
    yaEty_rec_dumper_cap.cpp
    yaEty_cap_file_processor.cpp
    yaEty_cap_file_manager.cpp
    yaEty_content_reassembly_share_mem.c
    yaEty_rtp_stream.cpp
    yaEty_rtp_stream_keeper.cpp
    yaEty_voip_stream_keeper.cpp
    rtp_h261_packer.cpp
    rtp_h263_packer.cpp
    rtp_h264_packer.cpp
    yaEty_extract_trailer.cpp
    yaEty_field_extractor.cpp
    yaEty_field_extractor_ah.cpp
    yaEty_field_extractor_http.cpp
    yaEty_field_extractor_dns.cpp
    yaEty_field_extractor_ftp.cpp
    yaEty_field_extractor_smtp.cpp
    yaEty_field_extractor_pop.cpp
    yaEty_field_extractor_imap.cpp
    yaEty_field_extractor_radius.cpp
    yaEty_field_extractor_sip.cpp
    yaEty_field_extractor_rtp.cpp
    yaEty_field_extractor_esp.cpp
    yaEty_field_extractor_l2tp.cpp
    yaEty_field_extractor_pptp.cpp
    yaEty_field_extractor_wxf.cpp
    yaEty_field_extractor_dhcp.cpp
    yaEty_field_extractor_telnet.cpp
    yaEty_field_extractor_bgp.cpp
    yaEty_field_extractor_ssh.cpp
    yaEty_field_extractor_mysql.cpp
    yaEty_field_extractor_dtls.cpp
    yaEty_field_extractor_tds.cpp
    yaEty_field_extractor_rip.cpp
    yaEty_field_extractor_snmp.cpp
    yaEty_field_extractor_megaco.cpp
    yaEty_field_extractor_rdp.cpp
    yaEty_field_extractor_diameter.cpp
    yaEty_field_extractor_eigrp.cpp
    yaEty_field_extractor_ospf.cpp
    yaEty_field_extractor_stun.cpp
    yaEty_field_extractor_classicstun.cpp
    yaEty_field_extractor_mgcp.cpp
    yaEty_field_extractor_ldap.cpp
    yaEty_field_extractor_gre.cpp
    yaEty_field_extractor_gtp_control.cpp
    yaEty_field_extractor_ssl_n.cpp
    yaEty_field_extractor_isakmp.cpp
    yaEty_field_extractor_tftp.cpp
    yaEty_field_extractor_iax.cpp
    yaEty_field_extractor_sdp_l5.cpp
    yaEty_field_extractor_wxa.cpp
    yaEty_field_extractor_voip.cpp
    yaEty_field_extractor_rtp_stream.cpp
    yaEty_field_extractor_sms.cpp
    yaEty_target_info.cpp
    ${COMMON_SRC}
)

# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address")
# set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=address")

add_executable(yaEty ${yaEty_FILES})
# 在 gcc 5.1 以上 std::basic_string 限定名将位于 std::__cxx11::basic_string
# 关闭此选项,否则 libjsoncpp.a 将无法提供对应的 cxx11 ABI 的符号
if (${CMAKE_CXX_COMPILER_VERSION} GREATER_EQUAL "5.1")
 message("C++ compiler version is ${CMAKE_CXX_COMPILER_VERSION}")
 target_compile_definitions(yaEty PRIVATE _GLIBCXX_USE_CXX11_ABI=0) 
endif()
target_link_options(yaEty PRIVATE "-pthread")
target_link_libraries(yaEty ${yaEty_LIBS})
