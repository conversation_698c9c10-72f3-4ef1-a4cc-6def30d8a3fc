/****************************************************************************************
* 文 件 名 : yaEty_cap_file_manager.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx    '2018-11-07
* 编    码 : zhangsx    '2018-11-07
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_cap_file_manager.h"

/*****************************************************************
*Function    :CapFileTree::loadRecord()
*Description :get field length
*Input       :none
*Output      :none
*Return      :打开文件,返回0;失败返回-1.
*Others      :none
*****************************************************************/

int CapFileTree::loadRecord(void)
{
    std::ifstream datafile_path;                                                  //存储处理过的文件名的文件路径

    rapidjson::Document     doc;
    rapidjson::StringBuffer buffer;
    std::string Json_text;
    std::unordered_set <std::string> filelist;

    datafile_path.open(record_filepath.data(), std::ios::in | std::ios::app);

    if (!datafile_path.is_open())
    {
        printf("invalid path:%s\n", record_filepath.data());
        return -1;
    }

    std::string cap_name;

    while (getline(datafile_path, cap_name))
    {
        Json_text.append(cap_name+"\n");
    }

    datafile_path.close();

    if (doc.Parse(Json_text.c_str()).HasParseError())
    {
        return -1;
    }
    for (rapidjson::Value::ConstMemberIterator iter = doc.MemberBegin() ; iter != doc.MemberEnd() ; ++iter)
    {
        std::string json_key = iter->name.GetString();
        const rapidjson::Value & json_value = iter->value;
        if (json_value.IsArray())
        {
            filelist.clear();
            for (rapidjson::SizeType i = 0; i < json_value.Size(); ++i )
            {
                filelist.emplace(json_value[i].GetString());
            }

        }

        process_record.emplace(json_key,filelist);
    }

    return 0;
}

/*****************************************************************
*Function    :CapFileTree::saveRecord()
*Description :get field length
*Input       :none
*Output      :none
*Return      :打开文件,返回0;失败返回-1.
*Others      :none
*****************************************************************/

int CapFileTree::saveRecord(void)
{
    std::fstream datafile_path;                                                  //存储处理过的文件名的文件路径

    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);

    writer.StartObject();

    for (const auto& iter:process_record)
    {
        writer.Key(iter.first.c_str());
        writer.StartArray();
        for (const auto& itr:iter.second)
        {
            writer.String(itr.c_str());
        }
        writer.EndArray();
    }

    writer.EndObject();

    datafile_path.open(record_filepath.data(), std::ios::out);

    if (!datafile_path.is_open())
    {
        printf("\n");
        return -1;
    }
    
    datafile_path << buffer.GetString();
    
    datafile_path << std::endl;
    
    datafile_path.close();

    return 0;
}


//CapFileNode::CapFileNode() : isLeaf(false)
//{
//    process_record.clear();
//}
//
//bool CapFileNode::isProcessed(std::string _file_name)
//{
//    return process_record.find(_file_name) != process_record.end() ? true : false;
//}


CapFileTree::CapFileTree()
{

}

void CapFileTree::setPath(const char * _path)
{
    if (_path == nullptr)
    {
        record_filepath = "";
    }
    record_filepath = _path;
}

void CapFileTree::setPath(std::string _path)
{
    record_filepath = _path;
}

std::string CapFileTree::getPath()
{
    return record_filepath;
}

int CapFileTree::size()
{
    return process_record.size();
}

void CapFileTree::clear()
{
    process_record.clear();
}



std::string CapFileTree::getCanonicalPath(const char* path)
{
    std::string canonical_path = "";
    std::deque <std::string> filename_list;

    int pathLen = strlen(path);
    char currentPath[PATH_MAX] = { 0 };
    char *pFilename;

    //相对路径
    if ('/' != path[0])
    {   //获取当前路径
        getcwd(currentPath, sizeof(currentPath));
        strcat(currentPath, "/");

        strcat(currentPath, path);
        if (path[pathLen - 1] != '/')
        {
            strcat(currentPath, "/");
        }
    }
    else
    {   //绝对路径
        strcpy(currentPath, path);
        if (path[pathLen - 1] != '/')
        {
            strcat(currentPath, "/");
        }
    }
    
    pFilename = strtok(currentPath, "/");
        
    filename_list.clear();

    filename_list.push_back(std::string(pFilename));

    while ((pFilename = strtok(NULL, "/")) != NULL)
    {
        if (strcmp(".", pFilename) == 0)
        {
            continue;
        }

        if (strcmp("..", pFilename) == 0)
        {
            filename_list.pop_back();
            continue;
        }

        filename_list.push_back(std::string(pFilename));
        //printf("Path %d:%s\n",i, pFilename);
    }

    for (const auto & itr : filename_list)
    {
        //printf("%s\n", pFilename);
        canonical_path.append("/"+itr);
    }
    
    filename_list.clear();

    if (access(canonical_path.c_str(), 0) != 0)
    {
        return "";
    }

    return canonical_path;
}


//CapFileNode * CapFileTree::find_bynode(CapFileNode * node, std::string dir_name)        //查找一个单独的文件名
//{
//    if (node == nullptr)
//    {
//        return node;
//    }
//
//    if (node->process_record.count(dir_name) > 0)
//    {
//        return node;
//    }
//    else
//    {
//        if (node->isDir)
//        {
//            for (const auto& itr : node->process_record)
//            {
//                for (const auto& it : itr.second)
//                {
//                    find_bynode(it, dir_name);
//                }
//            }
//        }
//        else
//        {
//            return nullptr;
//        }
//    }
//}


std::string CapFileTree::getFileDirFromPath(const char * path)
{
    std::string temp_path(path);
    return  temp_path.substr(0, temp_path.find_last_of('/') + 1);
}

std::string CapFileTree::getFileNameFromPath(const char * path)
{
    std::string temp_path(path);
    return  temp_path.substr(temp_path.find_last_of('/') + 1);
}
    
int CapFileTree::select_file(std::string path,std::string filename)
{
    if (process_record.count(path) == 0)
    {
        return 1;
    }
    else
    {
        if (process_record[path].count(filename) == 0)
        {
            return 2;
        }

        return 0;
    }
}

int CapFileTree::select_file(std::string path)
{
    path = getCanonicalPath(path.c_str());

    return select_file( getFileDirFromPath(path.c_str()), getFileNameFromPath(path.c_str()) );
}

void CapFileTree::add_file(std::string path, std::string filename)
{
    int rst = select_file(path, filename);

    if (rst == 1)
    {
        process_record.emplace(path, std::unordered_set<std::string>{filename});
    }
    else if (rst == 2)
    {
        process_record[path].emplace(filename);
    }
}

void CapFileTree::add_file(std::string path)
{
    path = getCanonicalPath(path.c_str());

    return add_file(getFileDirFromPath(path.c_str()), getFileNameFromPath(path.c_str()));
}








