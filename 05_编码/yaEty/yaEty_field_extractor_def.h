/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_macro.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-24
* 编    码 : zhengsw      '2018-05-24
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _YAETY_FIELD_EXTRACTOR_MACRO_H_
#define _YAETY_FIELD_EXTRACTOR_MACRO_H_

#include <string>
#include <functional>

enum eMappedType_t
{
    /* 低 8 bit, type 类型 */
    eMT_direct      = 1,         /* etyName 可直接映射到 wsName */
    eMT_fixed       = 1 << 1,    /* etyName 对应的 value 需要指定固定值 */
    eMT_transform   = 1 << 2,    /* etyName 对应的 value 需要由相应的 wsName 对应的 value 进行转换 */
    eMT_special     = 1 << 3,    /* special fields need special processing */
    eMT_fromEdt     = 1 << 4,    /* extract from Epan_dissect_t */
    
    eMT_type_mask   = 0xff,      /* type_mask */

    /* 其它， 描述类型 */
    eMT_lastplain   = 1 << 8,    /* last plain */
    eMT_delayField  = 1 << 9,    /* delay geting field name for extracting current field */
};

struct field_info;                            /* define in epan/proto.h */
typedef struct epan_dissect epan_dissect_t;   /* define in epan/epan.h */

//typedef std::string  (*funTransfrom_t)(epan_dissect_t * edt, const field_info *pFinfo);

typedef std::function<std::string(epan_dissect_t * edt, const field_info *pFinfo)> funTransfrom_t;

// class wsFieldProxy.                                                                                       
// adapt a field name or a function which return a field name to a unified entity.                           
class wsFieldProxy                                                                                           
{                                                                                                            
public:                                                                                                      
    typedef     std::function<const char *()> getFieldFun_t;                                                 
                                                                                                             
public:                                                                                                      
    wsFieldProxy(const char *fieldName) : fieldName_(fieldName){}   
    wsFieldProxy(getFieldFun_t fun)     : getFieldFun_(fun){}                                                
                                                                                                             
public:                                                                                                      
    operator const char *()                                                                                  
    {                                                                                                        
        return fieldName_;                                                                               
    }                                                                                                        
                                                                                                             
    operator std::string()                                                                                   
    {                                                                                                        
        return fieldName_;                                                                        
    }                                                                                                        

    void setUpFieldName(void)
    {
        fieldName_ = this->getFieldFun_();
    }                                                                                                   
                                                                                                             
private:                                                                                                     
    getFieldFun_t getFieldFun_;
    const char *fieldName_;                                                                            
}; 

struct ProtoFieldDesc
{
    const char *   etyFieldName;            /* etyName */ 
    wsFieldProxy   wsFieldName;          /* wireshark field name */
    eMappedType_t  eMappedType;       /* 映射类型 */
    const char *   defaultFieldValue; /* 默认值，在 eMappedType 为 eMT_fixed_value 时需要指定，其它情形指定为 NULL */
    funTransfrom_t funTransfrom;      /* 当 eMappedType 为 eMT_transform 时被调用 */
};

/*email common long field*/
#define ETY_DIS_NOT_TO               "Disposition-Notification-To"
#define ETY_DIS_NOT_OPT              "Disposition-Notification-Options"
#define ETY_ORI_ENC_INF_T            "Original-Encoded-Information-Types"
#define ETY_PRE_NON_REP              "Prevent-NonDelivery-Report"
#define ETY_DIS_X4_IP_EXT            "Discarded-X400-IPMS-Extensions"
#define ETY_DIS_X4_MT_EXT            "Discarded-X400-MTS-Extensions"
#define ETY_ORI_RE_ADR               "Originator-Return-Address"
#define ETY_CON_TRA_ENC              "Content-Transfer-Encoding"

#define WS_CON_TRA_ECODING           "imf.content.transfer_encoding"
#define WS_ORI_ENC_INF_T             "imf.original_encoded_information_types"
#define WS_DIS_X4_IP_EXT             "imf.discarded_x400_ipms_extensions"
#define WS_DIS_X4_MT_EXT             "imf.discarded_x400_mts_extensions"
#define WS_ORI_RE_ADR                "imf.originator_return_address"
#define WS_CON_TRA_ENC               "imf.content.transfer_encoding"

#define ProtoFieldDesc_GetType(pFD) ((pFD)->eMappedType & eMT_type_mask)

std::string strReplace(const char *pStr, char from, char to);

// 将 field_info 中的 time.secs 转换为 time 字符串，格式为 %Y-%m-%d %H:%M:%S
std::string transform_field_for_absolute_time(epan_dissect_t *edt, const field_info *pFinfo);
std::string transform_field_for_hash_code(epan_dissect_t *edt, const field_info *pFinfo);
std::string transform_field_for_may_occur_tbl_sep(epan_dissect_t *edt, const field_info *pFinfo);
std::string transform_field_for_may_occur_bin(epan_dissect_t *edt, const field_info *pFinfo);
std::string transform_field_for_task_id(epan_dissect_t *edt, const field_info *pFinfo);

struct choose_for_two_type_field;   //add by zhangsx 2018-11-14

struct transform_trailer_choose_field;

struct trailerFieldSelector;

/* Field Desc Item */
#define F_D_ITEM(etyName, wsName, mappedType, defValue, transFun) {(etyName), wsFieldProxy(wsName), (eMappedType_t)(mappedType), (defValue), (transFun)}

/* rtl 10 fields */
#define F_D_ITEM_RTL_10()                                                 \
    F_D_ITEM("RTL_TEID"       ,trailerFieldSelector("teid")             ,eMT_delayField|eMT_transform,     "",     transform_trailer_choose_field()), \
    F_D_ITEM("RTL_OUTTER_SRC" ,trailerFieldSelector("outer_src")        ,eMT_delayField|eMT_transform,     "",     transform_trailer_choose_field()), \
    F_D_ITEM("RTL_OUTTER_DST" ,trailerFieldSelector("outer_dst")        ,eMT_delayField|eMT_transform,     "",     transform_trailer_choose_field()), \
    F_D_ITEM("RTL_MSISDN"     ,trailerFieldSelector("msisdn")           ,eMT_delayField|eMT_transform,     "",     transform_trailer_choose_field()), \
    F_D_ITEM("RTL_IMEI"       ,trailerFieldSelector("imei")             ,eMT_delayField|eMT_transform,     "",     transform_trailer_choose_field()), \
    F_D_ITEM("RTL_IMSI"       ,trailerFieldSelector("imsi")             ,eMT_delayField|eMT_transform,     "",     transform_trailer_choose_field()), \
    F_D_ITEM("RTL_TAC"        ,trailerFieldSelector("tac")              ,eMT_delayField|eMT_transform,     "",     transform_trailer_choose_field()), \
    F_D_ITEM("RTL_PLMN_ID"    ,trailerFieldSelector("plmnid")           ,eMT_delayField|eMT_transform,     "",     transform_trailer_choose_field()), \
    F_D_ITEM("RTL_ULI"        ,trailerFieldSelector("uli")              ,eMT_delayField|eMT_transform,     "",     transform_trailer_choose_field()), \
    F_D_ITEM("RTL_BS"         ,trailerFieldSelector("basetype")         ,eMT_delayField|eMT_transform,     "",     transform_trailer_choose_field())

// #define F_D_ITEM_RTL_10()                                                 \
//     F_D_ITEM("RTL_TEID"       ,"rtl.teid",      eMT_direct,     "",     NULL), \
//     F_D_ITEM("RTL_OUTTER_SRC" ,"rtl.outer_src", eMT_direct,     "",     NULL), \
//     F_D_ITEM("RTL_OUTTER_DST" ,"rtl.outer_dst", eMT_direct,     "",     NULL), \
//     F_D_ITEM("RTL_MSISDN"     ,"rtl.msisdn",    eMT_direct,     "",     NULL), \
//     F_D_ITEM("RTL_IMEI"       ,"rtl.imei",      eMT_direct,     "",     NULL), \
//     F_D_ITEM("RTL_IMSI"       ,"rtl.imsi",      eMT_direct,     "",     NULL), \
//     F_D_ITEM("RTL_TAC"        ,"rtl.tac",       eMT_direct,     "",     NULL), \
//     F_D_ITEM("RTL_PLMN_ID"    ,"rtl.plmnid",    eMT_direct,     "",     NULL), \
//     F_D_ITEM("RTL_ULI"        ,"rtl.uli",       eMT_direct,     "",     NULL), \
//     F_D_ITEM("RTL_BS"         ,"rtl.basetype",  eMT_direct,     "",     NULL)

//changed by zhangsx 2018-11-14

#define F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27()                                                                                                \
    F_D_ITEM("DevNo"               , NULL,                       eMT_fixed,     "Blade", NULL),                                                 \
    F_D_ITEM("LineNo"              , NULL,                       eMT_fixed,     "B-42",  NULL),                                                 \
    F_D_ITEM("LinkLayerType"       , NULL,                       eMT_fixed,     "1",     NULL),                                                 \
    /* TODO:wireshark ipv6 使用了 ip.version 字段 问题 */                                                                                        \
    F_D_ITEM("isIPv6"              , "ip.version",               eMT_fixed,     "NO",    NULL),                                                 \
    F_D_ITEM("isMPLS"              , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("nLabel"              , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("innerLabel"          , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("otherLabel"          , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("resv1"               , "frame.len",                eMT_direct,    "0",     NULL),                                                 \
    F_D_ITEM("resv2"               , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("resv3"               , "ycf.mac",                  eMT_direct,    "",      NULL),                                                 \
    F_D_ITEM("resv4"               , "ycf.username",             eMT_direct,    "",      NULL),                                                 \
    F_D_ITEM("resv5"               , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("resv6"               , NULL,                       eMT_fromEdt,   "",      transform_field_for_hash_code),                        \
    F_D_ITEM("resv7"               , "eth.src",                  eMT_direct,    "",      NULL),                                                 \
    F_D_ITEM("resv8"               , "eth.dst",                  eMT_direct,    "",      NULL),                                                 \
    F_D_ITEM("CapDate"             , "frame.time",               eMT_transform, "",      transform_field_for_absolute_time),                    \
    F_D_ITEM("SrcIp"               , NULL,                       eMT_fromEdt,   "",      choose_for_two_type_field("ip.src","ipv6.src")),       \
    F_D_ITEM("SrcCountry"          , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("SrcArea"             , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("SrcCity"             , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("SrcCarrier"          , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("DstIp"               , NULL,                       eMT_fromEdt,   "",      choose_for_two_type_field("ip.dst", "ipv6.dst")),      \
    F_D_ITEM("DstCountry"          , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("DstArea"             , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("DstCity"             , NULL,                       eMT_fixed,     "",      NULL),                                                 \
    F_D_ITEM("DstCarrier"          , NULL,                       eMT_fixed,     "",      NULL)

 //changed by zhangsx 2018-11-14 
 //changed by zhangsx 2019-02-25 end

/* email reserve field*/
#define F_D_ITEM_MAIL_NAME_VALUE(NO, mapType)                                                                    \
    F_D_ITEM("ExtMailHdrName" NO                 , NULL,                       mapType,     "",      NULL),      \
    F_D_ITEM("ExtMailHdrValue" NO                , NULL,                       mapType,     "",      NULL)

#define F_D_ITEM_MAIL_NAME_VALUE_5(a, b, c, d, e, mapType)   \
        F_D_ITEM_MAIL_NAME_VALUE(a, mapType),                \
        F_D_ITEM_MAIL_NAME_VALUE(b, mapType),                \
        F_D_ITEM_MAIL_NAME_VALUE(c, mapType),                \
        F_D_ITEM_MAIL_NAME_VALUE(d, mapType),                \
        F_D_ITEM_MAIL_NAME_VALUE(e, mapType)

#define F_D_ITEM_MAIL_NAME_VALUE_10(a, b, c, d, e, f, g, h, i, j, mapType)   \
        F_D_ITEM_MAIL_NAME_VALUE_5(a, b, c, d, e, mapType),                  \
        F_D_ITEM_MAIL_NAME_VALUE_5(f, g, h, i, j, mapType)


#define F_D_ITEM_GCH_COMMON_FIELDS()   \
    F_D_ITEM("antId",                NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("antName",              NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("cmBandWidth",          NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("centerFreq",           NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("channelId",            NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("dataType",             NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("dataUid",              NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("devId",                NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("devLink",              NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("devName",              NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("devType",              NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("freqRange",            NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("pol",                  NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("satId",                NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("satName",              NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("signalId",             NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("siteId",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("siteName",             NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("taskId",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("unitId",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("unitName",             NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("userId",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("vsatSite",             NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("vsatUser",             NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("inputStartTime",       NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("inputEndTime",         NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("receivtingTime",       NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("rate",                 NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("netSiteName",          NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("netSiteType",          NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("field1",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("field2",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("field3",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("field4",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("field5",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("field6",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("field7",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("field8",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("field9",               NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("field10",              NULL,            eMT_special,        "",            NULL),    \
    F_D_ITEM("SourcePathList"   , NULL,               eMT_special,        "",            NULL)

#define F_D_ITEM_GCH_COMMON_FIELDS_COUNT 41


#endif /* _YAETY_FIELD_EXTRACTOR_MACRO_H_ */
