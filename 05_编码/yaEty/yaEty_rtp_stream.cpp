/****************************************************************************************
 * 文 件 名 : yaEty_rtp_stream.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-03-13
* 编    码 : root      '2019-03-13
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_rtp_stream.h"
#include "yaEty_cap_file_processor.h"
#include "yaEty_rtp_stream_keeper.h"
#include "iostream"

#include <ui/rtp_stream.h>
#include <epan/rtp_pt.h>
#include <epan/proto_data.h>
#include <codecs/codecs.h>
#include <codecs/speex/speex_resampler.h>

#include <thread>

#include <glib.h>
#include <assert.h>

RtpStreamInfo::RtpStreamInfo(packet_info *pinfo, const struct _rtp_info * rtpInfo)
{
    memset(&info_, 0, sizeof info_);

    copy_address(&(info_.src_addr),  &(pinfo->src));
    copy_address(&(info_.dest_addr), &(pinfo->dst));

    info_.src_port          = pinfo->srcport;
    info_.dest_port         = pinfo->destport;
    info_.ssrc              = rtpInfo->info_sync_src;
    info_.payload_type      = rtpInfo->info_payload_type;
}

RtpStreamInfo::RtpStreamInfo(const RtpStreamInfo & copy)
{
    memset(&info_, 0, sizeof info_);

    copy_address(&(info_.src_addr),  &(copy.info_.src_addr));
    copy_address(&(info_.dest_addr), &(copy.info_.dest_addr));

    info_.src_port          = copy.info_.src_port;
    info_.dest_port         = copy.info_.dest_port;
    info_.ssrc              = copy.info_.ssrc;
    info_.payload_type      = copy.info_.payload_type;
}

RtpStreamInfo::~RtpStreamInfo()
{
    free_address(&(info_.src_addr));
    free_address(&(info_.dest_addr));
    memset(&info_, 0, sizeof(info_));
}

bool RtpStreamInfo::operator < (const RtpStreamInfo &right) const
{
    int cmpSrc = cmp_address(&(this->info_.src_addr), &(right.info_.src_addr));
    if (cmpSrc != 0)
    {
        return cmpSrc < 0;
    }

    int cmpDest = cmp_address(&(this->info_.dest_addr), &(right.info_.dest_addr));
    if (cmpDest != 0)
    {
        return cmpDest < 0;
    }

    int cmpSrcP = this->info_.src_port - right.info_.src_port;
    if (cmpSrcP != 0)
    {
        return cmpSrcP < 0;
    }

    int cmpDestP = this->info_.src_port - right.info_.src_port;
    if (cmpDestP != 0)
    {
        return cmpDestP < 0;
    }

    int cmpSsrc = this->info_.ssrc - right.info_.ssrc;
    if (cmpSsrc != 0)
    {
        return cmpSsrc < 0;
    }
    int cmpPayloadType = this->info_.payload_type - right.info_.payload_type;
    if (cmpPayloadType != 0)
    {
        return cmpPayloadType < 0;
    }
}

RtpPacket::RtpPacket(const packet_info *pinfo, const struct _rtp_info *rtp_info, double startRelTime)
{
    memset(&rtpPkt_, 0, sizeof rtpPkt_);

    rtpPkt_.info = (struct _rtp_info *) g_memdup(rtp_info, sizeof(struct _rtp_info));

    rtpPkt_.payload_data = NULL;

    if (rtp_info->info_all_data_present
        && (rtp_info->info_payload_len != 0))
    {
        rtpPkt_.payload_data = (guint8 *) g_memdup(&(rtp_info->info_data[rtp_info->info_payload_offset]),
                                                   rtp_info->info_payload_len);
    }
    pktTimestamp_         = rtp_info->info_timestamp;
    pktSeq_               = rtp_info->info_seq_num;
    rtpPkt_.frame_num     = pinfo->num;
    rtpPkt_.arrive_offset = nstime_to_sec(&pinfo->rel_ts) - startRelTime;

    DEBUG_LOG("construct RtpPacket, sizeof RtpPacket:%d, tid:%d\n", sizeof (RtpPacket), std::this_thread::get_id());;
}

RtpPacket::~RtpPacket()
{
    if (rtpPkt_.info)
    {
        g_free(rtpPkt_.info);
        rtpPkt_.info = NULL;
    }

    if (rtpPkt_.payload_data)
    {
        g_free(rtpPkt_.payload_data);
        rtpPkt_.payload_data = NULL;
    }

    // DEBUG_LOG_("destruct RtpPacket, tid:%d\n", std::this_thread::get_id());
}

int onGotNalu_callback_public(NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata)
{
    RtpStream *rtpStream = reinterpret_cast<RtpStream *>(userdata);
    return rtpStream->onGotNalu_callback(naluType, nalu, naluLen);
}

int onGotH263Nalu_callback_public(H263NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata)
{
    RtpStream *rtpStream = reinterpret_cast<RtpStream *>(userdata);
    return rtpStream->onGotH263Nalu_callback(naluType, nalu, naluLen);
}

int onGotH261Nalu_callback_public(H261NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata)
{
    RtpStream *rtpStream = reinterpret_cast<RtpStream *>(userdata);
    return rtpStream->onGotH261Nalu_callback(naluType, nalu, naluLen);
}

RtpStream::RtpStream(const rtp_stream_info_t &rtpStreamInfo, packet_info *pinfo, RtpStreamKeeper *keeper)
    : rtpStreamInfo_(rtpStreamInfo)
    , sample_bytes_(sizeof(SAMPLE) / sizeof(char))
    , decoders_hash_(rtp_decoder_hash_table_new())
    , dirName_(CFG->GetContentReassemblyDir())
    , rtpH264Unpacker(onGotNalu_callback_public, this)
    , rtpH261Unpacker(onGotH261Nalu_callback_public, this)
    , rtpH263Unpacker(onGotH263Nalu_callback_public, this)
{
    mediaType_ = keeper->getRtpMediaType(&rtpStreamInfo_);

    if (CFG->AddTaskID())
        taskID_ = "_" + CFG->GetTaskID();
    rtpStreamInfo_.packet_count   = 0;
    rtpStreamInfo_.start_fd       = (frame_data *)g_memdup(pinfo->fd, sizeof(frame_data));
    rtpStreamInfo_.start_rel_time = pinfo->rel_ts;

    /* Get the Setup frame number who set this RTP stream */
    auto p_conv_data = (struct _rtp_conversation_info *)p_get_proto_data(wmem_file_scope(),
                                                                         pinfo, proto_get_id_by_filter_name("rtp"), 0);
    if (p_conv_data)
        rtpStreamInfo_.setup_frame_number = p_conv_data->frame_number;
    else
        rtpStreamInfo_.setup_frame_number = 0xFFFFFFFF;

    DEBUG_LOG("construct rtp stream, sizeof RtpStream:%d, tid:%d\n", sizeof(RtpStream), std::this_thread::get_id());
}

RtpStream::~RtpStream()
{
    struct stat file_state;
    memset(&file_state ,0, sizeof(struct stat));

    closeFileFp();

    if (file_state.st_size < CFG->GetContentLimit())
    {
        remove(ContentFileName_.c_str());
    }
    fileWrittenPart_ = 0;

    VoipStreamKeeper::getInstance()->writeRtpStreamFileTbl(&rtpStreamInfo_,this);
    VoipStreamKeeper::getInstance()->setRtpTrailerToSip(getTrailerInfo());
    if (rtpStreamInfo_.start_fd)
    {
        g_free(rtpStreamInfo_.start_fd);
        rtpStreamInfo_.start_fd = NULL;
    }
    memset(&rtpStreamInfo_,0,sizeof(rtpStreamInfo_));
    g_hash_table_destroy(decoders_hash_);
}

int RtpStream::enqueueRtpPacket(packet_info *pinfo, epan_dissect_t *edt, struct _rtp_info *rtpInfo)
{
    if (rtpStreamInfo_.packet_count < 1)
    {   // First packet
        start_abs_offset_ = nstime_to_sec(&pinfo->abs_ts) - start_rel_time_;
        start_rel_time_   = stop_rel_time_ = nstime_to_sec(&pinfo->rel_ts);
    }

    // TODO: lock queue
    queue_rtpPkt_.push(std::make_shared<RtpPacket>(pinfo, rtpInfo, start_rel_time_));

    // update stream statistics
    rtpStreamInfo_.packet_count++;
    rtpStreamInfo_.stop_rel_time = pinfo->rel_ts;
    lastEnqueuedPktNum_          = pinfo->num;

    return 0;
}

//只有在超出长度时才会进行切割
int RtpStream::writeFilePartTbl(int len){
    fileWrittenLen_ += len;
    int max_len = CFG->GetContentFileMaxSize();
    if(max_len!= 0 && max_len < fileWrittenLen_)
    {
        closeFileFp();
        fileWrittenLen_ = 0;
        VoipStreamKeeper::getInstance()->writeRtpStreamFileTbl(&rtpStreamInfo_,this);
        fileWrittenPart_ ++;
    }
}


int RtpStream::writeZeroToFile(uint32_t len)
{
    if (pContentFile_ == NULL)
    {
        return -1;
    }
    int ret = 0;
    char fill = {0};
    for(int i = 0;i < len ;i++){
        ret += fwrite(static_cast<void*>(&fill), 1, 1, pContentFile_) && fflush(pContentFile_);
    }
}

int RtpStream::writeToFile(void *buff, uint32_t len)
{
    if (pContentFile_ == NULL)
    {
        return -1;
    }

    return fwrite(buff, 1, len, pContentFile_) && fflush(pContentFile_);
}

int RtpStream::writeWavHeader(unsigned sample_rate, unsigned channels, int sample_bytes_)
{
    guint32 tmp32;
    guint16 tmp16;
    char    wav_hdr[44];

    /* First non-zero wins */
    audio_out_rate_ = sample_rate;

    RTP_STREAM_DEBUG("Audio sample rate is %u", audio_out_rate_);

    /* write WAVE header */
    memset(&wav_hdr, 0, sizeof(wav_hdr));
    memcpy(&wav_hdr[0], "RIFF", 4);
    memcpy(&wav_hdr[4], "\xFF\xFF\xFF\xFF", 4); /* XXX, unknown, file size - 8*/
    memcpy(&wav_hdr[8], "WAVE", 4);

    memcpy(&wav_hdr[12], "fmt ", 4);
    memcpy(&wav_hdr[16], "\x10\x00\x00\x00", 4); /* PCM */
    memcpy(&wav_hdr[20], "\x01\x00", 2);         /* PCM */
    /* # channels */
    tmp16 = channels;
    memcpy(&wav_hdr[22], &tmp16, 2);
    /* sample rate */
    tmp32 = sample_rate;
    memcpy(&wav_hdr[24], &tmp32, 4);
    /* byte rate */
    tmp32 = sample_rate * channels * sample_bytes_;
    memcpy(&wav_hdr[28], &tmp32, 4);
    /* block align */
    tmp16 = channels * sample_bytes_;
    memcpy(&wav_hdr[32], &tmp16, 2);
    /* bits per sample */
    tmp16 = 8 * sample_bytes_;
    memcpy(&wav_hdr[34], &tmp16, 2);

    memcpy(&wav_hdr[36], "data", 4);
    memcpy(&wav_hdr[40], "\xFF\xFF\xFF\xFF", 4); /* XXX, unknown, data size(file size - 44) */

    return writeToFile(wav_hdr, sizeof(wav_hdr));
}

int RtpStream::writeWavHeader(const char * file_name, unsigned file_size)
{
    uint32_t tmp_size;

    /* First non-zero wins */
    if (pContentFile_ != NULL &&decode_flag_ == 1)
    {
        fseek(pContentFile_, 4, SEEK_SET);

        tmp_size = file_size - 8;

        fwrite(&tmp_size, sizeof(int32_t), 1, pContentFile_);

        fseek(pContentFile_, 40, SEEK_SET);

        tmp_size = file_size - 44;

        fwrite(&tmp_size, sizeof(int32_t), 1, pContentFile_);
    }

    return 0;
}

int RtpStream::resample(rtp_packet_t *rtp_packet, unsigned sample_rate, uint64_t resample_buff_len,
                        SAMPLE *decode_buff, SAMPLE **ppResample_buff, char **write_buff)
{
    spx_uint32_t in_len, out_len;

    /* Resample the audio to match our previous output rate. */
    if (!audio_resampler_)
    {
        audio_resampler_ = speex_resampler_init(1, sample_rate, audio_out_rate_, 10, NULL);
        speex_resampler_skip_zeros(audio_resampler_);
        RTP_STREAM_DEBUG("Started resampling from %u to (out) %u Hz.", sample_rate, audio_out_rate_);
    }
    else
    {
        spx_uint32_t audio_out_rate;
        speex_resampler_get_rate(audio_resampler_, &cur_in_rate, &audio_out_rate);

        if (sample_rate != cur_in_rate)
        {
            speex_resampler_set_rate(audio_resampler_, sample_rate, audio_out_rate);
            RTP_STREAM_DEBUG("Changed input rate from %u to %u Hz. Out is %u.", cur_in_rate, sample_rate, audio_out_rate_);
        }
    }

    in_len  = (spx_uint32_t)rtp_packet->info->info_payload_len;
    out_len = (audio_out_rate_ * (spx_uint32_t)rtp_packet->info->info_payload_len / sample_rate) + (audio_out_rate_ % sample_rate != 0);
    if (out_len * sample_bytes_ > resample_buff_len)
    {
        while ((out_len * sample_bytes_ > resample_buff_len))
            resample_buff_len *= 2;
        *ppResample_buff = (SAMPLE *) g_realloc(*ppResample_buff, resample_buff_len);
    }

    speex_resampler_process_int(audio_resampler_, 0, decode_buff, &in_len, *ppResample_buff, &out_len);
    *write_buff  = (char *) *ppResample_buff;

    return out_len * sample_bytes_;
}

int RtpStream::tryToDecodeSomeRtpPackets()
{
    if(mediaType_ == RtpMediaType::unknown){
        //为了应对sdp后到达时，前面的音频拿不到媒体类型，先检查一次端口->mediaType映射
        mediaType_ = RtpStreamKeeper::getInstance()->getRtpMediaType(&rtpStreamInfo_);
        if(mediaType_ != RtpMediaType::unknown){
            closeFileFp();
            fileWrittenLen_ = 0;
        }
    }
    switch (mediaType_) {
        case RtpMediaType::audio:
            return decodeRtpPacketsToAudio();
        case RtpMediaType::video_h264:
            return decodeRtpPacketsToVideo_h264();
        case RtpMediaType::video_h263:
            return decodeRtpPacketsToVideo_h263();
        case RtpMediaType::video_h261:
            return decodeRtpPacketsToVideo_h261();
        case RtpMediaType::unknown:
        case RtpMediaType::raw:
            return outRawRtpPayload();
    }
    return -1;
}
int RtpStream::outRawRtpPayload(){
    int lRet = 0;
    if (pContentFile_ == NULL)
    {
        openFileFp(0);
    }

    for (; !queue_rtpPkt_.empty(); queue_rtpPkt_.pop())
    {
        auto          rtpPkt     = queue_rtpPkt_.front();
        rtp_packet_t *rtp_packet = rtpPkt->getPkt();
        fwrite(rtp_packet->payload_data, rtp_packet->info->info_payload_len, 1, pContentFile_);
        fflush(pContentFile_);
        writeFilePartTbl(rtp_packet->info->info_payload_len);
    }

    return 0;
}
int RtpStream::decodeRtpPacketsToAudio()
{
    gsize               resample_buff_len = 0x1000;
    SAMPLE             *resample_buff     = (SAMPLE *) g_malloc(resample_buff_len);
    alloced_uptr        resample_ptr      = {(char *)resample_buff, g_free};
    char               *write_buff        = NULL;
    gint64              write_bytes       = 0;
    unsigned            channels          = 0;
    unsigned            sample_rate       = 0;
    int                 lRet              = 0;

    for (; !queue_rtpPkt_.empty(); queue_rtpPkt_.pop())
    {
        auto          rtpPkt      = queue_rtpPkt_.front();
        rtp_packet_t *rtp_packet  = rtpPkt->getPkt();
        uint8_t      *payload     = rtp_packet->payload_data;
        uint32_t      payload_len = rtp_packet->info->info_payload_len;

        uint32_t      pktTimestamp= rtpPkt->getPktTimestamp();
        SAMPLE       *decode_buff   = NULL;
        alloced_uptr  decodeBuffPtr = {(char *)decode_buff, g_free}; // 每一轮 decode_rtp_packet 之后将 decode_buff 进行 g_free
        size_t        decoded_bytes = decode_rtp_packet(rtp_packet, &decode_buff, decoders_hash_, &channels, &sample_rate);
        lossInfo_.packet_total_num_++;
        if(0 == lossInfo_.streamPacketFirstNum)
        {
          lossInfo_.streamPacketFirstNum =  rtpPkt->getPktSeq();
        }
        if (CFG->isFullZeroBytes() == true && CFG->isCutFile() == true && lossInfo_.lastDequeuedPktEndTimestamp_ != 0 && pktTimestamp != 0     //非第一包进入
            && (pktTimestamp - lossInfo_.lastDequeuedPktEndTimestamp_) > 44100)  //时间戳跳变一个最大采样
        {
          std::queue<std::shared_ptr<RtpPacket>> emptyQueue;
          //建立空queue快速清空queue_rtpPkt_
          swap(emptyQueue, queue_rtpPkt_);
          setErrFlag();
          break;
        }
        if (decoded_bytes == 0 || sample_rate == 0)  // 当前 rtp payload 解码失败
        {
            if (pContentFile_ == NULL                // 还没有打开文件
                && rawBuff_.size() < 256)            // raw 缓存小于 256 字节
            {
                std::copy(&payload[0], &payload[payload_len], std::back_inserter(rawBuff_));
                continue;
            }

            // rawBuff_ 缓存够了，打开文件, 准备写入 raw 数据
            if (pContentFile_ == NULL)
            {
                openFileFp(0);
                writeToFile(rawBuff_.data(), rawBuff_.size());
                rawBuff_.clear();
            }

            // 将 payload 写入 raw 文件
            writeToFile(payload, payload_len);
            writeFilePartTbl(payload_len);
            continue;
        }

        // 能解码，但输出文件还没有打开，先打开文件
        if (pContentFile_ == NULL)
        {
            openFileFp(sample_rate);
            writeWavHeader(sample_rate, channels, sample_bytes_);
            decode_flag_ = 1;
        }

        write_buff  = (char *) decode_buff;
        write_bytes = decoded_bytes;

        // need resample ?
        if (audio_out_rate_ != sample_rate && audio_out_rate_ != 1)
        {
            write_bytes = resample(rtp_packet, sample_rate, resample_buff_len,
                                   decode_buff, &resample_buff, &write_buff);
        }

        //时间戳向很小的值跳动
        if (CFG->isFullZeroBytes() == true && lossInfo_.lastDequeuedPktEndTimestamp_ > pktTimestamp) {
            continue;
        }
        //时间戳向很大的时间跳动
        if(CFG->isFullZeroBytes() == true && lossInfo_.lastDequeuedPktEndTimestamp_ != 0 && (pktTimestamp - lossInfo_.lastDequeuedPktEndTimestamp_) > 44100)
        {
            continue;
        }

        if (lossInfo_.lastDequeuedPktNum_ == rtpPkt->getPktSeq()) {
            lossInfo_.packet_repeat_num_++;
            continue;
        }
        // 按时间戳来计算填充数据，或按包数来计算填充数据
        // 字节数 = 样本数*（深度/8）*通道数
        // 时间戳增量（样本数） = 采样率 * 数据真实时间（单位 秒）
        if(CFG->isFullZeroBytes() == true && lossInfo_.lastDequeuedPktNum_ + 1 < rtpPkt->getPktSeq() && lossInfo_.lastDequeuedPktNum_ != 0){
            lossInfo_.streamPacketLossNum_ += (rtpPkt->getPktSeq() - lossInfo_.lastDequeuedPktNum_ -1);
            int fill_size =  0;// 填充字节数
            fill_size = (pktTimestamp - lossInfo_.lastDequeuedPktEndTimestamp_)   //时间戳增量
                          * (16/8) * channels;
            lossInfo_.streamPacketLossBytes_ += fill_size;
            writeZeroToFile(fill_size);
        }
        decode_flag_ = 1;
        lossInfo_.packet_normal_num_++;
        writeToFile(write_buff, write_bytes);
        lossInfo_.lastDequeuedPktNum_ = rtpPkt->getPktSeq();
        lossInfo_.lastDequeuedPktEndTimestamp_ = pktTimestamp + (decoded_bytes/((16/8)*channels)); // 记录上一包头时间 + 解码后增量时间
        lossInfo_.channels_ = channels;
        lossInfo_.rtp_sample_rate_ = sample_rate;
        writeFilePartTbl(write_bytes);
    }


    return 0;
}


bool RtpStream::rtpPktCanDecode(struct _rtp_info *info)
{
    const char *payloadTypeStr  = try_val_to_str_ext(info->info_payload_type, &rtp_payload_type_short_vals_ext);

    if (payloadTypeStr == NULL)
    {
        return false;
    }

    return find_codec(payloadTypeStr);
}

int RtpStream::decodeRtpPacketsToVideo_h264()
{
    int lRet = 0;

    if (pContentFile_ == NULL)
    {
        openFileFp(0);
    }

    for (; !queue_rtpPkt_.empty(); queue_rtpPkt_.pop())
    {
        auto          rtpPkt     = queue_rtpPkt_.front();
        rtp_packet_t *rtp_packet = rtpPkt->getPkt();
        //统计丢包
        lossInfo_.packet_total_num_++;
        if(0 == lossInfo_.streamPacketFirstNum)
        {
          lossInfo_.streamPacketFirstNum =  rtpPkt->getPktSeq();
        }
        if (lossInfo_.lastDequeuedPktNum_ == rtpPkt->getPktSeq()) {
            lossInfo_.packet_repeat_num_++;
            continue;
        }
        rtpH264Unpacker.enqueueRtpPayload(rtp_packet->payload_data, rtp_packet->info->info_payload_len);
        lossInfo_.lastDequeuedPktNum_ = rtpPkt->getPktSeq();
    }

    return 0;
}



int RtpStream::onGotNalu_callback(uint8_t naluType, uint8_t *nalu, int naluLen)
{
    // 写入 start code
    fwrite("\x00\x00\x00\x01", 4, 1, pContentFile_);

    fwrite(nalu, naluLen, 1, pContentFile_);
    fflush(pContentFile_);

    writeFilePartTbl(naluLen);

    return 0;
}

int RtpStream::openFileFp(int sampleRate){
    ensureDirExist(dirName_.c_str());

    char fileNameBuff[1024] = { 0 };
    std::string && file_name = CapFileProcessor::GetCurrentProcessingPcapFilename();

    int pos = file_name.find_last_of('/') > 0 ? file_name.find_last_of('/') + 1 : 0;
    switch (mediaType_) {
        case RtpMediaType::audio:
            sprintf(fileNameBuff, "rtp_audio_0x%x_%ld_%d_%s%s", rtpStreamInfo_.ssrc, random(), fileWrittenPart_,
                file_name.substr(pos, file_name.find_last_of('.') - pos).c_str(), taskID_.c_str());
            if (sampleRate == 0) {
                audio_out_rate_ = 1;
                strcat(fileNameBuff, ".raw");
            } else {
                strcat(fileNameBuff ,".wav");
            }
            break;
        case RtpMediaType::video_h264:
            sprintf(fileNameBuff, "rtp_video_0x%x_%d_%d_%s%s.h264", rtpStreamInfo_.ssrc, random(), fileWrittenPart_,
                file_name.substr(pos, file_name.find_first_of('.') - pos).c_str(), taskID_.c_str());

            break;
        case RtpMediaType::video_h263:
            sprintf(fileNameBuff, "rtp_video_0x%x_%d_%d_%s%s.raw", rtpStreamInfo_.ssrc, random(), fileWrittenPart_,
                file_name.substr(pos, file_name.find_first_of('.') - pos).c_str(), taskID_.c_str());

            break;
        case RtpMediaType::video_h261:
            sprintf(fileNameBuff, "rtp_video_0x%x_%d_%d_%s%s.h261", rtpStreamInfo_.ssrc, random(), fileWrittenPart_,
                file_name.substr(pos, file_name.find_first_of('.') - pos).c_str(), taskID_.c_str());

            break;
        case RtpMediaType::unknown:
        case RtpMediaType::raw:
            sprintf(fileNameBuff, "rtp_unknown_0x%x_%d_%d_%s%s.raw", rtpStreamInfo_.ssrc, random(), fileWrittenPart_,
                file_name.substr(pos, file_name.find_first_of('.') - pos).c_str(), taskID_.c_str());
            break;
    }

    ContentFileName_ = dirName_ + '/' ;
    if(CFG->GetValueOf<int>("DATE_SUBDIR_FLAG") == 1){
        time_t unixTime = time(0);
        tm     tm       = *localtime((time_t *)&unixTime);
        char time_str[32] = {0};
        strftime(time_str, sizeof time_str, "%Y%m%d/", &tm);
        ContentFileName_ += time_str;
        mkdir(ContentFileName_.c_str(),  0755);
    }

    ContentFileName_ += fileNameBuff;
    ContentFileNameWriting_ = ContentFileName_ + ".writing";

    // 保存到 voipStreamKeeper 中，ssrc -> rtp media content file name
    // VoipStreamKeeper::set_voip_tbl_name(&rtpStreamInfo_, ContentFileName_.c_str());

    pContentFile_ = fopen(ContentFileNameWriting_.c_str(), "w");

    return 0;
}
int RtpStream::closeFileFp(){
    // 关闭文件;
    if (pContentFile_)
    {
        // 获取文件大小，并更新到 VoipStreamKeeper
        struct stat file_state;
        memset(&file_state ,0, sizeof(struct stat));
        stat(ContentFileNameWriting_.c_str(), &file_state);
        //decode_flag为1时才会回填wav头
        writeWavHeader(ContentFileNameWriting_.c_str(), file_state.st_size);
        fclose(pContentFile_);
        //重命名

        //输出完整率
        std::string lossInfoStr = "tmp_";
        if( lossInfo_.streamPacketLossNum_ > 0&&lossInfo_.packet_total_num_ - lossInfo_.streamPacketLossNum_>0)//有丢失
        {
          int percent = (lossInfo_.packet_total_num_ - lossInfo_.streamPacketLossNum_)*100 / lossInfo_.packet_total_num_;
          lossInfoStr +=  std::to_string(percent) + "_";
        }else {
          lossInfoStr += "100_";
        }
        ContentFileName_.insert(ContentFileName_.find_last_of('/')+1,lossInfoStr);
        rename(ContentFileNameWriting_.c_str(),ContentFileName_.c_str());
        VoipStreamKeeper::set_voip_tbl_name(&rtpStreamInfo_, ContentFileName_.c_str());
        VoipStreamKeeper::set_voip_rtp_stream_start_time(&rtpStreamInfo_,start_rel_time_*100);
        VoipStreamKeeper::set_voip_file_creat_time_name(&rtpStreamInfo_, time(NULL));
        VoipStreamKeeper::set_voip_rtp_size(&rtpStreamInfo_, file_state.st_size);
        VoipStreamKeeper::set_voip_pkt_loss_count(&rtpStreamInfo_, lossInfo_);
        VoipStreamKeeper::getInstance()->insertRtpStreamFileToVector(ContentFileName_);
        pContentFile_ = NULL;
    }
    return 0;
}


int RtpStream::onGotH263Nalu_callback(uint8_t naluType, uint8_t *nalu, int naluLen)
{
    fwrite(nalu, naluLen, 1, pContentFile_);
    fflush(pContentFile_);
    writeFilePartTbl(naluLen);
    return 0;
}

int RtpStream::decodeRtpPacketsToVideo_h263()
{
    int lRet = 0;

    if (pContentFile_ == NULL)
    {
        openFileFp(0);
    }

    for (; !queue_rtpPkt_.empty(); queue_rtpPkt_.pop())
    {
        auto          rtpPkt     = queue_rtpPkt_.front();
        rtp_packet_t *rtp_packet = rtpPkt->getPkt();

        //统计丢包
        lossInfo_.packet_total_num_++;
        if(0 == lossInfo_.streamPacketFirstNum)
        {
          lossInfo_.streamPacketFirstNum =  rtpPkt->getPktSeq();
        }
        if (lossInfo_.lastDequeuedPktNum_ == rtpPkt->getPktSeq()) {
            lossInfo_.packet_repeat_num_++;
            continue;
        }

        rtpH263Unpacker.enqueueRtpPayload(rtp_packet->payload_data, rtp_packet->info->info_payload_len);
        lossInfo_.lastDequeuedPktNum_ = rtpPkt->getPktSeq();
    }

    return 0;
}

int RtpStream::onGotH261Nalu_callback(uint8_t naluType, uint8_t *nalu, int naluLen)
{
    fwrite(nalu, naluLen, 1, pContentFile_);
    fflush(pContentFile_);
    writeFilePartTbl(naluLen);
    return 0;
}


int RtpStream::decodeRtpPacketsToVideo_h261()
{
    int lRet = 0;

    if (pContentFile_ == NULL)
    {
        openFileFp(0);
    }

    for (; !queue_rtpPkt_.empty(); queue_rtpPkt_.pop())
    {
        auto          rtpPkt     = queue_rtpPkt_.front();
        rtp_packet_t *rtp_packet = rtpPkt->getPkt();

        //统计丢包
        lossInfo_.packet_total_num_++;
        if(0 == lossInfo_.streamPacketFirstNum)
        {
          lossInfo_.streamPacketFirstNum =  rtpPkt->getPktSeq();
        }
        if (lossInfo_.lastDequeuedPktNum_ == rtpPkt->getPktSeq()) {
            lossInfo_.packet_repeat_num_++;
            continue;
        }

        rtpH261Unpacker.enqueueRtpPayload(rtp_packet->payload_data, rtp_packet->info->info_payload_len);
        lossInfo_.lastDequeuedPktNum_ = rtpPkt->getPktSeq();
    }

    return 0;
}