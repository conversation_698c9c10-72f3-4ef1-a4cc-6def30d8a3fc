/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_radius.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-14
* 编    码 : licl         '2018-06-21
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <iconv.h>

#include <algorithm>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_radius.h"

enum
{
    EncodingUTF8 = 1,
    EncodingGBK = 2,
};

/*****************************************************************
*Function    :isUTF8
*Description :检测数据是不是UTF8编码
*Input       :数据的长度与地址
*Output      :none
*Return      :返回UTF8数据的长度， -1代表不全是UTF8数据
*Others      :none
*****************************************************************/
int isUTF8(const char *pData, int len)
{
    if(NULL == pData || len < 1)
    {
        return -1;
    }

    int loop = len;
    const char *p = pData;
    while(loop)
    {
        if(0X0A == *p|| 0X0D == *p)
        {
            p++;
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }
        else if(loop>=3 &&  (0XE0 == (p[0] & 0XF0)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0)) )
        {
            p = p + 3;
            loop = loop - 3;
            continue;
        }
        return 0;/* 这不是 UTF-8 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}

/*****************************************************************
*Function    :isGBK
*Description :探测数据域是不是2字节的GBK数据
*Input       :数据的地址与长度
*Output      :none
*Return      :返回GBK数据的长度， -1代表不全是GBK数据
*Others      :none
*****************************************************************/
int isGBK(const char *pData, int len)
{
    int loop = len;
    const char *p = pData;
    while(loop)
    {
        if(0X0A == *p || 0X0D == *p)
        {
            p++;
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }

        /* 参考: 中华人民共和国信息技术标准化技术委员会 汉字内码扩展规范(GBK) */
        /* GBK/3 CJK 汉字区 6080个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0X81) && ((unsigned char)p[0] <= 0XA0)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XFE) )
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/1 GB2312符号,增补符号 717 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA1) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/5 扩充符号 166 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA8) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/4 CJK汉字和增补汉字 8160 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XAA) && ((unsigned char)p[0] <= 0XFE)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/2 GB2312汉字 6763 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XB0) && ((unsigned char)p[0] <= 0XF7)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        return 0;/* 这不是 GBK 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}



/*****************************************************************
*Function    :charset_convert
*Description :对iconv库的再次封装
*Input       :原编码， 目标编码， 原始数据与长度， 输出数据与容量
*Output      :转码后的数据
*Return      :返回转码成功长度， 转码失败-1
*Others      :none
*****************************************************************/
int charset_convert(const char *from_charset, const char *to_charset, const char *in_buf, size_t in_left, char *out_buf, size_t out_left)
{
    iconv_t icd;
    char *pin = (char *)in_buf;
    char *pout = out_buf;
    size_t out_len = out_left;
    if ((iconv_t)-1 == (icd = iconv_open(to_charset,from_charset)))
    {
        return -1;
    }
    if ((size_t)-1 == iconv(icd, &pin, &in_left, &pout, &out_left))
    {
        iconv_close(icd);
        return -1;
    }
    out_buf[out_len - out_left] = 0;
    iconv_close(icd);
    return (int)out_len - out_left;
}

/*****************************************************************
*Function    :transform_field_for_noprintable_char
*Description :将非UTF8编码的乱码，转16进制输出
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_field_for_noprintable_char(epan_dissect_t *edt, const field_info *pFinfo)
{
    const field_info *finfo    = NULL;
    char             *pHexStr  = NULL;
    uint8_t          *pRawData = NULL;
    char             *pValStr  = NULL;
    std::string       strRes;
    int              lEncoding = 0;
    int              lChineseChar = 0;

    finfo = pFinfo;

    if (NULL == finfo)
    {
        return "";
    }

    // clear content string box. we always return the same address of one
    // std::string objs so it's just like a box!!!
    std::string strContent;

    // is there any nonprintable charactars?
    pValStr  = finfo->value.value.string;
    for (int i = 0; i < finfo->length; i++)
    {
        switch (pValStr[i])
        {
        case '\r' :
        case '\n' :
        case '\t' :
            pValStr[i] = ' '; continue;
        case '|'  :
            pValStr[i] = '_'; continue;
        }

        if (pValStr[i] == '\0') /* 非正常结束 */
        {
            goto TO_HEX_STR;
        }

        if (!isprint(pValStr[i])) /* 可见吗? */
        {
            if(isUTF8(pValStr + i, 3) > 0 && (finfo->length - i) >= 3) /* 是UTF8编码 吗? */
            {
                i++;                       /* 应该步进3个字节, 加上for自带一次++操作， 刚好! */
                i++;
                lEncoding = EncodingUTF8;
                lChineseChar++;
                continue;
            }
            else if(isGBK(pValStr + i, 2) > 0 && (finfo->length - i) >= 2)
            {
                i++;
                lEncoding = EncodingGBK;
                lChineseChar++;
                continue;
            }
            /* 既不是 ASCII， 也不是UTF8， 也不是GBK */
            lChineseChar = 0; /* 连续 汉字计数器 */
            goto TO_HEX_STR;
        }
    }

    // 看来全是可见字符了
    if(EncodingGBK == lEncoding && lChineseChar > 2)
    {
        char   szpCharsetConvertBuffer[256*1024] = {0};
        charset_convert("GBK", "UTF-8//IGNORE", finfo->value.value.string, finfo->length, szpCharsetConvertBuffer, sizeof(szpCharsetConvertBuffer));
        std::string strConverted = szpCharsetConvertBuffer;
        return strConverted;
    }

    if(lChineseChar < 4) /* 如果不满足连续4个 汉字，当乱码处理 */
    {
        goto TO_HEX_STR;
    }

    strRes = pValStr;
    goto OUT;

TO_HEX_STR:
    pHexStr = ws_tvb_bytes_to_str(NULL, finfo->ds_tvb, finfo->start, finfo->length);
    strContent = pHexStr;
    wmem_free(NULL, pHexStr);
    strRes = strContent;
    goto OUT;

OUT:
    return strRes;
}


/*****************************************************************
*Function    :transform_field_for_C2S
*Description :Radius C2S
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_field_for_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{
    return "C2S";
}

static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // RTL tags fields
    F_D_ITEM_RTL_10(),

    // common fields
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    /* Radius 字段 */
    F_D_ITEM("SrcPort"                       , "udp.srcport"                        , eMT_direct,    "",      NULL),
    F_D_ITEM("DstPort"                       , "udp.dstport"                        , eMT_direct,    "",      NULL),
    //F_D_ITEM("C2S"                           , "frame.number" /* Debug */           , eMT_direct,    "",      NULL),
    F_D_ITEM("C2S"                           , "radius.req"                         , eMT_transform, "S2C",   transform_field_for_C2S),
    F_D_ITEM("Proto"                         , "ip.proto"                           , eMT_direct,    "",      NULL),
    F_D_ITEM("TTL"                           , "ip.ttl"                             , eMT_direct,    "",      NULL),
    F_D_ITEM("Code"                          , "radius.code"                        , eMT_direct,    "",      NULL),
    F_D_ITEM("PacketID"                      , "radius.id"                          , eMT_direct,    "",      NULL),
    F_D_ITEM("Length"                        , "radius.length"                      , eMT_direct,    "",      NULL),
    F_D_ITEM("Authenticator"                 , "radius.authenticator"               , eMT_direct,    "",      NULL),

    /* Radius  AVP 字段 */
    F_D_ITEM("User_Name"                     , "radius.User_Name"                   , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("User_Password"                 , "radius.User_Password"               , eMT_direct,    "",      NULL),
    F_D_ITEM("CHAP_Password"                 , "radius.CHAP_Password"               , eMT_direct,    "",      NULL),
    F_D_ITEM("NAS_IP_Address"                , "radius.NAS_IP_Address"              , eMT_direct,    "",      NULL),
    F_D_ITEM("NAS_Port"                      , "radius.NAS_Port"                    , eMT_direct,    "",      NULL),
    F_D_ITEM("Service_Type"                  , "radius.Service_Type"                , eMT_direct,    "",      NULL),
    F_D_ITEM("Framed_Protocol"               , "radius.Framed_Protocol"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Framed_IP_Address"             , "radius.Framed_IP_Address"           , eMT_direct,    "",      NULL),
    F_D_ITEM("Framed_IP_Netmask"             , "radius.Framed_IP_Netmask"           , eMT_direct,    "",      NULL),
    F_D_ITEM("Framed_Routing"                , "radius.Framed_Routing"              , eMT_direct,    "",      NULL),
    F_D_ITEM("Filter_Id"                     , "radius.Filter_Id"                   , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Framed_MTU"                    , "radius.Framed_MTU"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("Framed_Compression"            , "radius.Framed_Compression"          , eMT_direct,    "",      NULL),
    F_D_ITEM("Login_IP_Host"                 , "radius.Login_IP_Host"               , eMT_direct,    "",      NULL),
    F_D_ITEM("Login_Service"                 , "radius.Login_Service"               , eMT_direct,    "",      NULL),
    F_D_ITEM("Login_TCP_Port"                , "radius.Login_TCP_Port"              , eMT_direct,    "",      NULL),
    F_D_ITEM("Reply_Message"                 , "radius.Reply_Message"               , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Callback_Number"               , "radius.Callback_Number"             , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Callback_Id"                   , "radius.Callback_Id"                 , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Framed_Route"                  , "radius.Framed_Route"                , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Framed_IPX_Network"            , "radius.Framed_IPX_Network"          , eMT_direct,    "",      NULL),
    F_D_ITEM("State"                         , "radius.State"                       , eMT_direct,    "",      NULL),
    F_D_ITEM("Class"                         , "radius.Class"                       , eMT_direct,    "",      NULL),
    F_D_ITEM("Vendor_Specific"               , "radius.Vendor_Specific"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Session_Timeout"               , "radius.Session_Timeout"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Idle_Timeout"                  , "radius.Idle_Timeout"                , eMT_direct,    "",      NULL),
    F_D_ITEM("Termination_Action"            , "radius.Termination_Action"          , eMT_direct,    "",      NULL),
    F_D_ITEM("Called_Station_Id"             , "radius.Called_Station_Id"           , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Calling_Station_Id"            , "radius.Calling_Station_Id"          , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("NAS_Identifier"                , "radius.NAS_Identifier"              , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Proxy_State"                   , "radius.Proxy_State"                 , eMT_direct,    "",      NULL),
    F_D_ITEM("Login_LAT_Service"             , "radius.Login_LAT_Service"           , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Login_LAT_Node"                , "radius.Login_LAT_Node"              , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Login_LAT_Group"               , "radius.Login_LAT_Group"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Framed_AppleTalk_Link"         , "radius.Framed_AppleTalk_Link"       , eMT_direct,    "",      NULL),
    F_D_ITEM("Framed_AppleTalk_Network"      , "radius.Framed_AppleTalk_Network"    , eMT_direct,    "",      NULL),
    F_D_ITEM("Framed_AppleTalk_Zone"         , "radius.Framed_AppleTalk_Zone"       , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Acct_Status_Type"              , "radius.Acct_Status_Type"            , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Delay_Time"               , "radius.Acct_Delay_Time"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Input_Octets"             , "radius.Acct_Input_Octets"           , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Output_Octets"            , "radius.Acct_Output_Octets"          , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Session_Id"               , "radius.Acct_Session_Id"             , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Acct_Authentic"                , "radius.Acct_Authentic"              , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Session_Time"             , "radius.Acct_Session_Time"           , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Input_Packets"            , "radius.Acct_Input_Packets"          , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Output_Packets"           , "radius.Acct_Output_Packets"         , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Terminate_Cause"          , "radius.Acct_Terminate_Cause"        , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Multi_Session_Id"         , "radius.Acct_Multi_Session_Id"       , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Acct_Link_Count"               , "radius.Acct_Link_Count"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Input_Gigawords"          , "radius.Acct_Input_Gigawords"        , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Output_Gigawords"         , "radius.Acct_Output_Gigawords"       , eMT_direct,    "",      NULL),
    F_D_ITEM("Event_Timestamp"               , "radius.Event_Timestamp"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Egress_VLANID"                 , "radius.Egress_VLANID"               , eMT_direct,    "",      NULL),
    F_D_ITEM("Ingress_Filters"               , "radius.Ingress_Filters"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Egress_VLAN_Name"              , "radius.Egress_VLAN_Name"            , eMT_direct,    "",      NULL),
    F_D_ITEM("User_Priority_Table"           , "radius.User_Priority_Table"         , eMT_direct,    "",      NULL),
    F_D_ITEM("CHAP_Challenge"                , "radius.CHAP_Challenge"              , eMT_direct,    "",      NULL),
    F_D_ITEM("NAS_Port_Type"                 , "radius.NAS_Port_Type"               , eMT_direct,    "",      NULL),
    F_D_ITEM("Port_Limit"                    , "radius.Port_Limit"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("Login_LAT_Port"                , "radius.Login_LAT_Port"              , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Tunnel_Type"                   , "radius.Tunnel_Type"                 , eMT_direct,    "",      NULL),
    F_D_ITEM("Tunnel_Medium_Type"            , "radius.Tunnel_Medium_Type"          , eMT_direct,    "",      NULL),
    F_D_ITEM("Tunnel_Client_Endpoint"        , "radius.Tunnel_Client_Endpoint"      , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Tunnel_Server_Endpoint"        , "radius.Tunnel_Server_Endpoint"      , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Acct_Tunnel_Connection"        , "radius.Acct_Tunnel_Connection"      , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Tunnel_Password"               , "radius.Tunnel_Password"             , eMT_direct,    "",      NULL),
    F_D_ITEM("ARAP_Password"                 , "radius.ARAP_Password"               , eMT_direct,    "",      NULL),
    F_D_ITEM("ARAP_Features"                 , "radius.ARAP_Features"               , eMT_direct,    "",      NULL),
    F_D_ITEM("ARAP_Zone_Access"              , "radius.ARAP_Zone_Access"            , eMT_direct,    "",      NULL),
    F_D_ITEM("ARAP_Security"                 , "radius.ARAP_Security"               , eMT_direct,    "",      NULL),
    F_D_ITEM("ARAP_Security_Data"            , "radius.ARAP_Security_Data"          , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Password_Retry"                , "radius.Password_Retry"              , eMT_direct,    "",      NULL),
    F_D_ITEM("Prompt"                        , "radius.Prompt"                      , eMT_direct,    "",      NULL),
    F_D_ITEM("Connect_Info"                  , "radius.Connect_Info"                , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Configuration_Token"           , "radius.Configuration_Token"         , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("EAP_Message"                   , "radius.EAP_Message"                 , eMT_direct,    "",      NULL),
    F_D_ITEM("Message_Authenticator"         , "radius.Message_Authenticator"       , eMT_direct,    "",      NULL),
    F_D_ITEM("Tunnel_Private_Group_Id"       , "radius.Tunnel_Private_Group_Id"     , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Tunnel_Assignment_Id"          , "radius.Tunnel_Assignment_Id"        , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Tunnel_Preference"             , "radius.Tunnel_Preference"           , eMT_direct,    "",      NULL),
    F_D_ITEM("ARAP_Challenge_Response"       , "radius.ARAP_Challenge_Response"     , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Interim_Interval"         , "radius.Acct_Interim_Interval"       , eMT_direct,    "",      NULL),
    F_D_ITEM("Acct_Tunnel_Packets_Lost"      , "radius.Acct_Tunnel_Packets_Lost"    , eMT_direct,    "",      NULL),
    F_D_ITEM("NAS_Port_Id"                   , "radius.NAS_Port_Id"                 , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Framed_Pool"                   , "radius.Framed_Pool"                 , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Chargeable_User_Identity"      , "radius.Chargeable_User_Identity"    , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Tunnel_Client_Auth_Id"         , "radius.Tunnel_Client_Auth_Id"       , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Tunnel_Server_Auth_Id"         , "radius.Tunnel_Server_Auth_Id"       , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("NAS_Filter_Rule"               , "radius.NAS_Filter_Rule"             , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("NAS_IPv6_Address"              , "radius.NAS_IPv6_Address"            , eMT_direct,    "",      NULL),
    F_D_ITEM("Framed_Interface_Id"           , "radius.Framed_Interface_Id"         , eMT_direct,    "",      NULL),
    F_D_ITEM("Framed_IPv6_Prefix"            , "radius.Framed_IPv6_Prefix"          , eMT_direct,    "",      NULL),
    F_D_ITEM("Login_IPv6_Host"               , "radius.Login_IPv6_Host"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Framed_IPv6_Route"             , "radius.Framed_IPv6_Route"           , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Framed_IPv6_Pool"              , "radius.Framed_IPv6_Pool"            , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Error_Cause"                   , "radius.Error_Cause"                 , eMT_direct,    "",      NULL),
    F_D_ITEM("EAP_Key_Name"                  , "radius.EAP_Key_Name"                , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Digest_Response"               , "radius.Digest_Response"             , eMT_transform, "",      transform_field_for_noprintable_char),
    F_D_ITEM("Digest_Realm"                  , "radius.Digest_Realm"                , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Nonce"                  , "radius.Digest_Nonce"                , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Response_Auth"          , "radius.Digest_Response_Auth"        , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Nextnonce"              , "radius.Digest_Nextnonce"            , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Method"                 , "radius.Digest_Method"               , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_URI"                    , "radius.Digest_URI"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Qop"                    , "radius.Digest_Qop"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Algorithm"              , "radius.Digest_Algorithm"            , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Entity_Body_Hash"       , "radius.Digest_Entity_Body_Hash"     , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_CNonce"                 , "radius.Digest_CNonce"               , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Nonce_Count"            , "radius.Digest_Nonce_Count"          , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Username"               , "radius.Digest_Username"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Opaque"                 , "radius.Digest_Opaque"               , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Auth_Param"             , "radius.Digest_Auth_Param"           , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_AKA_Auts"               , "radius.Digest_AKA_Auts"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Domain"                 , "radius.Digest_Domain"               , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_Stale"                  , "radius.Digest_Stale"                , eMT_direct,    "",      NULL),
    F_D_ITEM("Digest_HA1"                    , "radius.Digest_HA1"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("SIP_AOR"                       , "radius.SIP_AOR"                     , eMT_direct,    "",      NULL),
};

ProtoFieldExtractorRadius::ProtoFieldExtractorRadius():ProtoFieldExtractor("RADIUS", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}
