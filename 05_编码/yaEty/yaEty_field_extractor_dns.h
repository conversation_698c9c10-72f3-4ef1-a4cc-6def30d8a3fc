/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_dns.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-24
* 编    码 : zhengsw      '2018-05-24
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _YAETY_FIELD_EXTRACTOR_DNS_H_
#define _YAETY_FIELD_EXTRACTOR_DNS_H_

#include "yaEty_field_extractor.h"

class ProtoFieldExtractorDns : public ProtoFieldExtractor
{
public:
    ProtoFieldExtractorDns();

public:
    virtual bool ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter);

private:
    int ExtractDnsQueryGroupNth(epan_dissect_t *edt, RecordWriter *pWriter,
                                int groupIndex, int groupItemCnt, int lGrpFirstDescIndex);
    int ExtractDnsAnswerGroupNth(epan_dissect_t *edt, RecordWriter *pWriter,
                                 int groupIndex, int groupItemCnt, int lGrpFirstDescIndex);

    field_info * ExtractDnsQaGroupItemOf(epan_dissect_t *edt, RecordWriter *pWriter,
                                         int groupIndex, int lItemDescIndex,
                                         const char *pRealWsFieldName);

    int OutputDefaultValueForSomeField(RecordWriter *pWriter, int fieldIndexFrom, int fieldCnt);
};


#endif /* _YAETY_FIELD_EXTRACTOR_DNS_H_ */
