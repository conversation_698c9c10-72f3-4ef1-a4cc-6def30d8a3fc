/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_gtp_control.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-11-27
* 编    码 : zhangsx      '2018-11-27
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "config.h"
#include "epan/epan_dissect.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_gtp_control.h"
#include  "yaEty_content_reassembly_share_mem.h"

static std::string transform_field_for_field_length(epan_dissect_t *edt, const field_info *pFinfo)
{
    return (NULL != pFinfo) ? std::to_string(pFinfo->length) : "";
}

static std::string transform_field_for_field_value(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (nullptr == pFinfo)
    {
        return "";
    }
    int value = fvalue_get_uinteger(const_cast<fvalue_t *>(&pFinfo->value));
    const char *pString = try_val_to_str_ext(value, (value_string_ext *)pFinfo->hfinfo->strings );
    return pString != nullptr ? std::string(pString) : std::to_string(value);
}

static std::string transform_for_gtp_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{
    field_info *pfinfo1 = get_first_field_info_from_interesting_fields(edt, "gtp.message");
    field_info *pfinfo2 = get_first_field_info_from_interesting_fields(edt, "gtpv2.message_type");
    
    field_info *pfinfo = pfinfo1 ? pfinfo1 : pfinfo2;
    
    if(nullptr == pfinfo)
    {
        return "";
    }
    
    uint8_t && type_value = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pfinfo->value)));
    
    if(type_value < 0xfe )
    {
        return type_value % 2 == 0 ? "C2S" : "S2C";
    }
    else
    {
        return "";
    }
}

struct choose_for_gtp_field_special
{
public:
    choose_for_gtp_field_special(int type, const char* field_name1, const char* field_name2)
    :_type(type), _field_name1(field_name1), _field_name2(field_name2)
    {
    }
    std::string operator() (epan_dissect_t *edt, const field_info *pFinfo)
    {
        std::string strRst = get_first_field_value_from_interesting_fields(edt, _field_name2);
        
        if ("" != strRst)
        {
            return strRst;
        }
        
        strRst = get_first_field_value_from_interesting_fields(edt, _field_name1);
        
        return strRst == std::to_string(_type) ? "1" : "";
    }

public:
    int _type;
    const char* _field_name1;
    const char* _field_name2;

};

struct choose_for_gtp_type_field
{
public:
    choose_for_gtp_type_field(int type, const char* field_type, const char* field_name)
        :_type(type), _field_type(field_type), _field_name(field_name)
    {
    }
    std::string operator() (epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info * pfinfo = nullptr;
        int indx = -1;

        int field_id = proto_registrar_get_id_byname(_field_type);

        GPtrArray *finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);

        for (int i = 0; i < g_ptr_array_len(finfos_array) && finfos_array != nullptr; ++i)
        {
            pfinfo = (field_info *)g_ptr_array_index(finfos_array, i);
            if (nullptr == pfinfo)
            {
                return "";
            }

            if (_type == fvalue_get_uinteger(const_cast<fvalue_t *>(&(pfinfo->value))))
            {
                indx = i;
                break;
            }
        }
                    
        field_id = proto_registrar_get_id_byname(_field_name);

        finfos_array = proto_get_finfo_ptr_array(edt->tree, field_id);

        if ((indx != -1 && indx < g_ptr_array_len(finfos_array)) && finfos_array != nullptr)
        {
            pfinfo = (field_info *)g_ptr_array_index(finfos_array, indx);
            if (nullptr == pfinfo)
            {
                return "";
            }
            return ety_ws_get_node_field_value(pfinfo, edt);
        }
        else
        {
            return "";
        }


    }

public:
    int _type;

    const char* _field_type;
    const char* _field_name;
};

static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort"                          , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("udp.srcport","tcp.srcport")),
    F_D_ITEM("DstPort"                          , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("udp.dstport","tcp.dstport")),
    F_D_ITEM("C2S"                              , ""                                    , eMT_fromEdt,      "",     transform_for_gtp_C2S),
    F_D_ITEM("Proto"                            , "ip.proto"                            , eMT_direct,       "",     NULL),
    F_D_ITEM("TTL"                              , "ip.ttl"                              , eMT_direct,       "",     NULL),
    F_D_ITEM("Version"                          , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.flags.version","gtpv2.version")),
    F_D_ITEM("MessageType"                      , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.message","gtpv2.message_type")),
    F_D_ITEM("SequenceNumber"                   , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.seq_number","gtpv2.seq")),
    F_D_ITEM("TEID"                             , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.teid","gtpv2.teid")),
    F_D_ITEM("TEIDDataI"                        , "gtp.teid_data"                       , eMT_direct,       "",     NULL),
    F_D_ITEM("TEIDDataII"                       , "gtp.teid_ii"                         , eMT_direct,       "",     NULL),
    F_D_ITEM("TEIDControlPlane"                 , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.teid_cp","gtpv2.teid_c")),
    F_D_ITEM("NSAPI"                            , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.nsapi","gtpv2.pdn_numbers_nsapi")),
    F_D_ITEM("ChargingID"                       , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.chrg_id","gtpv2.charging_id")),
    F_D_ITEM("MSISDN"                           , "e164.msisdn"                         , eMT_direct,       "",     NULL),
    F_D_ITEM("IMEI"                             , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.ext_imeisv","gtpv2.mei")),
    F_D_ITEM("IMSI"                             , "e212.imsi"                           , eMT_direct,       "",     NULL),
    F_D_ITEM("MCC"                              , "e212.mcc"                            , eMT_transform,    "",     transform_field_for_field_value),
    F_D_ITEM("MNC"                              , "e212.mnc"                            , eMT_transform,    "",     transform_field_for_field_value),
    F_D_ITEM("LAC"                              , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.lac"     , "gtpv2.lac")),
    F_D_ITEM("RAC"                              , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.rai_rac" , "gtpv2.rac")),
    F_D_ITEM("CellLAC"                          , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.lac"     , "gtpv2.uli_lai_lac")),
    F_D_ITEM("CellCI"                           , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.cgi_ci"  , "gtpv2.tgt_g_cell_id")),
    F_D_ITEM("SAC"                              , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.sai_sac" , "gtpv2.sac")),
    F_D_ITEM("APN"                              , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.apn"     , "gtpv2.apn")),
    F_D_ITEM("GSNaddressIPv4"                   , "gtp.gsn_ipv4"                        , eMT_direct,       "",     NULL),
    F_D_ITEM("RNCaddressIPv4"                   , "gtp.rnc_ipv4"                        , eMT_direct,       "",     NULL),
    F_D_ITEM("CGaddressIPv4"                    , "gtp.chrg_ipv4"                       , eMT_direct,       "",     NULL),
    F_D_ITEM("EndUserAddressIPv4"               , "gtp.user_ipv4"                       , eMT_direct,       "",     NULL),
    F_D_ITEM("EndUserAddressIPv6"               , "gtp.user_ipv6"                       , eMT_direct,       "",     NULL),
    F_D_ITEM("PDNAddressAndPrefixIPv4"          , "gtpv2.pdn_addr_and_prefix.ipv4"      , eMT_direct,       "",     NULL),
    F_D_ITEM("PDNAddressAndPrefixIPv6"          , "gtpv2.pdn_addr_and_prefix.ipv6"      , eMT_direct,       "",     NULL),
    F_D_ITEM("TrackingAreaCode"                 , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtpv2.tai_tac", "gtp.tac")),
    F_D_ITEM("ECGI_ECI"                         , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtpv2.ecgi_eci","gtp.eci")),
    F_D_ITEM("LAI"                              , "gtpv2.uli_lai_flg"                   , eMT_direct,       "",     NULL),
    F_D_ITEM("RAI"                              , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_field_special(2,"gtp.geo_loc_type","gtpv2.uli_rai_flg")),
    F_D_ITEM("SAI"                              , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_field_special(1,"gtp.geo_loc_type","gtpv2.uli_sai_flg")),
    F_D_ITEM("CGI"                              , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_field_special(1,"gtp.geo_loc_type","gtpv2.uli_sai_flg")),
    F_D_ITEM("ENODEBID"                         , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.home_enodeb_id","gtpv2.enodebid")),
    F_D_ITEM("CellID"                           , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.cgi_ci", "gtpv2.tgt_g_cell_id")),
    F_D_ITEM("RAT"                              , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.ext_rat_type", "gtpv2.secondary_rat_usage_data_report.rat_type")),
    F_D_ITEM("PDNType"                          , "gtpv2.pdn_type"                      , eMT_direct,       "",     NULL),
    F_D_ITEM("Cause"                            , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.cause", "gtpv2.cause")),
    F_D_ITEM("UETimeZone"                       , ""                                    , eMT_fromEdt,      "",     choose_for_two_type_field("gtp.timezone","gsm_a.dtap.timezone")),
    F_D_ITEM("ipcp_pri_dns_address"             , ""                                    , eMT_fixed,        "",     NULL),
    F_D_ITEM("ipcp_sec_dns_address"             , ""                                    , eMT_fixed,        "",     NULL),
    F_D_ITEM("DNSServerIPv4Address"             , ""                                    , eMT_fixed,        "",     NULL),
    F_D_ITEM("S1-U_eNodeB_GTP-U_TeidGREKey"     , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(0,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S1-U_SGW_GTP-U_TeidGREKey"        , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(1,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S12_RNC_GTP-U_TeidGREKey"         , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(2,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S12_SGW_GTP-U_TeidGREKey"         , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(3,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S5/S8_SGW_GTP-U_TeidGREKey"       , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(4,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S5/S8_PGW_GTP-U_TeidGREKey"       , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(5,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S5/S8_SGW_GTP-C_TeidGREKey"       , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(6,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S5/S8_PGW_GTP-C_TeidGREKey"       , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(7,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S11_MME_GTP-C_TeidGREKey"         , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(10, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S11/S4_SGW_GTP-C_TeidGREKey"      , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(11, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S10_MME_GTP-C_TeidGREKey"         , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(13, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S3_MME_GTP-C_TeidGREKey"          , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(14, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S3_SGSN_GTP-C_TeidGREKey"         , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(15, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S4_SGSN_GTP-U_TeidGREKey"         , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(16, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S4_SGW_GTP-U_TeidGREKey"          , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(17, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S4_SGSN_GTP-C_TeidGREKey"         , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(18, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_gre_key")),
    F_D_ITEM("S1-U_eNodeB_GTP-U_IPV4"           , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(0,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S1-U_SGW_GTP-U_IPV4"              , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(1,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S12_RNC_GTP-U_IPV4"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(2,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S12_SGW_GTP-U_IPV4"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(3,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S5/S8_SGW_GTP-U_IPV4"             , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(4,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S5/S8_PGW_GTP-U_IPV4"             , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(5,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S5/S8_SGW_GTP-C_IPV4"             , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(6,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S5/S8_PGW_GTP-C_IPV4"             , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(7,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S11_MME_GTP-C_IPV4"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(10, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S11/S4_SGW_GTP-C_IPV4"            , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(11, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S10_MME_GTP-C_IPV4"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(13, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S3_MME_GTP-C_IPV4"                , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(14, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S3_SGSN_GTP-C_IPV4"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(15, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S4_SGSN_GTP-U_IPV4"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(16, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S4_SGW_GTP-U_IPV4"                , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(17, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S4_SGSN_GTP-C_IPV4"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(18, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv4")),
    F_D_ITEM("S1-U_eNodeB_GTP-U_IPV6"           , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(0,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S1-U_SGW_GTP-U_IPV6"              , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(1,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S12_RNC_GTP-U_IPV6"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(2,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S12_SGW_GTP-U_IPV6"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(3,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S5/S8_SGW_GTP-U_IPV6"             , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(4,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S5/S8_PGW_GTP-U_IPV6"             , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(5,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S5/S8_SGW_GTP-C_IPV6"             , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(6,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S5/S8_PGW_GTP-C_IPV6"             , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(7,  "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S11_MME_GTP-C_IPV6"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(10, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S11/S4_SGW_GTP-C_IPV6"            , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(11, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S10_MME_GTP-C_IPV6"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(13, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S3_MME_GTP-C_IPV6"                , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(14, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S3_SGSN_GTP-C_IPV6"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(15, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S4_SGSN_GTP-U_IPV6"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(16, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S4_SGW_GTP-U_IPV6"                , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(17, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
    F_D_ITEM("S4_SGSN_GTP-C_IPV6"               , ""                                    , eMT_fromEdt,      "",     choose_for_gtp_type_field(18, "gtpv2.f_teid_interface_type", "gtpv2.f_teid_ipv6")),
};

ProtoFieldExtractorGtp_control::ProtoFieldExtractorGtp_control():MatchPrefixProtoFieldExtractor("GTP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}

bool ProtoFieldExtractorGtp_control::CanExtractFromThisProtocol(const std::string &strProtoName)
{
    return strBeginwith(strProtoName, strProtoName_) && strProtoName.find('<') == std::string::npos;
}
