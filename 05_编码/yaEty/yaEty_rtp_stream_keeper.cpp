/****************************************************************************************
 * 文 件 名 : yaEty_rtp_stream_keeper.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 : 
 * 功    能 : 
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-03-13
* 编    码 : root      '2019-03-13
* 修    改 : 
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_rtp_stream_keeper.h"
#include "yaEty_voip_stream_keeper.h"

#include <epan/tap.h>
#include <epan/rtp_pt.h>
#include <epan/addr_resolv.h>
#include <ui/rtp_stream.h>

#ifndef YAETY_PACKET_RTP_H
#define YAETY_PACKET_RTP_H 1
#include <epan/dissectors/packet-rtp.h>
#endif

#include <unistd.h>

RtpStreamKeeper::RtpStreamKeeper()
    : rtpStreamProcThread_(std::bind(&RtpStreamKeeper::run, this))
{
    if(!CFG->DoContentReassembly())
    {
        registerTapListener();
    }
    rtpStreamProcThread_.detach();
}

RtpStreamKeeper::~RtpStreamKeeper()
{
  stop();
    if(!CFG->DoContentReassembly())
    {
        // TODO: RtpStreamKeeper 作为单例，其销毁时 wireshark tap 结构已经销毁
        // unregisterTaplistener 需要重新寻找合适时机;
        // unregisterTapListener();
    }
}

int RtpStreamKeeper::registerTapListener()
{
    GString *error_string;

    error_string = register_tap_listener("rtp", this, "", 0, NULL,  &RtpStreamKeeper::onTapRtpPacket, NULL);
    if (error_string != NULL)
    {
        // TODO:
    }

    error_string = register_tap_listener("rtsp", this, "", 0, NULL, &RtpStreamKeeper::onTapRtspPacket, NULL);
    if (error_string != NULL)
    {
        // TODO:
    }

    return 0;
}

int RtpStreamKeeper::unregisterTapListener()
{
    remove_tap_listener(this);
    return 0;
}

// call when a cap file processed finish
void RtpStreamKeeper::onPcapProcessDone()
{
    // 文件已经更换
    this->lastDissectedPktNum_     = 0;

    this->checkStreams(true);
    TblRecordWriter::WriteTblsDone();
}

int RtpStreamKeeper::wakeUpStreamProcThread()
{
    // wake up check rtp stream proc thread
    std::lock_guard<std::mutex> lock(mtxCond_);
    cond_.notify_one();

    return 0;
}
std::string  RtpStreamKeeper::dissectFlowTrailer(epan_dissect_t *edt){
  return get_first_field_value_from_interesting_fields(edt, "gch.ext_data");
}
int RtpStreamKeeper::enqueueRtpPacketToStream(packet_info *pinfo, epan_dissect_t *edt, const void *arg2)
{
    auto              rtpInfo         = (struct _rtp_info *)arg2;
    RtpStreamInfo     streamInfo(pinfo, rtpInfo);
    
    std::lock_guard<std::mutex> lockStreams(mtx_streams_);
    
    // find rtp stream
    auto streamIter = map_streams_.find(streamInfo);
    if (streamIter == map_streams_.end())
    {   // create new rtp stream
        DEBUG_LOG("add new steam: 0x%x\n", streamInfo.get().ssrc);

        auto resPair = map_streams_.emplace(streamInfo, std::make_shared<RtpStream>(streamInfo.get(), pinfo, this));
        streamIter   = resPair.first;
    }

    DEBUG_LOG("add pkt(ssrc:0x%x) to stream(ssrc:0x%x)\n", rtpInfo->info_sync_src, streamInfo.get().ssrc);

    VoipStreamKeeper::set_voip_line_no(&streamInfo.get(), edt);
    std::string FlowTrailer = dissectFlowTrailer(edt);
    // enqueue rtp packet to rtp stream
    streamIter->second->setTrailerInfo(FlowTrailer);
    streamIter->second->enqueueRtpPacket(pinfo, edt, rtpInfo);

    return 0;
}

int RtpStreamKeeper::processRtpPacket(packet_info *pinfo, epan_dissect_t *edt, const void *arg2)
{
    // check wether can we decode it?
    //if (!RtpStream::rtpPktCanDecode((struct _rtp_info *)arg2))
    //{
    //    return -1;
    //}
    
    enqueueRtpPacketToStream(pinfo, edt, arg2);
    wakeUpStreamProcThread();
    
    DEBUG_LOG("wake up it, tid:%d\n",  std::this_thread::get_id());
    
    return 0;
}

int RtpStreamKeeper::processRtspPacket(packet_info *pinfo, epan_dissect_t *edt, const void *arg2)
{
    std::string rtspContentType = get_first_field_value_from_interesting_fields(edt, "rtsp.content-type");
    std::string rtspTransport   = get_first_field_value_from_interesting_fields(edt, "rtsp.transport");

    // DESCRIBE 消息的响应，存在 rtsp content
    if (!rtspContentType.empty() && rtspContentType == "application/sdp")
    {
        field_info *finfoTypeNumber = get_first_field_info_from_interesting_fields(edt, "sdp.media.format");
        if (finfoTypeNumber == NULL)
        {
            return -1;
        }

        // finfoTypenumber 的实际值类似: "DynamicRTP-Type-96"
        char        *typeNumberStr          = tvb_format_text(finfoTypeNumber->ds_tvb, finfoTypeNumber->start, finfoTypeNumber->length);
        uint8_t      mediaFormatType_number = atoi(typeNumberStr);
        std::string  mediaFormatType_type   = get_first_field_value_from_interesting_fields(edt, "sdp.mime.type");
        std::string  mediaH264PackMode      = get_first_field_value_from_interesting_fields(edt, "sdp.fmtp.h264_packetization_mode");
        std::string  H261mediaFormatType_type   = get_first_field_value_from_interesting_fields(edt, "sdp.media.format");

        // 如果有多层 ip?
        std::string server_ip = get_first_field_value_from_interesting_fields(edt, "ip.src");

        RtpMediaInfo minfo;
        if (mediaFormatType_type == "H264") {
            minfo.mediaType = RtpMediaType::video_h264;
        } else if (mediaFormatType_type == "H263") {
            minfo.mediaType = RtpMediaType::video_h263;
        } else if (H261mediaFormatType_type == "ITU-T H.261" || mediaFormatType_type.find("H.261")!= std::string::npos) {
            minfo.mediaType = RtpMediaType::video_h261;
        } else if (mediaFormatType_type == "OPUS") {
            minfo.mediaType = RtpMediaType::audio;
        } else if (mediaFormatType_type == "SIREN") {
            minfo.mediaType = RtpMediaType::audio;
        }
        else {
            minfo.mediaType = RtpMediaType::unknown;
        }
        minfo.rtpPayloadType = mediaFormatType_number;
        minfo.pack_mode = atoi(mediaH264PackMode.c_str());

        // 建立 server_ip 与 minfo 的关联
        map_serverIp2rtpMediaInfo_.emplace(server_ip, minfo);
    }
    else if (!rtspTransport.empty())
    {
        auto findPos = rtspTransport.find("ssrc=");
        if (findPos == std::string::npos)
        {
            return -1;
        }

        std::string  ssrcStr = rtspTransport.substr(findPos + 5, 8);
        const char  *pSsrc   = ssrcStr.c_str();
        uint32_t     ssrc    = strtol(pSsrc, NULL, 16);

        std::string server_ip = get_first_field_value_from_interesting_fields(edt, "ip.src");
        auto mapFindIter = map_serverIp2rtpMediaInfo_.find(server_ip);
        if (mapFindIter == map_serverIp2rtpMediaInfo_.end())
        {   // 未预先保存过该 rtsp 相关的 sdp 信息
            return -2;
        }

        RtpMediaInfo minfo = mapFindIter->second;
        map_ssrc2rtpMediaInfo_.emplace(ssrc, minfo);

        // 建立了 ssrc 与 info 关联后， server 不再需要;
        map_serverIp2rtpMediaInfo_.erase(mapFindIter);
    }

    return 0;
}

int RtpStreamKeeper::updateDissectedPktNum(packet_info *pinfo)
{
    std::lock_guard<std::mutex> lockStreams(mtx_streams_);

    lastDissectedPktNum_ = pinfo->num;
    return 0;
}

int RtpStreamKeeper::onTapEthPacket(void *arg, packet_info *pinfo, epan_dissect_t *edt, const void *arg2)
{
    auto              pKeeper         = (RtpStreamKeeper *)arg;
    return pKeeper->updateDissectedPktNum(pinfo);
}

int RtpStreamKeeper::onTapRtpPacket(void *arg, packet_info *pinfo, epan_dissect_t *edt, const void *arg2)
{
    auto              pKeeper         = (RtpStreamKeeper *)arg;
    return pKeeper->processRtpPacket(pinfo, edt, arg2);
}

int RtpStreamKeeper::onTapRtspPacket(void *arg, packet_info *pinfo, epan_dissect_t *edt, const void *arg2)
{
    auto              pKeeper         = (RtpStreamKeeper *)arg;
    return pKeeper->processRtspPacket(pinfo, edt, arg2);
}

int RtpStreamKeeper::tryToDestroyStream(const RtpStreamInfo &info, const RtpStream &rtpStream)
{
    // 1) 若干 pkt 之后仍未活跃；
    // 2) 已经更换了输入 pcap 文件;
    if (lastDissectedPktNum_ < rtpStream.getLastEnqueuedPktNum()                 // 更换了文件？
        || lastDissectedPktNum_ - rtpStream.getLastEnqueuedPktNum() > 200)       // 已经许久没有加入新 pkt 了
    {
        DEBUG_LOG("remove  stream: 0x%x\n", info.get().ssrc);

        map_streams_.erase(info);
    }
    
    return 0;
}

int RtpStreamKeeper::checkStreams(bool _is_file_changed)
{
    // a) 所有会话尝试解码自己的 rtp packets;
    // b) 检测所有 stream 是否应该销毁：
    std::lock_guard<std::mutex> lockStreams(mtx_streams_);

    for (auto iter = map_streams_.begin();
         iter != map_streams_.end();
         )
    {
        uint64_t nowtime = time(NULL);
        iter->second->tryToDecodeSomeRtpPackets();

        // tryToDestroyStream
        if (_is_file_changed                                                                   // 更换了文件？
            || lastDissectedPktNum_ - iter->second.get()->getLastEnqueuedPktNum() > 200       // 已经许久没有加入新 pkt 了
            || iter->second.get()->getErrFlag() == 1)
        {
            if (last_decode_time != 0 && nowtime - last_decode_time  > CFG->GetScanTimeoutRtpMapSecond()) {
                map_port2rtpMediaInfo_.clear();
            }
            DEBUG_LOG("remove  stream: 0x%x\n", iter->first.get().ssrc);
            iter = map_streams_.erase(iter);
        }
        else
        {
            iter++;
        }
        last_decode_time = nowtime;
    }
    
    return 0;
}

int RtpStreamKeeper::run()
{
    while (bRunFlag_)
    {
        {
            std::unique_lock<std::mutex> lock(mtxCond_);
            cond_.wait(lock, std::bind(&RtpStreamKeeper::anyStreamHasPktToDecode, this));
        }

        // 醒来还是先检查一下
        if (!bRunFlag_)
        {
            break;
        }

        checkStreams(false);
    }

    return 0;
}

int RtpStreamKeeper::stop()
{
    bRunFlag_ = false;

    std::lock_guard<std::mutex> lock(mtxCond_);
    cond_.notify_one();

    return 0;
}

bool RtpStreamKeeper::anyStreamHasPktToDecode()
{
    std::lock_guard<std::mutex> lock(mtx_streams_);
    for (auto &item : map_streams_)
    {
        if (item.second->hasPktToDecode())
        {
            return true;
        }
    }
    if(bRunFlag_ == false)
    {
      return true;
    }
    return false;
}

RtpMediaType RtpStreamKeeper::getRtpMediaType(rtp_stream_info_t *pInfo)
{
    guint8 type = pInfo->payload_type;

    switch (type)
    {
    case PT_PCMU:
    case PT_GSM:
    case PT_PCMA: // g711
    case PT_G721:
    case PT_G723:
    case PT_G722:
    case PT_G728:
    case PT_G729:
    // case PT_UNDF_96:  //OPUS
    // case PT_UNDF_113: //SIREN
    // case PT_UNDF_105: //AMR
        return RtpMediaType::audio;
    case PT_H261:
        return RtpMediaType::video_h261;
    case PT_H263:
        return RtpMediaType::video_h263;
    }

    //rtsp关联的h264
    auto findIter = map_ssrc2rtpMediaInfo_.find(pInfo->ssrc);
    if (findIter != map_ssrc2rtpMediaInfo_.end())
    {
        RtpMediaInfo minfo = findIter->second;
        return minfo.mediaType;
    }

    //sip关联的h264
    RtpPortInfo rtpPort;
    rtpPort.port_dst = pInfo->dest_port;
    rtpPort.port_src = pInfo->src_port;
    rtpPort.payload_type = pInfo->payload_type;

    auto findPortIter_src = map_port2rtpMediaInfo_.find(rtpPort);
    if (findPortIter_src != map_port2rtpMediaInfo_.end())
    {
        RtpMediaInfo minfo = findPortIter_src->second;
        return minfo.mediaType;
    }
    rtpPort.port_src = pInfo->dest_port;
    rtpPort.port_dst = pInfo->src_port;
    rtpPort.payload_type = pInfo->payload_type;

    auto findPortIter_dst = map_port2rtpMediaInfo_.find(rtpPort);
    if (findPortIter_dst != map_port2rtpMediaInfo_.end())
    {
        RtpMediaInfo minfo = findPortIter_dst->second;
        return minfo.mediaType;
    }
    return  RtpMediaType::unknown;

}
