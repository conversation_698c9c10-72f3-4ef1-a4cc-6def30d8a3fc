/****************************************************************************************
 * 文 件 名 : yaEty_pcap_scanner.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-06-24
* 编    码 : root      '2018-06-24
* 修    改 : zhangsx   '2018-11-12
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _YAETY_PCAP_SCANNER_H_
#define _YAETY_PCAP_SCANNER_H_

#include <string>
#include <vector>
#include <queue>
#include <functional>
#include <map>

#include "yaEty_cap_file_manager.h"

#define RECORD_PATH "./record.json"

class PcapScanner
{
public:
    PcapScanner(const std::vector<std::string> &dirList,
        const std::string &strWritingSuffix,
        const std::string &strIgnoreSuffixList,
        const std::string &strProcMarkSuffix);             
    ~PcapScanner();                                         //添加保存记录操作

public:
    int      scan();
    int      getPcapFileCnt();
    uint64_t getTotalWritingFileSize();
    int      foreachPcapFile(std::function<int (const char *)> funProc);

    int      getNextPcapFile(std::string &strFileName);

    int shouldProcessFile(const char *fileName, bool bIsDir);

private:
    const std::vector<std::string> &dirList_;
    std::string              strWritingSuffix_;
    uint64_t                 uTotalWritingFileSize_;
    std::vector<std::string> vecIgnoreSuffixs_;
    std::string              strProcMarkSuffix_;
    std::map<uint64_t,std::string>  pcapQueue_;
    uint64_t                 store_file_num = 0; //在读取文件名中 没有unknown_xxx_timestamp时，做累加插入pcapQueue_
    //添加文件管理
    CapFileTree file_manager;
};

#endif /* _YAETY_PCAP_SCANNER_H_ */
