#include "rtp_h264_packer.h"
#include <algorithm>
#include <arpa/inet.h>

struct NaluHeader
{
    NaluHeader(uint8_t naluHeader)
        : data(naluHeader)
    {
    }

    NaluType_e getType()
    {
        return (NaluType_e)(data & 0x1f); // 取低 5 位;
    }

    uint8_t getNRI()
    {
        return (data & 0x6f) >> 5;
    }

    uint8_t data;
};

struct FuHeader
{
    FuHeader(uint8_t fuHeader): data(fuHeader)
    {
    }

    enum FU_type
    {
        FU_ill,                 // 错误 fu
        FU_start,
        FU_middle,
        FU_end,
    };

    FU_type getType()
    {
        // 最高 bit 置 1
        if ((data & 0x80) == 0x80)
        {
            return FU_start;
        }

        // 次高 bit 置 1
        if ((data & 0x40) == 0x40)
        {
            return FU_end;
        }

        // 最高 2 bit 置 0
        if ((data & 0xc0) == 0)
        {
            return FU_middle;
        }

        return FU_ill;
    }

    NaluType_e getNaluType()
    {
        return (NaluType_e)(data & 0x18);
    }

    uint8_t data;
};

RtpH264Unpacker::RtpH264Unpacker(onGotNalu_callback_t callback, void *userdata)
    : onGotNalu_func_(callback)
    , userdata_(userdata)
    , naluBuff_(1400)
{
}

// | 1 byte   | 2 byte     | n byte |
// nalu-header nalu-size-01 nalu-01 nalu-size-02 nalu-02
int RtpH264Unpacker::processSTAP_A(uint8_t *rtpPayload, int len)
{
    for (int offset = 1; offset < len;)
    {
        // get nalu size and skip it.
        uint16_t *pNaluSize = (uint16_t *)&rtpPayload[offset];
        uint16_t naluSize = ntohs(*pNaluSize);
        offset += 2;

        // one nalu
        NaluHeader header(rtpPayload[offset]);
        onGotNalu_func_(header.getType(), &rtpPayload[offset], naluSize, userdata_);

        // forward to next (size + nalu)
        offset += naluSize;
    }

    return 0;
}

int RtpH264Unpacker::processFU_A(uint8_t *rtpPayload, int len)
{
    FuHeader fuHeader(rtpPayload[1]);

    if (fuHeader.getType() == FuHeader::FU_start)
    {
        uint8_t fu_identifier = rtpPayload[0];
        uint8_t fu_header     = rtpPayload[1];
        uint8_t nalu_header   = (fu_identifier & 0xe0) | (fu_header & 0x1f);

        naluBuff_.clear();

        // 保存 nalu header
        naluBuff_.push_back(nalu_header);

        // 2字节后数据
        std::copy(&rtpPayload[2], &rtpPayload[len], std::back_inserter(naluBuff_));
    }
    else if (fuHeader.getType() == FuHeader::FU_middle)
    {
        // 2字节后数据
        std::copy(&rtpPayload[2], &rtpPayload[len], std::back_inserter(naluBuff_));
    }
    else if (fuHeader.getType() == FuHeader::FU_end)
    {
        // 2字节后数据
        std::copy(&rtpPayload[2], &rtpPayload[len], std::back_inserter(naluBuff_));

        // 递交 nalu
        NaluHeader header(naluBuff_[0]);
        onGotNalu_func_(header.getType(), naluBuff_.data(), naluBuff_.size(), userdata_);
    }
    else
    {
        // TODO: log error type;
    }

    return 0;
}


int RtpH264Unpacker::enqueueRtpPayload(uint8_t *rtpPayload, int len)
{
    if (rtpPayload == NULL) {
        return 0;
    }
    NaluHeader header(rtpPayload[0]);

    NaluType_e naluType  = header.getType();

    if (naluType < NT_STAP_A)
    { // Single NALU
        onGotNalu_func_(naluType, rtpPayload, len, userdata_);
    }
    else if (naluType == NT_STAP_A)
    { // STAP-A: Single-time aggregation
        processSTAP_A(rtpPayload, len);
    }
    else if (naluType == NT_FU_A)
    { // FU-A: Fragment Unit A
        processFU_A(rtpPayload, len);
    }
    else
    {
        // TODO: log error rtp nalu type
    }

   return 0;
}
