/****************************************************************************************
 * 文 件 名 : yaEty_proto_field_desc_http.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-14
* 编    码 : zhengsw      '2018-05-14
* 修    改 : zhangsx      '2019-03-11
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include <algorithm>

#include "config.h"
#include "epan/dissectors/packet-http.h"
#include "epan/dissectors/packet-tcp.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/to_str.h"
#include "wsutil/str_util.h"

#include <errno.h>

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor_http.h"
#include "yaEty_cap_file_processor.h"
#include "yaEty_content_reassembly_share_mem.h"
#include "post.h"

#define F_D_ITEM_KV(NO, mapType)                                                               \
    F_D_ITEM("K" NO                 , NULL,                       mapType,     "",      NULL), \
    F_D_ITEM("V" NO                 , NULL,                       mapType,     "",      NULL)

#define F_D_ITEM_KV_5(a, b, c, d, e, mapType)   \
        F_D_ITEM_KV(a, mapType),                \
        F_D_ITEM_KV(b, mapType),                \
        F_D_ITEM_KV(c, mapType),                \
        F_D_ITEM_KV(d, mapType),                \
        F_D_ITEM_KV(e, mapType)

#define F_D_ITEM_KV_10(a, b, c, d, e, f, g, h, i, j, mapType)   \
        F_D_ITEM_KV_5(a, b, c, d, e, mapType),                  \
        F_D_ITEM_KV_5(f, g, h, i, j, mapType)

// 对 http content 进行转换
static std::string transform_field_for_http_content(epan_dissect_t *edt, const field_info *pFinfo)
{
    field_info *finfo    = NULL;
    char       *pHexStr  = NULL;
    uint8_t    *pRawData = NULL;
    char       *pValStr  = NULL;
    std::string strRes;

    finfo = get_first_field_info_from_interesting_fields(edt, "http.content");

    if (NULL == finfo)
    {   // 没有 content 字段
        return "";
    }

    std::string strContent;

    // is there any nonprintable charactars?
    pValStr  = finfo->value.value.string;
    pRawData = (uint8_t *)tvb_get_ptr(finfo->ds_tvb, finfo->start, finfo->length);
    for (int i = 0; i < finfo->length; i++)
    {
        switch (pValStr[i])
        {  // 以下这些字符都是不许出现在 post 中的
        case '\r' :
        case '\n' :
        case '\t' :
            pValStr[i] = ' '; continue; // continue，是的，给外层的 for 用的 --by zhengsw
        case '|'  :                     // ===!! TBL_SEP FOR POST CONTENT !!===
            pValStr[i] = '_'; continue;
        }

        if (pValStr[i] == '\0')
        {  // valStr 已经到了尽头，说明 rawdata 与 valstr 不相等
           // rawdata 有 valStr 无法表示的内容
            goto TO_HEX_STR;
        }

        if (!isprint(pRawData[i]))
        {   // 有不可打印的字符，转换为 16进制 str
            goto TO_HEX_STR;
        }
    }

    // 看来全是可见字符了
    strRes = pValStr;
    goto OUT;

TO_HEX_STR:
    pHexStr = ws_tvb_bytes_to_str(NULL, finfo->ds_tvb, finfo->start, finfo->length);
    strContent = pHexStr;
    wmem_free(NULL, pHexStr);
    strRes = strContent;
    goto OUT;

OUT:
    return strRes;
}

static std::string transform_field_for_http_content_transform(epan_dissect_t *edt, const field_info *pFinfo)
{
    int         lSts             = 0;
    int         dataLen          = 0;
    char       *pStrRes          = NULL;
    field_info *finfo            = NULL;
    char       *pRawData         = NULL;

    finfo = get_first_field_info_from_interesting_fields(edt, "http.content");

    if (NULL == finfo)
    {   // 没有 content 字段
        return "";
    }

    // http content
    pRawData = (char *)tvb_get_ptr(finfo->ds_tvb, finfo->start, finfo->length);

    pStrRes = pRawData;
    dataLen = finfo->length;

    // 经过 wireshark 得到的 http.file_data 已经经过了 transfer_encoding 与 content_encoding 的情况，
    // 不再调用 post 解析库处理

#if 0
    static char buffParsed[1024 * 1024] = { 0 };

    memset(buffParsed, 0, sizeof(buffParsed));

    // contentType 与 contentEncoding
    auto pContentType     = get_first_field_value_from_interesting_fields(edt, "http.content_type");
    auto pContentEncoding = get_first_field_value_from_interesting_fields(edt, "http.content_encoding");

    // parse post content
    ST_PostInfo stPostInfo;
    strcpy((char *)stPostInfo.szContentType,     pContentType.c_str());
    strcpy((char *)stPostInfo.szContentEncoding, pContentEncoding.c_str());

    lSts = YV_HttpPostParse_ParseRAW(&stPostInfo, (char *)pRawData, finfo->length, buffParsed, dimen_of(buffParsed));
    // CHECK_NOR_EXIT(lSts < 0, "[PostParsed error!]", "PostParsed error!\n");
    pStrRes = buffParsed;
    dataLen = lSts;

    printf("in [%s]:[%s], parse post:%d->%d\n",
           CapFileProcessor::GetCurrentProcessingPcapFilename().c_str(),
           CapFileProcessor::GetCurrentProcessingFrameNum().c_str(),
           finfo->length,
           dataLen);
#endif

    // 如果是 ascii 或者 utf-8 编码字符串，处理 "|"
    if (g_utf8_validate(pStrRes, dataLen, NULL))
    {
        for (int i=0;
             i < dataLen;
             i++)
        {
            switch (pStrRes[i])
            {  // 以下这些字符都是不许出现在 post 中的
            case '\r' :
            case '\n' :
            case '\t' :
                pStrRes[i] = ' '; continue;   // continue，是的，给外层的 for 用的 --by zhengsw
            case '|'  :                       // ===!! TBL_SEP FOR POST CONTENT !!===
                pStrRes[i] = '_'; continue;
            }
        }

        return std::string(pStrRes, dataLen);
    }

    // 对 非 utf-8 进行转义
    pStrRes = bytes_to_str(NULL, (guint8 *)pStrRes, dataLen);
    defer(wmem_free(NULL, pStrRes););

    return std::string(pStrRes);
}

#if 1
static std::string transform_field_for_http_content_test(epan_dissect_t *edt, const field_info *pFinfo)
{
    std::string p = transform_field_for_http_content_transform(edt, pFinfo);
    printf("old post is : %s\n", transform_field_for_http_content(edt, pFinfo).c_str());
    printf("new post is : %s\n", p.c_str());

    return p;
}
#endif

// 如果不是 http response 消息，http.response 字段根本不会被加入到 interesting_ids hash 中
// 那么，该字段的默认值就会被使用，也就是 "C2S"
static std::string transform_field_for_http_response(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (fvalue_type_ftenum(const_cast<fvalue_t *>(&pFinfo->value)) != FT_BOOLEAN)
    {
         return "error";
    }

    // FT_BOOLEAN 居然要使用 uinteger64, 岂有此理！！
    return fvalue_get_uinteger64(const_cast<fvalue_t *>(&pFinfo->value)) ? "S2C" : "C2S";
}

// transform for H_X_NUMBER
static std::string transform_field_for_x_header_num(epan_dissect_t *edt, const field_info *pFinfo)
{
    char buf[10]           = { 0 };
    field_info *finfo      = NULL;
    GPtrArray  *finfos     = NULL;
    int         field_id   = 0;
    int         finfos_cnt = 0;

    field_id   = proto_registrar_get_id_byname("http.request.x.line");
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);

    if (finfos == NULL)
    {
        field_id   = proto_registrar_get_id_byname("http.response.x.line");
        finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);
    }

    finfos_cnt = g_ptr_array_len(finfos);
    sprintf(buf, "%d", finfos_cnt);
    return buf;
}

// transform for H_A_NUMBER
static std::string transform_field_for_all_header_num(epan_dissect_t *edt, const field_info *pFinfo)
{
    char buf[10]           = { 0 };
    field_info *finfo      = NULL;
    GPtrArray  *finfos     = NULL;
    int         field_id   = 0;
    int         finfos_cnt = 0;

    field_id   = proto_registrar_get_id_byname("http.request.line");
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);

    if (finfos == NULL)
    {
        field_id   = proto_registrar_get_id_byname("http.response.line");
        finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);
    }

    finfos_cnt = g_ptr_array_len(finfos);
    sprintf(buf, "%d", finfos_cnt);

    return buf;
}

static std::string transform_field_for_reassembly_content(epan_dissect_t *edt, const field_info *pFinfo)
{
    field_info   *finfo     = NULL;
    http_picture *http_file = NULL;
    PktInfo       ptf;

    // 从 edt 中收集五元组信息
    finfo = get_first_field_info_from_interesting_fields(edt, "ip.src");
    if (NULL == finfo)
    {   // 看来是 ipv6，暂时不支持
        return "";
    }

    ptf.src_ip = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.dst");
    ptf.dst_ip = fvalue_get_ip_host_addr(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "ip.proto");
    ptf.proto = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "tcp.srcport");
    ptf.src_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    finfo = get_first_field_info_from_interesting_fields(edt, "tcp.dstport");
    ptf.dst_port = fvalue_get_uinteger(const_cast<fvalue_t *>(&finfo->value));

    // hash 获取其还原出的内容文件 path list.
    http_file = (http_picture *)DispGetHashdb(ptf, MEM_TYPE_HTTP_PICTURE);
    if (NULL == http_file)
    {
        return "";
    }

    std::string strJsonFileList;

       // 输出为 json 数组
    Json::Value jArrayPath;
    Json::Value jInsertItem;
    Json::StreamWriterBuilder jBuilder;
    jBuilder["commentStyle"] = "None";
    jBuilder["indentation"]  = "";

    for(int i = 0; i < http_file->pci_number; i++){
        jInsertItem["type"]=http_file->pic_gather[i].flag;
        jInsertItem["path"]=http_file->pic_gather[i].path;
        jArrayPath.append(jInsertItem);
    }

    strJsonFileList = Json::writeString(jBuilder, jArrayPath);
    return strJsonFileList;
}

static std::string transform_field_for_http_uri(epan_dissect_t *edt, const field_info *pFinfo)
{  
     std::string && uri = std::string((const char *)tvb_get_ptr(pFinfo->ds_tvb, pFinfo->start, pFinfo->length),pFinfo->length);

     std::replace(uri.begin(), uri.end(), '|', '_');
     return uri;
}

//
//
static std::string transform_field_for_http_host(epan_dissect_t *edt, const field_info *pFinfo)
{
    http_conv_t	*conv_data;

    field_info * pfinfo = get_first_field_info_from_interesting_fields(edt, "http.host");

    if (NULL != pfinfo)
    {
        return ety_ws_get_node_field_value(const_cast<field_info *>(pfinfo),edt);
    }

    int proto_http = proto_get_id_by_filter_name("http");

    conversation_t* conversation = find_conversation_pinfo(&(edt->pi), 0);

    if (nullptr != conversation)
    {
        conv_data = (http_conv_t *)conversation_get_proto_data(conversation, proto_http);
        if (conv_data != nullptr && conv_data->http_host != nullptr) {
            /* Setup the conversation structure itself */
            return conv_data->http_host;
        }
    }

    return "";
}

static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // RTL tags fields
    F_D_ITEM_RTL_10(),

    // common fields
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    // special fields
    F_D_ITEM("SrcPort"             , "tcp.srcport",              eMT_direct,    "",      NULL),
    F_D_ITEM("DstPort"             , "tcp.dstport",              eMT_direct,    "",      NULL),
    F_D_ITEM("C2S"                 , "http.response",            eMT_transform, "C2S",   transform_field_for_http_response),
    F_D_ITEM("Proto"               , "ip.proto",                 eMT_direct,    "",      NULL),
    F_D_ITEM("TTL"                 , "ip.ttl",                   eMT_direct,    "",      NULL),
    F_D_ITEM("Method"              , "http.request.method",      eMT_direct,    "",      NULL),
    F_D_ITEM("URI"                 , "http.request.uri",         eMT_transform, "",      transform_field_for_http_uri),
    F_D_ITEM("Version"             , "http.request.version",     eMT_direct,    "",      NULL),
    F_D_ITEM("Status"              , "http.response.code",       eMT_direct,    "",      NULL),
    F_D_ITEM("ResponseStatus"      , "http.response.phrase",     eMT_direct,    "",      NULL),
    F_D_ITEM("Cache-Control"       , "http.cache_control",       eMT_direct,    "",      NULL),
    F_D_ITEM("Connection"          , "http.connection",          eMT_direct,    "",      NULL),
    F_D_ITEM("Cookie"              , "http.cookie",              eMT_transform, "",      transform_field_for_may_occur_tbl_sep),
    F_D_ITEM("Cookie2"             , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Date"                , "http.date",                eMT_direct,    "",      NULL),
    F_D_ITEM("Pragma"              , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Trailer"             , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Transfer-Encoding"   , "http.transfer_encoding",   eMT_direct,    "",      NULL),
    F_D_ITEM("Upgrade"             , "http.upgrade",             eMT_direct,    "",      NULL),
    F_D_ITEM("Via"                 , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Warning"             , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Accept"              , "http.accept",              eMT_direct,    "",      NULL),
    F_D_ITEM("Accept-Charset"      ,  NULL,                      eMT_fixed,     "",      NULL),
    F_D_ITEM("Accept-Encoding"     , "http.accept_encoding",     eMT_direct,    "",      NULL),
    F_D_ITEM("Accept-Language"     , "http.accept_language",     eMT_direct,    "",      NULL),
    F_D_ITEM("Authorization"       , "http.authorization",       eMT_direct,    "",      NULL),
    F_D_ITEM("Expect"              , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("From"                , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Host"                , "http.host",                eMT_fromEdt,   "",      transform_field_for_http_host),
    F_D_ITEM("If-Match"            , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("If-Modified-Since"   , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("If-None-Match"       , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("If-Range"            , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("If-Unmodified-Since" , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Max-Forwards"        , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Proxy-Authorization" , "http.proxy_authorization", eMT_transform, "",      transform_field_for_may_occur_tbl_sep),
    F_D_ITEM("Range"               , NULL,                       eMT_fixed,     "",      NULL),
    F_D_ITEM("Referer"             , "http.referer",             eMT_transform, "",      transform_field_for_may_occur_tbl_sep),
    F_D_ITEM("TE"                  , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("User-Agent"          , "http.user_agent",          eMT_transform, "",      transform_field_for_may_occur_bin),
    F_D_ITEM("Accept-Ranges"       , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Age"                 , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("ETag"                , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Location"            , "http.location",            eMT_transform, "",      transform_field_for_may_occur_tbl_sep),
    F_D_ITEM("Proxy-Authenticate"  , "http.proxy_authenticate",  eMT_direct,    "",      NULL),
    F_D_ITEM("Retry-After"         , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Server"              , "http.server",              eMT_direct,    "",      NULL),
    F_D_ITEM("Vary"                , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("WWW-Authenticate"    , "http.www_authenticate",    eMT_direct,    "",      NULL),
    F_D_ITEM("Allow"               , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Content-Encoding"    , "http.content_encoding",    eMT_direct,    "",      NULL),
    F_D_ITEM("Content-Language"    , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Content-Length"      , "http.content_length",      eMT_direct,    "",      NULL),
    F_D_ITEM("Content-Location"    , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Content-MD5"         , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Content-Range"       , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Content-Type"        , "http.content_type",        eMT_direct,    "",      NULL),
    F_D_ITEM("Expires"             , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("Last-Modified"       , "http.last_modified",       eMT_direct,    "",      NULL),
    F_D_ITEM("X-Forwarded-For"     , "http.x_forwarded_for",     eMT_direct,    "",      NULL),
    F_D_ITEM("Set-Cookie"          , "http.set_cookie",          eMT_transform, "",      transform_field_for_may_occur_tbl_sep),
    F_D_ITEM("Set-Cookie2"         , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("DNT"                 , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("X-Powered-By"        , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("P3P"                 , NULL,                       eMT_fixed,     "",      NULL), // ???
    F_D_ITEM("H_A_NUMBER"          , NULL,                       eMT_fromEdt,                  "",      transform_field_for_all_header_num),
    F_D_ITEM("H_X_NUMBER"          , NULL,                       eMT_fromEdt | eMT_lastplain,  "",      transform_field_for_x_header_num),

    // KV pairs KV00 - KV09
    F_D_ITEM_KV_10("00", "01", "02", "03", "04", "05", "06", "07", "08", "09", eMT_fixed),

    // KV pairs KV10 - KV19
    F_D_ITEM_KV_10("10", "11", "12", "13", "14", "15", "16", "17", "18", "19", eMT_fixed),

    // KV pairs KV20 - KV29
    F_D_ITEM_KV_10("20", "21", "22", "23", "24", "25", "26", "27", "28", "29", eMT_fixed),

    // KV pairs KV30 - KV39
    F_D_ITEM_KV_10("30", "31", "32", "33", "34", "35", "36", "37", "38", "39", eMT_fixed),

    // KV pairs KV40 - KV49
    F_D_ITEM_KV_10("40", "41", "42", "43", "44", "45", "46", "47", "48", "49", eMT_fixed),

    // KV pairs KV50 - KV59
    F_D_ITEM_KV("50"  , eMT_fixed),
    F_D_ITEM("K51"    , NULL,           eMT_fixed,      "",      NULL),
    F_D_ITEM("V51"    , "http.content", eMT_fromEdt,    "",     transform_field_for_http_content_transform),
    F_D_ITEM_KV("52", eMT_fixed),
    F_D_ITEM_KV("53", eMT_fixed),
    F_D_ITEM_KV("54", eMT_fixed),
    F_D_ITEM_KV_5("55", "56", "57", "58", "59", eMT_fixed),

    // KV pairs KV60 - KV69
    F_D_ITEM_KV("60", eMT_fixed),
    F_D_ITEM("K61"  , NULL,              eMT_fixed,     "",      NULL),
    F_D_ITEM("V61"  , "",                eMT_fromEdt,    "",     transform_field_for_reassembly_content),
    F_D_ITEM_KV("62", eMT_fixed),
    F_D_ITEM_KV("63", eMT_fixed),
    F_D_ITEM_KV("64", eMT_fixed),
    F_D_ITEM_KV_5("65", "66", "67", "68", "69", eMT_fixed),

    // KV pairs KV70 - KV79
    F_D_ITEM_KV_10("70", "71", "72", "73", "74", "75", "76", "77", "78", "79", eMT_fixed),

    // KV pairs KV80 - KV89
    F_D_ITEM_KV_10("80", "81", "82", "83", "84", "85", "86", "87", "88", "89", eMT_fixed),

    // KV pairs KV90 - KV99
    F_D_ITEM_KV_10("90", "91", "92", "93", "94", "95", "96", "97", "98", "99", eMT_fixed),
};

ProtoFieldExtractorHttp::ProtoFieldExtractorHttp()
    : ProtoFieldExtractor("HTTP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}

bool ProtoFieldExtractorHttp::ShouldExtractThisFrame(epan_dissect_t *edit)
{
#ifdef ONLY_EXTRACT_POST
    std::string strMethod;

    pMethod = get_first_field_value_from_interesting_fields(edt, "http.request.method");
    if (strMethod != "POST")
    {   // 不是 post 不解析
        return false;
    }
#endif  // ONLY_EXTRACT_POST

    return true;
}

bool ProtoFieldExtractorHttp::ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter)
{
    ProtoFieldDesc *pFieldDescK   = NULL;
    ProtoFieldDesc *pFieldDescV   = NULL;
    field_info     *finfo         = NULL;
    GPtrArray      *finfos        = NULL;
    std::string     strWsFieldValue;
    std::string     strValueToWrite;
    std::string     strK;
    std::string     strV;
    const char *pV            = NULL;
    int             field_id      = 0;
    int             finfos_cnt    = 0;
    int             fields_cnt    = 0;

    field_id   = proto_registrar_get_id_byname("http.request.x.line");
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);

    if (finfos == NULL)
    {
        field_id   = proto_registrar_get_id_byname("http.response.x.line");
        finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);
    }

    finfos_cnt = g_ptr_array_len(finfos);
    for (int i = 0, plainFieldsCnt = nFieldIndexToExtract; i < finfos_cnt && i * 2 + plainFieldsCnt < GetFieldsCount(); i++)
    {
        // 获取接下来的一对 kv 的 fieldDesc
        pFieldDescK = this->GetProtoFieldDescByIndex(nFieldIndexToExtract);
        pFieldDescV = this->GetProtoFieldDescByIndex(nFieldIndexToExtract + 1);

        // 获取需要写入的头域值
        finfo = (field_info *)g_ptr_array_index(finfos, i);
        strWsFieldValue = ety_ws_get_node_field_value(finfo, edt);

        // name: value\r\n
        alloced_uptr pszWsFieldValue(strdup(strWsFieldValue.c_str()), free);
        strK = strtok(pszWsFieldValue.get(), ":");
        pWriter->writeRecordField(pFieldDescK->etyFieldName, strK);
        nFieldIndexToExtract++;     // 写一个field就步进一下

        // value
        pV = strtok(NULL, "\r\n");
        if (NULL == pV)
        {
            pV = "";
            goto WRITE;
        }

        pV = pV + 1;                    // 跳过 : 后的空格
        strV = strReplace(pV, '|', '_');  // ===!! TBL_SEP FOR http head value !!===

WRITE:
        pWriter->writeRecordField(pFieldDescV->etyFieldName, strV);
        nFieldIndexToExtract++;     // 写一个field就步进一下
    }

    // 继续输出后续的 kv, 尽管可能它们都为空
    fields_cnt = this->GetFieldsCount();
    for (int i = nFieldIndexToExtract; i < fields_cnt; i++)
    {
        pFieldDescK = this->GetProtoFieldDescByIndex(i);
        strValueToWrite = pFieldDescK->defaultFieldValue;

        // 进行可能地转换
        if (ProtoFieldDesc_GetType(pFieldDescK) == eMT_fromEdt
            && pFieldDescK->funTransfrom != NULL)
        {
            strValueToWrite = pFieldDescK->funTransfrom(edt, NULL);
            strValueToWrite = !strValueToWrite.empty() ? strValueToWrite : pFieldDescK->defaultFieldValue;
        }

#if 1   // kv 对的解析中暂时用不到 eMT_direct
        if (ProtoFieldDesc_GetType(pFieldDescK) == eMT_direct)
        {
            strValueToWrite = get_first_field_value_from_interesting_fields(edt, pFieldDescK->wsFieldName);
        }
#endif

        // 输出
        pWriter->writeRecordField(pFieldDescK->etyFieldName, strValueToWrite);
    }

    nFieldIndexToExtract = 0;
    return true;
}
