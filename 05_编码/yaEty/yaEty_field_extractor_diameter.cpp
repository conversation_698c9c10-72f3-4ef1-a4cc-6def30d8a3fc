/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_diameter.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-10-16
* 编    码 : zhangsx      '2018-10-16
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"


#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_diameter.h"

#include "yaEty_content_reassembly_share_mem.h"

/*****************************************************************
*Function    :transform_field_for_diameter
*Description :diameter 获取两种情形下的字段值
*Input       :epan_dissect_t， field_info，const char*,const char*
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
struct transform_field_for_diameter
{
public:
    transform_field_for_diameter(const char * field_name1, const char * field_name2)
    :_field_name1(field_name1),_field_name2(field_name2)
    {
    }

    std::string operator() (epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info *p_tempfield1 = get_first_field_info_from_interesting_fields(edt, _field_name1);

        field_info *p_tempfield2 = get_first_field_info_from_interesting_fields(edt, _field_name2);

        if (p_tempfield1)
        {
            return ety_ws_get_node_field_value(p_tempfield1, edt);
        }
        if (p_tempfield2)
        {
            return ety_ws_get_node_field_value(p_tempfield2, edt);
        }
        return "";
    }
public:
    const char * _field_name1;
    const char * _field_name2;
};

/*****************************************************************
*Function    :transform_avp_for_diameter
*Description :diameter 获取avp中的字段值
*Input       :epan_dissect_t, field_info, const char *, int
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

struct transform_avp_for_diameter
{
public:
    transform_avp_for_diameter(int num, const char * field_name)
    :_num(num),_field_name(field_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info *pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _num);
        
        if (nullptr == pfinfo)
        {
            return "";
        }
        
        std::string && strRst = ety_ws_get_node_field_value(pfinfo, edt);

        return strRst;
    }
public:  
    int _num;
    const char *_field_name;
};

/*****************************************************************
*Function    :transform_avp_data_for_diameter
*Description :diameter 获取avp中data的字段值
*Input       :epan_dissect_t, field_info, const char *, int
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

struct transform_avp_data_for_diameter:public transform_avp_for_diameter
{
public:
    transform_avp_data_for_diameter(int num, const char * field_name)
    :transform_avp_for_diameter(num,field_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info *pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _num);
        
        if (pfinfo)
        {
            proto_node *node = pfinfo->pnode;
            if (node)
            {
                node = node->next;
            }
            else
            {
                return "";
            }

            if (node)
            {
                pfinfo = node->finfo;
                if (pfinfo)
                {
                    return ety_ws_get_node_field_value(pfinfo, edt);
                }
            }
        }
        return "";
    }
};

/*****************************************************************
*Function    :transform_avp_code_for_diameter
*Description :diameter 获取avp中code的字段值
*Input       :epan_dissect_t, field_info, const char *, int
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

struct transform_avp_code_for_diameter :public transform_avp_for_diameter
{
public:
    transform_avp_code_for_diameter(int num, const char * field_name)
        :transform_avp_for_diameter(num, field_name)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        field_info *pfinfo = get_nth_field_info_from_interesting_fields(edt, _field_name, _num);

        if (pfinfo && pfinfo->rep)
        {
            if (pfinfo->rep->representation)
            {
                std::string && t_str = std::string(pfinfo->rep->representation);
                std::size_t && t_pos = t_str.find_first_of(":");
                return t_str.substr(t_pos + 1);
            }
        }
        return "";
    }
};


/*****************************************************************
*Function    :transform_field_for_C2S
*Description :rdp C2S(若dstport == 3868,则为C2S.若srcport == 3868,则为S2C)
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

static std::string transform_field_for_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{
    field_info * pFinfo_tsrc = get_first_field_info_from_interesting_fields(edt, "tcp.srcport");
    field_info * pFinfo_ssrc = get_first_field_info_from_interesting_fields(edt, "sctp.srcport");
    field_info * pFinfo_tdst = get_first_field_info_from_interesting_fields(edt, "tcp.dstport");
    field_info * pFinfo_sdst = get_first_field_info_from_interesting_fields(edt, "sctp.dstport");

    field_info *pfinfo = pFinfo_tsrc ? pFinfo_tsrc : pFinfo_ssrc;
    if (!pfinfo)
    {
        return "";
    }

    int && mssege_src = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pfinfo->value)));
   
    pfinfo = pFinfo_tsrc ? pFinfo_tdst : pFinfo_sdst;
    if (!pfinfo)
    {
        return "";
    }

    int && mssege_dst = fvalue_get_uinteger(const_cast<fvalue_t *>(&(pfinfo->value)));

    if (mssege_dst == 3868)
    {
        return "C2S";
    }

    if (mssege_src == 3868)
    {
        return "S2C";
    }
    
    return "";
   
}


#define DIAMETER_FIELDS_AVP(NO) \
        F_D_ITEM("AVP_code_"#NO					, ""								, eMT_fromEdt,		"",		transform_avp_code_for_diameter((NO),"diameter.avp.code")),\
        F_D_ITEM("AVP_flags_"#NO                , ""              					, eMT_fromEdt,      "",     transform_avp_for_diameter((NO),"diameter.avp.flags")),\
        F_D_ITEM("AVP_length_"#NO               , ""                				, eMT_fromEdt,      "",     transform_avp_for_diameter((NO),"diameter.avp.len")),\
        F_D_ITEM("Origin_data_"#NO              , ""                                , eMT_fromEdt,      "",     transform_avp_data_for_diameter((NO),"diameter.avp.len"))

#define DIAMETER_GROUP_AVP(a,b,c,d,e)\
        DIAMETER_FIELDS_AVP(a),\
        DIAMETER_FIELDS_AVP(b),\
        DIAMETER_FIELDS_AVP(c),\
        DIAMETER_FIELDS_AVP(d),\
        DIAMETER_FIELDS_AVP(e)

static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    
    F_D_ITEM("SrcPort"						, ""								, eMT_fromEdt,		"",		transform_field_for_diameter("tcp.srcport","sctp.srcport")),
    F_D_ITEM("DstPort"						, ""								, eMT_fromEdt,		"",		transform_field_for_diameter("tcp.dstport","sctp.dstport")),
    F_D_ITEM("C2S"							, ""								, eMT_fromEdt,		"",		transform_field_for_C2S),
    F_D_ITEM("Proto"						, "ip.proto"						, eMT_direct,		"",		NULL),
    F_D_ITEM("TTL"							, "ip.ttl"							, eMT_direct,		"",		NULL),
    
    F_D_ITEM("version"						, "diameter.version"				, eMT_direct,		"",		NULL),
    F_D_ITEM("length"						, "diameter.length"					, eMT_direct,		"",		NULL),
    F_D_ITEM("flag"							, "diameter.flags"					, eMT_direct,		"",		NULL),
    F_D_ITEM("cmdCode"						, "diameter.cmd.code"				, eMT_direct,		"",		NULL),
    F_D_ITEM("appId"						, "diameter.applicationId"			, eMT_direct,		"",		NULL),
    F_D_ITEM("h2h"							, "diameter.hopbyhopid"				, eMT_direct,		"",		NULL),
    F_D_ITEM("e2e"							, "diameter.endtoendid"				, eMT_direct,		"",		NULL),
    DIAMETER_GROUP_AVP(0,1,2,3,4),
    DIAMETER_GROUP_AVP(5,6,7,8,9),
    DIAMETER_GROUP_AVP(10,11,12,13,14)
};

ProtoFieldExtractorDiameter::ProtoFieldExtractorDiameter():ProtoFieldExtractor("DIAMETER", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}



