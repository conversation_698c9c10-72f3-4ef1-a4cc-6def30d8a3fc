/****************************************************************************************
 * 文 件 名 : yaEty_proto_field_keeper.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-14
* 编    码 : zhengsw      '2018-05-14
* 修    改 : zhangsx      '2018-11-14
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _YAETY_PROTO_FIELD_KEEPER_H_
#define _YAETY_PROTO_FIELD_KEEPER_H_

#include <string>
#include <vector>
#include <map>
#include <functional>

#include "yaEty_utils.h"
#include "yaEty_field_extractor_def.h"

// forward declaration
typedef struct epan_dissect epan_dissect_t;
class RecordWriter;

class IProtoFieldExtractor
{
public:
    virtual ~IProtoFieldExtractor(){}

public: // about Extract
    virtual bool CanExtractFromThisProtocol(const std::string &strProtoName) = 0;
    virtual bool ShouldExtractThisFrame(epan_dissect_t *edit) = 0;
    virtual bool ExtractPlainFields(epan_dissect_t *edt, RecordWriter *pWriter) = 0;
    virtual bool ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter) = 0;

public: // about fieldDesc
    virtual int GetFieldsCount() = 0;
    virtual ProtoFieldDesc *GetProtoFieldDescOf(const char *pstrEtyFieldName) = 0;
    virtual ProtoFieldDesc *GetProtoFieldDescByIndex(uint index) = 0;

public:
    virtual void SetRecordWriter(RecordWriter *pWriter) = 0;
    virtual RecordWriter *GetRecordWriter() = 0;
};

class ProtoFieldExtractorBase : public IProtoFieldExtractor
{
public:
    ProtoFieldExtractorBase(const char *strProtoName);
    ProtoFieldExtractorBase(const char *strProtoName, const char *strProtoFieldDescFile);
    ProtoFieldExtractorBase(const char *strProtoName, ProtoFieldDesc *pProtoFieldDescArray, int arrayLen);
    ~ProtoFieldExtractorBase();

public: // field desc
    virtual int GetFieldsCount() override;
    virtual ProtoFieldDesc *GetProtoFieldDescOf(const char *pstrEtyFieldName) override;
    virtual ProtoFieldDesc *GetProtoFieldDescByIndex(uint index) override;

    std::string GetWsFieldNameOf(const char *pstrEtyFieldName);
    std::string GetWsFieldNameByIndex(uint index);
    std::string GetEtyFieldNameByIndex(uint index);
    int AddDebugFieldsDesc();

public: // field extract
    virtual bool CanExtractFromThisProtocol(const std::string &strProtoName) override;
    virtual bool ShouldExtractThisFrame(epan_dissect_t *edit) override;
    virtual bool ExtractPlainFields(epan_dissect_t *edt, RecordWriter *pWriter) override;
    virtual bool ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter) override;

    virtual int  GetDebugFieldCount();

public: // record writer
    virtual void SetRecordWriter(RecordWriter *pWriter) override
    {
        pRecordWriter_ = pWriter;
    }
    virtual RecordWriter *GetRecordWriter() override
    {
        return pRecordWriter_;
    }

public:
    static  IProtoFieldExtractor* FindProtoFieldExtractorOf(const char *protoName);
    static  int                   SetCheckProtoEnableFun(std::function<bool(const char *)> fun);

protected:
    int AddFieldDescItem(ProtoFieldDesc *pItem);
    int AddFieldDescFromArray( ProtoFieldDesc *pFieldMapItemArray, int arrayLen);

private:
    int ParseProtoFieldDescFile();
    int ParseProtoFileDescFile_fake();
    int addProtoFieldDesc(const char *etyName, const char *wsName);


protected:
    std::string                             strProtoName_;
    std::string                             strProtoFieldDescFile_;
    std::vector<ProtoFieldDesc *>           vecProtoFieldDesc_;
    std::map<std::string, ProtoFieldDesc *> mapEtyName2FieldDesc_;
    RecordWriter                           *pRecordWriter_ = NULL;

protected:
    int                                     nFieldIndexToExtract = 0;

protected:
    static std::function<bool(const char *)> cs_funCheckProtoEnable;
};

template <typename T>
class ProtoFieldExtractorMap
{
public:
    using name2ExtratorMap_t = std::map<std::string, T *>;
    using funForeach_t       = std::function<void (const std::string &strProtoName, T *protoFieldExt)>;

public:
    static T *GetProtoFieldExtractorOf(const char *pszProtoName)
        {
            std::string strProtoName(pszProtoName);

            for (const auto &item : s_protoFieldMM)
            {
                if (item.second->CanExtractFromThisProtocol(strProtoName))
                {
                    return item.second;
                }
            }

            if (strProtoName.find('<') != std::string::npos && strProtoName.find('>') != std::string::npos && strProtoName.find_last_of('>') > strProtoName.find_first_of('<'))
            {
                std::string subProtoName = strProtoName.substr(strProtoName.find_first_of('<')+1, strProtoName.find_last_of('>') - strProtoName.find_first_of('<')-1);

                for (const auto &item : s_protoFieldMM)
                {
                    if (item.second->CanExtractFromThisProtocol(subProtoName))
                    {
                        return item.second;
                    }
                }
            }            

            return NULL;
        }

    static void ForeachFieldExtractor(funForeach_t fun)
        {
            for (const auto &item : s_protoFieldMM)
            {
                fun(item.first, item.second);
            }
        }

protected:
    static int RegisterProtoFieldExtractor(const std::string &strProtoName, T *protoFieldMapper)
        {
            s_protoFieldMM.emplace(strProtoName, protoFieldMapper);
            return 0;
        }

private:
    static name2ExtratorMap_t s_protoFieldMM;
};

class ProtoFieldExtractor : public ProtoFieldExtractorMap<ProtoFieldExtractor>
                          , public ProtoFieldExtractorBase
{
public:
    static int                  RegisterFieldExtractors();

    static int                  SetupDebugFieldDesc();

public:
    ProtoFieldExtractor(const char *strProtoName,
                        ProtoFieldDesc *pProtoFieldDescArray,
                        int arrayLen, ProtoFieldExtractor *pInnter = NULL)
        : ProtoFieldExtractorBase(strProtoName, pProtoFieldDescArray, arrayLen)
        {
            if (cs_funCheckProtoEnable(strProtoName))
            {
                RegisterProtoFieldExtractor(strProtoName_, this);
            }
        }
};

class TunnelProtoFieldExtractor : public ProtoFieldExtractorMap<TunnelProtoFieldExtractor>
                                , public ProtoFieldExtractorBase
{
public:
    TunnelProtoFieldExtractor(const char *strProtoName,
                              ProtoFieldDesc *pProtoFieldDescArray,
                              int arrayLen)
        : ProtoFieldExtractorBase(strProtoName, pProtoFieldDescArray, arrayLen)
        {
            if (cs_funCheckProtoEnable(strProtoName))
            {
                RegisterProtoFieldExtractor(strProtoName_, this);
            }
        }
};

class SubFormatFieldExtractor : public ProtoFieldExtractorMap<SubFormatFieldExtractor>
                                , public ProtoFieldExtractorBase
{
public:
    SubFormatFieldExtractor(const char *strProtoName,
                              ProtoFieldDesc *pProtoFieldDescArray,
                              int arrayLen)
        : ProtoFieldExtractorBase(strProtoName, pProtoFieldDescArray, arrayLen)
        {
            if (cs_funCheckProtoEnable(strProtoName))
            {
                    RegisterProtoFieldExtractor(strProtoName_, this);
            }
        }
};

class MatchPrefixProtoFieldExtractor : public ProtoFieldExtractor
{
public:
    using ProtoFieldExtractor::ProtoFieldExtractor;

public:
    virtual bool CanExtractFromThisProtocol(const std::string &strProtoName) override
    {
        return strBeginwith(strProtoName, strProtoName_);
    }
};

struct choose_for_two_type_field                                                        //add by zhangsx 2018-11-14
{
public:
    const char * _field_name1;
    const char * _field_name2;
    choose_for_two_type_field(const char * field_name1, const char * field_name2);
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo);
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo,int n);
};

struct string_format_limit_length
{
public:
    uint32_t _raw_str_len_lmt;
    uint32_t _out_str_len_lmt;
    string_format_limit_length(uint32_t raw_str_len_lmt, uint32_t out_str_len_lmt);
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo);
};

enum trailerType
{
    noTrailer = -1,
    rtTrailer,
    hwTrailer,
    jscTrailer   
};

struct trailerFieldSelector
{
private:
    static std::vector<std::map<std::string,std::string>> trailer_fields;
public:
    trailerFieldSelector(const char *field_name);
    const char * operator()(void);
public:
    const char * _field_name;
    
    static trailerType use_trailer_type;
};

struct transform_trailer_choose_field
{
public:
    transform_trailer_choose_field(void);
    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo);
};

::std::string transform_mac_for_lineno(epan_dissect_t *edt, const field_info *pFinfo);

::std::string transform_mac_for_lineno_le(epan_dissect_t *edt, const field_info *pFinfo);

::std::string transform_null_lineno(epan_dissect_t *edt, const field_info *pFinfo);

#endif /* _YAETY_PROTO_FIELD_KEEPER_H_ */
