/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_radius.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-14
* 编    码 : licl         '2018-06-21
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <iconv.h>

#include <algorithm>

#include "config.h"
#include "epan/epan_dissect.h"
#include "epan/ftypes/ftypes-int.h"         // for field_info
#include "epan/ipv4.h"
#include "epan/strutil.h"

#include "json/json.h"
#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_l2tp.h"

/*****************************************************************
*Function    :transform_field_for_InnerDstPort
*Description :L2TP Get InnerDstPort
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_field_for_InnerDstPort(epan_dissect_t *edt, const field_info *pFinfo)
{
    int             field_id      = 0;
    int             finfos_cnt    = 0;
    GPtrArray      *finfos        = NULL;
    gchar          *pWsFieldValue = NULL;
    field_info     *finfo         = NULL;

    field_id   = proto_registrar_get_id_byname("tcp.dstport");      /* 如果有TCP， 直接拿第一层，即可*/
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);
    if(NULL != finfos && g_ptr_array_len(finfos) >= 1)
    {
        finfo = (field_info *)g_ptr_array_index(finfos, 0);
        return ety_ws_get_node_field_value(finfo, edt);
    }

    field_id   = proto_registrar_get_id_byname("udp.dstport");  /* 因为L2TP 本身就是UDP协议， 所以拿第2层 */
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);
    if(NULL != finfos && g_ptr_array_len(finfos) >= 2)
    {
        finfo = (field_info *)g_ptr_array_index(finfos, 1);
        return ety_ws_get_node_field_value(finfo, edt);
    }

    /* 被注释的代码， 留作扩展使用 */
    // finfos_cnt = g_ptr_array_len(finfos);
    // for (int i = 1; i < finfos_cnt && i<= 2; i++)
    // {
    //     finfo = (field_info *)g_ptr_array_index(finfos, i);
    //     return pWsFieldValue = get_node_field_value(finfo, edt);
    // }
    return "";
}

/*****************************************************************
*Function    :transform_field_for_InnerSrcPort
*Description :L2TP Get InnerSrcPort
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_field_for_InnerSrcPort(epan_dissect_t *edt, const field_info *pFinfo)
{
    int             field_id      = 0;
    int             finfos_cnt    = 0;
    GPtrArray      *finfos        = NULL;
    field_info     *finfo         = NULL;

    field_id   = proto_registrar_get_id_byname("tcp.srcport");      /* 如果有TCP， 直接拿第一层，即可*/
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);
    if(NULL != finfos && g_ptr_array_len(finfos) >= 1)
    {
        finfo = (field_info *)g_ptr_array_index(finfos, 0);
        return ety_ws_get_node_field_value(finfo, edt);
    }

    field_id   = proto_registrar_get_id_byname("udp.srcport");  /* 因为L2TP 本身就是UDP协议， 所以拿第2层 */
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);
    if(NULL != finfos && g_ptr_array_len(finfos) >= 2)
    {
        finfo = (field_info *)g_ptr_array_index(finfos, 1);
        return ety_ws_get_node_field_value(finfo, edt);
    }

    /* 被注释的代码， 留作扩展使用 */
    // finfos_cnt = g_ptr_array_len(finfos);
    // for (int i = 1; i < finfos_cnt && i<= 2; i++)
    // {
    //     finfo = (field_info *)g_ptr_array_index(finfos, i);
    //     return pWsFieldValue = get_node_field_value(finfo, edt);
    // }
    return "";
}

/*****************************************************************
*Function    :transform_field_for_InnerIPProto
*Description :L2TP Get InnerIPProto
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_field_for_InnerIPProto(epan_dissect_t *edt, const field_info *pFinfo)
{
    int             field_id      = 0;
    int             finfos_cnt    = 0;
    GPtrArray      *finfos        = NULL;
    field_info     *finfo         = NULL;

    field_id   = proto_registrar_get_id_byname("ip.proto");
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);
    finfos_cnt = g_ptr_array_len(finfos);
    for (int i = 1; i < finfos_cnt && i< 2; i++)          /* 取出 L2TP 内部的 ip.proto */
    {
        finfo = (field_info *)g_ptr_array_index(finfos, i);
        return ety_ws_get_node_field_value(finfo, edt);
    }
    return "";
}


/*****************************************************************
*Function    :transform_field_for_InnerIPDst
*Description :L2TP Get InnerIPDst
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_field_for_InnerIPDst(epan_dissect_t *edt, const field_info *pFinfo)
{
    int             field_id      = 0;
    int             finfos_cnt    = 0;
    GPtrArray      *finfos        = NULL;
    field_info     *finfo         = NULL;

    field_id   = proto_registrar_get_id_byname("ip.dst");
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);
    finfos_cnt = g_ptr_array_len(finfos);
    for (int i = 1; i < finfos_cnt && i< 2; i++)          /* 取出 L2TP 内部的 ip.dst*/
    {
        finfo = (field_info *)g_ptr_array_index(finfos, i);
        return ety_ws_get_node_field_value(finfo, edt);                                 // update by zhangsx 2019 1.31
    }
    return "";
}


/*****************************************************************
*Function    :transform_field_for_InnerIPSrc
*Description :L2TP Get InnerIPSrc
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_field_for_InnerIPSrc(epan_dissect_t *edt, const field_info *pFinfo)
{
    int             field_id      = 0;
    int             finfos_cnt    = 0;
    GPtrArray      *finfos        = NULL;
    field_info     *finfo         = NULL;

    field_id   = proto_registrar_get_id_byname("ip.src");
    finfos     = proto_get_finfo_ptr_array(edt->tree, field_id);
    finfos_cnt = g_ptr_array_len(finfos);
    for (int i = 1; i < finfos_cnt && i< 2; i++)          /* 取出 L2TP 内部的 ip.src*/
    {
        finfo = (field_info *)g_ptr_array_index(finfos, i);
        return ety_ws_get_node_field_value(finfo, edt);
    }
    return "";
}


/*****************************************************************
*Function    :transform_field_for_C2S
*Description :L2TP C2S
*Input       :epan_dissect_t， field_info
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/
static std::string transform_field_for_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{
    return "C2S";
}

static ProtoFieldDesc ms_protoFieldDescArray[] =
{
    // 10 个 RTL 标签头
    F_D_ITEM_RTL_10(),

    // 27 个通用字段头
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    /* SIP SDP 字段 */
    //F_D_ITEM("Frame_number"                       , "frame.number"                               , eMT_direct,    "",      NULL), /* DEBUG */
    F_D_ITEM("SrcPort"                            , "udp.srcport"                                , eMT_direct,    "",      NULL),
    F_D_ITEM("DstPort"                            , "udp.dstport"                                , eMT_direct,    "",      NULL),
    F_D_ITEM("C2S"                                , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("Proto"                              , "ip.proto"                                   , eMT_direct,    "",      NULL),
    F_D_ITEM("TTL"                                , "ip.ttl"                                     , eMT_direct,    "",      NULL),
    F_D_ITEM("tunnel_id"                          , "l2tp.tunnel"                                , eMT_direct,    "",      NULL),
    F_D_ITEM("session_id"                         , "l2tp.session"                               , eMT_direct,    "",      NULL),
    F_D_ITEM("l2tp_type"                          , "l2tp.type"                                  , eMT_direct,    "",      NULL),
    F_D_ITEM("message_type"                       , "l2tp.avp.message_type"                      , eMT_direct,    "",      NULL),
    F_D_ITEM("Protocol_version"                   , "l2tp.version"                               , eMT_direct,    "",      NULL),
    F_D_ITEM("Protocol_reversion"                 , "l2tp.avp.protocol_revision"                 , eMT_direct,    "",      NULL),
    F_D_ITEM("Minimum_bps"                        , "l2tp.broadband.minimum_dr_up"               , eMT_direct,    "",      NULL),
    F_D_ITEM("Maximum_bps"                        , "l2tp.broadband.maximum_dr_up"               , eMT_direct,    "",      NULL),
    F_D_ITEM("Firmwave_revision"                  , "l2tp.avp.firmware_revision"                 , eMT_direct,    "",      NULL),
    F_D_ITEM("Host_name"                          , "l2tp.avp.host_name"                         , eMT_direct,    "",      NULL),
    F_D_ITEM("Vendor_name"                        , "l2tp.avp.vendor_name"                       , eMT_direct,    "",      NULL),
    F_D_ITEM("Assigned_tunnel_id"                 , "l2tp.avp.assigned_tunnel_id"                , eMT_direct,    "",      NULL),
    F_D_ITEM("Assigned_session_id"                , "l2tp.avp.assigned_session_id"               , eMT_direct,    "",      NULL),
    F_D_ITEM("Vendor_Specific_AVP"                , "l2tp.vendor_specific_avp_data"              , eMT_direct,    "",      NULL),
    F_D_ITEM("Bear_type"                          , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("Bear_capabilities"                  , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("Farming_type"                       , "l2tp.avp.async_framing_type"                , eMT_direct,    "",      NULL),
    F_D_ITEM("Farming_capabilities"               , NULL                                         , eMT_fixed,     "",      NULL),
    F_D_ITEM("Call_serial_number"                 , "l2tp.avp.call_serial_number"                , eMT_direct,    "",      NULL),
    F_D_ITEM("Called_number"                      , "l2tp.avp.called_number"                     , eMT_direct,    "",      NULL),
    F_D_ITEM("Calling_number"                     , "l2tp.avp.calling_number"                    , eMT_direct,    "",      NULL),
    F_D_ITEM("Rx_connect_speed"                   , "l2tp.avp.rx_connect_speed"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("Tx_connect_speed"                   , "l2tp.avp.csu.current_tx_speed"              , eMT_direct,    "",      NULL),
    F_D_ITEM("Physical_channel"                   , "l2tp.avp.physical_channel"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("Private_group_id"                   , "l2tp.avp.private_group_id"                  , eMT_direct,    "",      NULL),
    F_D_ITEM("Router_id"                          , "l2tp.avp.router_id"                         , eMT_direct,    "",      NULL),
    F_D_ITEM("Assigned_cookie"                    , "l2tp.avp.assigned_cookie"                   , eMT_direct,    "",      NULL),
    F_D_ITEM("Chap_challenge"                     , "l2tp.avp.chap_challenge"                    , eMT_direct,    "",      NULL),
    F_D_ITEM("Chap_challenge_response"            , "l2tp.avp.chap_challenge_response"           , eMT_direct,    "",      NULL),
    F_D_ITEM("Proxy_authen_type"                  , "l2tp.avp.proxy_authen_type"                 , eMT_direct,    "",      NULL),
    F_D_ITEM("Proxy_authen_name"                  , "l2tp.avp.proxy_authen_name"                 , eMT_direct,    "",      NULL),
    F_D_ITEM("Proxy_authen_challenge"             , "l2tp.avp.proxy_authen_challenge"            , eMT_direct,    "",      NULL),
    F_D_ITEM("Proxy_authen_id"                    , "l2tp.avp.proxy_authen_id"                   , eMT_direct,    "",      NULL),
    F_D_ITEM("Proxy_authen_response"              , "l2tp.avp.proxy_authen_response"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Initial_received_lcp_confreq"       , "l2tp.avp.initial_received_lcp_confreq"      , eMT_direct,    "",      NULL),
    F_D_ITEM("Last_sent_lcp_confreq"              , "l2tp.avp.last_sent_lcp_confreq"             , eMT_direct,    "",      NULL),
    F_D_ITEM("Last_received_lcp_confreq"          , "l2tp.avp.last_received_lcp_confreq"         , eMT_direct,    "",      NULL),
    F_D_ITEM("InnerIPSrc"                         , ""                                           , eMT_fromEdt,   "",     transform_field_for_InnerIPSrc),
    F_D_ITEM("InnerIPDst"                         , ""                                           , eMT_fromEdt,   "",     transform_field_for_InnerIPDst),
    F_D_ITEM("InnerIPProto"                       , ""                                           , eMT_fromEdt,   "",     transform_field_for_InnerIPProto),
    F_D_ITEM("InnerSrcPort"                       , ""                                           , eMT_fromEdt,   "",     transform_field_for_InnerSrcPort),
    F_D_ITEM("InnerDstPort"                       , ""                                           , eMT_fromEdt,   "",     transform_field_for_InnerDstPort),
};

ProtoFieldExtractorL2tp::ProtoFieldExtractorL2tp():TunnelProtoFieldExtractor("L2TP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray)) // 从proto_register_protocol 可查看注册的是什么
{
}
