#pragma once

#include "yaEty_field_extractor_voip.h"
#include "yaEty_field_extractor_rtp_stream.h"
#include "yaEty_stream_keeper_def.h"
#include "yaEty_target_info.h"


#include <stdlib.h>
#include <string.h>

#include <epan/tap.h>
#include <epan/addr_resolv.h>
#ifndef YAETY_PACKET_RTP_H
#define YAETY_PACKET_RTP_H 1
#include <epan/dissectors/packet-rtp.h>
#endif
#include <epan/dissectors/packet-h225.h>
#include <ui/voip_calls.h>
#include <ui/simple_dialog.h>
#include <ui/rtp_media.h>
#include <ui/rtp_stream.h>

#include <tuple>
#include <queue>
#include <vector>
#include <map>
#include <set>
// typedef std::tuple<uint32_t, uint16_t, uint16_t> voip_key_t;
// struct compare_voip_key
// {
//     bool operator()(const voip_key_t k1 , const voip_key_t k2){
//       if(std::get<1>(k1) != std::get<1>(k2)  && std::get<1>(k1) != std::get<2>(k2)){
//           return std::get<1>(k1) < std::get<1>(k2);
//       }
//       if(std::get<2>(k1) != std::get<2>(k2)  && std::get<2>(k1) != std::get<1>(k2)){
//           return std::get<2>(k1) < std::get<2>(k2);
//       }
//       if(std::get<0>(k1) != std::get<0>(k2)){
//           return std::get<0>(k1) < std::get<0>(k2);
//       }

//       return false;
//     }
// };


struct RtpPortInfo;
struct ya_rtp_loss_info
{
  ya_rtp_loss_info(){
    lastDequeuedPktNum_ = 0;
    lastDequeuedPktEndTimestamp_ = 0;
    streamPacketLossNum_ = 0;
    streamPacketLossBytes_ = 0;
    streamPacketFirstNum = 0;
    channels_ = 1;
    rtp_sample_rate_ = 8000;
    packet_total_num_ = 0;        //收到包总数
    packet_normal_num_ = 0;       //正常包数
    packet_repeat_num_ = 0;       //重复包数
  }
    void operator=(const ya_rtp_loss_info info ) {
    lastDequeuedPktNum_ = info.lastDequeuedPktNum_;
    lastDequeuedPktEndTimestamp_ = info.lastDequeuedPktEndTimestamp_;
    streamPacketLossNum_ = info.streamPacketLossNum_;
    streamPacketLossBytes_ = info.streamPacketLossBytes_;
    streamPacketFirstNum = info.streamPacketFirstNum;
    channels_ = info.channels_;
    rtp_sample_rate_ = info.rtp_sample_rate_;
    packet_total_num_  = info.packet_total_num_;        //收到包总数
    packet_normal_num_ = info.packet_normal_num_;       //正常包数
    packet_repeat_num_ = info.packet_repeat_num_;       //重复包数
    }
    uint32_t    lastDequeuedPktNum_;          // 上一次出队的包序号
    uint32_t    lastDequeuedPktEndTimestamp_;  // 上一次出队的包时间戳
    uint64_t    streamPacketLossNum_;           // 丢包数统计
    uint64_t    streamPacketLossBytes_;         // 插入字节数/丢包字节数
    uint64_t    streamPacketFirstNum;      // 第一次出队的包序号
    uint32_t    channels_;
    uint32_t    rtp_sample_rate_;
    uint32_t    packet_total_num_;        //收到包总数
    uint32_t    packet_normal_num_;       //正常包数
    uint32_t    packet_repeat_num_;       //重复包数
};

struct ya_voip_info
{
    uint32_t    ssrc;

    std::string filepath;
    uint32_t    filesize;
    std::string lineno;
    time_t file_creat_time;
    time_t start_fd_time;
    ya_rtp_loss_info    loss_info;
    ya_flow_tunnel_info_t trailer_info;

};

typedef struct ya_voip_info_keeper {
    voip_calls_info_t * voip_info;
    uint16_t            trailer_type;
    ya_trailer_t *      trailer;
    ya_flow_tunnel_info_t * flow_tunnel_info;

public:
    ya_voip_info_keeper()
    {
        voip_info = nullptr;
        trailer = nullptr;
        flow_tunnel_info = nullptr;
        trailer_type = 0;
    }

    int reset()
    {
        map_voip_info_.clear();

        return 0;
    }

public:
    int setVoipRtpStreamStartTime(const rtp_stream_info_t *info,time_t start_fd_time){
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return -1;
        }

        finditer->second.start_fd_time = start_fd_time;
        return 0;
    }
    int setFlowTrailerInfo(const rtp_stream_info_t *info,const char* trailer_info_str){
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return -1;
        }

        finditer->second.trailer_info.tunnel_str = trailer_info_str;

        return 0;
    }
    int setVoipLossInfo(const rtp_stream_info_t *info, const ya_rtp_loss_info LossInfo){
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return -1;
        }

        finditer->second.loss_info = LossInfo;
        return 0;
    }
    //更新文件名称
    int setVoipFileInfo(const rtp_stream_info_t *info,time_t creat_time)
    {
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return -1;
        }

        finditer->second.file_creat_time = creat_time;
        return 0;
    }

    //更新文件名称
    int setVoipFileInfo(const rtp_stream_info_t *info, const char *filename, uint32_t filesize)
    {
        voip_key_t v_key = fromRtpStreamInfo(info);
        ya_voip_info voip_info{info->ssrc, filename, filesize, ""};

        auto search = map_voip_info_.find(v_key);
        if(search != map_voip_info_.end())
        {
            search->second.filepath.clear();
            search->second.filepath += filename;
        }else{
            map_voip_info_.emplace(v_key, voip_info);
        }
        return 0;
    }

    int setVoipFileSize(const rtp_stream_info_t *info, uint32_t filesize)
    {
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return -1;
        }

        finditer->second.filesize = filesize;
        return 0;
    }

    int setVoipLineno(const rtp_stream_info_t *info, const char *lineno)
    {
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return -1;
        }

        finditer->second.lineno = lineno;

        return 0;
    }

    std::string getFlowTrailerInfo(const rtp_stream_info_t *info){
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return "";
        }

        return finditer->second.trailer_info.tunnel_str;
    }

    const char* getVoipFilenameOf(const rtp_stream_info_t *info)
    {
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return "";
        }

        return finditer->second.filepath.c_str();
    }

    time_t getVoipRtpStreamStartTime(const rtp_stream_info_t *info){
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return -1;
            //-1 时为未找到
        }

        return finditer->second.start_fd_time;
    }
    uint32_t getVoipFilesizeOf(const rtp_stream_info_t *info)
    {
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return 0;
        }

        return finditer->second.filesize;
    }

    const char *getVoipLineno(const rtp_stream_info_t *info)
    {
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return "";
        }

        return finditer->second.lineno.c_str();
    }
    const ya_rtp_loss_info getVoipLossInfo(const rtp_stream_info_t *info){
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
          ya_rtp_loss_info info;
            return info;
        }

        return finditer->second.loss_info;
    }

    const time_t getVoipFileCreatTime(const rtp_stream_info_t *info){
        voip_key_t v_key = fromRtpStreamInfo(info);
        auto finditer = map_voip_info_.find(v_key);
        if (finditer == map_voip_info_.end())
        {
            return time(NULL);
        }

        return finditer->second.file_creat_time;
    }

private:
    std::map<voip_key_t, ya_voip_info> map_voip_info_;

} ya_voip_info_keeper;

struct _ya_rtp_stream_info_t {
    rtp_stream_info_t * rtp_info;
    std::string line_no;

    std::string file_name;
    uint32_t file_size;
    time_t file_creat_time;
    uint16_t stream_status;
    uint8_t voip_status;
    /* trailer info */
    uint16_t trailer_type;
    ya_trailer_t * trailer;
    ya_flow_tunnel_info_t * flow_tunnel_info;
    /* from sip */ 
    std::string payload_name;
    std::string sample_rate;
    std::string from;
    std::string to;
    /*from q931*/
    std::string Q931CallingPartyNumber;
    std::string Q931CalledPartyNumber;

    /* rtp丢包统计信息 */
    ya_rtp_loss_info    loss_info_;

public:
    _ya_rtp_stream_info_t()
    {
        rtp_info = nullptr;
        trailer = nullptr;
        flow_tunnel_info = nullptr;
        file_size = 0;
        trailer_type = 0;
        stream_status = 0;
    }
    void setPktLossInfo(const ya_rtp_loss_info  info ) {
    loss_info_ = info;
    }
    void setFileCreatTime(time_t creat_time){
      file_creat_time = creat_time;
    }
};

typedef struct _ya_rtp_stream_info_t ya_rtp_stream_info_t;


struct RtpPortInfo;
class RtpStream;
class VoipStreamKeeper
{
public:
    VoipStreamKeeper();
    ~VoipStreamKeeper();
    void writeVoipTblDone();

public: // rtp stream management
    static void onResetPackets(void *arg);

    static int onTapPacket(void *arg, packet_info *pinfo, epan_dissect_t *edt, const void *arg2);

    static void onDrawPackets(void *arg);

    static int onTapSipPackets(void *arg, packet_info *pinfo, epan_dissect_t *edt, const void *arg2);
    static int onTapQ931Packets(void *arg, packet_info *pinfo, epan_dissect_t *edt, const void *arg2);

    static VoipStreamKeeper * getInstance();

public:
    int onTapVoipPacket(voip_calls_tapinfo_t *tapinfo, packet_info * pinfo, epan_dissect_t * edt);

public: // thread
    static void voip_tbl_init(void);
    static void set_voip_tbl_name(const rtp_stream_info_t *p, const char * file_name);
    static void set_voip_pkt_loss_count(const rtp_stream_info_t *p, const ya_rtp_loss_info LossInfo);
    static void set_voip_file_creat_time_name(const rtp_stream_info_t *p, time_t creat_time);
    static void set_voip_rtp_stream_start_time(const rtp_stream_info_t *p, time_t start_time);

    static void set_voip_line_no(const rtp_stream_info_t *p, epan_dissect_t *edt);
    static void set_voip_rtp_size(const rtp_stream_info_t *p, uint32_t file_size);
    static void set_voip_tbl_info(voip_calls_info_t * voip_info);
    static void insert_rtpstream_file_to_vector(std::string filename);

    static ya_voip_info_keeper * get_voip_info_keeper();
    std::string get_voip_rtp_file_list(rtp_stream_info_t *);
    void setRtpTrailerToSip(std::string trailer_str){
      if ((voip_flow_tunnel_info_.tunnel_str.size() == 0 ||
              voip_flow_tunnel_info_.tunnel_str.length() ==(std::size_t) GCHFieldsExtractor::get_fields_cnt() ) &&
              trailer_str.length() != (std::size_t)GCHFieldsExtractor::get_fields_cnt()) {
          voip_flow_tunnel_info_.tunnel_str = trailer_str;
      }
    }
public:
    void writeSipFileTbl(voip_calls_tapinfo_t * p_tapinfo);
    void writeRtpStreamFileTbl(rtp_stream_info_t * rsi,RtpStream * rtpStream);
    void insertRtpStreamFileToVector(std::string filename);
    void doVoipClean(void);

public:
    static void lineNoParserInit(void);
    static funTransfrom_t lineNoParser;

public:
    void cleanRtpStreamSsrc(void);

    void cleanRtpStreamInfo(void);
    void cleanMapSipInfoInfo(RtpPortInfo rtp_tuple);
public:
    void setGCHFieldExtrPointer(GCHFieldsExtractor *);

private:
    void writeVoipTbl(voip_calls_tapinfo_t * p_tapinfo);

    int updateList(GQueue *callsinfos);

    void registerTapListener();
    
    void removeTapListener();

    void dissectTrailer(epan_dissect_t *edt);
    void dissectH323(epan_dissect_t *edt);
    void dissectSip(epan_dissect_t *edt);
    void dissectFlowTunnel(epan_dissect_t *edt);
    void dissectSipMeidaDescription(epan_dissect_t *edt);
    void dissectSipSDP(epan_dissect_t *edt);
    void matchSipConvNum();

    rtp_stream_info_t * getRsiFromTapinfo(rtp_stream_info_t *rsi);
    void RtpStreamRenameAllTmpFile();

private:// rtp stream
    static ya_voip_info_keeper voip_info_keeper_;

    // k : call_num -> v : voip_calls_info
    // 每一条voip通话对应一个voip_calls_info(也就是 "voip通话" 按钮渲染出的列表)
    std::map<uint32_t, voip_calls_info_t>       voip_map;

    std::set<uint32_t>                          rtp_streams_set;

    voip_calls_tapinfo_t                        tapinfo_;

    ya_rtp_stream_info_t                        sip_info_;
    std::map<RtpPortInfo, ya_rtp_stream_info_t>    map_sip_info_;

    ya_trailer_t                                voip_trailer_;

    ya_flow_tunnel_info_t                       voip_flow_tunnel_info_;

private:
    ProtoFieldExtractorVoip                     m_voipExt;
    TblRecordWriter                             m_voipWriter;
    
    ProtoFieldExtractorRtpStream                m_rtpStreamExt;
    TblRecordWriter                             m_rtpStreamWriter;

    GCHFieldsExtractor                          *m_gch_extr;

    //这里保存了所有的以`tmp_`开头的文件名 大数据搬运程序不处理这个文件 当所有voip写完结束的时候 再对所有的`tmp_`开头的文件进行rename处理
    std::vector<std::string>                    rtp_filename_v_;

private:
    // 通过ssrc可以得到一条流信息，但是一条流可能会写多个file_name
    // 在有服务器存在的转发传输中 ssrc并不能代表一条真实的流量(同样的ssrc会在服务器上转发 导致出现两个五元组不同的 相同ssrc的流)
    // k:call_num -> v:rtp_stream_info
    std::map<uint16_t, std::set<rtp_stream_info_t *> > voip_rsi_list_;

    //k:rsi -> v:文件名
    std::map< voip_key_t, std::vector<std::string>,compare_voip_key> rtp_stream_filelist_;

public:
    int rtp_stream_file_num_;
    int voip_file_num_;

};
