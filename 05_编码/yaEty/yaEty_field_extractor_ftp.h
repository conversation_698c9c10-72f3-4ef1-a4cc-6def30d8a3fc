/****************************************************************************************
 * 文 件 名 : yaEty_proto_desc_ftp.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : liugh        '2018-05-29'
* 编    码 : liugh        '2018-05-29'
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "yaEty_field_extractor.h"

class ProtoFieldExtractorFtp : public ProtoFieldExtractor
{
public:
    ProtoFieldExtractorFtp();

public:
    virtual bool ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter);
};

