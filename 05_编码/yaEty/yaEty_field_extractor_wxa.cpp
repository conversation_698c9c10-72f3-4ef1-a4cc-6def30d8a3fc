/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_wxa.cpp
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-12-03
* 编    码 : zhangsx      '2018-12-03
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include"config.h"
#include"epan/epan_dissect.h"
#include"yaEty_utils.h"
#include"yaEty_ws_utils.h"
#include"yaEty_rec_writer_tbl.h"
#include"yaEty_field_extractor.h"
#include"yaEty_field_extractor_wxa.h"
#include"yaEty_content_reassembly_share_mem.h"

static std::string transform_for_field_length(epan_dissect_t *edt, const field_info *pFinfo)
{
    return (NULL != pFinfo) ? std::to_string(pFinfo->length) : "";
}

static std::string transform_for_wxa_C2S(epan_dissect_t *edt, const field_info *pFinfo)
{
    field_info *pfinfo = get_first_field_info_from_interesting_fields(edt, "udp.srcport");

    if (nullptr == pfinfo)
    {
        return "";
    }
    int srcport = fvalue_get_uinteger((fvalue_t *)(&pfinfo->value));

    pfinfo = get_first_field_info_from_interesting_fields(edt, "udp.dstport");

    if (nullptr == pfinfo)
    {
        return "";
    }
    int dstport = fvalue_get_uinteger((fvalue_t *)(&pfinfo->value));

    return srcport > dstport ? "C2S" : "S2C";
}

static std::string transform_for_field_hex(epan_dissect_t *edt, const field_info *pFinfo)
{
    if (nullptr == pFinfo)
    {
        return "";
    }
    else
    {
        char * pHexStr = ws_tvb_bytes_to_str(NULL, pFinfo->ds_tvb, pFinfo->start, pFinfo->length);//这里从完整的合并包后产生的树上获取数据,wireshark无对应字段值
        std::string strRst = pHexStr;
        wmem_free(NULL, pHexStr);

        return strRst;
    }
}

static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),

    F_D_ITEM("SrcPort"                  , "udp.srcport"                     , eMT_direct,       "",     NULL),
    F_D_ITEM("DstPort"                  , "udp.dstport"                     , eMT_direct,       "",     NULL),
    F_D_ITEM("C2S"                      , ""                                , eMT_transform,    "",     transform_for_wxa_C2S),
    F_D_ITEM("Proto"                    , "ip.proto"                        , eMT_direct,       "",     NULL),
    F_D_ITEM("TTL"                      , "ip.ttl"                          , eMT_direct,       "",     NULL),

    F_D_ITEM("Type"                     , ""                                , eMT_fromEdt,      "",     choose_for_two_type_field("wxa.type","wxa.group_flag")),
    F_D_ITEM("Seq"                      , "wxa.seq"                         , eMT_direct,       "",     NULL),
    F_D_ITEM("Session_Id"               , "wxa.session_id"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("Calling_Type"             , "wxa.call"                        , eMT_direct,       "",     NULL),
    F_D_ITEM("Length"                   , "wxa.unknown"                     , eMT_transform,    "",     transform_for_field_length),
    F_D_ITEM("Data"                     , "wxa.unknown"                     , eMT_transform,    "",     transform_for_field_hex),
};

ProtoFieldExtractorWxa::ProtoFieldExtractorWxa():ProtoFieldExtractor("WXA", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{
}
