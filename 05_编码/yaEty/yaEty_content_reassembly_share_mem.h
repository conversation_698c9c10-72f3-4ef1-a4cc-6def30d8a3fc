/****************************************************************************************
 * 文 件 名 : yaEty_content_reassembly_share_mem.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : liugh      '2018-05-21
* 编    码 : liugh      '2018-05-21
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _MEM_SHARE_H_
#define _MEM_SHARE_H_


/* share mem id*/
#define COMM_PICTURE_MEM_ID     30001
#define COMM_MAIL_MEM_ID        30002
#define COMM_FTP_MEM_ID         30003
#define COMM_VOIP_MEM_ID        30004

#define MAX_FORK_NUMBER         999



#define HASH_SIZE               9999

#define PICTURE_GATHER_NUMBER   50
#define FTP_GATHER_NUMBER       20
#define MAIL_GATHER_NUMBER      20
#define VOIP_GATHER_NUMBER      20



#define COMMON_PATH_SIZE        256
#define PICTURE_PATH_SIZE       256
#define MAIL_DATA_PATH_SIZE     256
#define FTP_DATA_PATH_SIZE      256
#define VOIP_DATA_PATH_SIZE     256


#define POLY 0x01101 // CRC20生成多项式x^20+x^12+x^8+1即:01101 CRC32:04C11DB7L


/* type */
#define MEM_TYPE_HTTP_PICTURE   1
#define MEM_TYPE_EMAIL_DATA     2
#define MEM_TYPE_FTP_DATA       3
#define MEM_TYPE_VOIP_DATA      4



//typedef unsigned char bool;

/****************** http pciture vedio ***************/
typedef struct _picture_path{
	char path[PICTURE_PATH_SIZE];
	unsigned char flag;
}picture_path;

typedef struct _http_picture{
	picture_path   pic_gather[PICTURE_GATHER_NUMBER];
	unsigned int   src_ip;
	unsigned int   dst_ip;
	unsigned short src_port;
	unsigned short dst_port;
	unsigned short pci_number;
	unsigned int   next;
	unsigned char  flag;
}http_picture;



/****************** emails ***************/
typedef struct _mail_path{
	char path[MAIL_DATA_PATH_SIZE];
	unsigned char flag;
}mail_path;

typedef struct _mail_data{
	mail_path      mail_gather[MAIL_GATHER_NUMBER];
	unsigned int   src_ip;
	unsigned int   dst_ip;
	unsigned short src_port;
	unsigned short dst_port;
	unsigned short mail_number;
	unsigned int   next;
	unsigned char  flag;
}mail_data;




/****************** ftp files *******************/
typedef struct _ftp_path{
	char path[FTP_DATA_PATH_SIZE];
	unsigned char flag;
}ftp_path;


typedef struct _ftp_data{
	ftp_path   ftp_gather[FTP_GATHER_NUMBER];
	unsigned int   src_ip;
	unsigned int   dst_ip;
	unsigned short src_port;
	unsigned short dst_port;
	unsigned short file_number;
	unsigned int   next;
	unsigned char  flag;
}ftp_data;



/****************** voip file *******************/
typedef struct _voip_path{
	char path[VOIP_DATA_PATH_SIZE];
	unsigned char flag;
}voip_path;


typedef struct _voip_data{
	ftp_path   voip_gather[VOIP_GATHER_NUMBER];
	unsigned int   src_ip;
	unsigned int   dst_ip;
	unsigned short src_port;
	unsigned short dst_port;
	unsigned short voip_number;
	unsigned int   next;
	unsigned char  flag;
}voip_data;


/******************* data info *******************/
typedef struct _data_info{
	unsigned int   src_ip;
	unsigned short src_port;
	unsigned int   dst_ip;
	unsigned short dst_port;
	char path[COMMON_PATH_SIZE];
	unsigned char  type;
}data_info;

typedef struct _PktInfo{
	unsigned int   src_ip;
	unsigned short src_port;
	unsigned int   dst_ip;
	unsigned short dst_port;
	unsigned short proto;
}PktInfo;


typedef struct  _share_shmid{
	unsigned int   http_shmid;
	unsigned int   mail_shmid;
	unsigned int   ftp_shmid;
	unsigned int   voip_shmid;
}share_shmid;



#define HTTP_PICTURE_LIST_SIZE  sizeof(http_picture)
#define MAIL_DATA_LIST_SIZE     sizeof(mail_data)
#define FTP_DATA_LIST_SIZE      sizeof(ftp_data)
#define VOIP_DATA_LIST_SIZE     sizeof(voip_data)

#include "c_lang_linkage_start.h"


#if 0
bool DispAttachShareMem(void);
bool DispInitShareMem(void);
#else
bool DispAttachShareMem(unsigned int pid);
bool DispInitShareMem(unsigned int pid);
#endif
unsigned int DispGetFreeMemDB(data_info info, unsigned int hashval, int type, int *path_flag);

bool DispSetHashdb(data_info info, int type);
bool DispUdpSetHashdb(data_info info, int type);

void *DispGetHashdb(PktInfo ptf, int type);

int  DispDelShareMem(unsigned int pid);

/* hash calculate */
int Hash( PktInfo ptf );
unsigned int CRC20Key(unsigned char* data, int len);
void CreateCrcTable(void);
unsigned int GetSumPoly(unsigned char data);


#include "c_lang_linkage_end.h"



#endif
