/****************************************************************************************
 * 文 件 名 : yaEty_field_extractor_sip.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-17
* 编    码 : licl         '2018-06-23
* 修    改 :.
*************.***************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _YAETY_FIELD_EXTRACTOR_SIP_H_
#define _YAETY_FIELD_EXTRACTOR_SIP_H_

#include "yaEty_field_extractor.h"

class ProtoFieldExtractorSip : public ProtoFieldExtractor
{
public:
    ProtoFieldExtractorSip();

public:
    //virtual bool ShouldExtractThisFrame(epan_dissect_t *edit);
    //virtual bool ExtractSpecialFields(epan_dissect_t *edt, RecordWriter *pWriter);
};

#endif /* _YAETY_FIELD_EXTRACTOR_SIP_H_ */
