/****************************************************************************************
* 文 件 名 : yaEty_field_extractor_eigrp.h
* 项目名称 : YVBD1207001B
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 无
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2018-10-22
* 编    码 : zhangsx      '2018-10-22
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include "config.h"
#include "epan/epan_dissect.h"

#include "yaEty_utils.h"
#include "yaEty_ws_utils.h"
#include "yaEty_rec_writer_tbl.h"
#include "yaEty_field_extractor.h"
#include "yaEty_field_extractor_eigrp.h"
#include "yaEty_content_reassembly_share_mem.h"


/*****************************************************************
*Function    :choose_for_eigrp_field
*Description :从两个字段中选出不为空的一项以供填入结果
*Input       :epan_dissect_t, field_info, const char*, const char*
*Output      :none
*Return      :0结尾的 可见字符串
*Others      :none
*****************************************************************/

struct choose_for_eigrp_field
{
public:
    choose_for_eigrp_field(const char * field_name1, const char * field_name2)
        :_field_name1(field_name1),_field_name2(field_name2)
    {
    }

    std::string operator()(epan_dissect_t *edt, const field_info *pFinfo)
    {
        std::string rst = "";
        field_info * p_finfo = nullptr;
        GPtrArray *finfos_array = nullptr;

        GPtrArray *finfos_array1 = nullptr;
        GPtrArray *finfos_array2 = nullptr;

        int field_id1 = proto_registrar_get_id_byname(_field_name1);
        
        int field_id2 = proto_registrar_get_id_byname(_field_name2);

        if (field_id1 > -1)
        {
            finfos_array1 = proto_get_finfo_ptr_array(edt->tree, field_id1);
        }
        
        if (field_id2 > -1)
        {
            finfos_array2 = proto_get_finfo_ptr_array(edt->tree, field_id2);
        }

        if (nullptr != finfos_array1)
        {
            finfos_array = finfos_array1;
        }
        if (nullptr != finfos_array2)
        {
            finfos_array = finfos_array2;
        }
        
        if (nullptr == finfos_array)
        {
            return "";
        }

        int field_size = g_ptr_array_len(finfos_array);
        
        if (field_size == 0)
        {
            return "";
        }

        for (int i = 0; i < field_size; ++i)
        {
            p_finfo = (field_info *)g_ptr_array_index(finfos_array, i);
            rst += ety_ws_get_node_field_value(p_finfo, edt);
            if (field_size > 1 && i < field_size-1 )
            {
                rst += ",";
            }
        }

        return rst;
    }
public:
    const char * _field_name1;
    const char * _field_name2;
};

static ProtoFieldDesc ms_protoFieldDescArray[] = 
{ 
    F_D_ITEM_RTL_10(),
    F_D_ITEM_COMMON_DevNo_TO_DstCarrier_27(),
    F_D_ITEM("SrcPort"                  , ""                                , eMT_fixed,        "",     NULL),
    F_D_ITEM("DstPort"                  , ""                                , eMT_fixed,        "",     NULL),
    F_D_ITEM("C2S"                      , ""                                , eMT_fixed,        "",     NULL),
    F_D_ITEM("Proto"                    , "ip.proto"                        , eMT_direct,       "",     NULL),
    F_D_ITEM("TTL"                      , "ip.ttl"                          , eMT_direct,       "",     NULL),
    F_D_ITEM("Version"                  , "eigrp.version"                   , eMT_direct,       "",     NULL),
    F_D_ITEM("Opcode"                   , "eigrp.opcode"                    , eMT_direct,       "",     NULL),
    F_D_ITEM("AS"                       , "eigrp.as"                        , eMT_direct,       "",     NULL),
    F_D_ITEM("Type"                     , "eigrp.tlv_type"                  , eMT_direct,       "",     NULL),
    F_D_ITEM("NextHop"                  , ""                                , eMT_fromEdt,      "",     choose_for_eigrp_field("eigrp.ipv4.nexthop","eigrp.ipv6.nexthop")),
    F_D_ITEM("OrigRoutID"               , "eigrp.extdata.origrid"           , eMT_direct,       "",     NULL),
    F_D_ITEM("OrigAS"                   , "eigrp.extdata.as"                , eMT_direct,       "",     NULL),
    F_D_ITEM("ExtProtoID"               , "eigrp.extdata.proto"             , eMT_direct,       "",     NULL),
    F_D_ITEM("Delay"                    , "eigrp.old_metric.delay"          , eMT_direct,       "",     NULL),
    F_D_ITEM("BW"                       , "eigrp.old_metric.bw"             , eMT_direct,       "",     NULL),
    F_D_ITEM("MTU"                      , "eigrp.metric.mtu"                , eMT_direct,       "",     NULL),
    F_D_ITEM("HopCount"                 , "eigrp.old_metric.hopcount"       , eMT_direct,       "",     NULL),
    F_D_ITEM("Reliability"              , "eigrp.old_metric.rel"            , eMT_direct,       "",     NULL),
    F_D_ITEM("Load"                     , "eigrp.old_metric.load"           , eMT_direct,       "",     NULL),
    F_D_ITEM("PrefixLength"             , ""                                , eMT_fromEdt,      "",     choose_for_eigrp_field("eigrp.ipv4.prefixlen","eigrp.ipv6.prefixlen")),//""
    F_D_ITEM("Destination"              , ""                                , eMT_fromEdt,      "",     choose_for_eigrp_field("eigrp.ipv4.destination","eigrp.ipv6.destination")),//""
};
ProtoFieldExtractorEigrp::ProtoFieldExtractorEigrp():ProtoFieldExtractor("EIGRP", ms_protoFieldDescArray, dimen_of(ms_protoFieldDescArray))
{

}