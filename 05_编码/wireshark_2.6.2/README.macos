This file tries to help building Wireshark for macOS (The Operating
System Formerly Known As Mac OS X And Then OS X) (Wireshark does not
work on the classic Mac OS).

You must have the developer tools (called Xcode) installed.  For
versions of macOS up to and including Snow Leopard, Xcode 3 should be
available on the install DVD; Xcode 4 is available for download from
developer.apple.com and, for Lion and later releases, from the Mac App
Store.  See

	http://guide.macports.org/chunked/installing.xcode.html

for details.  For Xcode 4, you will need to install the command-line
tools; select Preferences from the Xcode menu, select Downloads in the
Preferences window, and install Command Line Tools.

You must also have GLib and, if you want to build Wireshark as well as
TShark, you must have also Qt installed.  You can download precompiled
Qt packages and source code from

	https://www.qt.io/download-open-source/

or use the tools/macos-setup.sh script described below.

You should have CMake installed; you can download binary distributions
for macOS from

	https://cmake.org/download/

The Wireshark source includes an autoconf configure script; however,
that script cannot find recent versions of Qt for macOS, and will not
try to find macOS frameworks that Wireshark can use to improve the user
experience, so we don't recomment using the configure script.

The tools/macos-setup.sh script can be used to download, patch as
necessary, build as necessary, and install those libraries and the
libraries on which they depend, along with tools such as CMake; it will,
by default, also install other libraries that can be used by Wireshark
and TShark.  The versions of libraries and tools to download are
specified by variables set early in the script; you can comment out the
settings of optional libraries if you don't want them downloaded and
installed.  Before running the tools/macos-setup.sh script, and before
attempting to build Wireshark, make sure your PKG_CONFIG_PATH
environment variable's setting includes both /usr/X11/lib/pkgconfig and
/usr/local/lib/pkgconfig.

The tools/macos-setup.sh script must be run from the top-level source
directory.

If you wish to build the legacy (GTK+) UI you must have X11 and the X11
developer headers and libraries installed, as well as the Pango, ATK,
and GTK+ libraries; otherwise, you will not be able to build or install
GTK+.  The X11 and X11 SDK that come with macOS releases for releases
from Panther to Lion can be used to build and run Wireshark.  Mountain
Lion and later do not include X11; you should install X11 from
elsewhere, such as

	http://xquartz.macosforge.org/

After you have installed those libraries:

 1. If you have installed Qt into some non-standard place, as is
	distinctly possible with the build included with
	macos-setup.sh, you must inform cmake by either including its
	"bin" directory as part of the PATH environment variable or
	setting CMAKE_PREFIX_PATH to the directory above Qt's "lib"
	directory.  For Qt 5.8 installed into one's home directory,
	for instance:

	% export CMAKE_PREFIX_PATH=~/Qt5.8.0/5.8/clang_64

	This step is unnecessary if you've used a recent version of
	Homebrew, as the CMake build scripts will find Qt.

 2. Make a directory in which Wireshark is to be built, separate
	from the top-level source directory for Wireshark - it can be a
	subdirectory of that top-level source directory;

 3. cd to that directory, and run CMake, with an argument that is a
	path to the top-level source directory;

 4. When CMake finishes, run make to build Wireshark.

For example, to build Wireshark in a subdirectory of the top-level
source directory, named "build", do, from the top-level source
directory;

	mkdir build
	cd build
	cmake ..
	make

If you upgrade the major release of macOS on which you are building
Wireshark, we advise that, before you do any builds after the upgrade,
you remove the build directory and all its subdiretories, and repeat the
above process, re-running CMake and rebuilding from scratch.

On Snow Leopard (10.6) and later releases, if you are building on a
machine with a 64-bit processor (with the exception of the early Intel
Core Duo and Intel Core Solo machines, all Apple machines with Intel
processors have 64-bit processors), the C/C++/Objective-C compiler will
build 64-bit by default.

This means that you will, by default, get a 64-bit version of Wireshark.

One consequence of this is that, if you built and installed any required
or optional libraries for Wireshark on an earlier release of macOS, those
are probably 32-bit versions of the libraries, and you will need to
un-install them and rebuild them on your current version of macOS, to get
64-bit versions.

Some required and optional libraries require special attention if you
install them by building from source code on Snow Leopard and later
releases; the tools/macos-setup.sh script will handle that for you.

GLib - the GLib configuration script determines whether the system's
libiconv is GNU iconv or not by checking whether it has libiconv_open(),
and the compile will fail if that test doesn't correctly indicate
whether libiconv is GNU iconv.  In macOS, libiconv is GNU iconv, but the
64-bit version doesn't have libiconv_open(); a workaround for this is to
replace all occurrences of "libiconv_open" with "iconv_open" in the
configure script before running the script.  The tools/macos-setup.sh
setup script will patch GLib to work around this.

GTK+ - GTK+ 2.24.10, at least, doesn't build on Mountain Lion with the
CUPS printing backend - either the CUPS API changed incompatibly or the
backend was depending on non-API implementation details.  The
tools/macos-setup.sh setup script will, on Mountain Lion and later,
configure GTK+ with the CUPS printing backend disabled.

libgcrypt - the libgcrypt configuration script attempts to determine
which flavor of assembler-language routines to use based on the platform
type determined by standard autoconf code.  That code uses uname to
determine the processor type; however, in macOS, uname always reports
"i386" as the processor type on Intel machines, even Intel machines with
64-bit processors, so it will attempt to assemble the 32-bit x86
assembler-language routines, which will fail.  The workaround for this
is to run the configure script with the --disable-asm argument, so that
the assembler-language routines are not used.  The tools/macos-setup.sh
will configure libgcrypt with that option.

PortAudio - when compiling on macOS, the configure script for the
pa_stable_v19_20071207 version of PortAudio will cause certain
platform-dependent build environment #defines to be set in the Makefile
rules, and to cause a universal build to be done; those #defines will be
incorrect for all but one of the architectures for which the build is
being done, and that will cause a compile-time error on Snow Leopard.
Newer versions don't have this problem, but still fail to build on Lion
if a universal build is attempted.  The tools/macos-setup.sh script
downloads a newer version, and also suppresses the universal build.

If you want to build Wireshark installer packages on a system that
doesn't include Xcode 3.x or earlier, you will need to install some
additional tools.  From the Xcode menu, select the Open Developer Tool
menu, and then select More Developer Tools... from that menu.  That will
open up a page on the Apple Developer Connection Web site; you may need
a developer account to download the additional tools.  Download the
Auxiliary Tools for Xcode package; when the dmg opens, drag all its
contents to the Contents/Applications subdirectory of the Xcode.app
directory (normally /Applications/Xcode.app/Contents/Applications); then
copy .../Contents/Applications/PackageMaker.app/Contents/MacOS/PackageMaker
to /usr/bin/packagemaker (the PackageMaker app, when run from the
command line rather than as a double-clicked app, is the packagemaker
command).
