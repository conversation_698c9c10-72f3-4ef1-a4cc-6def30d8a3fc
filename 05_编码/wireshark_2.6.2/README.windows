Installing Wireshark on Windows
=============================
To install Wireshark, simply download the appropriate installer program from

https://www.wireshark.org/download.html

and start it. Just keep the default settings and start Wireshark after the 
installation finished (e.g. using the start menu entry).

For detailed descriptions on how to install and use Wireshark and the 
related command line tools, see the Wireshark User's Guide at: 

https://www.wireshark.org/docs/


Compiling the Wireshark distribution from source
================================================
In case you want to develop Wireshark code yourself, you can find a 
comprehensive guide how to do this in the Developer's Guide,  which
you can find (and much more info) at: 

https://wiki.wireshark.org/Development
