/* -*-mode: flex-*- */

%top {
/* Include this before everything else, for various large-file definitions */
#include "config.h"
}

/*
 * We don't use input, so don't generate code for it.
 */
%option noinput

/*
 * We don't use unput, so don't generate code for it.
 */
%option nounput

/*
 * We don't read interactively from the terminal.
 */
%option never-interactive

/*
 * We want to stop processing when we get to the end of the input.
 */
%option noyywrap

/*
 * Prefix scanner routines with "text2pcap_" rather than "yy" to avoid a
 * "redefined macro" warning with flex 2.6.3.
 */
%option prefix="text2pcap_"

%{

/********************************************************************************
 *
 * text2pcap-scanner.l
 *
 * Utility to convert an ASCII hexdump into a libpcap-format capture file
 *
 * (c) Copyright 2001 <PERSON><PERSON> <<EMAIL>>
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 *
 *******************************************************************************/

#include <stdio.h>
#include <stdlib.h>

#include "text2pcap.h"

/*
 * Disable diagnostics in the code generated by Flex.
 */
DIAG_OFF_FLEX

/*
 * Flex (v 2.5.35) uses this symbol to "exclude" unistd.h
 */
#ifdef _WIN32
#define YY_NO_UNISTD_H
#endif

%}

hexdigit [0-9A-Fa-f]
directive ^#TEXT2PCAP.*\r?\n
comment ^[\t ]*#.*\r?\n
byte [0-9A-Fa-f][0-9A-Fa-f][ \t]
byte_eol [0-9A-Fa-f][0-9A-Fa-f]\r?\n
offset [0-9A-Fa-f]+[: \t]
offset_eol [0-9A-Fa-f]+\r?\n
text [^ \n\t]+
mailfwd >
eol \r?\n\r?

%%

{byte}            { if (parse_token(T_BYTE, yytext) != EXIT_SUCCESS) return EXIT_FAILURE; }
{byte_eol}        { if (parse_token(T_BYTE, yytext) != EXIT_SUCCESS) return EXIT_FAILURE;
	if (parse_token(T_EOL, NULL) != EXIT_SUCCESS) return EXIT_FAILURE; }
{offset}          { if (parse_token(T_OFFSET, yytext) != EXIT_SUCCESS) return EXIT_FAILURE; }
{offset_eol}      { if (parse_token(T_OFFSET, yytext) != EXIT_SUCCESS) return EXIT_FAILURE;
	if (parse_token(T_EOL, NULL) != EXIT_SUCCESS) return EXIT_FAILURE; }
{mailfwd}{offset} { if (parse_token(T_OFFSET, yytext+1) != EXIT_SUCCESS) return EXIT_FAILURE; }
{eol}             { if (parse_token(T_EOL, NULL) != EXIT_SUCCESS) return EXIT_FAILURE; }
[ \t]             ; /* ignore whitespace */
{directive}       { if (parse_token(T_DIRECTIVE, yytext) != EXIT_SUCCESS) return EXIT_FAILURE;
	if (parse_token(T_EOL, NULL) != EXIT_SUCCESS) return EXIT_FAILURE; }
{comment}         { if (parse_token(T_EOL, NULL) != EXIT_SUCCESS) return EXIT_FAILURE; }
{text}            { if (parse_token(T_TEXT, yytext) != EXIT_SUCCESS) return EXIT_FAILURE; }

%%

/*
 * Turn diagnostics back on, so we check the code that we've written.
 */
DIAG_ON_FLEX

int
text2pcap_scan(void)
{
    int ret;

    ret = text2pcap_lex();
    text2pcap_lex_destroy();
    return ret;
}
