commit 7bc91035d0
Author: <PERSON> <<EMAIL>>
Date:   Tue Jul 17 13:43:45 2018 -0700

    Release 2.6.2.
    
    Change-Id: I580580a40162ddc543d2b335688a76730d3646e4

commit 1ef4bbed55
Author: <PERSON> <<EMAIL>>
Date:   Tue Jul 17 00:20:01 2018 +0200

    Qt: update to QCustomPlot 1.3.2
    
    QCustomPlot 1.3.1 includes a fix for a crash ("Fixed potential crash in
    QCPGraph::pointDistance if getScatterPlotData returns empty list").
    Full changelog: http://www.qcustomplot.com/release/1.3.2/changelog.txt
    
    The new files are based on QCustomPlot-source.tar.gz (1.3.2) with
    <PERSON>' typo and license header changes from v1.99.2rc0-479-gc411029a3c
    ("Update to QCustomPlot 1.3.0") applied. All successive Wireshark
    patches up to v2.5.0rc0-854-g6f28f1a59e ("Fix MacOS build") were
    applied. The Retina patches ("Fix QCustmPlot retina problems." and "QCP:
    Fix retina label calculations.") gave conflicts due contextual changes
    which I resolved by renaming newCachedLabel to cachedLabel (following
    the change in upstream commit baaad24706187f5be0a68011c780b51c4de11558).
    
    Bug: 14971
    Change-Id: I5012cb5a867891b466cbf1898fe4c28cbd11ba20
    Reviewed-on: https://code.wireshark.org/review/28732
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Alexis La Goutte <<EMAIL>>
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 8563b94d567c7ab8dfbaef7e72623e413a573ab9)
    Reviewed-on: https://code.wireshark.org/review/28739
    Petri-Dish: Anders Broman <<EMAIL>>
    Reviewed-by: Peter Wu <<EMAIL>>

commit 76a728ea14
Author: Peter Wu <<EMAIL>>
Date:   Sat Apr 28 16:06:57 2018 +0200

    checkAPIs.pl: fix false positive with C++ method names
    
    Fix false positives due to method names that are considered deprecated:
    
        int CaptureFileDialog::open(...
        first_elapsed = QString().sprintf(
        int open(QString &file_name, unsigned int &type);
    
    Change-Id: Ib3c255a9f17b2cb44cd441e5277a97db63afaa72
    Reviewed-on: https://code.wireshark.org/review/27189
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit 4694c7acf0772d780499f1d4d83fe52e0d9a7fe7)
    Reviewed-on: https://code.wireshark.org/review/28752

commit 3a0da5fbb2
Author: Gerald Combs <<EMAIL>>
Date:   Mon Jul 16 09:06:39 2018 -0700

    Qt: Create the main display filter combo sooner.
    
    Construct MainWindow::df_combo_box_ in our initializer list, otherwise
    anything that triggers a resizeEvent early on might result in an NPE.
    
    Wait until everything is in place before calling setMinimumWidth.
    
    Conflicts:
            ui/qt/main_window.cpp
    
    Bug: 14979
    Change-Id: I78b349f9c3ea53d8b1399a77169bbbd0a4a69191
    Reviewed-on: https://code.wireshark.org/review/28728
    Petri-Dish: Gerald Combs <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Gerald Combs <<EMAIL>>
    (cherry picked from commit 2cfa3013bc2890dd1ea8468366344c3d3878f3a0)
    Reviewed-on: https://code.wireshark.org/review/28746

commit d266d15f4f
Author: Gerald Combs <<EMAIL>>
Date:   Mon Jul 16 17:45:50 2018 -0700

    Updates for 2.6.2.
    
    Update the release notes. Copy over gen-bugnote from master.
    
    Change-Id: I90ddfed6db9c60ac570e4318cd98c83a62a99264
    Reviewed-on: https://code.wireshark.org/review/28743
    Reviewed-by: Gerald Combs <<EMAIL>>

commit 370a5d3eb6
Author: Peter Wu <<EMAIL>>
Date:   Mon Jul 16 13:17:27 2018 +0200

    wmem: make wmem_strsplit behave more like g_strsplit
    
    In the past "g_strsplit" users were converted to use "wmem_strsplit" to
    avoid memory leaks when dissection fails. The semantics were slightly
    different though. When the DNS dissector tried to split the string "."
    using delimiter ".", it would previously (unexpectedly) receive an empty
    vector (and crash). Now it will receive a vector with one element.
    
    Additionally, suggest that users of wmem_strsplit with a NULL allocator
    use g_strsplit instead, otherwise it will leak the elements.
    
    Bug: 14980
    Change-Id: I408dfdb0ffa9e24ccdba69c8ee095abea72f6feb
    Reviewed-on: https://code.wireshark.org/review/28724
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 3d1e3023d2300fe558c6b03066b2b6497f5b02b0)
    Reviewed-on: https://code.wireshark.org/review/28736
    Petri-Dish: Anders Broman <<EMAIL>>

commit 878e79b9cf
Author: Pascal Quantin <<EMAIL>>
Date:   Mon Jul 16 16:06:48 2018 +0200

    Lua: add include/lua-5.1 and include/lua-5.2 to CMake path suffixes
    
    Bug: 14983
    Change-Id: I8be206ace7f61c62e2e42bc53841067ec39e3a0a
    Reviewed-on: https://code.wireshark.org/review/28726
    Petri-Dish: Pascal Quantin <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: João Valverde <<EMAIL>>
    Reviewed-by: Peter Wu <<EMAIL>>
    Reviewed-by: Pascal Quantin <<EMAIL>>
    (cherry picked from commit 40d180ec6d3b1154bc314d14eac90822feed7e33)
    Reviewed-on: https://code.wireshark.org/review/28729

commit 4592f8c4e4
Author: Guy Harris <<EMAIL>>
Date:   Sun Jul 15 13:50:27 2018 -0700

    Update the comment giving I-D URLs.
    
    Change-Id: Id2ec0092369083b7bd6951c7121ac885c067ac1f
    Reviewed-on: https://code.wireshark.org/review/28715
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 16860320e1751ca04c5416a7995a229838e95f1b)
    Reviewed-on: https://code.wireshark.org/review/28719

commit c40e6897d3
Author: Yuri Chislov <<EMAIL>>
Date:   Tue Mar 27 09:24:36 2018 +0000

    WCCP: use proto_tree_add_ipv4_format() if ipv4 used
    
    Bug: 14573
    Change-Id: I429477940d8e7a827a3f35630be64a7b06869d59
    Reviewed-on: https://code.wireshark.org/review/26661
    Reviewed-by: Peter Wu <<EMAIL>>
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 8f2b1fdc38288a62bbaf0f2d00f2e523de3668db)
    Reviewed-on: https://code.wireshark.org/review/28716
    Reviewed-by: Guy Harris <<EMAIL>>

commit d055190514
Author: Erika Szelleova <<EMAIL>>
Date:   Mon Jun 18 19:47:17 2018 +0200

    OpcUa: fix in function getExtensionObjectType
    
    The function getExtensionObjectType was not reading the corrects bytes,
    this sometimes ended in reading outside the buffer and that way
    exception was raised even for correct packets.
    
    Bug: 14465
    Change-Id: I5d7d9ca5f43f0afbc93f40487a78709c52f0658a
    Reviewed-on: https://code.wireshark.org/review/28328
    Petri-Dish: Alexis La Goutte <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 6b13c05da11e7735b4a50995c23ecf309d55a62d)
    Reviewed-on: https://code.wireshark.org/review/28714
    Reviewed-by: Guy Harris <<EMAIL>>

commit b7a09de319
Author: Erika Szelleova <<EMAIL>>
Date:   Fri Jun 15 12:26:43 2018 +0200

    Fix in CRC checker for FPP
    
     fix in the computation of CRC + little change in the dissector - now it
     shows the fields SMD and FRAG_COUNT in the tree correctly
    
    Bug: 14610
    Change-Id: I74982ff836f02803843f6b44a0955a4b20f48e43
    Reviewed-on: https://code.wireshark.org/review/28286
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 2bac238efb817523ce616b020479ce6015386516)
    Reviewed-on: https://code.wireshark.org/review/28713
    Reviewed-by: Guy Harris <<EMAIL>>

commit 1530dbf6dc
Author: Gerald Combs <<EMAIL>>
Date:   Sun Jul 15 08:27:07 2018 +0000

    [Automatic update for 2018-07-15]
    
    Update manuf, services enterprise numbers, translations, and other items.
    
    Change-Id: I84d3d13c6366749b84c6138ce06c6c1c54160146
    Reviewed-on: https://code.wireshark.org/review/28705
    Reviewed-by: Gerald Combs <<EMAIL>>

commit 9012ff05fc
Author: Guy Harris <<EMAIL>>
Date:   Sat Jul 14 14:43:55 2018 -0700

    The maximum offset in an IP option dissector is the length of the option.
    
    It's *not* the sum of the length of the option and the length of the
    option header.
    
    Change-Id: I0b5ab0e35ca33dc02a0bc2501e0f0f531ec3f376
    Reviewed-on: https://code.wireshark.org/review/28701
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 1834cda2ee945c09f3306e001af9d9bac97d6422)
    Reviewed-on: https://code.wireshark.org/review/28702

commit 3bdaf7bd54
Author: Peter Wu <<EMAIL>>
Date:   Fri Jul 6 22:01:22 2018 +0200

    Qt: try to sort numeric columns (such as ports) as number
    
    Ensure that columns with numbers are sorted according to their numeric
    value instead of their alphabetical order.
    
    Bug: 11460
    Change-Id: I6ccfb9d3699c7e95de4ed31eb9424c5687661593
    Reviewed-on: https://code.wireshark.org/review/28652
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 99ca2f3ed226fa4a250814be39317a4148249b87)
    Reviewed-on: https://code.wireshark.org/review/28700
    Reviewed-by: Peter Wu <<EMAIL>>

commit e8c61fb24c
Author: Jaap Keuter <<EMAIL>>
Date:   Thu Jul 12 21:31:03 2018 +0200

    CoAP: handle per packet data properly
    
    Bug: 14966
    Change-Id: I9c5c1da923a0cc2881465bcc484850d042a314d6
    Signed-off-by: Jaap Keuter <<EMAIL>>
    Reviewed-on: https://code.wireshark.org/review/28694
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Roland Knall <<EMAIL>>
    (cherry picked from commit b9c18a4188864a5c47d9c9b299cd97cba236a472)
    Reviewed-on: https://code.wireshark.org/review/28696
    Reviewed-by: Pascal Quantin <<EMAIL>>

commit be50371666
Author: Peter Wu <<EMAIL>>
Date:   Tue Jul 10 00:33:24 2018 +0200

    Qt: make "Save As" in Follow Streams usable
    
    For formats other than "Raw" and "UTF-8", the written file does not
    match the actual stream data. It would be duplicated in strange ways.
    Executing the "Save As" action twice while the dialog is open would also
    write two different files (huh?).
    
    As a quick fix, just replace the strange save logic by writing the text
    field contents. A functional difference is that previously it would
    write data while parsing the "follow data" list, now it uses the text
    field contents. That data will now be truncated after 500 MB.
    
    Bug: 14933
    Change-Id: I498676389d0da3ac070346d6903bd2e6b0fc7674
    Fixes: v1.11.0-rc1-2538-g80f9326b2f ("Add TCP/UDP/SSL Follow feature to QtShark")
    Reviewed-on: https://code.wireshark.org/review/28663
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit 16953695d734555a4472bada9d2dec799544d619)
    Reviewed-on: https://code.wireshark.org/review/28684

commit 6456cce1f0
Author: Guy Harris <<EMAIL>>
Date:   Mon Jul 9 21:45:33 2018 -0700

    If we explicitly started a new page, print a column header line if necessary.
    
    Change-Id: I9be7b41ce5ec5ece502035d0ca7c0fbb3eb3b37d
    Ping-Bug: 14960
    Reviewed-on: https://code.wireshark.org/review/28672
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 3e2d3837ddbc2fcebc643f080111d78149e2dcbd)
    Reviewed-on: https://code.wireshark.org/review/28673

commit 0c2a874293
Author: Guy Harris <<EMAIL>>
Date:   Mon Jul 9 21:10:51 2018 -0700

    Expand another comment to explain why we're ignoring empty lines.
    
    Change-Id: Iafaeaffa1004ae741bdb4c6be91528f65aa06cfd
    Reviewed-on: https://code.wireshark.org/review/28668
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 325b033ca4f307846ac2cc8d42899c2e51cbd27b)
    Reviewed-on: https://code.wireshark.org/review/28669

commit 234d814b6c
Author: Guy Harris <<EMAIL>>
Date:   Mon Jul 9 20:41:07 2018 -0700

    Note that this code is checking for being on a new page.
    
    Change-Id: Ib92292834bc00487958ff62584025e512de6d755
    Reviewed-on: https://code.wireshark.org/review/28664
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit c29c043cdcfe6b381e9ef57c796c94783f3b32ef)
    Reviewed-on: https://code.wireshark.org/review/28665

commit 3979bbf964
Author: Peter Wu <<EMAIL>>
Date:   Thu Jul 5 23:07:52 2018 +0200

    tcp: ignore zero-length payloads for Follow TCP Stream
    
    In the Qt Follow TCP Stream dialog with the ASCII mode, sometimes
    selecting the first few bytes would wrongly select a packet with a
    higher frame number.
    
    This happens because Qt iterates through the list of payloads, then
    stores appends the payload data and maps the new cursor position to the
    packet number. If the payload data was empty, then it would overwrite
    previous cursor positions.
    
    To fix this, do not add records for empty TCP payloads.
    
    Bug: 14898
    Change-Id: I598d73899b56eac3d2a022f108bf097bdd363b5c
    Reviewed-on: https://code.wireshark.org/review/28613
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 59192f79fc226b77ef71b9012e0e867002882aaf)
    Reviewed-on: https://code.wireshark.org/review/28646
    Reviewed-by: Peter Wu <<EMAIL>>

commit 2f3b56c21e
Author: Peter Wu <<EMAIL>>
Date:   Wed Jul 4 19:52:49 2018 +0200

    tcp: fix Follow TCP Stream for overlapping data
    
    When two segments overlap each other, previously the second segment
    would wrongly be appended to the first one while it should only append a
    subset from the end of the second segment. (It is assumed that the very
    first segment is received in time such that an extension on both the
    left and right side of the previous stream is not possible.)
    
    Make sure that "frag_follow_record->data" uses a subset (starting at the
    end of the previous stream) instead of the full tvb contents. While at
    it, add some documentation and restructure the logic to avoid code
    duplication and unnecessary memory allocations.
    
    (From bug 9882:) Tested with hao123-com_packet-injection-filtered.pcap,
    it now ignores the new overlapping data. Likewise for overlap-2.pcap.
    Tested with retransmission_with_additional_payload.pcapng (bug 13700).
    Unfortunately, there is no extra expert info to warn about the
    non-matching overlapping segment data, but that is a separate issue.
    
    Bug: 13700
    Change-Id: I74a941199d75b23b5d297e4dd534680ae610627d
    Reviewed-on: https://code.wireshark.org/review/28597
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit aecb143e2c704d0b59b15420f60f780927fe7438)
    Reviewed-on: https://code.wireshark.org/review/28645
    Reviewed-by: Peter Wu <<EMAIL>>

commit 6fc9501956
Author: Peter Wu <<EMAIL>>
Date:   Thu Jul 5 20:59:32 2018 +0200

    tcp: fix Follow TCP Stream with missing (but ACKed) segments
    
    Restore the direction for the first check_follow_fragments call to match
    the situation before commit 57acc227f0 (which broke other things, so its
    logic was reimplemented in commit v2.3.0rc0-1449-g66fa31415f ("tcp: Fix
    Follow TCP tap data and when its tapped.")).
    
    Ensure that the ACK value is checked before processing the sequence
    number and payload for the current flow.
    
    Bug: 14944
    Change-Id: If8947d7732683a4943f405eb72b1a8526a35a6dc
    Fixes: v2.1.0rc0-1339-g57acc227f0 ("KISS the Follow TCP functionality.")
    Reviewed-on: https://code.wireshark.org/review/28612
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 079d1521c6a24e39d283e3ce4e27d7c399e8e068)
    Reviewed-on: https://code.wireshark.org/review/28644
    Reviewed-by: Peter Wu <<EMAIL>>

commit f3b2462c12
Author: Peter Wu <<EMAIL>>
Date:   Wed Jul 4 19:03:19 2018 +0200

    tcp: remove repetitive "follow_record->is_server"
    
    To improve readability, do not repeat "follow_record" a dozen times.
    No functional change.
    
    Change-Id: I854434974a94d69d4591ad1bc3acf911073b0923
    Reviewed-on: https://code.wireshark.org/review/28596
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 8d117f327f54d3a86bd8582c820b4828c0858957)
    Reviewed-on: https://code.wireshark.org/review/28643

commit a197af6297
Author: Gerald Combs <<EMAIL>>
Date:   Sun Jul 8 08:27:04 2018 +0000

    [Automatic update for 2018-07-08]
    
    Update manuf, services enterprise numbers, translations, and other items.
    
    Change-Id: Ibead9082a73cc264ee4c6a1f2746ff4c5e91fcf0
    Reviewed-on: https://code.wireshark.org/review/28654
    Reviewed-by: Gerald Combs <<EMAIL>>

commit 0759402963
Author: Peter Wu <<EMAIL>>
Date:   Thu Jul 5 23:08:37 2018 +0200

    Qt: fix wrong Follow Stream text position after changing mode
    
    When switching from ASCII to other modes (such as Hex), the previous
    text to packet number mapping was not cleared. This resulted in
    using the wrong packet number when hovering over the packet data.
    
    Change-Id: I29ba1786925490c33fc9181373a31d51f5091642
    Reviewed-on: https://code.wireshark.org/review/28614
    Petri-Dish: Peter Wu <<EMAIL>>
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 7a3f397c2b746677f7f7e326b92bd9620b9a15dd)
    Reviewed-on: https://code.wireshark.org/review/28647
    Tested-by: Petri Dish Buildbot

commit e56148c443
Author: Peter Wu <<EMAIL>>
Date:   Fri Jul 6 12:38:26 2018 +0200

    Follow Stream: ensure linear performance with many packets
    
    Reverse the payload chunks list to achieve a running time of O(n) rather
    than O(n²) for insertion of all chunks. Executing a RelWithDebInfo+ASAN
    build with `tshark -r chargen-session.pcapng.gz -qz follow,tcp,hex,0`
    previously took 11m5s to complete, but now finishes in 16 seconds.
    
    Tested using a capture file with 152k TCP packets (from bug 11777).
    Backport note: must update ui/gtk/follow_stream.c too.
    
    Change-Id: Icf70d45f33d4399e53209fb6199d3809608c8d99
    Reviewed-on: https://code.wireshark.org/review/28595
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit c40c26c04c6a39ef7867a35c50293c666255c9a4)
     [Peter: updated ui/gtk/follow_stream.c for master-2.6]
    Reviewed-on: https://code.wireshark.org/review/28642

commit 76568277a7
Author: Guy Harris <<EMAIL>>
Date:   Fri Jul 6 02:58:06 2018 -0700

    Plug extension list leak.
    
    Change-Id: Ie3d2d5fb76ca5de2488e270771b94f7374a8838f
    Reviewed-on: https://code.wireshark.org/review/28640
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 11871bee1c06723f9d7d4ef111d12d02d78814a2)
    Reviewed-on: https://code.wireshark.org/review/28641

commit 48ee72f4e3
Author: Guy Harris <<EMAIL>>
Date:   Fri Jul 6 02:46:30 2018 -0700

    Don't use QFileInfo()::baseName().
    
    It strips off all suffixes, which is not useful behavior; it assumes
    that nobody uses "." for any purpose other than separating a file name
    from an extension - 1994 called, they want their version of Windows
    back (and UN*X called, too...).
    
    For the "Saving XXX" status bar message, just use the entire last
    component of the file name.
    
    Change-Id: Ib34fde3e49cd791c7baf333eebb71a8dbd672c19
    Reviewed-on: https://code.wireshark.org/review/28638
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 238c7dd61d3de89b9341655625547a404708b396)
    Reviewed-on: https://code.wireshark.org/review/28639

commit f96b094a33
Author: Guy Harris <<EMAIL>>
Date:   Fri Jul 6 02:06:56 2018 -0700

    Fix the calculation of a file's "basename".
    
    Strip off only extensions that correspond to file types we know about;
    QFileInfo::baseName() strips off *all* extensions, where "extension" is
    "anything preceded by a .", so it turns foo.bar.pcap.gz into foo, not
    foo.bar.  We don't want that; instead, we strip off only those
    extensions that correspond to file types we know how to read, so we'd
    strip off .pcap.gz in foo.bar.pcap.gz, and strip off .pcap in
    foo.bar.pcap, leaving foo.bar in both cases.
    
    Change-Id: I5385921ad2f0fef815d52e9902fef15735fd9dae
    Reviewed-on: https://code.wireshark.org/review/28636
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit aab5ad074e88e3c03e438b2b9ff503191c6f6458)
    Reviewed-on: https://code.wireshark.org/review/28637

commit 916ca126a8
Author: Robert Sauter <<EMAIL>>
Date:   Thu Jul 5 09:17:23 2018 +0200

    wisun: fix remove EAPOL-ready field; add Routing Cost to info
    
    The EAPOL-ready field has not been part of the specification since
    before this dissector has been created.
    
    Change-Id: I7bd25a44ad3ee61e7a2a3b105f7bbffb1e28b31f
    Reviewed-on: https://code.wireshark.org/review/28602
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Alexis La Goutte <<EMAIL>>
    (cherry picked from commit eb5f330ebf6b8a545779d92264bb46b007e9a160)
    Reviewed-on: https://code.wireshark.org/review/28605
    Reviewed-by: Anders Broman <<EMAIL>>

commit 6758160b24
Author: Guy Harris <<EMAIL>>
Date:   Thu Jul 5 19:10:23 2018 -0700

    Use the display name in "Save As..." output for statistics taps.
    
    Change-Id: I78ecc2db6cf0020a51637b1595f2b001d66addf4
    Reviewed-on: https://code.wireshark.org/review/28633
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 2d36c475f985e0499431ffb93bc65c8738370549)
    Reviewed-on: https://code.wireshark.org/review/28634

commit f6e01af011
Author: Guy Harris <<EMAIL>>
Date:   Thu Jul 5 17:31:02 2018 -0700

    Use the file *base* name for statistics graphs etc..
    
    For example, if the file is foo.pcap, make the default name for a saved
    PDF of some graph be foo.pdf, as it was prior to 2.6, not foo.pcap.pdf.
    
    Change-Id: Ide99c9c7fa1f3d16f829e731f968a209fbb52b8d
    Reviewed-on: https://code.wireshark.org/review/28624
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 874c978bbce62a7ed43934249057c4937d299627)
    Reviewed-on: https://code.wireshark.org/review/28631

commit 07946bcef8
Author: Guy Harris <<EMAIL>>
Date:   Wed Jul 4 10:45:00 2018 -0700

    Some fixes.
    
    For filePath() and fileName(), just return a null string if we can't
    convert from the native encoding to UTF-8 - those aren't used for
    displaying, those are used for setting the main window's file name and
    for generating names of files to save based on the capture file name.
    
    Have fileDisplayName() just return the display name, without
    "[closing]"/"[closed]" decoration or a special case for no file being
    open (just return a null string if there's no file open), and have
    fileTitle() return the decorated display name.
    
    Change-Id: I244f318d5444dcf58527e5d38c4d073c28b73810
    Reviewed-on: https://code.wireshark.org/review/28594
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 1c6dc6d31f147d1ea7b8694204e81cf5d827de23)
    Reviewed-on: https://code.wireshark.org/review/28630

commit 9cfb825644
Author: Guy Harris <<EMAIL>>
Date:   Tue Jul 3 19:08:27 2018 -0700

    fileTitle -> fileDisplayName.
    
    That more closely matches the name of the file.h routine that it uses.
    
    Change-Id: Ia206fb8331f4f3ad8035da9f6137ad2428d53a49
    Reviewed-on: https://code.wireshark.org/review/28589
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 9655acc757ab51758f45ac4ffe1dce482801ab5f)
    Reviewed-on: https://code.wireshark.org/review/28629

commit 5569a77c00
Author: Guy Harris <<EMAIL>>
Date:   Tue Jul 3 18:38:01 2018 -0700

    We have cf_get_display_name(); use it to get the title bar string.
    
    Change-Id: I318472670722d24c3d3896cbd1e88623f5adb297
    Reviewed-on: https://code.wireshark.org/review/28588
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 60d5c04dc351aaff90d2eae3d70abb89cec91f03)
    Reviewed-on: https://code.wireshark.org/review/28628

commit bdca768ac6
Author: Guy Harris <<EMAIL>>
Date:   Tue Jul 3 17:51:30 2018 -0700

    Set the file path for a main window if it's not a temporary file.
    
    That's needed to support the "proxy icon", so it can be dragged.
    
    Change-Id: I1ad209cd43a2a6df9c52d076f6513780b0ac51be
    Reviewed-on: https://code.wireshark.org/review/28587
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit a442fd512bcf46e03ed4b8b40d756a2f17215fe6)
    Reviewed-on: https://code.wireshark.org/review/28627

commit d98d41386a
Author: Guy Harris <<EMAIL>>
Date:   Tue Jul 3 15:51:39 2018 -0700

    Just use CaptureFile::fileTitle() in MainWindow::setTitlebarForCaptureFile().
    
    It now does the heavy lifting, so MainWindow::setTitlebarForCaptureFile()
    doesn't have to duplicate it.
    
    Change-Id: I97ded85306e625b2c67c3fde62a636ec6818a6f5
    Reviewed-on: https://code.wireshark.org/review/28586
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 9541945aa217a43d285e7b44b6c4516253f716a1)
    Reviewed-on: https://code.wireshark.org/review/28626

commit 2345492843
Author: Guy Harris <<EMAIL>>
Date:   Tue Jul 3 15:14:50 2018 -0700

    Don't show temporary file names in title bars.
    
    For dialogs and auxiliary windows, if we have a live capture that hasn't
    yet been saved to a permanent location, there's no good reason to show
    the temporary file name in the title bar, as:
    
      it's a random string that doesn't indicate where the capture was done
      and that could confuse people (see, for example, the confusion in bug
      14929, in which somebody referred to the "Follow TCP Stream" window as
      the ".pcap dialog" because its title had ".pcap" at the end, due to
      the capture file being a temporary file and its name showing up in the
      title bar of that window);
    
      it differs from what the main window title bar shows.
    
    While we're at it, don't assume that the file name in the capture_file
    structure is a UTF-8 string - some UN*Xes might not use UTF-8 for file
    names.
    
    Change-Id: I0d3dfd5d7f896ea37533daf7089b688710dbabf0
    Reviewed-on: https://code.wireshark.org/review/28581
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 670377f491dd11bfe2b59738e9dfb329dbc826d4)
    Reviewed-on: https://code.wireshark.org/review/28625

commit 31a0dbfc90
Author: Guy Harris <<EMAIL>>
Date:   Thu Jul 5 17:10:58 2018 -0700

    Change the print preview if "include column headings" changes.
    
    While we're at it, sort some method declarations and definitions, to
    group the top-level summary/details/bytes yes/no options together, with
    two groups of suboptions for summary and details below.
    
    Bug: 14945
    Change-Id: Id06dd64e44b18b13e2131482edef46aee3efbd63
    Reviewed-on: https://code.wireshark.org/review/28620
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit d8df975e2e120c75dfae3c6624de127b5a72b592)
    Reviewed-on: https://code.wireshark.org/review/28621

commit 02355fe2b9
Author: Guy Harris <<EMAIL>>
Date:   Thu Jul 5 16:48:53 2018 -0700

    Add a "Include column headings" checkbox in export dissections and print.
    
    Bug: 14945
    Change-Id: I1c5ed0bc7e738a5c8d65c09f25686549e1e6dd67
    Reviewed-on: https://code.wireshark.org/review/28615
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit d7ce2bece8e9c0048f25bf60ab2b017079db7940)
    Reviewed-on: https://code.wireshark.org/review/28616

commit 4080e95d92
Author: Guy Harris <<EMAIL>>
Date:   Wed Jul 4 15:51:29 2018 -0700

    Distinguish between "reserved for implementation" and "reserved for future use".
    
    Some flags in the connectionless PDU header are "reserved for
    implementation", which presumably means an implementation can set them
    to 0 or 1 and use it to send information to a compatible implementation;
    others are "reserved for future use" and "must be set to 0".
    
    Don't test the "reserved for implementation" flags in the heuristic, and
    show them as "Reserved for implementation" and show the others as
    "Reserved for future use (MBZ)".
    
    Bug: 14942
    Change-Id: Iff40f155e057301096fec1dbb68f71d041508ff1
    Reviewed-on: https://code.wireshark.org/review/28598
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 9e480c51bb64bf002a1cd28e9291b05b80049936)
    Reviewed-on: https://code.wireshark.org/review/28599

commit 6ced31d223
Author: Guy Harris <<EMAIL>>
Date:   Tue Jul 3 15:40:45 2018 -0700

    CaptureFile.fileTitle() is for display, not for file name processing.
    
    Don't use CaptureFile.fileTitle() if you're constructing a pathname; use
    it only if you're constructing a window title.
    
    Change-Id: I40f225ddb07be2f7dc3ae03108dae816846f20c7
    Reviewed-on: https://code.wireshark.org/review/28582
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit d76db1de783dca7fd99d7e663d7be4031ae426de)
    Reviewed-on: https://code.wireshark.org/review/28583

commit e968bd8a84
Author: Peter Wu <<EMAIL>>
Date:   Sat Jun 30 12:26:40 2018 -0700

    file: add more sanity checks to detect UI/file loading issues
    
    As "cf_read" and "rescan_packets" can end up calling back to the GUI
    code, that could destroy "cf->epan" which could result in use-after-free
    crashes. While I can find most issues with ASAN, it would be even
    better to detect the destructive action in "cf_close".
    
    Change-Id: I72700a60c6786d153c2aaa8478bfdfb16a01dcda
    Ping-Bug: 10870
    Reviewed-on: https://code.wireshark.org/review/28542
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 2f4cdb97c7085bad56cce68dd00c9d0092a12dc9)
    Reviewed-on: https://code.wireshark.org/review/28579
    Reviewed-by: Peter Wu <<EMAIL>>

commit c1fb20adc7
Author: Peter Wu <<EMAIL>>
Date:   Sat Jun 30 12:08:54 2018 -0700

    Qt: fix crash on opening a capture file while loading/saving another
    
    Closing a capture file while it is being loaded will result in a crash.
    As a workaround, disallow closing the capture file. The requested action
    (e.g. MainWindow::openCaptureFile) will be silently ignored.
    
    While at it, protect process_specified_records (called when saving
    files) similarly to cf_read and fix a crash that occurs when a capture
    from the Capture Dialog is started while a file is being loaded:
    
        file.c:360:cf_close: assertion failed: (cf->state != FILE_READ_IN_PROGRESS)
    
    Bug: 10870 # moving rapidly between large files in a file set
    Bug: 13594 # start capture while loading/saving file
    Bug: 14351 # open another file while loading file
    Change-Id: I6ce8f3163c3fa4869f0299e49909a32594326ce4
    Reviewed-on: https://code.wireshark.org/review/28541
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 536e26c55ecd89825855b14b7edd9899dfe6a78c)
    Reviewed-on: https://code.wireshark.org/review/28578
    Reviewed-by: Peter Wu <<EMAIL>>

commit 16d111276d
Author: Peter Wu <<EMAIL>>
Date:   Fri Jun 29 22:38:10 2018 -0700

    file: fix packet list update after dfilter change during live capture
    
    During live captures, "cf->state==FILE_READ_IN_PROGRESS" holds and as
    such setting "cf->redissection_queued" from "cf_filter_packets" will
    prevent the packet list from being updated (no new packets are added and
    display filter changes are not applied).
    
    Fix this by not checking "cf->state" and instead perform an explicit
    check to detect the "update_progress_dlg" issue (see original commit).
    As "cf->read_lock" is implied by "cf->redissecting", remove that check
    as well (see "rescan_packets").
    
    Print a warning instead of aborting in "cf_read" since I am not sure if
    that condition is currently prevented by its callers.
    
    Bug: 14918
    Change-Id: Ieb7d1ae3cbeef18f17c850ae3778822ee625dc68
    Fixes: v2.9.0rc0-1110-g8e07b778f6 ("file: do not perform recursive redissections to avoid crashes")
    Reviewed-on: https://code.wireshark.org/review/28538
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 8a1e517befc032b5607ca34ea60399db5d2359c4)
    Reviewed-on: https://code.wireshark.org/review/28577
    Reviewed-by: Peter Wu <<EMAIL>>

commit 47912aaad0
Author: Gerald Combs <<EMAIL>>
Date:   Sun Jul 1 08:33:42 2018 +0000

    [Automatic update for 2018-07-01]
    
    Update manuf, services enterprise numbers, translations, and other items.
    
    Change-Id: Id6f856d45c57b2396a0fb84648217422dbbc07bf
    Reviewed-on: https://code.wireshark.org/review/28548
    Reviewed-by: Gerald Combs <<EMAIL>>

commit 737d7c0e00
Author: Guy Harris <<EMAIL>>
Date:   Sun Jul 1 00:13:59 2018 -0700

    Fix creation of conversation.
    
    The last argument to conversation_new_by_id() is the options for the
    conversation, *not* the frame number.
    
    Change-Id: I44e1819123432aa043e82f6b74ebdfad26ce76c0
    Reviewed-on: https://code.wireshark.org/review/28545
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 9d1cea0c18a9c97843ff79a759b60953cd8df107)
    Reviewed-on: https://code.wireshark.org/review/28546

commit 5005abc360
Author: Guy Harris <<EMAIL>>
Date:   Sat Jun 30 19:27:13 2018 -0700

    Fix debugging code.
    
    Don't show address or ports that aren't provided; this is especially
    important for address 1, where its absence is indicated by the pointer
    being null, so we can't blithely dereference it.
    
    Show ports as unsigned, because that's what they are.
    
    Change-Id: I162b6f08a3973c0cded0742c267a016dbb5ee31a
    Reviewed-on: https://code.wireshark.org/review/28543
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 747793afaea8a8abbb84012880173fd6a7b8434f)
    Reviewed-on: https://code.wireshark.org/review/28544

commit ce62875ce7
Author: Peter Wu <<EMAIL>>
Date:   Wed Jun 27 17:28:06 2018 -0700

    file: do not perform recursive redissections to avoid crashes
    
    When packets are being read (in "cf_read") or rescanned/redissected (in
    "rescan_packets"), it could call "update_progress_dlg". That could end
    up accepting GUI actions such as changing profiles (which triggers a
    redissection via "cf_redissect_packets") or changing the display filter
    (which triggers another "rescan_packets" via "cf_filter_packets").
    
    Such recursive calls waste CPU and in case of "cf_redissect_packets" it
    also causes memory corruption (since "cf->epan" is destroyed while
    "cf_read" tries to read and process packets).
    
    Fix this by delaying the rescan/redissection when an existing rescan is
    pending. Abort an existing rescan/redissection if a new redissection
    (due to profile changes) or rescan (due to display filter changes) is
    requested and restart this to ensure that the intended user action is
    applied (such as a new display filter).
    
    Bug: 14918
    Change-Id: I646730f639b20aa9ec35306e3f11bf22f5923786
    Reviewed-on: https://code.wireshark.org/review/28500
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 8e07b778f6a79b17e905f52e5e0e8c9187c091bd)
    Reviewed-on: https://code.wireshark.org/review/28527
    Petri-Dish: Peter Wu <<EMAIL>>
    Reviewed-by: Roland Knall <<EMAIL>>

commit d43ce7a9be
Author: Guy Harris <<EMAIL>>
Date:   Thu Jun 28 13:34:46 2018 -0700

    Fix indentation.
    
    Change-Id: I0ef6d0a9a957e645aa7f7e507609b9195fe9c19f
    Reviewed-on: https://code.wireshark.org/review/28520
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 411c5e9dcfa78d5eeadcdc357cb99ffb7b423ce2)
    Reviewed-on: https://code.wireshark.org/review/28524

commit 947a11ac36
Author: Guy Harris <<EMAIL>>
Date:   Thu Jun 28 00:31:31 2018 -0700

    Make white space consistent.
    
    Change-Id: I19053ecc53b7f0d2b4dfb0462f381f7d28bb578a
    Reviewed-on: https://code.wireshark.org/review/28502
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit bb47336086a26bdee4c8d75ab7a999229da453d8)
    Reviewed-on: https://code.wireshark.org/review/28521

commit 15f7765662
Author: Peter Wu <<EMAIL>>
Date:   Tue Jun 26 16:12:37 2018 -0700

    TRANSUM: fix crash when switching profiles
    
    "output_rrpd" is NULL when the TRANSUM dissector is disabled (which is
    the default behavior). When switching to a profile where the dissector
    is enabled, redissection happens, but without invoking the init routine.
    This leads to a crash when dissect_transum tries to query "output_rrpd".
    
    Fix this by creating the map unconditionally. Use wmem_map_new_autoreset
    since its contents should be erased for new capture files.
    
    Bug: 13697
    Change-Id: Iea897da8faf8042dffdc74327d9d1221e5fb155f
    Fixes: v2.3.0rc0-1887-g78d56e5dd7 ("Cleanup transum post-dissector.")
    Reviewed-on: https://code.wireshark.org/review/28474
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 910bc034abd8e5209dc6ccd8cacdafdc59b1c9dc)
    Reviewed-on: https://code.wireshark.org/review/28487
    Reviewed-by: Peter Wu <<EMAIL>>

commit c73673cc9b
Author: Roland Knall <<EMAIL>>
Date:   Tue Jun 26 14:55:27 2018 -0700

    epan: Fix session null-pointer check
    
    Fix rare null-pointer when switching profiles
    
    Change-Id: I2a57ef22b4567f936f3a87e133db6132864a83ac
    Reviewed-on: https://code.wireshark.org/review/28468
    Reviewed-by: Roland Knall <<EMAIL>>
    (cherry picked from commit d3b4f1980e066b832cf8e09eee2ca4ca91a7c118)
    Reviewed-on: https://code.wireshark.org/review/28473
    Petri-Dish: Roland Knall <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Gerald Combs <<EMAIL>>

commit 0cbd0cccda
Author: Peter Wu <<EMAIL>>
Date:   Tue Jun 26 15:52:11 2018 -0700

    prefs: fix crash when importing old filter expression preference
    
    When the filter label was missing, it would result in a crash
    (use-after-free) while reading the next expression. For example:
    
        gui.filter_expressions.label: Not-Junk
        gui.filter_expressions.expr: tcp.flags.reset==1
        # note: missing label preference
        gui.filter_expressions.expr: dns
    
    While at it, do not duplicate the filter expression,
    "filter_expression_new" has always been copying it.
    
    Change-Id: I980fd720c9a04b679a71dd2e7e8bf5e53c72ac43
    Fixes: 1a046d693b ("Added Filter Toolbar Save functionality.")
    Bug: 11648
    Reviewed-on: https://code.wireshark.org/review/28471
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit d3e3c00fbbe205f16fc279068f1cb989259c3b04)
    Reviewed-on: https://code.wireshark.org/review/28480
    Reviewed-by: Guy Harris <<EMAIL>>

commit c616ea9ceb
Author: Roland Knall <<EMAIL>>
Date:   Tue Jun 26 10:57:50 2018 -0700

    Qt: Fix crash when starting to drag on the wrong position
    
    When dragging on the wrong position in the toolbar, wireshark
    crashes
    
    Change-Id: I756e9caebc844d32e99e9fd3e338a872986b9e96
    Reviewed-on: https://code.wireshark.org/review/28458
    Petri-Dish: Roland Knall <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Roland Knall <<EMAIL>>
    (cherry picked from commit 1437b421a29e361d3669e5c27ae22906efbd0832)
    Reviewed-on: https://code.wireshark.org/review/28463

commit d4a5778061
Author: Gerald Combs <<EMAIL>>
Date:   Tue Jun 26 10:39:24 2018 -0700

    CMake: Make the pdb_zip_package target passive.
    
    Remove the pdb_zip_package target's dependency on epan, otherwise we
    might end up triggering a build which creates a mismatch.
    
    Conflicts:
            CMakeLists.txt
    
    Change-Id: I1e077e5f119273ee80a89c30f54e29fdb242e082
    Reviewed-on: https://code.wireshark.org/review/28457
    Petri-Dish: Gerald Combs <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Gerald Combs <<EMAIL>>
    (cherry picked from commit 2d8e8b9574fb21fb9a09ca6059c633940b23ff6a)
    Reviewed-on: https://code.wireshark.org/review/28461

commit 951ea5910d
Author: Pascal Quantin <<EMAIL>>
Date:   Tue Jun 26 07:09:58 2018 -0700

    Q931: do not tap packet if no packet info is present
    
    Follow-up of gd08a53a7b9
    
    Change-Id: Ice15c7cf97c2d84e80e39944012c54947517232f
    Reviewed-on: https://code.wireshark.org/review/28452
    Reviewed-by: Pascal Quantin <<EMAIL>>
    Petri-Dish: Pascal Quantin <<EMAIL>>
    Reviewed-by: Tomáš Kukosa <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    (cherry picked from commit 328f5cf440e1f5ca1f9329d4f856dc31d23909ef)
    Reviewed-on: https://code.wireshark.org/review/28453

commit bd1cdb5f98
Author: Tigran Mkrtchyan <<EMAIL>>
Date:   Tue Jun 26 11:07:00 2018 +0200

    nfs: fix displaying of operation CLONE
    
    due to missing mapping from operation number to a string representation
    wireshark displays '71' instead of 'CLONE'
    
    Change-Id: Ic5da0a110d5475b2467d6110ea2896332f93288c
    Signed-off-by: Tigran Mkrtchyan <<EMAIL>>
    Reviewed-on: https://code.wireshark.org/review/28447
    Reviewed-by: Alexis La Goutte <<EMAIL>>
    Petri-Dish: Alexis La Goutte <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Jaap Keuter <<EMAIL>>
    (cherry picked from commit d2a016737575927d453081675502346ed5eac8dd)
    Reviewed-on: https://code.wireshark.org/review/28451
    Petri-Dish: Jaap Keuter <<EMAIL>>

commit 32d609ee3b
Author: Peter Wu <<EMAIL>>
Date:   Fri May 18 11:01:24 2018 +0200

    cmake: remove dependency of i18n.qrc on .qm files
    
    The i18n.qrc file is generated at cmake time while the .qm files are
    also built as dependency of the AUTORCC process. The extraneous .qm
    dependency of i18n.qrc possibly causes duplicate wireshark_zh_CN.qm
    builds (see v2.9.0rc0-605-g644939ed9e), so remove it.
    
    Change-Id: I629e7b809486a65b27158f66a7e62cfcf61657d3
    Reviewed-on: https://code.wireshark.org/review/27633
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit 71a240d6118c99eb2e918d71e81164808bd64a29)
    Reviewed-on: https://code.wireshark.org/review/28433
    Petri-Dish: Pascal Quantin <<EMAIL>>
    Reviewed-by: Pascal Quantin <<EMAIL>>

commit a226168d55
Author: Peter Wu <<EMAIL>>
Date:   Thu May 17 19:44:12 2018 +0200

    CMake: try to fix duplicate qm builds on Windows
    
    The Windows buildbot showed that .qm files are generated twice. This
    resulted in rebuilding i18n.cpp, which resulted in rebuilding qtui,
    which finally resulted in Wireshark.exe being built twice for no reason.
    
    Assuming it is the same issue as commit v2.3.0rc0-3078-gaa9a0b3bf8,
    avoid having multiple targets depend on the same output (.qm files).
    This patch requires CMake 3.1 or newer.
    
    For master-2.6 backport, limit the change to WIN32 platform (as we already
    require CMake 3.7 as minimum).
    
    Change-Id: Iffd8946701553b89c09504278afa4b16d2a0b5b4
    Fixes: v2.5.0rc0-1366-gdfa893b1cf ("cmake: set CMAKE_AUTOUIC and CMAKE_AUTORCC to avoid CMP0071 warning")
    Reviewed-on: https://code.wireshark.org/review/27620
    Petri-Dish: Peter Wu <<EMAIL>>
    Reviewed-by: Gerald Combs <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit 644939ed9e4e5449970e27a674a6936723daed5c)
    Reviewed-on: https://code.wireshark.org/review/28434
    Reviewed-by: Pascal Quantin <<EMAIL>>

commit c292e18ddb
Author: Balint Reczey <<EMAIL>>
Date:   Fri Jun 22 17:17:23 2018 +0200

    Update shared library package names
    
    Change-Id: I536623a57adfecc2392ee9deffaa7053bed29cb6
    Reviewed-on: https://code.wireshark.org/review/28398
    Petri-Dish: Balint Reczey <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Balint Reczey <<EMAIL>>

commit 0bbee8871a
Author: Gerald Combs <<EMAIL>>
Date:   Sun Jun 24 08:29:01 2018 +0000

    [Automatic update for 2018-06-24]
    
    Update manuf, services enterprise numbers, translations, and other items.
    
    Change-Id: Ia8a38350c9ef3414ab1d0fd30df36bce2d27fe2e
    Reviewed-on: https://code.wireshark.org/review/28407
    Reviewed-by: Gerald Combs <<EMAIL>>

commit c280333ba6
Author: Darius Davis <<EMAIL>>
Date:   Sat May 12 17:30:48 2018 +1000

    BGP: Validate length of Path Attribute records.
    
    Bug 13741 showed a case where the BGP dissector's failure to validate the
    length of the Path Attribute record allowed a pathological BGP UPDATE packet to
    generate more than one million items in the protocol tree by repeatedly
    dissecting certain segments of the packet.
    
    It's easy enough to detect when the Path Attribute length cannot be valid, so
    let's do so.  When the condition arises, let's raise an Expert Info error in
    the same style and format as used elsewhere in the same routine, and abandon
    dissection of the Path Attributes list.
    
    With this check in place, an incorrect length computation is revealed at a
    callsite.  This would only have prevented a small (less than 5 bytes) Path
    Attribute from being dissected if it was at the very end of the Path Attributes
    list, but the bounds checking added in this change makes this problem much more
    apparent, so we fix the length computation while we're here.
    
    Testing Done: Built wireshark on Linux amd64.  Using bgp.pcap from the Sample
       Captures page on the wiki, verified that the dissection of the UPDATE
       packets were unaltered by this fix.  Using the capture attached to bug 13741
       (clusterfuzz-testcase-minimized-6689222578667520.pcap), verified that the
       packet no longer triggers the "too many items" exception, instead we see
       an Expert Info for each oversized Path Attribute length, and eventually an
       exception for "length of contained item exceeds length of containing item".
       30,000 iterations of fuzz test with bgp.pcap as input, and many iterations
       of randpkt-test too.  Crafted a packet with a 3-byte ATOMIC_AGGREGATE Path
       Attribute at the end of the Path Attributes list; Before this change, an
       exception is raised during dissection, but after this change it is dissected
       correctly.
    
    Bug: 13741
    Change-Id: I80f506b114a61e5b060d93b59bed6b94fb188b3e
    Reviewed-on: https://code.wireshark.org/review/27466
    Reviewed-by: Peter Wu <<EMAIL>>
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 6e88943d0eabc8c8bc11334ba4213ec64129575c)
    Reviewed-on: https://code.wireshark.org/review/28401
    Reviewed-by: Guy Harris <<EMAIL>>

commit dbd2cd6a73
Author: Balint Reczey <<EMAIL>>
Date:   Sat Aug 5 22:58:45 2017 -0400

    Set libwscodecs.so's version to 2.0.0
    
    It was 1.0.0 in the 2.4.x branch already and there are exported function
    removals.
    
    Change-Id: I9fc4333a146febd60e82586f868dde74988cc80e
    Reviewed-on: https://code.wireshark.org/review/28397
    Petri-Dish: Balint Reczey <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Balint Reczey <<EMAIL>>

commit 00fc5d12ee
Author: Balint Reczey <<EMAIL>>
Date:   Fri Jun 22 13:10:17 2018 +0200

    Fix shared library symlink names
    
    Change-Id: I7be54d75580e5b8d53a39e00c6c0cf69c7c304cf
    Reviewed-on: https://code.wireshark.org/review/28396
    Petri-Dish: Balint Reczey <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Balint Reczey <<EMAIL>>

commit 9d3f8b82ee
Author: Pascal Quantin <<EMAIL>>
Date:   Fri Jun 22 14:01:25 2018 +0200

    S1AP: fix a copy/paste error in a field name
    
    Change-Id: I3c602deaaeffa6738f325df5e4abda7b8214f560
    Reviewed-on: https://code.wireshark.org/review/28386
    Reviewed-by: Pascal Quantin <<EMAIL>>
    (cherry picked from commit ff3b7c9a935e3b910e0ab988534a92e417f37ef0)
    Reviewed-on: https://code.wireshark.org/review/28388

commit 341e5079ce
Author: Your Name <<EMAIL>>
Date:   Fri Jun 22 14:45:59 2018 +0930

    editcap: ifix time shift with useconds carry
    
    time shift to a whole number of seconds need to carry the seconds
    
    Change-Id: I188d915bca8f86a2cc19fc603bf472f461e8beea
    Reviewed-on: https://code.wireshark.org/review/28372
    Petri-Dish: Alexis La Goutte <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 7728a336ed3b7684b08e66fa58d92325dfa1ee38)
    Reviewed-on: https://code.wireshark.org/review/28377
    Reviewed-by: Guy Harris <<EMAIL>>

commit 09d8804d24
Author: Adrian Granados <<EMAIL>>
Date:   Wed May 23 18:28:32 2018 -0400

    Remove tabs around manuf names as shown for BASE_OUI
    
    And handle comments in manuf file entries correctly.
    
    There are entries in the manuf file where columns are
    separated by two or more tab characters. These extra
    tab characters are not being trimmed from the manuf name.
    
    OUI: 00:40:96 (         Cisco Systems, Inc.)
     ->
    OUI: 00:40:96 (Cisco Systems, Inc.)
    
    Change-Id: Ie6545480848bb84c20bea6566a3ccf11c7ed9233
    Reviewed-on: https://code.wireshark.org/review/27759
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit 4e82b0b6dacf6d8a818c20cec62014762cf31abf)
    Reviewed-on: https://code.wireshark.org/review/28370
    Reviewed-by: Jaap Keuter <<EMAIL>>

commit 1e6e42f8a6
Author: Erika Szelleova <<EMAIL>>
Date:   Sun Jun 17 18:39:36 2018 +0200

    HTTP: fix in desegmentation of HEAD requests and responses
    
    Previously HTTP message bodies following a HEAD request in the same conversation
    were not desegmented, resulting in spurious "Continuation" messages and failure
    to reassemble HTTP bodies. Fix this by properly taking the current HTTP message
    type (request or response) into account.
    
    Bug: 14793
    Change-Id: I1ffb052468cf414b73243447138466aca47db3e6
    Reviewed-on: https://code.wireshark.org/review/28312
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit 69fc3d8f3a9cdf3cb82b897107da52abda2930d3)
    Reviewed-on: https://code.wireshark.org/review/28356

commit 2d125c3f41
Author: Gerald Combs <<EMAIL>>
Date:   Tue Jun 19 10:07:31 2018 -0700

    CMake+macOS: Allow the use of ENV{CMAKE_PREFIX_PATH}.
    
    If we find /usr/local/opt/qt5, pass it as a PATHS option to find_package
    instead of adding it to the CMAKE_PREFIX_PATH CMake variable. This
    allows setting a Qt path via the CMAKE_PREFIX_PATH environment variable.
    
    Conflicts:
            CMakeLists.txt
    
    Change-Id: I5d23fcd092c0ea137482253f3f86c1a6d27f7a5e
    Reviewed-on: https://code.wireshark.org/review/28341
    Reviewed-by: Gerald Combs <<EMAIL>>
    Petri-Dish: Gerald Combs <<EMAIL>>
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit ccd2512602913b6b89a98878cbcc16d6c6d3ee1c)
    Reviewed-on: https://code.wireshark.org/review/28351

commit 8dd1aad4c7
Author: Guy Harris <<EMAIL>>
Date:   Sun Jun 17 17:30:01 2018 -0700

    Better name for the routine.
    
    It doesn't necessarily produce an FT_BYTES value any more.
    
    Change-Id: I7bad1e328394a829400bd139c48a9538c4892818
    Reviewed-on: https://code.wireshark.org/review/28318
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 333e915cff0078215492dc01206edbb1d77886e7)
    Reviewed-on: https://code.wireshark.org/review/28319

commit 610a74b178
Author: Guy Harris <<EMAIL>>
Date:   Sun Jun 17 15:01:24 2018 -0700

    For the contains operator, both sides have to have the same type.
    
    Have charconst_to_bytes() take the desired type as an argument, and pass
    it to dfilter_fvalue_from_unparsed().
    
    Bug: 14084
    Change-Id: I11db417311b9681b18c4a3fca2862b35837194d7
    Reviewed-on: https://code.wireshark.org/review/28315
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 078a53f9942e4092d4d18344fa758ab47be39335)
    Reviewed-on: https://code.wireshark.org/review/28316

commit d92a7a6be6
Author: Erika Szelleova <<EMAIL>>
Date:   Sat Jun 16 11:20:12 2018 +0200

    character constant in dfilter now must fit into one byte
    
      The value of a string in single quotes in dfilter must fit into one
      byte. The parser correctly parsed the beginning of the string,
      however it didn't check whether there are more characters to parse.
    
    Bug: 14084
    Change-Id: Ifa2d7a31052b2c1020d84c42637b9b7afc57d8c0
    Reviewed-on: https://code.wireshark.org/review/28298
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit df9cd64550616283b10a5a9b6a84a7f525171c05)
    Reviewed-on: https://code.wireshark.org/review/28313

commit 592f87a996
Author: Gerald Combs <<EMAIL>>
Date:   Sun Jun 17 08:27:13 2018 +0000

    [Automatic update for 2018-06-17]
    
    Update manuf, services enterprise numbers, translations, and other items.
    
    Change-Id: Ic3a2aea668acd66c850e456364b62e68b3d3986a
    Reviewed-on: https://code.wireshark.org/review/28306
    Reviewed-by: Gerald Combs <<EMAIL>>

commit 33412a1155
Author: Guy Harris <<EMAIL>>
Date:   Fri Jun 15 15:03:43 2018 -0700

    Put the entire server response into the Info column as is.
    
    Don't assume that the 3-digit code we got was followed by a blank, and
    display the code followed by a blank followed by the parameters..
    Instead, just put the raw text of the entire line into the Info column.
    
    Bug: 14878
    Change-Id: I1e081366bf859723158a36f10e86614fe52f124d
    Reviewed-on: https://code.wireshark.org/review/28292
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 2d4b50fc3d0bbb8a87ac613e050d671d7c5fd80e)
    Reviewed-on: https://code.wireshark.org/review/28293

commit 8f56e9e3ee
Author: Bruno Verstuyft <<EMAIL>>
Date:   Mon Jun 11 16:15:22 2018 +0200

    DOCSIS: correct reassembly of UCD in REG-RSP-MP and DBC-REQ messages
    
    Change-Id: I7c84b6cce113b71aea0c17741024916f9b17bdf9
    Reviewed-on: https://code.wireshark.org/review/28202
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 4059b56d3da08e1f5a016b4fed306dd786e96ed5)
    Reviewed-on: https://code.wireshark.org/review/28283

commit 7398cea6e1
Author: Guy Harris <<EMAIL>>
Date:   Thu Jun 14 21:01:57 2018 -0700

    Fix some "bad file" messages.
    
    The number being compared against is the amount of data *remaining* in
    the comment information, not the *size* of the comment information.
    
    And it's unsigned, so format it with %u.
    
    Change-Id: I5f02302ad4acbc3b27655ff5518e6e56d464020d
    Reviewed-on: https://code.wireshark.org/review/28280
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 1e84591f4f07dc712bee9be3a33508a5510bb357)
    Reviewed-on: https://code.wireshark.org/review/28281

commit 1474ca9e12
Author: Guy Harris <<EMAIL>>
Date:   Thu Jun 14 17:38:55 2018 -0700

    No need to check for a UTF-16 string's length being a multiple of 2.
    
    utf_16_to_utf_8() just ignores the extra octet.
    
    Change-Id: I7bf003b674e5d9b0fb0265b0e8c6c142107084e3
    Reviewed-on: https://code.wireshark.org/review/28277
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 92412335d195129b1df593030eeaae38f43ad6d7)
    Reviewed-on: https://code.wireshark.org/review/28278

commit c5fad5055c
Author: Guy Harris <<EMAIL>>
Date:   Thu Jun 14 15:09:51 2018 -0700

    Fix indentation.
    
    Change-Id: I6a3db704c9046ff696820ce157423c5867c2c2e8
    Reviewed-on: https://code.wireshark.org/review/28275
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 3df05f3ec866a477db24c9db8a4ad6ce3316a9a5)
    Reviewed-on: https://code.wireshark.org/review/28276

commit 42a97f115a
Author: Guy Harris <<EMAIL>>
Date:   Thu Jun 14 13:40:11 2018 -0700

    Fix the handling of paths in the process information.
    
    Pathnames are not limited to 260 characters in recent versions of
    Windows; boost the limit to handle up to 32767 UTF-16 octet pairs worth
    of path.
    
    The pathname is in UTF-16-encoded Unicode; convert it to UTF-8 for our
    internal use.
    
    Bug: 14876
    Change-Id: I4ef19fd47c7dbdd74dcaf31a7a80f432d57dbb0d
    Reviewed-on: https://code.wireshark.org/review/28273
    Petri-Dish: Guy Harris <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 58d211dc7f801db2d7d2e5e6d6430eaac835e233)
    Reviewed-on: https://code.wireshark.org/review/28274

commit 1f51c80d56
Author: Matej Tkac <<EMAIL>>
Date:   Wed Jun 13 13:34:14 2018 +0200

    Diameter_3GPP: Added missing 'RAN-Support-Info' flag for GX Feature-List 2
    
        According to TS 29.212 v15.2.0
    
    Change-Id: Ibcf3929323e5d19dc13fb1b0aa06fdadd5f997a2
    Reviewed-on: https://code.wireshark.org/review/28258
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>

commit 9169aef3f1
Author: Pascal Quantin <<EMAIL>>
Date:   Wed Jun 13 12:12:38 2018 +0200

    HTTP2: prevent a segmentation fault if HTTP2 dissector was not called on first pass
    
    With HTTP2 heuristics to identify the conversation, a packet can be
    skipped on first pass and then decoded as HTTP2 on subsequent ones.
    Check that header data is available before attempting header
    decompression.
    
    Bug: 14869
    Change-Id: I8ef7669ca33835b509acb38d797e33d6167a1bd1
    Reviewed-on: https://code.wireshark.org/review/28257
    Petri-Dish: Pascal Quantin <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Pascal Quantin <<EMAIL>>
    (cherry picked from commit e281ca6f83560a24ac1d490dcca9fe49b03bbdcd)
    Reviewed-on: https://code.wireshark.org/review/28259

commit cfb8dccbfd
Author: Guy Harris <<EMAIL>>
Date:   Tue Jun 12 16:02:32 2018 -0700

    Make sure *both* sides are unsigned.
    
    Change-Id: Id25ea93aee888eda665f52da4c00d75970ee69e8
    Reviewed-on: https://code.wireshark.org/review/28253
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 69ad89caa96f004513f5191b1f49c513a21f1a7b)
    Reviewed-on: https://code.wireshark.org/review/28254

commit 4f5f928d43
Author: Guy Harris <<EMAIL>>
Date:   Tue Jun 12 15:38:22 2018 -0700

    Try again to fix the signed vs. unsigned comparison warning.
    
    Change-Id: I97dae4b6325fe5fe952c579e1d1ab3f0b37f461a
    Reviewed-on: https://code.wireshark.org/review/28249
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 0cc092d4b13b1dc890e44100a691bd309dc24261)
    Reviewed-on: https://code.wireshark.org/review/28250

commit 1fd26168e4
Author: Guy Harris <<EMAIL>>
Date:   Tue Jun 12 15:13:37 2018 -0700

    Fix signed vs. unsigned comparison warning.
    
    (In retrospect, signed offsets probably were the wrong choice; we
    rarely, if ever, use them to signify offsets from the end of the packet.
    Let's not do so any more in the future.)
    
    Change-Id: I7ace539be8bf927e21148c34b71e9c2b7535581e
    Reviewed-on: https://code.wireshark.org/review/28245
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 96c4655ae088c5162799df3b799696b6dece814f)
    Reviewed-on: https://code.wireshark.org/review/28246

commit 730003d6eb
Author: Guy Harris <<EMAIL>>
Date:   Tue Jun 12 14:30:50 2018 -0700

    Add some length checks, remove a DISSECTOR_ASSERT().
    
    Do more checks to make sure we don't run past the end of the data we're
    handed, and don't do a DISSECTOR_ASSERT(), as there may well be packets
    that don't have enough data to pass the assertion - that was causing
    some errors to show up in the 2.6 buildbot when doing 802.11 decryption
    tests.  Those errors should instead be reported as "sorry, we can't do
    decryption" errors by the decryption code.
    
    (XXX - the 802.11 *dissector* should probably be extracting the relevant
    fields and doing the relevant checks, and hand the data to the
    decryption code, so that we don't duplicate 802.11 frame parsing with
    code that might not do as much necessary work as the 802.11 dissector.)
    
    Tweak some comments while we're at it.
    
    Change-Id: I1d230e07cec2fca8c23f265b5875a0bf83f79432
    Reviewed-on: https://code.wireshark.org/review/28240
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 871e96462750b616ad8cc1063c84b844c1a8bfc4)
    Reviewed-on: https://code.wireshark.org/review/28241

commit c13063adbc
Author: Pascal Quantin <<EMAIL>>
Date:   Tue Jun 12 19:19:35 2018 +0200

    BT Common: fix btcommon.eir_ad.entry.le_role filter
    
    Bug: 14868
    Change-Id: Ia52764c45d509a27545e266328702b79db3985b7
    Reviewed-on: https://code.wireshark.org/review/28226
    Petri-Dish: Pascal Quantin <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Pascal Quantin <<EMAIL>>
    (cherry picked from commit 169ff5a7f58ead93ee87df5078d1fd11e7a78b0c)
    Reviewed-on: https://code.wireshark.org/review/28236

commit addd4b70fc
Author: Guy Harris <<EMAIL>>
Date:   Tue Jun 12 11:23:32 2018 -0700

    Don't let randpkt write packets libwiretap can't read.
    
    Wiretap imposes an arbitrary limit on the maximum packet size, to
    prevent it from trying to allocate a huge packet buffer and possibly
    running out of address space on ILP32 platforms or just eating too much
    backing store on LP64/LLP64 platforms.  Don't write packets with a
    length greater than that limit.
    
    Bug: 14107
    Change-Id: Iba4fe3b008b044215647ba3f838ae7b3ac66c585
    Reviewed-on: https://code.wireshark.org/review/28232
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 4e7f4881d2cf6fe69de0661c2441e82dd525e59e)
    Reviewed-on: https://code.wireshark.org/review/28233

commit 470d59e651
Author: Guy Harris <<EMAIL>>
Date:   Tue Jun 12 10:47:36 2018 -0700

    Clean up handling of counted blobs of bytes.
    
    Don't treat the count+blob as itself a blob of bytes; use FT_NONE.
    Create it with an unknown length (-1, meaning "to end of packet, for
    now"), and set its length once we've finished dissecting it.  Dissect
    the raw bytes of a prefixed-bytes item regardless of whether we're
    building a protocol tree or not.
    
    This means we do a better job of handling a too-large length; instead of
    overflowing the offset, we throw an exception and stop dissecting, so we
    don't run the risk of looping infinitely.
    
    Bug: 14841
    Change-Id: I593be9b6ba9aa15d8529f96458e53b85ace6402a
    Reviewed-on: https://code.wireshark.org/review/28228
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit a03eacc7aabd04a6fd0db978e0d7597220ac0515)
    Reviewed-on: https://code.wireshark.org/review/28229

commit 1ca612836f
Author: Pascal Quantin <<EMAIL>>
Date:   Tue Jun 12 13:56:16 2018 +0200

    BT Common: use bluetooth_address_type_vals with btcommon.eir_ad.entry.le_bd_addr.type
    
    Bug: 14866
    Change-Id: I087469dabe0cebc2a94e70953a7ec00c48d72862
    Reviewed-on: https://code.wireshark.org/review/28218
    Reviewed-by: Alexis La Goutte <<EMAIL>>
    Petri-Dish: Alexis La Goutte <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 1568ea952b5d733be7aab5372a847c3890ae21ee)
    Reviewed-on: https://code.wireshark.org/review/28223
    Petri-Dish: Pascal Quantin <<EMAIL>>
    Reviewed-by: Pascal Quantin <<EMAIL>>

commit 36792cc766
Author: Robert Sauter <<EMAIL>>
Date:   Tue Jun 12 16:26:16 2018 +0200

    wisun: fix name of Netricity Frame Type IE
    
    Change-Id: I8cafb3b6475991b223146dbe6fc59bfb915b1bf2
    Reviewed-on: https://code.wireshark.org/review/28220
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 6825f47bd1c984d9378a7a1884a2659683aaa832)
    Reviewed-on: https://code.wireshark.org/review/28221

commit a85033ea71
Author: Gerald Combs <<EMAIL>>
Date:   Mon Jun 11 11:18:35 2018 -0700

    Windows: HiDPI-ify our native print dialogs.
    
    Call set_thread_per_monitor_v2_awareness and
    revert_thread_per_monitor_v2_awareness when showing native print
    dialogs.
    
    Change-Id: I295540953e50547ee0bb0b162c805f2d568d88ba
    Reviewed-on: https://code.wireshark.org/review/28209
    Petri-Dish: Gerald Combs <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Gerald Combs <<EMAIL>>

commit 289e7476aa
Author: Peter Wu <<EMAIL>>
Date:   Mon Jun 11 21:06:58 2018 +0200

    tools: ship make-regs.py with autotools
    
    Change-Id: I04d4752abdadb480b5e98f315383eb7170ddb66c
    Fixes: v2.6.2rc0-79-g63f90a302e ("Rewrite make-{dissectors,taps} in Python")
    Reviewed-on: https://code.wireshark.org/review/28210
    Reviewed-by: Peter Wu <<EMAIL>>

commit 3d508ef07a
Author: Guy Harris <<EMAIL>>
Date:   Mon Jun 11 11:40:12 2018 -0700

    Give more detailed information about capture permissions on Debian.
    
    Indicate what you're supposed to do when running dpkg-reconfigure
    wireshark-common, and indicate that you have to run it as root using
    sudo.
    
    Emphasize in README.Debian, and indicate in the permission failure
    secondary message, that you have to add users to the "wireshark" group
    after doing that, and that a user may have to log out and log in again
    to make this change take effect.
    
    Bug: 14847
    Change-Id: Ia83ff8e92bd2f00b6c3779272322a40201416da0
    Reviewed-on: https://code.wireshark.org/review/28206
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 2f64ec0844d7509f00f63a225e056c1e08cf30c2)
    Reviewed-on: https://code.wireshark.org/review/28207

commit 63f90a302e
Author: João Valverde <<EMAIL>>
Date:   Fri Jun 8 18:09:11 2018 +0100

    Rewrite make-{dissectors,taps} in Python
    
    Having these build tools in machine code poses problems when cross-compiling.
    The most significant being that we need to find the host and build GLiB
    dependencies at compile-time.
    
    There is no noticeable speed difference between the Python and C implementation.
    
    Ping-Bug: 14622
    Change-Id: Id13f823c7f4abf51edfa291e703028873748989f
    Reviewed-on: https://code.wireshark.org/review/28130
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit 722d6b06f606bc045ecef6fe5c8f2c700a3dad65)
     [Peter: adjusted autotools to use the new make-regs.py script]
    Reviewed-on: https://code.wireshark.org/review/28204

commit 605c095662
Author: Guy Harris <<EMAIL>>
Date:   Sun Jun 10 18:33:55 2018 -0700

    Offer a permissions suggestion for macOS.
    
    If the user installed from the wireshark.org package, perhaps they chose
    not to install the "Set capture permissions on startup" item.  Suggest
    that they choose otherwise.
    
    Change-Id: Ic5053da9cb6e54e7a7b1aa5a9dd59a1a84ddee16
    Reviewed-on: https://code.wireshark.org/review/28197
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit deddb545b82f52d4514cb5d9d538bbfee67dfc20)
    Reviewed-on: https://code.wireshark.org/review/28198

commit a3ce8d98a2
Author: Guy Harris <<EMAIL>>
Date:   Sun Jun 10 18:26:40 2018 -0700

    Fix names in declarations to match definitions.
    
    Change-Id: I7b3dd147b523fd3251d93dd1917d2e2c47433207
    Reviewed-on: https://code.wireshark.org/review/28195
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit dd87f2018a45de643d05bc426e9353a1d67bb037)
    Reviewed-on: https://code.wireshark.org/review/28196

commit a9017bda3f
Author: Guy Harris <<EMAIL>>
Date:   Sat Jun 9 23:39:12 2018 -0700

    Improve the secondary error messages for pcap errors.
    
    On Windows, if WinPcap isn't installed, warn about that for errors other
    than failed attempts to start capturing.
    
    On HP-UX, if we appear to have an old version of libpcap, warn about
    that for errors other than failed attempts to start capturing.
    
    If we know the error is a permissions problem, don't make suggestions
    appropriate to other problems.
    
    If we know the error is *not* a permissions problem, don't make
    suggestions appropriate to permissions problems.
    
    For permissions problems, or possible permissions problems, on Linux,
    suggest doing dpkg-reconfigure wireshark-common if you've installed from
    a package on Debian or a Debian derivative such as Ubuntu.
    
    Change-Id: If4aac0343095ac0b984eebc21853920c3b6d3c63
    Ping-Bug: 14847
    Reviewed-on: https://code.wireshark.org/review/28189
    Petri-Dish: Guy Harris <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 5ecbcaf483a552a8b78d525b9f32c5cfa575d59c)
    Reviewed-on: https://code.wireshark.org/review/28194
    Reviewed-by: Guy Harris <<EMAIL>>

commit eb1a7ec17f
Author: Gerald Combs <<EMAIL>>
Date:   Sun Jun 10 08:28:03 2018 +0000

    [Automatic update for 2018-06-10]
    
    Update manuf, services enterprise numbers, translations, and other items.
    
    Change-Id: I2829e84c0e189b84eecb5a2fdf6c0fe5e62a41d8
    Reviewed-on: https://code.wireshark.org/review/28191
    Reviewed-by: Gerald Combs <<EMAIL>>

commit 4bd70c411e
Author: Guy Harris <<EMAIL>>
Date:   Sat Jun 9 19:42:32 2018 -0700

    If device->active_dlt = -1, show "Unknown" rather than "DLT -1".
    
    It means we don't know the active link-layer header type - probably
    because the device can't be opened, so we can't get the default linktype
    or the list of available linktypes - so show it as "Unknown".
    
    Bug: 14847
    Change-Id: I5a1ad360d2ae461e8db57e387679700a566b0949
    Reviewed-on: https://code.wireshark.org/review/28185
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 3c9c2c65341bae5a7b983af2a572b8a9a99b543c)
    Reviewed-on: https://code.wireshark.org/review/28186

commit 9aa3dc7830
Author: Guy Harris <<EMAIL>>
Date:   Sat Jun 9 18:11:52 2018 -0700

    Do bounds checking of the offset and length in proto_tree_add_string().
    
    Throw an exception if they don't correspond to data available in the
    packet - and do so even if the protocol tree argument is null, so that
    we catch very long strings that could cause the offset to overflow.
    
    Ask why we try to handle a null pointer passed as the string argument,
    while we're at it.
    
    Bug: 14738
    Change-Id: I2fa79ad0dcd1f41608844a573e045197ac60aa62
    Reviewed-on: https://code.wireshark.org/review/28179
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 0fbb5f84d00426040bd37dd89d74e2ecda5b598e)
    Reviewed-on: https://code.wireshark.org/review/28180

commit 658336f334
Author: Guy Harris <<EMAIL>>
Date:   Sat Jun 9 15:49:48 2018 -0700

    Use FT_NONE, not FT_STRINGZ, for the field that covers the packet.
    
    They're collections of binary data divided into subfields, not
    null-terminated strings, so give them the right type.
    
    Change-Id: If2685b9b41ca6711e12de6688ae51d5211767770
    Reviewed-on: https://code.wireshark.org/review/28175
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 33eb5e73ddd61b27621eb1e821c7864d95f2cbc9)
    Reviewed-on: https://code.wireshark.org/review/28176

commit 8cabf3ba1f
Author: Guy Harris <<EMAIL>>
Date:   Sat Jun 9 10:51:26 2018 -0700

    Fix handling of text headers with non-text values.
    
    Determine the length of non-text values in the standard fashion for WSP,
    and treat the value as having that length, rather than running to the
    end of the packet.
    
    Change-Id: If3501cf726df4d8338e86515906f67790a773b02
    Reviewed-on: https://code.wireshark.org/review/28167
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 8c73874ef070874b5220af6e8400e09505b1a79d)
    Reviewed-on: https://code.wireshark.org/review/28168

commit d962adfb4f
Author: Lorand Jakab <<EMAIL>>
Date:   Fri Jun 8 14:08:46 2018 +0200

    LISP: Decode AFNUM_802 as AFNUM_EUI48
    
    Some LISP implementations use the AFI value 6 (RFC 1700) for MAC
    addresses instead of AFI value 16389 (RFC 7042). This patch allows
    correct decoding of both.
    
    Change-Id: I12c3d6b90fd8a85911f76dec5448e6a2e237e797
    Signed-off-by: Lorand Jakab <<EMAIL>>
    Reviewed-on: https://code.wireshark.org/review/28115
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit bd2c4deb724022163000d6fc736480b7c0198ac0)
    Reviewed-on: https://code.wireshark.org/review/28119

commit 9e86400d28
Author: Guy Harris <<EMAIL>>
Date:   Fri Jun 8 16:15:55 2018 -0700

    Make the string representation of the frame bitmap cover the frame bitmap.
    
    Make it begin where the frame bitmap begins, and end where the frame
    bitmap ends, rather than pretending it begins where the frame bitmap
    *ends* and is as many bytes long as the generated string is.
    
    Change-Id: Id62ba067116e3191646af764d3ae846474ac29c8
    Reviewed-on: https://code.wireshark.org/review/28160
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 89540b9cba201855385fd90d1bffec270bf74ce9)
    Reviewed-on: https://code.wireshark.org/review/28161

commit 7464dc4588
Author: Guy Harris <<EMAIL>>
Date:   Fri Jun 8 14:05:01 2018 -0700

    The string part of a NULL string is zero bytes long, not 4 bytes long.
    
    There aren't 4 bytes of 'N', 'U', 'L', and 'L' in the packet, so the
    length is *not* 4 bytes.
    
    Change-Id: I81331ef3f307dc65458da37e7d46e299eb7e727a
    Reviewed-on: https://code.wireshark.org/review/28149
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 09a7e8e863ac8a3b80a7a016b9943f815fc92367)
    Reviewed-on: https://code.wireshark.org/review/28150

commit 6dc42eaec4
Author: Pascal Quantin <<EMAIL>>
Date:   Fri Jun 8 14:32:18 2018 +0200

    BT Common: fix dissection of 32 bits and 128 bits UUID
    
    They are in little endian, not big endian.
    
    Bug: 14843
    Change-Id: I1680e84bfce9a03eaeeda9e38c84b471fda2bd8e
    Reviewed-on: https://code.wireshark.org/review/28116
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Pascal Quantin <<EMAIL>>
    (cherry picked from commit ee88dc8196d248ba88e0d8fa6c380d144d9a9b6a)
    Reviewed-on: https://code.wireshark.org/review/28124

commit c8387eb09d
Author: Guy Harris <<EMAIL>>
Date:   Thu Jun 7 22:04:32 2018 -0700

    Fix again.
    
    Change-Id: I054df4458c7ef9f6e108c9d9a5f29e2546c28041
    Reviewed-on: https://code.wireshark.org/review/28101
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit e020ccab499432d620e267db498d13f147ec136d)
    Reviewed-on: https://code.wireshark.org/review/28102

commit 153421db61
Author: Guy Harris <<EMAIL>>
Date:   Thu Jun 7 22:02:24 2018 -0700

    Fix indentation.
    
    Change-Id: Ib89cc2b7f416422868fb6f7c2f8f7bc1f9b347bf
    Reviewed-on: https://code.wireshark.org/review/28097
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 36c79feda803f8eef025460b2e9a19cf4decdeb3)
    Reviewed-on: https://code.wireshark.org/review/28098

commit 9518aaa254
Author: Guy Harris <<EMAIL>>
Date:   Thu Jun 7 21:58:34 2018 -0700

    Use proto_tree_add_expert{_format}() to report errors.
    
    Don't create fake string items.
    
    Have the item cover the part of the packet that has the issue, e.g. if
    there's an unexpected identifier, or an invalid length, cover that
    rather than the value; that 1) shows where the problem is and 2) avoids
    throwing an exception.
    
    Clean up the error messages and expert info items while we're at it.
    
    Change-Id: If698db98158f7a5532a865be02f1028d92af262f
    Reviewed-on: https://code.wireshark.org/review/28093
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 0a9e92d585a3b8a42aff8ad968710ea1d6a4e7ff)
    Reviewed-on: https://code.wireshark.org/review/28094

commit 6bc74bd044
Author: Gerald Combs <<EMAIL>>
Date:   Wed Jun 6 15:08:53 2018 -0700

    Qt: Set the ByteViewText field and protocol foreground.
    
    Set a foreground color when we add a field and protocol format range. On
    some systems the highlight background is darker or lighter than the
    normal background, and we need to adjust the foreground to match.
    
    Add a note about correctly handling our hover rect color.
    
    Change-Id: Ib6b50403dec889280eb3d27f3deb10150d22a599
    Reviewed-on: https://code.wireshark.org/review/28076
    Petri-Dish: Gerald Combs <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Gerald Combs <<EMAIL>>
    (cherry picked from commit 0882e94cff6a6a5087bfaf3883c9842cb8f9d944)
    Reviewed-on: https://code.wireshark.org/review/28087

commit 06c8101263
Author: Jaap Keuter <<EMAIL>>
Date:   Wed Jun 6 23:45:23 2018 +0200

    MTP3: Have prototype parameter names match the implementation
    
    Parameter names are diffferent (in name and order) in the prototype
    found in the header file than in the implementation. Let these match.
    
    Change-Id: I170d1d96631b1edbe613933663f746edb8c2e1fd
    Signed-off-by: Jaap Keuter <<EMAIL>>
    Reviewed-on: https://code.wireshark.org/review/28075
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 56a33b8093d03e207fbf971f0fb5b6d4801ef90c)
    Reviewed-on: https://code.wireshark.org/review/28078

commit 78d5103e44
Author: Gerald Combs <<EMAIL>>
Date:   Wed Jun 6 17:16:34 2018 -0700

    Debian: GNUTLS updates.
    
    Try to make our libgnutls28-dev / libgnutls-dev dependency work across
    different OS versions.
    
    Change-Id: I9f3b9d29a0c643fb6c6d93ef82e1205e7312507c
    Reviewed-on: https://code.wireshark.org/review/28077
    Reviewed-by: Gerald Combs <<EMAIL>>
    Petri-Dish: Gerald Combs <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>

commit 3711020497
Author: Guy Harris <<EMAIL>>
Date:   Wed Jun 6 12:29:59 2018 -0700

    Add COMMON_ARGS to the "Command and args" message.
    
    Change-Id: I286b7727a7b92ecbc913f55b8e77a505df4a7d5e
    Reviewed-on: https://code.wireshark.org/review/28069
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit c99e9d6aa46ae90af30ddbaa2b36ee493d2f86c3)
    Reviewed-on: https://code.wireshark.org/review/28070

commit 464674ca46
Author: Jaap Keuter <<EMAIL>>
Date:   Mon Jun 4 14:52:26 2018 +0200

    Correctly handle the PPI GPS timestamp fraction
    
    The timestamp and timestamp fraction are processed in one step,
    since this creates a proper timestamp interpretation. There are
    two fixes to this code to deal with erroneous packets.
    One is that when taking into account the timestamp fragment the
    available data must be 8 bytes in total, not just 4.
    The other is that when the mask indicates that there's only a
    timestamp fraction, nothing was shown.
    
    Change-Id: I4a0a65229f322ad56673a26ff6b3e769e994062d
    Signed-off-by: Jaap Keuter <<EMAIL>>
    Reviewed-on: https://code.wireshark.org/review/28007
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 447ffdf042e0eebf87ea9d33a9823e6d24345a0e)
    Reviewed-on: https://code.wireshark.org/review/28052

commit 8d7a1096b4
Author: Guy Harris <<EMAIL>>
Date:   Wed Jun 6 09:22:45 2018 -0700

    Make sure proto_tree_add_expert items are always added to the tree.
    
    Make sure that proto_tree_add_text_internal() and
    proto_tree_add_text_valist_internal() don't throw an exception, so the
    indication always appears in the tree to indicate the issue.  Do the
    "do the bytes exist" check *after* we've added all of the expert info to
    the protocol tree, so we still throw the appropriate exception.
    
    Change-Id: I4e0d2dcc48f9c8f4482550ae16284b9e021232cd
    Reviewed-on: https://code.wireshark.org/review/28062
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit f2dada066344924b754ea30569c05adce0c4e9dc)
    Reviewed-on: https://code.wireshark.org/review/28063

commit a89e686301
Author: Pascal Quantin <<EMAIL>>
Date:   Wed Jun 6 10:36:29 2018 +0200

    3GPP NAS: fix dissection of Linked TI IE
    
    Change-Id: Idb5713e8ab9ef7c33110380516261f0202b8b599
    Reviewed-on: https://code.wireshark.org/review/28047
    Reviewed-by: Pascal Quantin <<EMAIL>>
    (cherry picked from commit d10f0a62b20fb629e5ea7c204d86267baa90265a)
    Reviewed-on: https://code.wireshark.org/review/28048

commit 30f09de76a
Author: Joerg Mayer <<EMAIL>>
Date:   Tue Jun 5 02:21:26 2018 +0200

    Move a comment where it belongs
    
    Minor whitespace fixes
    
    Change-Id: I6431a04d90a5898d46bdc67a02afa6998a4ff38d
    Reviewed-on: https://code.wireshark.org/review/28021
    Reviewed-by: Jörg Mayer <<EMAIL>>
    (cherry picked from commit 63ded3e9df3b34de18827447f4d12fe8eeb07d29)
    Reviewed-on: https://code.wireshark.org/review/28039
    Reviewed-by: Anders Broman <<EMAIL>>

commit f05b47681d
Author: Pascal Quantin <<EMAIL>>
Date:   Tue Jun 5 10:32:46 2018 +0200

    SYNC: fix decoding of PDU type 3 format
    
    Bug: 14823
    Change-Id: I143c57d9d81ff8510d60b8a0d9f2daea78c33e0b
    Reviewed-on: https://code.wireshark.org/review/28032
    Petri-Dish: Pascal Quantin <<EMAIL>>
    Reviewed-by: Pascal Quantin <<EMAIL>>
    (cherry picked from commit ecc9b011c5c0a1c5b7b9efdadafe2d41794c4036)
    Reviewed-on: https://code.wireshark.org/review/28034

commit 488fdbc4d1
Author: Guy Harris <<EMAIL>>
Date:   Mon Jun 4 21:39:58 2018 -0700

    Remove a now-unused field.
    
    Change-Id: I5e85632bd901687b815cf76d7e25e082a9cb3657
    Reviewed-on: https://code.wireshark.org/review/28028
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 94c92f2492c2755ca75bc5edc7acec40987e19d6)
    Reviewed-on: https://code.wireshark.org/review/28029

commit 0dca69c5b6
Author: Guy Harris <<EMAIL>>
Date:   Mon Jun 4 20:37:56 2018 -0700

    Don't use FT_STRING fields for non-strings.
    
    If you're reporting an error, use an expert info item.
    
    If you're putting a structure into the protocol tree, use FT_NONE for
    the structure as a whole.
    
    Change-Id: Ie89b552576b15195acb0a9108d33430115d99f00
    Reviewed-on: https://code.wireshark.org/review/28024
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 8e7396edb4b4ac9580cdddb4feb0903da071905f)
    Reviewed-on: https://code.wireshark.org/review/28025

commit 25e44410cb
Author: Anders <<EMAIL>>
Date:   Mon Jun 4 17:25:38 2018 +0200

    gtpv2: Fix timestamps in GTPv2 Secondary RAT Usage Data Report IE
    
    Bug:14818
    Change-Id: Icf6116a51c586626d6dc058e5ce5148e1f1340fd
    Reviewed-on: https://code.wireshark.org/review/28012
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 6addd9602b908ff346d9ae22593e26e4b0224ccc)
    Reviewed-on: https://code.wireshark.org/review/28016

commit 79e8d39a5a
Author: Guy Harris <<EMAIL>>
Date:   Mon Jun 4 01:21:33 2018 -0700

    Send the "Command and args:" message to the right output file.
    
    Send it to same output file that gets the output ofthetest command, so
    that it shows upin the output.
    
    (I spent entirely too much time trying to figure out why 14738 was
    happening on the buildbot but not on my machine - it turns out that the
    test where it was failing was the one where -V *wasn't* specified, so
    the protocol tree *wasn't* being generated, so proto_tree_add_string()
    *wasn't* doing some tests that should have thrown an exception.  The
    output didn't make it clear that the test that was failing was the one
    where the arguments to tshark were -nr, not -nVxr.)
    
    Change-Id: I54e4450029ac56b9ac3d6eff9baf8acc849a5e4c
    Reviewed-on: https://code.wireshark.org/review/28003
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 4af23446b697602e51831ca7fce59a9c9d284003)
    Reviewed-on: https://code.wireshark.org/review/28004

commit 45509316e4
Author: Guy Harris <<EMAIL>>
Date:   Sun Jun 3 10:13:20 2018 -0700

    Fix handling of guintvars.
    
    1) A value that fits in a 32-bit unsigned integer may take more than 4
    octets - the uppermost bit of the octet is a "more octets follows" flag,
    so 4 octets contain only 7*4 - 28 bits of value, so a fifth octet
    preceding that with the upper 3 bits zero could result in a value that
    fits in 32 bits, and further octets of 0x80 just add further leading
    zeroes.
    
    We should, instead, check for *overflow*, meaning that if we add more
    bits at the bottom, the result is *less* than the previous value.
    
    2) When the result overflows, we should clamp it a UINT_MAX, rather than
    setting it to zero, and should keep accumulating octets, so that we
    return the correct octet count.  That prevents infinite loops where the
    item's length, and the item itself, are considered zero-length.
    
    This should fix bug 14738.
    
    Bug: 14738
    Change-Id: I1d1b60e22f169959c1573b1fcb7e010e027b5132
    Reviewed-on: https://code.wireshark.org/review/27986
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 5c51008ef0898adae9ce12283e34b29a9c406c9f)
    Reviewed-on: https://code.wireshark.org/review/27987

commit a3ecedb74e
Author: Gerald Combs <<EMAIL>>
Date:   Sun Jun 3 08:28:34 2018 +0000

    [Automatic update for 2018-06-03]
    
    Update manuf, services enterprise numbers, translations, and other items.
    
    Change-Id: I1781892116188e7fea0ec647f527b45f26ce9143
    Reviewed-on: https://code.wireshark.org/review/27978
    Reviewed-by: Gerald Combs <<EMAIL>>

commit fe8a63ecb9
Author: Guy Harris <<EMAIL>>
Date:   Sat Jun 2 15:10:57 2018 -0700

    Name update.
    
    With the name change from Ethereal to Wireshark, asn2eth was renamed
    asn2wrs.
    
    Change-Id: I5bdfa2362ca7de81b0bda6ec9faa78cdb0ba10b4
    Reviewed-on: https://code.wireshark.org/review/27968
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 2f9c61f03f7cd2186bacba827cad489ffeedf663)
    Reviewed-on: https://code.wireshark.org/review/27969

commit 8c23abe395
Author: Peter Wu <<EMAIL>>
Date:   Tue May 15 16:34:35 2018 +0200

    ber: clamp BER lengths to avoid integer overflow
    
    Many callers treat the length as signed integer, so ensure that the
    length fits in such a number. Failure to do so can have unintended
    consequences (such as calling "tvb_memdup(tvb, 0, -1)" and assuming that
    the length is actually 2^32-1).
    
    Although an exception could be thrown as well, let's give the caller a
    chance to handle this themselves.
    
    Change-Id: If92545f7d3603250f75741040435000ba879b7e3
    Ping-Bug: 14682
    Reviewed-on: https://code.wireshark.org/review/27563
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 67c642047362b5e2546259dd012622ff3dd6c9d9)
    Reviewed-on: https://code.wireshark.org/review/27965
    Reviewed-by: Guy Harris <<EMAIL>>

commit c092f65f20
Author: Peter Wu <<EMAIL>>
Date:   Tue May 15 16:31:40 2018 +0200

    ber: fix buffer overrun (read) in dissect_ber_constrained_bitstring
    
    The length is an unsigned integer, but some users (such as tvb_memdup)
    expect signed integers and treat negative values specially.
    
    Bug: 14682
    Change-Id: Ic3330d23d964b5cc44718b61c8985880f901674d
    Link: https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=8011
    Reviewed-on: https://code.wireshark.org/review/27562
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit df5a8b29bbb046933b73e9e369b9bc9e4b03aaa8)
    Reviewed-on: https://code.wireshark.org/review/27962
    Reviewed-by: Guy Harris <<EMAIL>>

commit ea197d88a3
Author: João Valverde <<EMAIL>>
Date:   Fri Jun 1 16:15:47 2018 +0100

    Fix compilation with Qt 5.11
    
    On my system (Arch Linux) with Qt 5.11 these includes are required
    to avoid undefined declarations.
    
    Change-Id: Iffb0aa4d867ea55cf4a5e8fe7df9a6264c5b3be3
    Reviewed-on: https://code.wireshark.org/review/27947
    Petri-Dish: João Valverde <<EMAIL>>
    Reviewed-by: Roland Knall <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: João Valverde <<EMAIL>>

commit ae43f03f7f
Author: Guy Harris <<EMAIL>>
Date:   Thu May 31 10:22:49 2018 -0700

    Don't check the CRC if the message isn't big enough to have a CRC.
    
    We should really do a better length check.  This also suggests that we
    might be going past the length if it's too short - should we create a
    new tvbuff, with tvb_subset_length(), and dissect based on that?
    
    Bug: 14780
    Change-Id: Iaaab529f34b0168ad74c7b4f3e1b4255504c1b57
    Reviewed-on: https://code.wireshark.org/review/27930
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 00f51fef21288100ee5b47aa51610533966de35a)
    Reviewed-on: https://code.wireshark.org/review/27931

commit cad7c8e2ff
Author: João Valverde <<EMAIL>>
Date:   Thu May 31 13:01:21 2018 +0100

    autotools: Use CC_FOR_BUILD with make-taps and make-dissectors
    
    Bug: 14622
    Change-Id: Ibb8b78221040d8cdc903fa358966e95e50d5ab12
    Reviewed-on: https://code.wireshark.org/review/27927
    Petri-Dish: João Valverde <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: João Valverde <<EMAIL>>

commit 99ec2b58eb
Author: João Valverde <<EMAIL>>
Date:   Thu May 31 00:35:44 2018 +0100

    Move make-taps and make-dissectors to tools/
    
    make-taps and make dissectors are build tools so that is the natural
    location for them. This also makes it easier to fix bug 14622 for
    autotools. When cross compiling we need to use the host target to
    build these executables.
    
    Adjust CMake and autotools builds to reflect the change.
    
    Ping-Bug: 14622
    Change-Id: I3034f544dad53fcc83230f5050512260fc1ab198
    Reviewed-on: https://code.wireshark.org/review/27920
    Petri-Dish: João Valverde <<EMAIL>>
    Reviewed-by: João Valverde <<EMAIL>>

commit 320a890cd7
Author: Peter Wu <<EMAIL>>
Date:   Tue May 15 13:08:49 2018 +0200

    ISMP: fix tuple decoding
    
    EDP_TUPLE_HOLD dissection was broken due to a length parameter mixup in
    v1.99.1rc0-224-g6720c80bab. The TLV length calculation was changed in
    commit ed5453d892, but the only pcap I could find for which it made a
    difference includes the TL lengths in the length field.
    
    Since commit 067a076179, the IPXNET type was wrongly decoded, fixed now.
    Check IPX address length to avoid a buffer overrun (read) in
    get_ether_name by at most 5 bytes.
    
    Bug: 4943
    Bug: 14672
    Link: https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=6451
    Change-Id: Ia99ab15578ecae6d5a3ec22989507d64f9926933
    Reviewed-on: https://code.wireshark.org/review/27554
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit e67283ddca70a7652b7dd41ef8883ee3278501d0)
    Reviewed-on: https://code.wireshark.org/review/27923
    Reviewed-by: Guy Harris <<EMAIL>>

commit 6b516f590a
Author: João Valverde <<EMAIL>>
Date:   Thu May 31 00:26:53 2018 +0100

    Don't link make-taps and make-dissectors with libwsutil
    
    Bug: 14722
    Change-Id: I276ca81f7bd9a75c6fd1e10c4d0cb0840ae03439
    Reviewed-on: https://code.wireshark.org/review/27919
    Reviewed-by: João Valverde <<EMAIL>>
    Petri-Dish: João Valverde <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>

commit b557482534
Author: Guy Harris <<EMAIL>>
Date:   Thu May 31 00:11:31 2018 -0700

    Don't require post-GLib 2.22.0 functions in build tools.
    
    Somebody doing a build will not necessarily have GLib 2.30 or later
    installed, and somebody doing a cross-build may not be able to get that
    function from libwsutil, as libwsutil may have been built for a platform
    different from the one on which the build is being done.
    
    Change-Id: Ic3d483b543f9c4d4175879650e157483676fd267
    Ping-Bug: 14622
    Reviewed-on: https://code.wireshark.org/review/27922
    Reviewed-by: Guy Harris <<EMAIL>>

commit 7f4b27b434
Author: Guy Harris <<EMAIL>>
Date:   Wed May 30 16:15:50 2018 -0700

    Fix problem building on NetBSD 7.1.
    
    The version of Berkeley YACC that comes with NetBSD 7.1 puts a
    declaration of ascendlval into ascend.h, even when we're building a
    reentrant parser.  That causes a shadowing warning.
    
    Suppress some diagnostics before we include ascend.h.
    
    Change-Id: I190f0439c36b48c7dfb19a2fe6cef0eb1e96f198
    Reviewed-on: https://code.wireshark.org/review/27917
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 93e6ab8eb9dfe950426e1169095d2f400ba30eb2)
    Reviewed-on: https://code.wireshark.org/review/27918

commit 2359750669
Author: Guy Harris <<EMAIL>>
Date:   Wed May 30 13:35:04 2018 -0700

    ifaddrs.h may require sys/types.h to be included first.
    
    That's the case on DragonFly BSD 5.2.1, at least.
    
    Change-Id: I8bbd51462d74380004c611183f4b9229f4d20ff6
    Reviewed-on: https://code.wireshark.org/review/27913
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit a5d3079b07df552e2e0a1f7879a2b8e4d070548c)
    Reviewed-on: https://code.wireshark.org/review/27914

commit dccea18468
Author: Roland Knall <<EMAIL>>
Date:   Fri May 25 15:19:46 2018 +0200

    Qt:Fix ProtoTree Copy
    
    There was an issue, where siblings where not copied correctly.
    
    Bug:14355
    Change-Id: I31611a6731f3f4de6b204c7ee708e42f0b7b170c
    Reviewed-on: https://code.wireshark.org/review/27802
    Petri-Dish: Roland Knall <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Kenneth Soerensen <<EMAIL>>
    Reviewed-by: Roland Knall <<EMAIL>>
    (cherry picked from commit 9e4f7dc50d6ff75543e9a3f0850396c6e6eb2878)
    Reviewed-on: https://code.wireshark.org/review/27909
    Petri-Dish: Jaap Keuter <<EMAIL>>

commit eaee956193
Author: Guy Harris <<EMAIL>>
Date:   Tue May 29 16:44:09 2018 -0700

    Boost the maximum frame size.
    
    Lemon gets this error on its Parse() function, at least on FreeBSD 11.1
    with its version of Clang.
    
    Change-Id: I4fc1674373af5c0016ee953b61066bf6b24b7ad6
    Reviewed-on: https://code.wireshark.org/review/27905
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 8f6c3ee8e137a19cf8ffac1a0baebe3adc7105ad)
    Reviewed-on: https://code.wireshark.org/review/27906

commit d258370145
Author: Guy Harris <<EMAIL>>
Date:   Tue May 29 11:40:01 2018 -0700

    Use rel_time_to_secs_str() to format a time delta.
    
    Change-Id: Iba90a9f38c61ea361f990ed1d5cb8859b93a32ba
    Reviewed-on: https://code.wireshark.org/review/27902
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit c74ba09dcf873852d8f359c6c6bf60b917750360)
    Reviewed-on: https://code.wireshark.org/review/27903

commit ff72047c3d
Author: Peter Wu <<EMAIL>>
Date:   Tue May 15 16:08:04 2018 +0200

    tvbuff_zlib: reject negative lengths to avoid buffer overrun
    
    Negative lengths and empty buffers are not uncompressable, reject them.
    A buffer overrun (read) could occur otherwise due to unsigned "avail_in"
    becoming insanely large.
    
    Bug: 14675
    Change-Id: I20b686cc6ad6ef8a8d1975ed3d2f52c8eb1f1c76
    Link: https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=7166
    Reviewed-on: https://code.wireshark.org/review/27561
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 9ee790e99c72ddb5b599b8076b4ecf74611b184e)
    Reviewed-on: https://code.wireshark.org/review/27899
    Reviewed-by: Peter Wu <<EMAIL>>

commit 46e28166a0
Author: Peter Wu <<EMAIL>>
Date:   Tue May 29 12:27:19 2018 +0200

    RPC-over-RDMA: fix infinite loop
    
    When there are two segments, but zero requests, "i=1" will be reset to
    "i=0" and an infinite loop occurs.
    
    Change-Id: I32cb387ce0724936bcb5d5832b1c90d2bc585998
    Fixes: v2.5.2rc0-100-g8f0f691312 ("RPC-over-RDMA: add reassembly for reply, read and write chunks")
    Link: https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=7138
    Reviewed-on: https://code.wireshark.org/review/27891
    Reviewed-by: Jaap Keuter <<EMAIL>>
    Petri-Dish: Jaap Keuter <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit d2671e6b6ac3557dc605cd0e32e45eca2858a440)
    Reviewed-on: https://code.wireshark.org/review/27898

commit abcfec4be1
Author: Peter Wu <<EMAIL>>
Date:   Sat May 26 12:15:16 2018 +0200

    Qt: select a better default Decode As table
    
    USB has three possible tables, usb.device, usb.product and usb.protocol
    (shown in that order in the Decode As dialog). For single packets with
    no prior device descriptors, the last two tables have no valid selector
    (integer zero). In such cases it seems more reasonable to use tables for
    which a valid selector exists (for example, "usb.device").
    
    Bug: 14717
    Change-Id: I2319817fa11318a97519d8cfc912343b16224c12
    Reviewed-on: https://code.wireshark.org/review/27820
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit ea763a96387d9f56d5325d5dcd96048666636aab)
    Reviewed-on: https://code.wireshark.org/review/27890
    Reviewed-by: Peter Wu <<EMAIL>>

commit e84f5f8053
Author: Dario Lombardo <<EMAIL>>
Date:   Fri May 25 19:50:35 2018 +0200

    wsutil: fix compilation on freebsd.
    
    Change-Id: Iadb3f3daa1095ac9cc971f79ba4d288e8d835a44
    Reviewed-on: https://code.wireshark.org/review/27864
    Petri-Dish: Dario Lombardo <<EMAIL>>
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit fab15cd64ff6d59b27883d8f5bd870f146545250)
    Reviewed-on: https://code.wireshark.org/review/27882

commit 3c5737771f
Author: Guy Harris <<EMAIL>>
Date:   Mon May 28 11:37:20 2018 -0700

    Indicate *which* field would go past the MAX_TREE_ITEMS limit.
    
    That could help find the problem if all we have is the error message, as
    it'd at least indicate where the problem is occurring.
    
    Change-Id: I01154ff62088a4b710c131cb153e8e4593ebc3b2
    Reviewed-on: https://code.wireshark.org/review/27878
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 24394835935e3036545bb84306ef4d5f0d91bf93)
    Reviewed-on: https://code.wireshark.org/review/27879

commit 0109a15956
Author: Guy Harris <<EMAIL>>
Date:   Mon May 28 11:22:52 2018 -0700

    Fix test for early exit from MMSE dissector.
    
    We don't need to dissect the body if 1) we're *not* building a protocol
    tree (tree == NULL) *and* 2) the PDU doesn't have content that can be
    handed off to subdissectors.
    
    (Fix which vs. that issue in a comment while we're at it.)
    
    Change-Id: I90890975c05e72cc9ebc776a21683905828f57b5
    Reviewed-on: https://code.wireshark.org/review/27876
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 91c069d5b3dd4736653287a9e454b06354564011)
    Reviewed-on: https://code.wireshark.org/review/27877

commit d8a077c03d
Author: Pascal Quantin <<EMAIL>>
Date:   Mon May 28 17:52:30 2018 +0200

    GTK: fix compilation error introduced in ga42dbe842c
    
    While we are at it, let's fix a segmentation fault when preparing the
    SCTP filter (Qt code was correct).
    
    Change-Id: Iaf1be4d2935b211914a842f551fce870bfa7a364
    Reviewed-on: https://code.wireshark.org/review/27874
    Petri-Dish: Pascal Quantin <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Pascal Quantin <<EMAIL>>

commit b16f3c7b89
Author: Pascal Quantin <<EMAIL>>
Date:   Sat May 26 13:35:16 2018 +0200

    SCTP: plug the remaining memleaks in the tap
    
    Change-Id: I50da5f8c54c7be49ad8339d22639605daf2c5616
    Reviewed-on: https://code.wireshark.org/review/27821
    Reviewed-by: Pascal Quantin <<EMAIL>>
    Petri-Dish: Pascal Quantin <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit e299b4098b0677bb1598bfe332fc8be6ea10fb99)
    Conflicts:
            ui/tap-sctp-analysis.c
    Reviewed-on: https://code.wireshark.org/review/27866
    Reviewed-by: Anders Broman <<EMAIL>>

commit a42dbe842c
Author: Pascal Quantin <<EMAIL>>
Date:   Fri May 25 18:54:53 2018 +0200

    SCTP: fix crash when filtering an association
    
    Do not free a tsn_t element if it has already been inserted in a GList.
    The code structure is complex enough to add an explicit check before
    calling g_free().
    Fixes a regression introduced in gb19ca06fcc.
    
    While we are at it, let's call the correct free function and plug some
    memory leaks.
    
    Bug: 14733
    Change-Id: I071da96982da569083fd98b790e0d37ac0826ff1
    Reviewed-on: https://code.wireshark.org/review/27808
    Reviewed-by: Pascal Quantin <<EMAIL>>
    Petri-Dish: Pascal Quantin <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit 06879e89c04ed8edf7c9a02805ff0adf781bf151)
    Conflicts:
            ui/tap-sctp-analysis.c
    Reviewed-on: https://code.wireshark.org/review/27865
    Reviewed-by: Anders Broman <<EMAIL>>

commit b7125bcb97
Author: Alexis La Goutte <<EMAIL>>
Date:   Sun May 27 12:02:03 2018 +0200

    ICMPv6: fix wrong tree for some bitmask field
    
    Ping-Bug: 14755
    Fixes: v2.3.0rc0-1236-gdcb49539d ("ICMPv6: Convert flag fields to use proto_tree_add_bitmask")
    Change-Id: I5ba03391739b34fcba60f636d87d46ec5a3f7660
    Reviewed-on: https://code.wireshark.org/review/27847
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 20db0cafec0c7a249f32d789d077b20f2553d41c)
    Reviewed-on: https://code.wireshark.org/review/27859

commit 8e7679b36a
Author: Alexis La Goutte <<EMAIL>>
Date:   Sun May 27 11:51:49 2018 +0200

    ICMPv6: fix malformed packet when there is Use Prefix Part Label (Router Renumbering)
    
    Bug: 14755
    Change-Id: Ie7edc6d7eeb74b968f1358d980bdb86f41ae85ec
    Reviewed-on: https://code.wireshark.org/review/27846
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit e54c0477bc12b8b46c21b95660f421b71e9db1d0)
    Reviewed-on: https://code.wireshark.org/review/27857

commit a64ad5439c
Author: Mikael Kanstrup <<EMAIL>>
Date:   Wed May 23 17:10:27 2018 +0200

    androiddump: Fix tcpdump encap type when libwiretap is used
    
    androiddump determines encap type by parsing pcap global header from
    tcpdump binary output. Only when libpcap is used with androiddump
    this (pcap linktype) value can be used right away. Libwiretap
    uses its own encap values but is feed with pcap linktype values
    making the whole packet encapsulation invalid.
    
    Fix by converting pcap linktype values into wiretap encap values
    
    (For pcap linktype definitions see http://www.tcpdump.org/linktypes.html)
    (For wtap encap defininitions see wiretap/wtap.h)
    
    Fixes: bfef57ebb7 ("androiddump: Fix and simplify tcpdump capture")
    Change-Id: I5ea86204b8e5774fd84d4007db8c2910680dbd53
    Reviewed-on: https://code.wireshark.org/review/27747
    Petri-Dish: Anders Broman <<EMAIL>>
    Reviewed-by: Florian Bezold <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Roland Knall <<EMAIL>>
    (cherry picked from commit afd0eef4f63839b30711cba6d0ebe334ff2ef450)
    Reviewed-on: https://code.wireshark.org/review/27771
    Reviewed-by: Anders Broman <<EMAIL>>

commit fa315d638e
Author: Guy Harris <<EMAIL>>
Date:   Sun May 27 19:42:35 2018 -0700

    Make sure the offset doesn't overflow.
    
    It'd only overflow if the value is *so* large that it won't fit in the
    packet (given that we have a 2^32-1-byte limit on the packet size), so
    use tvb_ensure_bytes_exist() to ensure we have the entire value before
    processing the value.
    
    (The real problem is that we don't handle the case where there's a value
    multiplicity > 1 for some types; in those cases, we should loop,
    processing all the values, which would cause us to eventually throw an
    exception when we ran past the end of the packet.  This is just a quick
    fix.)
    
    Bug: 14742
    Change-Id: I447ece81d1c84d3b1d218faeb2c155f910208c29
    Reviewed-on: https://code.wireshark.org/review/27853
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 25118898a931589cfdea4693e056a02b7daecf10)
    Reviewed-on: https://code.wireshark.org/review/27854

commit 083e569105
Author: Guy Harris <<EMAIL>>
Date:   Sun May 27 11:55:40 2018 -0700

    Fix incorrect formatting of NSAP area addresses/address prefixes.
    
    If the area address/address prefix is 13 octets long, use the 13th
    octet, not the 21st octet; the latter is *not* part of the area
    address/address prefix, and might either not be in the packet or might
    be some random other part of the packet.
    
    Add/expand comments while we're at it.
    
    Bug: 14744
    Change-Id: I7b90318a72a49b67d8ec17952add528185fd064b
    Reviewed-on: https://code.wireshark.org/review/27848
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 101b692569f17d14f7165446a8a2ae26415057cb)
    Reviewed-on: https://code.wireshark.org/review/27849

commit 13d58c8c5f
Author: tchang <<EMAIL>>
Date:   Fri May 25 15:38:30 2018 +0200

    6LoRH: fix in IP-in-IP header dissector
    
    Change-Id: Iaba22d9b431a097eea4bb0dbea3c514a607ef001
    Reviewed-on: https://code.wireshark.org/review/27805
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Alexis La Goutte <<EMAIL>>
    (cherry picked from commit 07f7008e4cea4eb3fa57d94e55f0f4330d176d57)
    Reviewed-on: https://code.wireshark.org/review/27819
    Petri-Dish: Alexis La Goutte <<EMAIL>>
    Reviewed-by: Anders Broman <<EMAIL>>

commit d4bd609e7d
Author: Gerald Combs <<EMAIL>>
Date:   Sun May 27 08:29:30 2018 +0000

    [Automatic update for 2018-05-27]
    
    Update manuf, services enterprise numbers, translations, and other items.
    
    Change-Id: I1c965ee7870ca5a8603f6d41b7f361fa5a02ccf8
    Reviewed-on: https://code.wireshark.org/review/27841
    Reviewed-by: Gerald Combs <<EMAIL>>

commit 3ffd9649a5
Author: Guy Harris <<EMAIL>>
Date:   Sat May 26 10:53:03 2018 -0700

    Fix STUN-over-SSL/TLS/DTLS.
    
    Different dissectors are required for protocols running atop SSL/TLS and
    protocols running atop DTLS - SSL/TLS provides a byte-stream service, so
    there's no guarantee that there's a correspondence between SSL/TLS
    application data record boundaries and packet boundaries, but DTLS
    provides a datagram service, with packet boundaries corresponding to
    application data record boundaries.
    
    This is similar to the difference between dissectors for protocols
    running atop TCP and protocols running atop protocols such as UDP.
    
    So have two separate tables mapping Application-Layer Protocol
    Negotiation (ALPN) Protocol IDs to dissector names - one for SSL/TLS and
    one for DTLS.
    
    There are both "over a byte-stream protocol" and "over a packet-oriented
    protocol" dissectors for STUN and TURN ChannelData packets.  Register
    the "over a byte-stream protocol" ones by name, and use the appropriate
    ones in the appropriate tables.  (There is not one named "stun", so the
    STUN dissector wouldn't have been called at all.)
    
    Change-Id: I054e169f6ae3291abdc7eb58918ef65a17c90a63
    Reviewed-on: https://code.wireshark.org/review/27822
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 59b2ea5129bfa3046ee2ffc21a6f2564ecbf78f0)
    Reviewed-on: https://code.wireshark.org/review/27823

commit 275bd75a31
Author: Peter Wu <<EMAIL>>
Date:   Thu May 24 13:57:37 2018 +0200

    wslua: fix NSTime:__tostring for negative values
    
    "nstime_t{A, B}" is defined as "A + B * 10^9" rather than an integer
    part A and fractional part B.
    
    Bug: 14720
    Change-Id: I5321db7d5ecea8f976291d2a22667b02162194e2
    Reviewed-on: https://code.wireshark.org/review/27775
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit a1e5a157802428cee7c01ada4def7c818ebfea82)
    Reviewed-on: https://code.wireshark.org/review/27814
    Reviewed-by: Peter Wu <<EMAIL>>

commit 25d0ea6476
Author: Peter Wu <<EMAIL>>
Date:   Mon May 21 22:33:37 2018 +0200

    Qt: fix use-after-free on error while saving exported packets
    
    When an error occurs while saving packets using the Export Specified
    Packets dialog (e.g. try to overwrite the opened capture file), the
    dialog is displayed again. As PacketRangeGroupBox freed the packet
    selection range, a crash (use-after-free) occurs.
    
    Removes some unnecessary code in MainWindow::exportDissections as well.
    
    Change-Id: I63898427eff7e71799d89c8a22246db8f93a9ff6
    Fixes: v2.5.0rc0-968-g38b40acb2d ("Qt: fix a memory leak when exporting packets")
    Reviewed-on: https://code.wireshark.org/review/27695
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Stig Bjørlykke <<EMAIL>>
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit b078310bd0daf7cf8ba6df765cba9fcd98713dd8)
    Reviewed-on: https://code.wireshark.org/review/27813
    Reviewed-by: Peter Wu <<EMAIL>>

commit 37d1cf1e7c
Author: Peter Wu <<EMAIL>>
Date:   Fri May 25 00:23:01 2018 +0200

    smb: fix wrong exported smb2 object due to hash collision
    
    While SMB1 FIDs are 16-bit, those used in SMB2 are a GUID of 128-bit
    which are compressed down to 32-bit using g_str_hash. To reduce
    collision probability which could associate wrong file data with an
    Exported Object entry, do not truncate this hash value to 16 bits.
    
    Bug: 14662
    Change-Id: I2a353eca96b0f5ed2157f3678280642151e2e4e7
    Reviewed-on: https://code.wireshark.org/review/27794
    Petri-Dish: Peter Wu <<EMAIL>>
    Petri-Dish: Richard Sharpe <<EMAIL>>
    Reviewed-by: Richard Sharpe <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    (cherry picked from commit 67051511eda77d96fe58d7c1b883ccb4999b97ca)
    Reviewed-on: https://code.wireshark.org/review/27806
    Reviewed-by: Peter Wu <<EMAIL>>

commit 01232935a4
Author: Stig Bjørlykke <<EMAIL>>
Date:   Thu May 24 21:02:26 2018 +0200

    media_type: Default decode application/octet-stream as data
    
    Remove registering media_type application/octet-stream from both
    thread and uasip because the settings will interfere each other.
    
    Enable decoding as for media_type instead.
    
    Bug: 14729
    Change-Id: I58c527977fe4713418219fc3126ce7a93c4bb641
    Reviewed-on: https://code.wireshark.org/review/27789
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 0362891ecfaba58b1e82fe9e5c579396f81c28bb)
    Reviewed-on: https://code.wireshark.org/review/27798
    Petri-Dish: Anders Broman <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>

commit 224bd77be5
Author: Gerald Combs <<EMAIL>>
Date:   Thu May 24 11:35:45 2018 -0700

    Asciidoctor: Remove underscores from macro names.
    
    AsciiDoc allows dashes in macro names but not underscores. Current
    versions of AsciiDoctor allow the inverse. Remove underscores to allow
    for easier copying and pasting.
    
    Remove asciidoc.conf while we're here. It's no longer used.
    
    Conflicts:
            docbook/release-notes.asciidoc
    
    Change-Id: I32d8a4ec695b9e17a80ac720ee9faf62dbb362d3
    (cherry picked from commit 585f45cb36853fd3eeaa5b0e2d046f8a818d9acd)
    Reviewed-on: https://code.wireshark.org/review/27790
    Petri-Dish: Gerald Combs <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Gerald Combs <<EMAIL>>

commit 61438fed96
Author: Alexis La Goutte <<EMAIL>>
Date:   Thu May 24 17:48:50 2018 +0000

    Makefile (ui/cli): fix duplicat entry for tap-endpoints.
    
    Bug: 14637
    Change-Id: I0f271170ce8abcc027a568b01db2bdd8ace56b84
    Reviewed-on: https://code.wireshark.org/review/27783
    Reviewed-by: Anders Broman <<EMAIL>>

commit 305f63f021
Author: Peter Wu <<EMAIL>>
Date:   Thu May 24 01:01:57 2018 +0200

    6lowpan: fix reassembly for forwarded packets
    
    With 6LoWPAN mesh routing over 802.15.4 networks, the same packet will
    be forwarded over different links. Such duplicate datagram tags will
    result in collisions (wrong "Reasssembled in" and reassembly failure).
    
    Incorporate the link-layer address to differentiate fragments in
    forwarded packets, in addition to the datagram tag.
    
    Bug: 14700
    Change-Id: I8e4adae54b3e903cfcd74844c6bbe0ecf17eefda
    Reviewed-on: https://code.wireshark.org/review/27761
    Petri-Dish: Peter Wu <<EMAIL>>
    Reviewed-by: Eduardo Montoya Marín <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Robert Sauter <<EMAIL>>
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 06d6fbfdc145a647900c0b03661d05e6722b8313)
    Reviewed-on: https://code.wireshark.org/review/27777
    Reviewed-by: Peter Wu <<EMAIL>>

commit 9ecc1078e9
Author: Stig Bjørlykke <<EMAIL>>
Date:   Thu May 24 11:19:34 2018 +0200

    coap: Revert "Store ctype values in transaction tracking"
    
    This change was based on a incomplete/incorrect implementation of
    LwM2M and is not correct because the payload encoding is mandatory
    in the response.
    
    This reverts commit 2dd789638d310ff6078032f8116fd8513f66aac3.
    
    Change-Id: I89ae1f84e2735ad049a0f7c9045175940bed25cb
    Reviewed-on: https://code.wireshark.org/review/27772
    Petri-Dish: Stig Bjørlykke <<EMAIL>>
    Reviewed-by: Stig Bjørlykke <<EMAIL>>

commit 65bd1c9089
Author: Pascal Quantin <<EMAIL>>
Date:   Wed May 23 21:51:45 2018 +0200

    IEEE 1722: rename IEC subdissector from 61883 to iec61883
    
    Otherwise it can collide with the value 61883 typed in a display filter.
    See https://ask.wireshark.org/question/3177 for details.
    
    Change-Id: Ida4872bc4e4b56cb096079a0f0c42e11856e228c
    Reviewed-on: https://code.wireshark.org/review/27754
    Petri-Dish: Pascal Quantin <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit 949bb98319c60a8c72449d4e8d301a321556ebbc)
    Reviewed-on: https://code.wireshark.org/review/27767
    Reviewed-by: Pascal Quantin <<EMAIL>>

commit 76fcdbc93f
Author: Stig Bjørlykke <<EMAIL>>
Date:   Wed May 23 20:15:32 2018 +0200

    Qt: Support search without a selected packet
    
    Support doing Find Packet, search for next/previous marked packet and
    search for next/previous time reference without having a packet selected
    in the packet list.
    
    Change-Id: I648b26365385d98155e905cda270e9e785b9f1da
    Reviewed-on: https://code.wireshark.org/review/27752
    Petri-Dish: Stig Bjørlykke <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 1b6c702226a6b84ad89b2acc444254b93bf616b7)
    Reviewed-on: https://code.wireshark.org/review/27762
    Petri-Dish: Anders Broman <<EMAIL>>
    Reviewed-by: Stig Bjørlykke <<EMAIL>>

commit e28f397717
Author: Stig Bjørlykke <<EMAIL>>
Date:   Wed May 23 08:27:20 2018 +0200

    Qt: Update menus when mark/ignore/timeref packet
    
    Update menus when changing mark, ignore or time reference on a packet
    to enable/disable entries accordingly.
    
    Change-Id: Icb95b3371fe8183cdeea1b6b2c71d1d33f998dc0
    Reviewed-on: https://code.wireshark.org/review/27737
    Petri-Dish: Stig Bjørlykke <<EMAIL>>
    Reviewed-by: Anders Broman <<EMAIL>>
    (cherry picked from commit 603fe4c7ad9c3bd39975afb8cc836cf1942b659f)
    Reviewed-on: https://code.wireshark.org/review/27743
    Reviewed-by: Stig Bjørlykke <<EMAIL>>

commit 4318549f09
Author: Gerald Combs <<EMAIL>>
Date:   Tue May 22 15:36:03 2018 -0700

    CMake: Search for asciidoctor-pdf separate from asciidoctor.
    
    Search for asciidoctorj and asciidoctor-pdf and set
    ASCIIDOCTOR_PDF_EXECUTABLE if either are found. Only enable the PDF
    targets if we find ASCIIDOCTOR_PDF_EXECUTABLE.
    
    Manual backport of geed4961387.
    
    Change-Id: Idd3c19791dfdca950053f60a59751ca04e976b1a
    Reviewed-on: https://code.wireshark.org/review/27729
    Reviewed-by: Gerald Combs <<EMAIL>>
    Petri-Dish: Gerald Combs <<EMAIL>>
    Reviewed-by: Anders Broman <<EMAIL>>

commit 66c0bfa1d6
Author: Peter Wu <<EMAIL>>
Date:   Sat May 19 00:27:51 2018 +0200

    rnsap: fix use-after-free of "obj_id"
    
    dissect_PrivateIEFieldValue could use "obj_id" after it was freed. Use
    per-packet info instead of globals to avoid such dangling pointers and
    erase any previous state to avoid interference in the same packet.
    
    Change-Id: I7376210ef02a8e781b5a34858ae47d2254c74948
    Link: https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=4311
    Reviewed-on: https://code.wireshark.org/review/27650
    Petri-Dish: Peter Wu <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Darien Spencer <<EMAIL>>
    Reviewed-by: Peter Wu <<EMAIL>>
    (cherry picked from commit 8fdaeb80e81dca1cd7c6af3fba8648b664fb7141)
    Reviewed-on: https://code.wireshark.org/review/27705

commit ed580c77c2
Author: Stig Bjørlykke <<EMAIL>>
Date:   Tue May 22 14:28:45 2018 +0200

    Qt: Default no selection in UAT dialog
    
    Do not start editing the first column for the first item in a UAT dialog.
    This is the same behaviour as in UAT frame.
    
    Change-Id: If8dd90c90301b4af9108cc5286d99f83f613a19e
    Reviewed-on: https://code.wireshark.org/review/27708
    Petri-Dish: Stig Bjørlykke <<EMAIL>>
    Tested-by: Petri Dish Buildbot
    Reviewed-by: Stig Bjørlykke <<EMAIL>>
    (cherry picked from commit eadfd6d3c3742665d76f75da429b86dd16912b4b)
    Reviewed-on: https://code.wireshark.org/review/27738

commit e37b2c432c
Author: Guy Harris <<EMAIL>>
Date:   Sun May 20 01:44:15 2018 -0700

    Do IP address resolution synchronously before printing in TShark.
    
    Otherwise, the first N packets printed, for a value of N dependent on
    various factors, won't get IP addresses resolved to names, even if the
    user wants them resolved.  Unlike Wireshark, which, when a name is
    resolved in asynchronously, can go back and fix up the displayed packet
    when the asynchronous operation completes, once TShark has written
    packet data to the standard output, it can't go back and fix it if it
    did name resolution asynchronously.
    
    Bug: 14711
    Change-Id: I8ebbd83103e5780c903b5560e01b7b92fa39c924
    Reviewed-on: https://code.wireshark.org/review/27668
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 0542c5b7009e4b6cd4e7760a504b16e30b6b3bf4)
    Reviewed-on: https://code.wireshark.org/review/27734

commit f0e98f97e0
Author: Guy Harris <<EMAIL>>
Date:   Tue May 22 20:42:28 2018 -0700

    Creat protocol tvbuff before allocating and freeing memory.
    
    That way, if the attempt to create the protocol tvbuff throws an
    exception, we won't leak the protocol representation string, as we won't
    even try to allocate it.
    
    Bug: 14719
    Change-Id: Id2855bc97e71aa0682737d1a04486a2a01f5f1e6
    Reviewed-on: https://code.wireshark.org/review/27730
    Reviewed-by: Guy Harris <<EMAIL>>
    (cherry picked from commit 0f1f1d0ab827243d3e639ec1d00693e0d2edb883)
    Reviewed-on: https://code.wireshark.org/review/27731

commit cc42ff324f
Author: Gerald Combs <<EMAIL>>
Date:   Tue May 22 12:58:02 2018 -0700

    2.6.1 → 2.6.2.
    
    Change-Id: I0e58217c49e9bcd12c6a8e0b39381936bfabd6e5
    Reviewed-on: https://code.wireshark.org/review/27722
    Reviewed-by: Gerald Combs <<EMAIL>>
