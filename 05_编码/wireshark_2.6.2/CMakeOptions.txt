# Build options for use by CMake

option(BUILD_wireshark     "Build Wireshark" ON)               # mark by zhengsw@20:50 2018/08/09: should not build wireshark which need qt.
option(BUILD_wireshark_gtk "Build Wireshark (GTK+ UI)" OFF)
option(BUILD_tshark        "Build tshark" ON)
option(BUILD_tfshark       "Build tfshark" OFF)
option(BUILD_rawshark      "Build rawshark" ON)
option(BUILD_dumpcap       "Build dumpcap" ON)
option(BUILD_text2pcap     "Build text2pcap" ON)
option(BUILD_mergecap      "Build mergecap" ON)
option(BUILD_reordercap    "Build reordercap" ON)
option(BUILD_editcap       "Build editcap" ON)
option(BUILD_capinfos      "Build capinfos" ON)
option(BUILD_captype       "Build captype" ON)
option(BUILD_randpkt       "Build randpkt" ON)
option(BUILD_dftest        "Build dftest" ON)
option(BUILD_corbaidl2wrs  "Build corbaidl2wrs" OFF)
option(BUILD_dcerpcidl2wrs "Build dcerpcidl2wrs" ON)
option(BUILD_xxx2deb       "Build xxx2deb" OFF)
option(BUILD_androiddump   "Build androiddump" ON)
option(BUILD_sshdump       "Build sshdump" ON)
option(BUILD_ciscodump     "Build ciscodump" ON)
option(BUILD_randpktdump   "Build randpktdump" ON)
option(BUILD_udpdump       "Build udpdump" ON)
option(BUILD_sharkd        "Build sharkd" ON)
if(WIN32)
	option(BUILD_fuzzshark     "Build fuzzshark" OFF)
else()
	option(BUILD_fuzzshark     "Build fuzzshark" ON)
endif()
option(BUILD_mmdbresolve   "Build MaxMind DB resolver" ON)

option(DISABLE_WERROR    "Do not treat warnings as errors" ON)
option(DISABLE_FRAME_LARGER_THAN_WARNING "Disable warning if the size of a function frame is large" OFF)
option(EXTCAP_ANDROIDDUMP_LIBPCAP    "Build androiddump using libpcap" OFF)
option(ENABLE_EXTRA_COMPILER_WARNINGS "Do additional compiler warnings (disables -Werror)" OFF)
option(ENABLE_CODE_ANALYSIS "Enable the compiler's static analyzer if possible" OFF)
option(ENABLE_ASAN "Enable AddressSanitizer (ASAN) for debugging (degrades performance)" OFF)
option(ENABLE_TSAN "Enable ThreadSanitizer (TSan) for debugging" OFF)
option(ENABLE_UBSAN "Enable UndefinedBehaviorSanitizer (UBSan) for debugging" OFF)
option(ENABLE_CHECKHF_CONFLICT "Enable hf conflict check for debugging (start-up may be slower)" OFF)
option(ENABLE_CCACHE "Speed up compiling and linking using ccache if possible" OFF)

#
# Leave GTK2 the default on Windows, looks better than GTK3
#
if(WIN32)
	option(ENABLE_GTK3       "Use GTK3 instead of GTK2 to build wireshark" OFF)
else()
	option(ENABLE_GTK3       "Use GTK3 instead of GTK2 to build wireshark" ON)
endif()
option(ENABLE_QT5        "Use Qt5 instead of Qt4 to build wireshark" ON)
option(ENABLE_PCAP       "Enable libpcap support (required for capturing)" ON)
#
# AirPcap support is available only on Windows.  It might be nice to have it
# check whether the AirPcap SDK is available, and turn AirPcap support on
# only if it is.
#
if(WIN32)
	option(ENABLE_AIRPCAP    "Enable AirPcap support" ON)
endif()
option(ENABLE_STATIC            "Build Wireshark libraries statically" OFF)          # zhengsw: should build staticly for deploying.
option(ENABLE_PLUGINS           "Build with plugins" ON)
option(ENABLE_PLUGIN_IFDEMO     "Build with plugin interface demo" OFF)
option(ENABLE_PCAP_NG_DEFAULT   "Enable pcapng as default file format" ON)

option(ENABLE_PORTAUDIO  "Build with PortAudio support" ON)
option(ENABLE_ZLIB       "Build with zlib compression support" ON)
option(ENABLE_LZ4        "Build with LZ4 compression support" ON)
option(ENABLE_SNAPPY     "Build with Snappy compression support" ON)
option(ENABLE_NGHTTP2    "Build with HTTP/2 header decompression support" ON)
option(ENABLE_LUA        "Build with Lua dissector support" ON)
option(ENABLE_SMI        "Build with libsmi snmp support" ON)
option(ENABLE_GNUTLS     "Build with GNU TLS support" ON)
if(WIN32)
	option(ENABLE_WINSPARKLE "Enable WinSparkle support" ON)
endif()
if (NOT WIN32)
	option(ENABLE_CAP        "Build with Posix capabilities support" ON)
endif()
option(ENABLE_CARES      "Build with c-ares support" ON)
if(UNIX)
	# Libnl is Linux-specific.
	if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
		set(_enable_libnl ON)
	endif()
	option(ENABLE_NETLINK    "Build with libnl support" ${_enable_libnl})
endif()
option(ENABLE_KERBEROS   "Build with Kerberos support" ON)                    # zhengsw: should not build packet-kerberos
option(ENABLE_SBC        "Build with SBC Codec support in RTP Player" ON)
option(ENABLE_SPANDSP    "Build with G.722/G.726 codecs support in RTP Player" ON)
option(ENABLE_BCG729     "Build with G.729 codec support in RTP Player" ON)
option(ENABLE_LIBXML2    "Build with libxml2 support" ON)
# How to install
set(DUMPCAP_INSTALL_OPTION   "normal" CACHE STRING "Permissions to install")
set(DUMPCAP_INST_VALS "normal" "suid" "capabilities")
set_property(CACHE DUMPCAP_INSTALL_OPTION PROPERTY STRINGS ${DUMPCAP_INST_VALS})
if(APPLE)
	option(ENABLE_APPLICATION_BUNDLE "Build a macOS application bundle (Wireshark.app)" ON)
endif()
