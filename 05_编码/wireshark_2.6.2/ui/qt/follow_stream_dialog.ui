<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FollowStreamDialog</class>
 <widget class="QDialog" name="FollowStreamDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>594</width>
    <height>620</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Follow Stream</string>
  </property>
  <property name="sizeGripEnabled">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="FollowStreamText" name="teStreamContent">
     <property name="readOnly">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="hintLabel">
     <property name="text">
      <string>Hint.</string>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,0,0,1,0,0">
     <item>
      <widget class="QComboBox" name="cbDirections">
       <property name="sizeAdjustPolicy">
        <enum>QComboBox::AdjustToContents</enum>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>Show and save data as</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="cbCharset">
       <property name="currentIndex">
        <number>-1</number>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="streamNumberSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="streamNumberLabel">
       <property name="text">
        <string>Stream</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QSpinBox" name="streamNumberSpinBox"/>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0,1,0">
     <item>
      <widget class="QLabel" name="lFind">
       <property name="text">
        <string>Find:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="FindLineEdit" name="leFind"/>
     </item>
     <item>
      <widget class="QPushButton" name="bFind">
       <property name="text">
        <string>Find &amp;Next</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="standardButtons">
      <set>QDialogButtonBox::Close|QDialogButtonBox::Help</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>FollowStreamText</class>
   <extends>QTextEdit</extends>
   <header>widgets/follow_stream_text.h</header>
  </customwidget>
  <customwidget>
   <class>FindLineEdit</class>
   <extends>QLineEdit</extends>
   <header>widgets/find_line_edit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
