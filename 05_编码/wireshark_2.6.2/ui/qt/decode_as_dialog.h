/* decode_as_dialog.h
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later*/

#ifndef DECODE_AS_DIALOG_H
#define DECODE_AS_DIALOG_H

#include <config.h>

#include <glib.h>

#include "cfile.h"
#include <ui/qt/models/decode_as_model.h>
#include <ui/qt/models/decode_as_delegate.h>

#include "geometry_state_dialog.h"
#include <QMap>
#include <QAbstractButton>

class QComboBox;

namespace Ui {
class DecodeAsDialog;
}

class DecodeAsDialog : public GeometryStateDialog
{
    Q_OBJECT

public:
    explicit DecodeAsDialog(QWidget *parent = 0, capture_file *cf = NULL, bool create_new = false);
    ~DecodeAsDialog();

private:
    Ui::DecodeAsDialog *ui;

    DecodeAsModel* model_;
    DecodeAsDelegate* delegate_;

    void addRecord(bool copy_from_current = false);
    void applyChanges();
    void fillTable();
    void resizeColumns();

private slots:
    void on_decodeAsTreeView_currentItemChanged(const QModelIndex &current, const QModelIndex &previous);

    void on_newToolButton_clicked();
    void on_deleteToolButton_clicked();
    void on_copyToolButton_clicked();

    void on_buttonBox_clicked(QAbstractButton *button);
};

#endif // DECODE_AS_DIALOG_H

/*
 * Editor modelines
 *
 * Local Variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * ex: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
