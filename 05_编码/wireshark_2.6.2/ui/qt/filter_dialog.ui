<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FilterDialog</class>
 <widget class="QDialog" name="FilterDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>584</width>
    <height>390</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTreeWidget" name="filterTreeWidget">
     <property name="selectionMode">
      <enum>QAbstractItemView::ExtendedSelection</enum>
     </property>
     <property name="textElideMode">
      <enum>Qt::ElideMiddle</enum>
     </property>
     <property name="indentation">
      <number>0</number>
     </property>
     <property name="rootIsDecorated">
      <bool>false</bool>
     </property>
     <column>
      <property name="text">
       <string>Name</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Filter</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,0,0,1">
     <item>
      <widget class="QToolButton" name="newToolButton">
       <property name="toolTip">
        <string>Create a new filter.</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../../image/stock_icons.qrc">
         <normaloff>:/stock/plus-8.png</normaloff>:/stock/plus-8.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="deleteToolButton">
       <property name="toolTip">
        <string>Remove this filter.</string>
       </property>
       <property name="icon">
        <iconset resource="../../image/stock_icons.qrc">
         <normaloff>:/stock/minus-8.png</normaloff>:/stock/minus-8.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="copyToolButton">
       <property name="toolTip">
        <string>Copy this filter.</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../../image/stock_icons.qrc">
         <normaloff>:/stock/copy-8.png</normaloff>:/stock/copy-8.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="ElidedLabel" name="pathLabel">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>1</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
       <property name="openExternalLinks">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Help|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ElidedLabel</class>
   <extends>QLabel</extends>
   <header>widgets/elided_label.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../image/stock_icons.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>FilterDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>FilterDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
