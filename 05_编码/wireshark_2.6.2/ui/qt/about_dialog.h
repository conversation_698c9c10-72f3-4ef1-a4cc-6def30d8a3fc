/* about_dialog.h
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */

#ifndef ABOUT_DIALOG_H
#define ABOUT_DIALOG_H

#include "config.h"

#include <ui/qt/models/astringlist_list_model.h>

#include <QDialog>
#include <QAbstractItemModel>
#include <QModelIndex>
#include <QHash>
#include <QString>
#include <QSortFilterProxyModel>

namespace Ui {
class AboutDialog;
}

class AuthorListModel : public AStringListListModel
{
Q_OBJECT

public:
    explicit AuthorListModel(QObject * parent = Q_NULLPTR);
    virtual ~AuthorListModel();

    QString acknowledgment() const;

protected:
    virtual QStringList headerColumns() const;

private:
    QString acknowledgement_;

};

class PluginListModel : public AStringListListModel
{
    Q_OBJECT
public:
    explicit PluginListModel(QObject * parent = Q_NULLPTR);

    QStringList typeNames() const;

protected:
    virtual QStringList headerColumns() const;

private:
    QStringList typeNames_;
};

class ShortcutListModel : public AStringListListModel
{
    Q_OBJECT
public:
    explicit ShortcutListModel(QObject * parent = Q_NULLPTR);

protected:
    virtual QStringList headerColumns() const;
};

class FolderListModel : public AStringListListModel
{
    Q_OBJECT
public:
    explicit FolderListModel(QObject * parent = Q_NULLPTR);

protected:
    virtual QStringList headerColumns() const;
};

class AboutDialog : public QDialog
{
    Q_OBJECT

public:
    explicit AboutDialog(QWidget *parent = 0);
    ~AboutDialog();

protected:
    virtual void showEvent(QShowEvent *);

private:
    Ui::AboutDialog *ui;

private slots:
    void urlDoubleClicked(const QModelIndex &);
    void handleCopyMenu(QPoint);
    void copyActionTriggered(bool row = false);
    void copyRowActionTriggered();
#ifdef HAVE_LUA
    void on_tblPlugins_doubleClicked(const QModelIndex &index);
#endif
};

#endif // ABOUT_DIALOG_H

/*
 * Editor modelines
 *
 * Local Variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * ex: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
