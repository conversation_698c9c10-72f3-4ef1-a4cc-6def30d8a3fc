<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ColumnEditorFrame</class>
 <widget class="AccordionFrame" name="ColumnEditorFrame">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1018</width>
    <height>34</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Frame</string>
  </property>
  <property name="frameShape">
   <enum>QFrame::NoFrame</enum>
  </property>
  <property name="frameShadow">
   <enum>QFrame::Plain</enum>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1,0,0,0,0,0,2,0,0,0,0,0">
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QLabel" name="label">
     <property name="text">
      <string>Title:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLineEdit" name="titleLineEdit"/>
   </item>
   <item>
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>5</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>Type:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QComboBox" name="typeComboBox"/>
   </item>
   <item>
    <spacer name="horizontalSpacer_3">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>5</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QLabel" name="label_3">
     <property name="text">
      <string>Fields:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="FieldFilterEdit" name="fieldsNameLineEdit"/>
   </item>
   <item>
    <spacer name="horizontalSpacer_4">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>5</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QLabel" name="label_4">
     <property name="text">
      <string>Occurrence:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="SyntaxLineEdit" name="occurrenceLineEdit"/>
   </item>
   <item>
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>5</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>27</height>
      </size>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>AccordionFrame</class>
   <extends>QFrame</extends>
   <header>accordion_frame.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>FieldFilterEdit</class>
   <extends>QLineEdit</extends>
   <header>widgets/field_filter_edit.h</header>
  </customwidget>
  <customwidget>
   <class>SyntaxLineEdit</class>
   <extends>QLineEdit</extends>
   <header>widgets/syntax_line_edit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
