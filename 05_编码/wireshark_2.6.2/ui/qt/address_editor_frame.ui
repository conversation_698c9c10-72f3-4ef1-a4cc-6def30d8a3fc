<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AddressEditorFrame</class>
 <widget class="AccordionFrame" name="AddressEditorFrame">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>833</width>
    <height>34</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Frame</string>
  </property>
  <property name="frameShape">
   <enum>QFrame::NoFrame</enum>
  </property>
  <property name="frameShadow">
   <enum>QFrame::Plain</enum>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,3,0,0,0,0,1,0,0">
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QToolButton" name="nameResolutionPreferencesToolButton">
     <property name="text">
      <string>Name Resolution Preferences…</string>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>81</width>
       <height>5</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QLabel" name="addressLabel">
     <property name="text">
      <string>Address:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QComboBox" name="addressComboBox"/>
   </item>
   <item>
    <spacer name="horizontalSpacer_3">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>10</width>
       <height>5</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QLabel" name="label">
     <property name="text">
      <string>Name:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="SyntaxLineEdit" name="nameLineEdit">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
       <horstretch>1</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>80</width>
       <height>0</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>13</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>27</height>
      </size>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>AccordionFrame</class>
   <extends>QFrame</extends>
   <header>accordion_frame.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>SyntaxLineEdit</class>
   <extends>QLineEdit</extends>
   <header>widgets/syntax_line_edit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
