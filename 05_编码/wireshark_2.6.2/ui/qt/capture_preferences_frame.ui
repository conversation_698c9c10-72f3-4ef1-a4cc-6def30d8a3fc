<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CapturePreferencesFrame</class>
 <widget class="QFrame" name="CapturePreferencesFrame">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>354</width>
    <height>220</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>191</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Frame</string>
  </property>
  <property name="lineWidth">
   <number>0</number>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="label">
     <property name="text">
      <string>Default interface</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="defaultInterfaceSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Fixed</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>18</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QComboBox" name="defaultInterfaceComboBox">
       <property name="editable">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QCheckBox" name="capturePromModeCheckBox">
     <property name="toolTip">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;You probably want to enable this. Usually a network card will only capture the traffic sent to its own network address. If you want to capture all traffic that the network card can &amp;quot;see&amp;quot;, mark this option. See the FAQ for some more details of capturing packets from a switched network.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="text">
      <string>Capture packets in promiscuous mode</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QCheckBox" name="capturePcapNgCheckBox">
     <property name="toolTip">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Capture packets in the next-generation capture file format.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="text">
      <string>Capture packets in pcapng format</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QCheckBox" name="captureRealTimeCheckBox">
     <property name="toolTip">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Update the list of packets while capture is in progress. This can result in dropped packets on high-speed networks.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="text">
      <string>Update list of packets in real time</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QCheckBox" name="captureAutoScrollCheckBox">
     <property name="toolTip">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Keep the packet list scrolled to the bottom while capturing.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="text">
      <string>Automatic scrolling in live capture</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QCheckBox" name="captureNoExtcapCheckBox">
     <property name="text">
      <string>Disable external capture interfaces</string>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>3</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
