<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DisplayFilterExpressionDialog</class>
 <widget class="QDialog" name="DisplayFilterExpressionDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>657</width>
    <height>588</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QLabel" name="fieldLabel">
         <property name="toolTip">
          <string>Select a field to start building a display filter.</string>
         </property>
         <property name="text">
          <string>Field Name</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTreeWidget" name="fieldTreeWidget">
         <property name="uniformRowHeights">
          <bool>true</bool>
         </property>
         <property name="headerHidden">
          <bool>true</bool>
         </property>
         <column>
          <property name="text">
           <string notr="true">1</string>
          </property>
         </column>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="searchLabel">
           <property name="toolTip">
            <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Search the list of field names.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
           </property>
           <property name="text">
            <string>Search:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="searchLineEdit"/>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_6" stretch="0,1,0,4,1,0">
       <item>
        <layout class="QVBoxLayout" name="relationLayout">
         <item>
          <widget class="QLabel" name="relationLabel">
           <property name="toolTip">
            <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Relations can be used to restrict fields to specific values. Each relation does the following:&lt;/p&gt;&lt;table border=&quot;0&quot; style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px;&quot; cellspacing=&quot;2&quot; cellpadding=&quot;0&quot;&gt;&lt;tr&gt;&lt;td&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;is present&lt;/span&gt;&lt;/p&gt;&lt;/td&gt;&lt;td&gt;&lt;p&gt;Match any packet that contains this field&lt;/p&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;==, !=, etc.&lt;/span&gt;&lt;/p&gt;&lt;/td&gt;&lt;td&gt;&lt;p&gt;Compare the field to a specific value.&lt;/p&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;contains, matches&lt;/span&gt;&lt;/p&gt;&lt;/td&gt;&lt;td&gt;&lt;p&gt;Check the field against a string (contains) or a regular expression (matches)&lt;/p&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;in&lt;/span&gt;&lt;/p&gt;&lt;/td&gt;&lt;td&gt;&lt;p&gt;Compare the field to a specific set of values&lt;/p&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;&lt;/body&gt;&lt;/html&gt;

</string>
           </property>
           <property name="text">
            <string>Relation</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QListWidget" name="relationListWidget"/>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>12</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QVBoxLayout" name="valueLayout">
         <item>
          <widget class="QLabel" name="valueLabel">
           <property name="toolTip">
            <string>Match against this value.</string>
           </property>
           <property name="text">
            <string>Value</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="valueLineEdit"/>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="enumLayout">
         <item>
          <widget class="QLabel" name="enumLabel">
           <property name="toolTip">
            <string>If the field you have selected has a known set of valid values they will be listed here.</string>
           </property>
           <property name="text">
            <string>Predefined Values</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QListWidget" name="enumListWidget"/>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>12</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QVBoxLayout" name="rangeLayout">
         <item>
          <widget class="QLabel" name="rangeLabel">
           <property name="toolTip">
            <string>If the field you have selected covers a range of bytes (e.g. you have selected a protocol) you can restrict the match to a range of bytes here.</string>
           </property>
           <property name="text">
            <string>Range (offset:length)</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="rangeLineEdit"/>
         </item>
        </layout>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="DisplayFilterEdit" name="displayFilterLineEdit">
     <property name="readOnly">
      <bool>true</bool>
     </property>
     <property name="placeholderText">
      <string>No display filter</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="hintLabel">
     <property name="text">
      <string>&lt;small&gt;&lt;i&gt;A hint.&lt;/i&gt;&lt;/small&gt;</string>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Help|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>DisplayFilterEdit</class>
   <extends>QLineEdit</extends>
   <header>widgets/display_filter_edit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>DisplayFilterExpressionDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>DisplayFilterExpressionDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
