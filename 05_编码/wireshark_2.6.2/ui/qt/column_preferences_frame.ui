<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ColumnPreferencesFrame</class>
 <widget class="QFrame" name="ColumnPreferencesFrame">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>550</width>
    <height>350</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="MinimumExpanding" vsizetype="MinimumExpanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Frame</string>
  </property>
  <property name="lineWidth">
   <number>0</number>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTreeWidget" name="columnTreeWidget">
     <column>
      <property name="text">
       <string>Displayed</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Title</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Type</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Fields</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Field Occurrence</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QToolButton" name="newToolButton">
       <property name="icon">
        <iconset resource="../../image/stock_icons.qrc">
         <normaloff>:/stock/plus-8.png</normaloff>:/stock/plus-8.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="deleteToolButton">
       <property name="icon">
        <iconset resource="../../image/stock_icons.qrc">
         <normaloff>:/stock/minus-8.png</normaloff>:/stock/minus-8.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../image/stock_icons.qrc"/>
 </resources>
 <connections/>
</ui>
