<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CaptureFilePropertiesDialog</class>
 <widget class="QDialog" name="CaptureFilePropertiesDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>799</width>
    <height>585</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="locale">
   <locale language="English" country="UnitedStates"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="opaqueResize">
      <bool>false</bool>
     </property>
     <property name="childrenCollapsible">
      <bool>false</bool>
     </property>
     <widget class="QWidget" name="">
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QLabel" name="detailsLabel">
         <property name="text">
          <string>Details</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTextEdit" name="detailsTextEdit">
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="">
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QLabel" name="commentsLabel">
         <property name="text">
          <string>Capture file comments</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTextEdit" name="commentsTextEdit">
         <property name="sizeIncrement">
          <size>
           <width>0</width>
           <height>10</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="standardButtons">
      <set>QDialogButtonBox::Apply|QDialogButtonBox::Close|QDialogButtonBox::Help|QDialogButtonBox::Reset|QDialogButtonBox::Save</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
