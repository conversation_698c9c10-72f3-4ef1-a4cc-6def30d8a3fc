<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BluetoothHciSummaryDialog</class>
 <widget class="QDialog" name="BluetoothHciSummaryDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>880</width>
    <height>477</height>
   </rect>
  </property>
  <property name="baseSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Bluetooth HCI Summary</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTreeWidget" name="tableTreeWidget">
     <property name="contextMenuPolicy">
      <enum>Qt::CustomContextMenu</enum>
     </property>
     <property name="editTriggers">
      <set>QAbstractItemView::NoEditTriggers</set>
     </property>
     <property name="showDropIndicator" stdset="0">
      <bool>false</bool>
     </property>
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
     <property name="selectionMode">
      <enum>QAbstractItemView::ExtendedSelection</enum>
     </property>
     <property name="textElideMode">
      <enum>Qt::ElideMiddle</enum>
     </property>
     <property name="indentation">
      <number>30</number>
     </property>
     <property name="rootIsDecorated">
      <bool>true</bool>
     </property>
     <property name="uniformRowHeights">
      <bool>false</bool>
     </property>
     <property name="itemsExpandable">
      <bool>true</bool>
     </property>
     <property name="sortingEnabled">
      <bool>false</bool>
     </property>
     <property name="animated">
      <bool>false</bool>
     </property>
     <property name="allColumnsShowFocus">
      <bool>false</bool>
     </property>
     <property name="headerHidden">
      <bool>false</bool>
     </property>
     <attribute name="headerCascadingSectionResizes">
      <bool>false</bool>
     </attribute>
     <attribute name="headerDefaultSectionSize">
      <number>100</number>
     </attribute>
     <attribute name="headerHighlightSections">
      <bool>false</bool>
     </attribute>
     <attribute name="headerMinimumSectionSize">
      <number>100</number>
     </attribute>
     <attribute name="headerShowSortIndicator" stdset="0">
      <bool>false</bool>
     </attribute>
     <attribute name="headerStretchLastSection">
      <bool>true</bool>
     </attribute>
     <column>
      <property name="text">
       <string>Name</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>OGF</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>OCF</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Opcode</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Event</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Subevent</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Status</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Reason</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Hardware Error</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Occurrence</string>
      </property>
     </column>
     <item>
      <property name="text">
       <string>Link Control Commands</string>
      </property>
      <property name="text">
       <string>0x01</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Link Policy Commands</string>
      </property>
      <property name="text">
       <string>0x02</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Controller &amp; Baseband Commands</string>
      </property>
      <property name="text">
       <string>0x03</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Informational Parameters</string>
      </property>
      <property name="text">
       <string>0x04</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Status Parameters</string>
      </property>
      <property name="text">
       <string>0x05</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Testing Commands</string>
      </property>
      <property name="text">
       <string>0x06</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>LE Controller Commands</string>
      </property>
      <property name="text">
       <string>0x08</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Bluetooth Logo Testing Commands</string>
      </property>
      <property name="text">
       <string>0x3E</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Vendor-Specific Commands</string>
      </property>
      <property name="text">
       <string>0x3F</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Unknown OGF</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Events</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Status</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Reason</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Hardware Errors</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </item>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="resultsFilterHorizontalLayout">
     <property name="topMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="resultsFilterLabel">
       <property name="text">
        <string>Results filter:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="resultsFilterLineEdit"/>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,0,0">
     <property name="spacing">
      <number>-1</number>
     </property>
     <property name="sizeConstraint">
      <enum>QLayout::SetDefaultConstraint</enum>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>Display filter:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="DisplayFilterEdit" name="displayFilterLineEdit"/>
     </item>
     <item>
      <widget class="QComboBox" name="interfaceComboBox">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>350</width>
         <height>0</height>
        </size>
       </property>
       <item>
        <property name="text">
         <string>All Interfaces</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="adapterComboBox">
       <property name="minimumSize">
        <size>
         <width>320</width>
         <height>0</height>
        </size>
       </property>
       <item>
        <property name="text">
         <string>All Adapters</string>
        </property>
       </item>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="hintLabel">
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Close</set>
     </property>
    </widget>
   </item>
  </layout>
  <action name="actionCopy_Cell">
   <property name="text">
    <string>Copy Cell</string>
   </property>
  </action>
  <action name="actionCopy_Rows">
   <property name="text">
    <string>Copy Rows</string>
   </property>
  </action>
  <action name="actionCopy_All">
   <property name="text">
    <string>Copy All</string>
   </property>
  </action>
  <action name="actionSave_as_image">
   <property name="text">
    <string>Save as image</string>
   </property>
  </action>
  <action name="actionMark_Unmark_Row">
   <property name="text">
    <string>Mark/Unmark Row</string>
   </property>
   <property name="toolTip">
    <string>Mark/Unmark Row</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+M</string>
   </property>
  </action>
  <action name="actionMark_Unmark_Cell">
   <property name="text">
    <string>Mark/Unmark Cell</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>DisplayFilterEdit</class>
   <extends>QLineEdit</extends>
   <header>widgets/display_filter_edit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>BluetoothHciSummaryDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>374</x>
     <y>407</y>
    </hint>
    <hint type="destinationlabel">
     <x>374</x>
     <y>214</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>BluetoothHciSummaryDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>374</x>
     <y>407</y>
    </hint>
    <hint type="destinationlabel">
     <x>374</x>
     <y>214</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
