<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FontColorPreferencesFrame</class>
 <widget class="QFrame" name="FontColorPreferencesFrame">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>540</width>
    <height>390</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>540</width>
    <height>390</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Frame</string>
  </property>
  <property name="lineWidth">
   <number>0</number>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>Main window font:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="fontPushButton">
       <property name="text">
        <string>Select Font</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLineEdit" name="fontSampleLineEdit">
     <property name="text">
      <string/>
     </property>
     <property name="readOnly">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_3">
     <property name="text">
      <string>Colors:</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QGridLayout" name="gridLayout">
     <item row="1" column="1">
      <widget class="QPushButton" name="ignoredBGPushButton">
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="2">
      <widget class="QLineEdit" name="ignoredSampleLineEdit">
       <property name="text">
        <string>Sample ignored packet text</string>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QPushButton" name="clientBGPushButton">
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="0" column="2">
      <widget class="QLineEdit" name="markedSampleLineEdit">
       <property name="text">
        <string>Sample marked packet text</string>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QPushButton" name="clientFGPushButton">
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="2" column="2">
      <widget class="QLineEdit" name="clientSampleLineEdit">
       <property name="text">
        <string>Sample &quot;Follow Stream&quot; client text</string>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QPushButton" name="serverFGPushButton">
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="3" column="2">
      <widget class="QLineEdit" name="serverSampleLineEdit">
       <property name="text">
        <string>Sample &quot;Follow Stream&quot; server text</string>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="0" column="0">
      <widget class="QPushButton" name="markedFGPushButton">
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QPushButton" name="ignoredFGPushButton">
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="3" column="1">
      <widget class="QPushButton" name="serverBGPushButton">
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QPushButton" name="markedBGPushButton">
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="4" column="2">
      <widget class="QLineEdit" name="validFilterSampleLineEdit">
       <property name="text">
        <string>Sample valid filter</string>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="5" column="2">
      <widget class="QLineEdit" name="invalidFilterSampleLineEdit">
       <property name="text">
        <string>Sample invalid filter</string>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="6" column="2">
      <widget class="QLineEdit" name="deprecatedFilterSampleLineEdit">
       <property name="text">
        <string>Sample warning filter</string>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="4" column="1">
      <widget class="QPushButton" name="validFilterBGPushButton">
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="5" column="1">
      <widget class="QPushButton" name="invalidFilterBGPushButton">
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="6" column="1">
      <widget class="QPushButton" name="deprecatedFilterBGPushButton">
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>178</width>
       <height>13</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <connections/>
</ui>
