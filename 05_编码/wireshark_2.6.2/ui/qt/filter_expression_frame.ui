<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FilterExpressionFrame</class>
 <widget class="AccordionFrame" name="FilterExpressionFrame">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>796</width>
    <height>82</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>82</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Frame</string>
  </property>
  <property name="frameShape">
   <enum>QFrame::NoFrame</enum>
  </property>
  <property name="frameShadow">
   <enum>QFrame::Plain</enum>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_5">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <widget class="QToolButton" name="filterExpressionPreferencesPushButton">
       <property name="text">
        <string>Filter Buttons Preferences…</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>5</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="labelLabel">
           <property name="text">
            <string>Label:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="SyntaxLineEdit" name="labelLineEdit">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
             <horstretch>1</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>0</height>
            </size>
           </property>
           <property name="placeholderText">
            <string>Enter a description for the filter button</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="filterLabel">
           <property name="text">
            <string>Filter:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="DisplayFilterEdit" name="displayFilterLineEdit">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
             <horstretch>1</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>0</height>
            </size>
           </property>
           <property name="placeholderText">
            <string>Enter a filter expression to be applied</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <widget class="QLabel" name="commentLabel">
         <property name="text">
          <string>Comment:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="SyntaxLineEdit" name="commentLineEdit">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
           <horstretch>1</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>80</width>
           <height>0</height>
          </size>
         </property>
         <property name="placeholderText">
          <string>Enter a comment for the filter button</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>AccordionFrame</class>
   <extends>QFrame</extends>
   <header>accordion_frame.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>SyntaxLineEdit</class>
   <extends>QLineEdit</extends>
   <header>widgets/syntax_line_edit.h</header>
  </customwidget>
  <customwidget>
   <class>DisplayFilterEdit</class>
   <extends>QLineEdit</extends>
   <header>widgets/display_filter_edit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
