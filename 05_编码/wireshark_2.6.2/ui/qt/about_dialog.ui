<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AboutDialog</class>
 <widget class="QDialog" name="AboutDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>740</width>
    <height>610</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>About Wireshark</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_6">
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="minimumSize">
      <size>
       <width>371</width>
       <height>231</height>
      </size>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <property name="documentMode">
      <bool>false</bool>
     </property>
     <widget class="QWidget" name="tab_wireshark">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <attribute name="title">
       <string>Wireshark</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout" stretch="0,1,0,1">
       <item>
        <widget class="QLabel" name="label_logo">
         <property name="text">
          <string/>
         </property>
         <property name="pixmap">
          <pixmap resource="../../image/about.qrc">:/about/wssplash.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>false</bool>
         </property>
         <property name="alignment">
          <set>Qt::AlignHCenter|Qt::AlignTop</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_title">
         <property name="text">
          <string>&lt;span size=\&quot;x-large\&quot; weight=\&quot;bold\&quot;&gt;Network Protocol Analyzer&lt;/span&gt;</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_wireshark">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_authors">
      <attribute name="title">
       <string>Authors</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QLineEdit" name="searchAuthors">
         <property name="placeholderText">
          <string>Search Authors</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTreeView" name="tblAuthors">
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="selectionMode">
          <enum>QAbstractItemView::ContiguousSelection</enum>
         </property>
         <property name="selectionBehavior">
          <enum>QAbstractItemView::SelectRows</enum>
         </property>
         <attribute name="headerMinimumSectionSize">
          <number>100</number>
         </attribute>
         <attribute name="headerStretchLastSection">
          <bool>true</bool>
         </attribute>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_folders">
      <attribute name="title">
       <string>Folders</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QLineEdit" name="searchFolders">
         <property name="placeholderText">
          <string>Filter by path</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTreeView" name="tblFolders">
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="selectionMode">
          <enum>QAbstractItemView::ContiguousSelection</enum>
         </property>
         <property name="selectionBehavior">
          <enum>QAbstractItemView::SelectRows</enum>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_plugins">
      <attribute name="title">
       <string>Plugins</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_4">
       <item>
        <widget class="QLabel" name="label_no_plugins">
         <property name="text">
          <string>No plugins found.</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <widget class="QLineEdit" name="searchPlugins">
           <property name="placeholderText">
            <string>Search Plugins</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>Filter by type:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="cmbType"/>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QTreeView" name="tblPlugins">
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="selectionMode">
          <enum>QAbstractItemView::ContiguousSelection</enum>
         </property>
         <property name="selectionBehavior">
          <enum>QAbstractItemView::SelectRows</enum>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_shortcuts">
      <attribute name="title">
       <string>Keyboard Shortcuts</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_shortcuts">
       <item>
        <widget class="QLineEdit" name="searchShortcuts">
         <property name="placeholderText">
          <string>Search Shortcuts</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTreeView" name="tblShortcuts">
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="selectionMode">
          <enum>QAbstractItemView::ContiguousSelection</enum>
         </property>
         <property name="selectionBehavior">
          <enum>QAbstractItemView::SelectRows</enum>
         </property>
         <property name="textElideMode">
          <enum>Qt::ElideRight</enum>
         </property>
         <property name="wordWrap">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_acknowledgements">
      <attribute name="title">
       <string>Acknowledgments</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_8">
       <item>
        <widget class="QPlainTextEdit" name="pte_Authors">
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_license">
      <attribute name="title">
       <string>License</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_5">
       <item>
        <widget class="QPlainTextEdit" name="pte_License">
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../image/about.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>AboutDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>254</x>
     <y>595</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>AboutDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>322</x>
     <y>595</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
