<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BluetoothDevicesDialog</class>
 <widget class="QDialog" name="BluetoothDevicesDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>880</width>
    <height>477</height>
   </rect>
  </property>
  <property name="baseSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Bluetooth Devices</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTreeWidget" name="tableTreeWidget">
     <property name="contextMenuPolicy">
      <enum>Qt::CustomContextMenu</enum>
     </property>
     <property name="selectionMode">
      <enum>QAbstractItemView::ExtendedSelection</enum>
     </property>
     <property name="textElideMode">
      <enum>Qt::ElideMiddle</enum>
     </property>
     <property name="rootIsDecorated">
      <bool>false</bool>
     </property>
     <property name="itemsExpandable">
      <bool>false</bool>
     </property>
     <property name="sortingEnabled">
      <bool>true</bool>
     </property>
     <attribute name="headerCascadingSectionResizes">
      <bool>false</bool>
     </attribute>
     <attribute name="headerHighlightSections">
      <bool>false</bool>
     </attribute>
     <attribute name="headerShowSortIndicator" stdset="0">
      <bool>true</bool>
     </attribute>
     <column>
      <property name="text">
       <string>BD_ADDR</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>OUI</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Name</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>LMP Version</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>LMP Subversion</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Manufacturer</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>HCI Version</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>HCI Revision</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Is Local Adapter</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0">
     <property name="spacing">
      <number>-1</number>
     </property>
     <property name="sizeConstraint">
      <enum>QLayout::SetDefaultConstraint</enum>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QComboBox" name="interfaceComboBox">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>350</width>
         <height>0</height>
        </size>
       </property>
       <item>
        <property name="text">
         <string>All Interfaces</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="showInformationStepsCheckBox">
       <property name="text">
        <string>Show information steps</string>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="hintLabel">
     <property name="text">
      <string>%1 items; Right click for more option; Double click for device details</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Close</set>
     </property>
    </widget>
   </item>
  </layout>
  <action name="actionCopy_Cell">
   <property name="text">
    <string>Copy Cell</string>
   </property>
  </action>
  <action name="actionCopy_Rows">
   <property name="text">
    <string>Copy Rows</string>
   </property>
  </action>
  <action name="actionCopy_All">
   <property name="text">
    <string>Copy All</string>
   </property>
  </action>
  <action name="actionSave_as_image">
   <property name="text">
    <string>Save as image</string>
   </property>
  </action>
  <action name="actionMark_Unmark_Row">
   <property name="text">
    <string>Mark/Unmark Row</string>
   </property>
   <property name="toolTip">
    <string>Mark/Unmark Row</string>
   </property>
   <property name="shortcut">
    <string>CtrlM</string>
   </property>
  </action>
  <action name="actionMark_Unmark_Cell">
   <property name="text">
    <string>Mark/Unmark Cell</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>BluetoothDevicesDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>374</x>
     <y>407</y>
    </hint>
    <hint type="destinationlabel">
     <x>374</x>
     <y>214</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>BluetoothDevicesDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>374</x>
     <y>407</y>
    </hint>
    <hint type="destinationlabel">
     <x>374</x>
     <y>214</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
