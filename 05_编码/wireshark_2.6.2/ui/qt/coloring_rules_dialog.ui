<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ColoringRulesDialog</class>
 <widget class="QDialog" name="ColoringRulesDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>650</width>
    <height>480</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="TabnavTreeView" name="coloringRulesTreeView">
     <property name="selectionMode">
      <enum>QAbstractItemView::ExtendedSelection</enum>
     </property>
     <property name="textElideMode">
      <enum>Qt::ElideMiddle</enum>
     </property>
     <property name="rootIsDecorated">
      <bool>false</bool>
     </property>
     <property name="uniformRowHeights">
      <bool>true</bool>
     </property>
     <property name="itemsExpandable">
      <bool>false</bool>
     </property>
     <property name="expandsOnDoubleClick">
      <bool>false</bool>
     </property>
     <property name="dragEnabled">
      <bool>true</bool>
     </property>
     <property name="DropIndicatorShown">
      <bool>true</bool>
     </property>
     <property name="dragDropMode">
      <enum>QAbstractItemView::InternalMove</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="hintLabel">
     <property name="text">
      <string>&lt;small&gt;&lt;i&gt;A hint.&lt;/i&gt;&lt;/small&gt;</string>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,0,0,0,0,1">
     <item>
      <widget class="QToolButton" name="newToolButton">
       <property name="toolTip">
        <string>Add a new coloring rule.</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../../image/stock_icons.qrc">
         <normaloff>:/stock/plus-8.png</normaloff>:/stock/plus-8.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="deleteToolButton">
       <property name="toolTip">
        <string>Delete this coloring rule.</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../../image/stock_icons.qrc">
         <normaloff>:/stock/minus-8.png</normaloff>:/stock/minus-8.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="copyToolButton">
       <property name="toolTip">
        <string>Duplicate this coloring rule.</string>
       </property>
       <property name="icon">
        <iconset resource="../../image/stock_icons.qrc">
         <normaloff>:/stock/copy-8.png</normaloff>:/stock/copy-8.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="fGPushButton">
       <property name="toolTip">
        <string>Set the foreground color for this rule.</string>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="text">
        <string>Foreground</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
       <property name="visible">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="bGPushButton">
       <property name="toolTip">
        <string>Set the background color for this rule.</string>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton { border: 1px solid palette(Dark); }</string>
       </property>
       <property name="text">
        <string>Background</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
       <property name="visible">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="displayFilterPushButton">
       <property name="toolTip">
        <string>Set the display filter using this rule.</string>
       </property>
       <property name="text">
        <string>Apply as filter</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
       <property name="visible">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Help|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>TabnavTreeView</class>
   <extends>QTreeView</extends>
   <header>widgets/tabnav_tree_view.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../image/stock_icons.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>ColoringRulesDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>ColoringRulesDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
