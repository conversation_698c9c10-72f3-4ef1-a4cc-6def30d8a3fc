/* column_editor_frame.h
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later*/

#ifndef COLUMN_EDITOR_FRAME_H
#define COLUMN_EDITOR_FRAME_H

#include "accordion_frame.h"

namespace Ui {
class ColumnEditorFrame;
}

class ColumnEditorFrame : public AccordionFrame
{
    Q_OBJECT

public:
    explicit ColumnEditorFrame(QWidget *parent = 0);
    ~ColumnEditorFrame();
    void editColumn(int column);

signals:
    void columnEdited();
    void pushFilterSyntaxStatus(const QString&);

protected:
    virtual void showEvent(QShowEvent *event);
    virtual void keyPressEvent(QKeyEvent *event);

private slots:
    void on_typeComboBox_activated(int index);
    void on_fieldsNameLineEdit_textEdited(const QString &fields);
    void on_occurrenceLineEdit_textE<PERSON>(const QString &occurrence);
    void on_buttonBox_rejected();
    void on_buttonBox_accepted();

private:
    bool syntaxIsValid(void);
    Ui::ColumnEditorFrame *ui;
    int cur_column_;
    QString saved_fields_;
    QString saved_occurrence_;
    void setFields(int index);
};

#endif // COLUMN_EDITOR_FRAME_H

/*
 * Editor modelines
 *
 * Local Variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * ex: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
