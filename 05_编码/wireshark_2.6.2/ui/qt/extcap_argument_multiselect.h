/* extcap_argument_multiselect.h
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later*/

#ifndef UI_QT_EXTCAP_ARGUMENT_MULTISELECT_H_
#define UI_QT_EXTCAP_ARGUMENT_MULTISELECT_H_

#include <QObject>
#include <QWidget>
#include <QStandardItem>
#include <QTreeView>
#include <QAbstractItemModel>
#include <QItemSelection>

#include <extcap_parser.h>
#include <extcap_argument.h>

class ExtArgMultiSelect : public ExtcapArgument
{
    Q_OBJECT
public:
    ExtArgMultiSelect(extcap_arg * argument);
    virtual ~ExtArgMultiSelect();

    virtual QString value();
    virtual bool isValid();

protected:
    virtual QList<QStandardItem *> valueWalker(ExtcapValueList list, QStringList &defaults);
    void selectItemsWalker(QStandardItem * item, QStringList defaults);
    virtual QWidget * createEditor(QWidget * parent);

private Q_SLOTS:

    void selectionChanged(const QItemSelection & selected, const QItemSelection & deselected);

private:

    QTreeView * treeView;
    QAbstractItemModel * viewModel;

};

#endif /* UI_QT_EXTCAP_ARGUMENT_MULTISELECT_H_ */

/*
 * Editor modelines
 *
 * Local Variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * ex: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
