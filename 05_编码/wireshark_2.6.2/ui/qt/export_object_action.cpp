/* conversation_colorize_action.cpp
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later*/

#include <config.h>

#include <glib.h>
#include <epan/packet_info.h>
#include <epan/proto_data.h>
#include <epan/packet.h>
#include <wsutil/utf8_entities.h>
#include "export_object_action.h"

#include <QMenu>

#include <ui/qt/utils/qt_ui_utils.h>

ExportObjectAction::ExportObjectAction(QObject *parent, register_eo_t *eo) :
    QAction(parent),
    eo_(eo)
{
    if (eo_) {
          setText( QString("%1%2").arg(proto_get_protocol_short_name(find_protocol_by_id(get_eo_proto_id(eo)))).arg(UTF8_HORIZONTAL_ELLIPSIS));
    }
}

void ExportObjectAction::captureFileEvent(CaptureEvent e)
{
    if ( e.captureContext() == CaptureEvent::File )
    {
        if ( e.eventType() == CaptureEvent::Opened )
            setEnabled(true);
        else if ( e.eventType() == CaptureEvent::Closed )
            setEnabled(false);
    }
}

/*
 * Editor modelines
 *
 * Local Variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * ex: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
