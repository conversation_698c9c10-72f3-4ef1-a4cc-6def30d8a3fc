<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DecodeAsDialog</class>
 <widget class="QDialog" name="DecodeAsDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>750</width>
    <height>460</height>
   </rect>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <widget class="TabnavTreeView" name="decodeAsTreeView">
     <property name="indentation">
      <number>0</number>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,0,0,1">
     <item>
      <widget class="QToolButton" name="newToolButton">
       <property name="toolTip">
        <string>Change the dissection behavior for a protocol.</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../../image/stock_icons.qrc">
         <normaloff>:/stock/plus-8.png</normaloff>:/stock/plus-8.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="deleteToolButton">
       <property name="toolTip">
        <string>Remove this dissection behavior.</string>
       </property>
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="icon">
        <iconset resource="../../image/stock_icons.qrc">
         <normaloff>:/stock/minus-8.png</normaloff>:/stock/minus-8.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="copyToolButton">
       <property name="toolTip">
        <string>Copy this dissection behavior.</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../../image/stock_icons.qrc">
         <normaloff>:/stock/copy-8.png</normaloff>:/stock/copy-8.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="ElidedLabel" name="pathLabel">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>1</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
       <property name="openExternalLinks">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Help|QDialogButtonBox::Ok|QDialogButtonBox::Save</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ElidedLabel</class>
   <extends>QLabel</extends>
   <header>widgets/elided_label.h</header>
  </customwidget>
  <customwidget>
    <class>TabnavTreeView</class>
    <extends>QTreeView</extends>
    <header>widgets/tabnav_tree_view.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../image/stock_icons.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>DecodeAsDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>DecodeAsDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
