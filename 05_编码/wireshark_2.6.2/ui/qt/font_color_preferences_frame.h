/* font_color_preferences_frame.h
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later*/

#ifndef FONT_COLOR_PREFERENCES_FRAME_H
#define FONT_COLOR_PREFERENCES_FRAME_H

#include <QFrame>
#include <QFont>

#include <epan/prefs.h>

namespace Ui {
class FontColorPreferencesFrame;
}

class FontColorPreferencesFrame : public QFrame
{
    Q_OBJECT

public:
    explicit FontColorPreferencesFrame(QWidget *parent = 0);
    ~FontColorPreferencesFrame();

protected:
    void showEvent(QShowEvent *evt);

private:
    Ui::FontColorPreferencesFrame *ui;

    pref_t *pref_qt_gui_font_name_;
    pref_t *pref_marked_fg_;
    pref_t *pref_marked_bg_;
    pref_t *pref_ignored_fg_;
    pref_t *pref_ignored_bg_;
    pref_t *pref_client_fg_;
    pref_t *pref_client_bg_;
    pref_t *pref_server_fg_;
    pref_t *pref_server_bg_;
    pref_t *pref_valid_bg_;
    pref_t *pref_invalid_bg_;
    pref_t *pref_deprecated_bg_;
    QFont cur_font_;

    void updateWidgets();
    void changeColor(pref_t *pref);

private slots:
    void on_fontPushButton_clicked();

    void on_markedFGPushButton_clicked();
    void on_markedBGPushButton_clicked();
    void on_ignoredFGPushButton_clicked();
    void on_ignoredBGPushButton_clicked();
    void on_clientFGPushButton_clicked();
    void on_clientBGPushButton_clicked();
    void on_serverFGPushButton_clicked();
    void on_serverBGPushButton_clicked();
    void on_validFilterBGPushButton_clicked();
    void on_invalidFilterBGPushButton_clicked();
    void on_deprecatedFilterBGPushButton_clicked();
};

#endif // FONT_COLOR_PREFERENCES_FRAME_H
