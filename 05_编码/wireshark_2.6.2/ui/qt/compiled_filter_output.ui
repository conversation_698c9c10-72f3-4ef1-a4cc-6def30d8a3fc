<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CompiledFilterOutput</class>
 <widget class="QDialog" name="CompiledFilterOutput">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>654</width>
    <height>380</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Compiled Filter Output</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,5">
     <item>
      <widget class="QListWidget" name="interfaceList">
       <property name="editTriggers">
        <set>QAbstractItemView::NoEditTriggers</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QTextBrowser" name="filterList"/>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Close</set>
     </property>
     <property name="centerButtons">
      <bool>false</bool>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>CompiledFilterOutput</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>CompiledFilterOutput</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
