/* export_object_ui.h
 * Common routines for tracking & saving objects found in streams of data
 * Copyright 2007, <PERSON> (see AUTHORS file)
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */

#ifndef __EXPORT_OBJECT_UI_H__
#define __EXPORT_OBJECT_UI_H__

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#include <epan/export_object.h>

/* Common between protocols */

gboolean eo_save_entry(const gchar *save_as_filename, export_object_entry_t *entry, gboolean show_err);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __EXPORT_OBJECT_UI_H__ */

/*
 * Editor modelines
 *
 * Local Variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * ex: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
