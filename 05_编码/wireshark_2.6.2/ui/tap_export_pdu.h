/* tap_export_pdu.h
 * Routines for exporting PDUs to file
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later*/

#ifndef __TAP_EXPORT_PDU_H__
#define __TAP_EXPORT_PDU_H__

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef struct _exp_pdu_t {
    int          pkt_encap;
    wtap_dumper* wdh;
} exp_pdu_t;

/**
* Registers the tap listener which will add matching packets to the exported
* file. Must be called before exp_pdu_open.
*
* @param tap_name  One of the names registered with register_export_pdu_tap().
* @param filter    An tap filter, may be NULL to disable filtering which
* improves performance if you do not need a filter.
* @return NULL on success or an error string on failure which must be freed
* with g_free(). Failure could occur when the filter or tap_name are invalid.
*/
char *exp_pdu_pre_open(const char *tap_name, const char *filter,
    exp_pdu_t *exp_pdu_tap_data);

/**
* Use the given file descriptor for writing an output file. Can only be called
* once and exp_pdu_pre_open() must be called before.
*
* @return 0 on success or a wtap error code.
*/
int exp_pdu_open(exp_pdu_t *data, int fd, char *comment);

/* Stops the PDUs export. */
int exp_pdu_close(exp_pdu_t *exp_pdu_tap_data);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __TAP_EXPORT_PDU_H__ */

/*
 * Editor modelines
 *
 * Local Variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * ex: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
