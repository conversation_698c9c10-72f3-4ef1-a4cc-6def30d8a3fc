/* -*-mode: flex-*- */

%top {
/* Include this before everything else, for various large-file definitions */
#include "config.h"
}

/*
 * We want a reentrant scanner.
 */
%option reentrant

/*
 * We don't use input, so don't generate code for it.
 */
%option noinput

/*
 * We don't use unput, so don't generate code for it.
 */
%option nounput

/*
 * We don't read interactively from the terminal.
 */
%option never-interactive

/*
 * We want to stop processing when we get to the end of the input.
 */
%option noyywrap

/*
 * Prefix scanner routines with "text_import_" rather than "yy", so this scanner
 * can coexist with other scanners.
 */
%option prefix="text_import_"

/*
 * We have to override the memory allocators so that we don't get
 * "unused argument" warnings from the yyscanner argument (which
 * we don't use, as we have a global memory allocator).
 *
 * We provide, as macros, our own versions of the routines generated by Flex,
 * which just call malloc()/realloc()/free() (as the Flex versions do),
 * discarding the extra argument.
 */
%option noyyalloc
%option noyyrealloc
%option noyyfree

%{

/********************************************************************************
 *
 * text_import_scanner.l
 * Scanner for text import
 * November 2010, Jaap Keuter <<EMAIL>>
 *
 * Wireshark - Network traffic analyzer
 * By Gerald Combs <<EMAIL>>
 * Copyright 1998 Gerald Combs
 *
 * Based on text2pcap-scanner.l by Ashok Narayanan <<EMAIL>>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 *
 *******************************************************************************/

#include <stdio.h>
#include <stdlib.h>

#include "text_import_scanner.h"

/*
 * Disable diagnostics in the code generated by Flex.
 */
DIAG_OFF_FLEX

/*
 * Flex (v 2.5.35) uses this symbol to "exclude" unistd.h
 */
#ifdef _WIN32
#  define YY_NO_UNISTD_H
#endif

/*
 * Sleazy hack to suppress compiler warnings in yy_fatal_error().
 */
#define YY_EXIT_FAILURE ((void)yyscanner, 2)

/*
 * Macros for the allocators, to discard the extra argument.
 */
#define text_import_alloc(size, yyscanner)		(void *)malloc(size)
#define text_import_realloc(ptr, size, yyscanner)	(void *)realloc((char *)(ptr), (size))
#define text_import_free(ptr, yyscanner)		free((char *)ptr)

%}

hexdigit [0-9A-Fa-f]
directive ^#TEXT2PCAP.*\r?\n
comment ^[\t ]*#.*\r?\n
byte [0-9A-Fa-f][0-9A-Fa-f][ \t]
byte_eol [0-9A-Fa-f][0-9A-Fa-f]\r?\n
offset [0-9A-Fa-f]+[: \t]
offset_eol [0-9A-Fa-f]+\r?\n
text [^ \n\t]+
mailfwd >
eol \r?\n\r?

%%

{byte}            { parse_token(T_BYTE, yytext); }
{byte_eol}        { parse_token(T_BYTE, yytext); parse_token(T_EOL, NULL); }
{offset}          { parse_token(T_OFFSET, yytext); }
{offset_eol}      { parse_token(T_OFFSET, yytext); parse_token(T_EOL, NULL); }
{mailfwd}{offset} { parse_token(T_OFFSET, yytext+1); }
{eol}             { parse_token(T_EOL, NULL); }
[ \t]             ; /* ignore whitespace */
{directive}       { parse_token(T_DIRECTIVE, yytext); parse_token(T_EOL, NULL); }
{comment}         { parse_token(T_EOL, NULL); }
{text}            { parse_token(T_TEXT, yytext); }

<<EOF>>           { write_current_packet(); yyterminate(); }

%%

/*
 * Turn diagnostics back on, so we check the code that we've written.
 */
DIAG_ON_FLEX

int
text_import_scan(FILE *input_file)
{
    yyscan_t scanner;

    if (text_import_lex_init(&scanner) != 0)
        return errno;

    text_import_set_in(input_file, scanner);

    text_import_lex(scanner);

    text_import_lex_destroy(scanner);

    return 0;
}
