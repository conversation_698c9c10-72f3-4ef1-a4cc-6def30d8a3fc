/* capture_globals.h
 * Capture-related globals.
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later*/

#ifndef __CAPTURE_GLOBALS_H__
#define __CAPTURE_GLOBALS_H__

#include "capture_opts.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

extern capture_options global_capture_opts;

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __CAPTURE_GLOBALS_H__ */

/*
 * Editor modelines
 *
 * Local Variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * ex: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
