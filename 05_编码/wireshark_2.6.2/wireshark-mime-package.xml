<?xml version="1.0" encoding="UTF-8"?>

<!--
    This file allows freedesktop.org-compliant desktops (such as GNOME and
    KDE) to know which files should be opened by what program (and what icon
    to show for the file in a file browser).

    Both magic and file name glob patterns are supported; glob patterns are
    preferred since they don't require require opening and reading part of
    the file to determine its type.

    The specification for this file can be found here:
    https://standards.freedesktop.org/shared-mime-info-spec/latest/


    Note that ideally the MIME types listed here should be IANA-registered:

    https://www.iana.org/assignments/media-types/media-types.xhtml

    If your file type is not registered then using one of the
    (not-really-a-good-idea and deprecated) x- media types is the only option
    if you want files to be associated with Wireshark in
    freedesktop.org-compliant file browsers.


    NOTE: if you do add a MIME type here, don't forget to add it to the list
    of MIME types handled by Wireshark in wireshark.desktop.
-->

<mime-info xmlns="http://www.freedesktop.org/standards/shared-mime-info">
  <mime-type type="application/vnd.tcpdump.pcap">
    <comment>Packet Capture (PCAP)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <alias type="application/x-pcap"/>
    <alias type="application/pcap"/>
    <magic>
      <!-- standard PCAP file -->
      <match type="big32"    offset="0" value="0xa1b2c3d4"/>
      <match type="little32" offset="0" value="0xa1b2c3d4"/>
      <!-- extended (Alexey-Kuznetsov's-patches) PCAP file -->
      <match type="big32"    offset="0" value="0xa1b2cd34"/>
      <match type="little32" offset="0" value="0xa1b2cd34"/>
    </magic>
    <glob pattern="*.pcap"/>
    <glob pattern="*.pcap.gz"/>
  </mime-type>

  <mime-type type="application/x-pcapng">
    <comment>Packet Capture (PCAPNG)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="big32"      offset="0" value="0x0a0d0d0a">
	<match type="big32"    offset="8" value="0x1a2b3c4d"/>
      </match>
      <match type="little32"   offset="0" value="0x0a0d0d0a">
	<match type="little32" offset="8" value="0x1a2b3c4d"/>
      </match>
    </magic>
    <glob pattern="*.pcapng"/>
    <glob pattern="*.ntar"/>
    <glob pattern="*.pcapng.gz"/>
    <glob pattern="*.ntar.gz"/>
  </mime-type>

  <mime-type type="application/x-snoop">
    <comment>Packet Capture (Snoop)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="string" offset="0" value="snoop"/>
    </magic>
    <glob pattern="*.snoop"/>
    <glob pattern="*.snoop.gz"/>
  </mime-type>

  <mime-type type="application/x-iptrace">
    <comment>Packet Capture (AIX iptrace)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="string" offset="0" value="iptrace 1.0"/>
      <match type="string" offset="0" value="iptrace 2.0"/>
    </magic>
  </mime-type>

  <mime-type type="application/x-lanalyzer">
    <comment>Packet Capture (Novell LANalyzer)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="little16" offset="0" value="0x1001"/>
      <match type="little16" offset="0" value="0x1007"/>
    </magic>
    <glob pattern="*.tr1"/>
    <glob pattern="*.tr1.gz"/>
  </mime-type>

  <mime-type type="application/x-nettl">
    <comment>Packet Capture (HP-UX nettl)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="string" offset="0" value="\x54\x52\x00\x64\x00"/>
    </magic>
    <glob pattern="*.trc0"/>
    <glob pattern="*.trc1"/>
    <glob pattern="*.trc0.gz"/>
    <glob pattern="*.trc1.gz"/>
  </mime-type>

  <mime-type type="application/x-radcom">
    <comment>Packet Capture (Radcom WAN/LAN Analyzer)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="string" offset="0" value="\x42\xd2\x00\x34\x12\x66\x22\x88"/>
    </magic>
  </mime-type>

  <mime-type type="application/x-etherpeek">
    <comment>Packet Capture (Savvius/Etherpeek/Airopeek tagged/v9)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="string" offset="0" value="\177ver"/>
    </magic>
    <glob pattern="*.apc"/>
    <glob pattern="*.pkt"/>
    <glob pattern="*.tpc"/>
    <glob pattern="*.wpz"/>
    <glob pattern="*.apc.gz"/>
    <glob pattern="*.pkt.gz"/>
    <glob pattern="*.tpc.gz"/>
    <glob pattern="*.wpz.gz"/>
  </mime-type>

  <mime-type type="application/x-visualnetworks">
    <comment>Packet Capture (Visual Networks)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="string" offset="0" value="\x05VNF"/>
    </magic>
  </mime-type>

  <mime-type type="application/x-netinstobserver">
    <comment>Packet Capture (Network Instruments Observer)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="string" offset="0" value="ObserverPktBuffe"/>
    </magic>
    <glob pattern="*.bfr"/>
    <glob pattern="*.bfr.gz"/>
  </mime-type>

  <mime-type type="application/x-5view">
    <comment>Packet Capture (Accellent/InfoVista 5view)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="string" offset="0" value="\xaa\xaa\xaa\xaa"/>
    </magic>
    <glob pattern="*.5vw"/>
    <glob pattern="*.5vw.gz"/>
  </mime-type>

  <mime-type type="application/x-tektronix-rf5">
    <comment>Packet Capture (Tektronix rf5)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="string" offset="0" value="\x00\x00\x02\x00\x12\x05\x00\x10"/>
    </magic>
    <glob pattern="*.rf5"/>
    <glob pattern="*.rf5.gz"/>
  </mime-type>

  <mime-type type="application/x-micropross-mplog">
    <comment>Packet Capture (Micropross mplog)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <magic>
      <match type="string" offset="0" value="MPCSII"/>
    </magic>
    <glob pattern="*.mplog"/>
    <glob pattern="*.mplog.gz"/>
  </mime-type>

  <mime-type type="application/x-apple-packetlogger">
    <comment>Packet Capture (macOS PacketLogger)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <glob pattern="*.pklg"/>
    <glob pattern="*.pklg.gz"/>
  </mime-type>

  <mime-type type="application/x-endace-erf">
    <comment>Packet Capture (Endace ERF)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <glob pattern="*.erf"/>
    <glob pattern="*.erf.gz"/>
  </mime-type>

  <mime-type type="application/ipfix">
    <comment>Packet Capture (IPFIX)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <glob pattern="*.ipfix"/>
    <glob pattern="*.ipfix.gz"/>
    <!-- Don't register for .pfx: that extension has another (more common) use -->
  </mime-type>

  <mime-type type="application/x-ixia-vwr">
    <comment>Packet Capture (Ixia IxVeriWave)</comment>
    <generic-icon name="application-wireshark-doc"/>
    <glob pattern="*.vwr"/>
    <glob pattern="*.vwr.gz"/>
  </mime-type>
</mime-info>
