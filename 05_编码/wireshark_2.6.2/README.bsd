Installing Wireshark on FreeBSD/OpenBSD/NetBSD/DragonFly BSD
========================================================================

     1. Extra packages required
     2. Compiling Wireshark 
     3. Berkeley Packet Filter (BPF) requirement
     4. Running Wireshark as a non-root user


1. Extra packages required
---------------------------
Wireshark requires a number of additional programs to function.
Install the latest versions of the following programs before compiling:

The easiest way to install these is by using your operating system's
ports or packages system.  If you prefer to build from source, the programs
can be found at the following sites:

    glib 2.16 or later:
         ftp.gnome.org:/pub/gnome/sources/glib/
	 http://ftp.gnome.org/pub/gnome/sources/glib/

    pkgconfig:
         http://pkgconfig.freedesktop.org/releases/

    python 2.5 or later:
         https://www.python.org/downloads/source/

If you want to use the Wireshark GUI, install one or both of these toolkits:

   gtk+ 2.12 or later:
         ftp.gnome.org:/pub/gnome/sources/gtk+/
	 http://ftp.gnome.org/pub/gnome/sources/gtk+/

    Qt 4.7 or later:
	 http://download.qt-project.org/official_releases/qt/


(These programs may require additional dependencies)

Additional programs can be used to enhance Wireshark's functionality.
These can be found by typing ./configure --help or looking at the output
at the end of running the configure script.


2. Compiling Wireshark
-----------------------
To compile Wireshark with the default options, run configure, make and
make install (you may have to run "autogen.sh" first):

     ./configure
     make
     make install

The configure and make steps can be run as a non-root user and you can
run Wireshark from the compilation directory itself.  You must run make
install as root in order to copy the program to the proper directories.


3. Berkeley Packet Filter (BPF) requirement
--------------------------------------------
In order to capture packets (with Wireshark/TShark, tcpdump, or any
other packet capture program) on a BSD system, your kernel must have the
Berkeley Packet Filter mechanism enabled.  The default kernel
configurations in recent versions of BSD systems have this enabled
already.  To verify the bpf device is present, look in the /dev
directory:

    ls -l /dev/bpf*

You should see one or more bpf devices listed similar to this:

    crw-------  1 root  wheel    0,  90 Aug 10 21:05 /dev/bpf0
    crw-------  1 root  wheel    0,  91 Aug 10 21:05 /dev/bpf1

Packet-capturing programs will pick the first bpf device that's not in
use.  Recent versions of most BSDs will create bpf devices as needed, so
you don't have to configure the number of devices that will be
available.

4. Running wireshark as a non-root user
-------------------------------------------
Since the bpf devices are read-only by the owner (root), you normally
have to run packet capturing programs such as Wireshark as root.  It is
safer to run programs as a non-root user if possible.  To run Wireshark
as a non-root user, you must change the permissions on the bpf device(s).
If you are the only user that needs to use Wireshark, the easiest way
is to change the owner of each bpf device to your username.  You can also
add the read/write ability to the group (typically wheel) and add users
that need to use Wireshark to the wheel group.  Check your operating
system's documentation on how to make permanent these changes as they
are often reset upon reboot; if /dev is implemented with devfs, it might
be possible to configure devfs to create all bpf devices owned by a
particular user and/or group and with particular permissions.  In
FreeBSD 6.0 and later this can be done by creating an /etc/devfs.rules
file with content such as

	[localrules=10]
	add path 'bpf*' {mode and permissions}

where "mode and permissions" can include clauses such as

	mode {octal permissions}

to set the permissions on the device (e.g., "mode 0660" to set the
permissions to rw-rw-r--),

	user {user}

to set the user who owns the device, or

	group {group}

to set the group that owns the device and adding a line such as

	devfs_system_ruleset=localrules

to /etc/rc.conf.  For example, an /etc/devfs.rules file with

	[localrules=10]
	add path 'bpf*' mode 0660 group wheel

will grant read and write permissions on all BPF devices to all users in
the "wheel" group.
