language: cpp
sudo: required
dist: trusty
os:
  - osx
  - linux
compiler:
  - clang
  - gcc
env:
 - BUILD_CMAKE=yes
 - BUILD_CMAKE=yes CMAKE_OPTS="-DBUILD_wireshark_gtk=ON"
 - BUILD_CMAKE=yes CMAKE_OPTS="-DENABLE_PCAP=OFF"
 - BUILD_CMAKE=no AUTOTOOLS_OPTS="--with-gtk=3"
matrix:
  exclude:
  # Exclude gcc build (Need some work) with osx
  - os: osx
    compiler: gcc
  # Exclude autotools build (missing libtool...) with osx
  - os: osx
    env: BUILD_CMAKE=no AUTOTOOLS_OPTS="--with-gtk=3"
  # Exclude build with GTK+3
  - os: osx
    env: BUILD_CMAKE=yes CMAKE_OPTS="-DBUILD_wireshark_gtk=ON"
before_install:
  - echo $TRAVIS_OS_NAME
  - $CC --version
  # macos
  - if [ "$TRAVIS_OS_NAME" == "osx" ]; then ./tools/macos-setup-brew.sh; fi
  - if [ "$TRAVIS_OS_NAME" == "osx" ]; then PATH=/usr/local/opt/qt5/bin:$PATH; fi
  # linux
  - if [ "$TRAVIS_OS_NAME" == "linux" ]; then sudo add-apt-repository --yes ppa:ubuntu-toolchain-r/test; fi
  - if [ "$TRAVIS_OS_NAME" == "linux" ]; then sudo apt-get update -qq; fi
  # libstdc++-4.8 is needed by Clang to build
  - if [ "$TRAVIS_OS_NAME" == "linux" ]; then sudo apt-get -qq install libstdc++-4.8-dev; fi
  - if [ "$TRAVIS_OS_NAME" == "linux" ]; then sudo ./tools/debian-setup.sh --install-optional -qq; fi
  - $CC --version
before_script:
  - sudo gem install asciidoctor
  - if [ ${BUILD_CMAKE} == "yes" ]; then mkdir build && cd build && cmake ${CMAKE_OPTS} ..; else ./autogen.sh && ./configure ${AUTOTOOLS_OPTS} ; fi
script:
  - make
