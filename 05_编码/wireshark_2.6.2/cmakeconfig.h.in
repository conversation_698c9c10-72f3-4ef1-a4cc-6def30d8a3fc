/* cmakeconfig.h.in */

#ifndef __CONFIG_H__
#define __CONFIG_H__

/* Note: You cannot use earlier #defines in later #cmakedefines (cmake 2.6.2). */

/* Name of package */
#define PACKAGE "wireshark"

#define VERSION_EXTRA "$ENV{WIRESHARK_VERSION_EXTRA}"

/* Version number of package */
#define VERSION "${CPACK_PACKAGE_VERSION}${VERSION_EXTRA}"
#define VERSION_MAJOR ${PROJECT_MAJOR_VERSION}
#define VERSION_MINOR ${PROJECT_MINOR_VERSION}
#define VERSION_MICRO ${PROJECT_PATCH_VERSION}

#define VERSION_RELEASE "${PROJECT_RELEASE_VERSION}"
#define VERSION_FLAVOR "${VERSION_FLAVOR}"

/* FIXME: Move the path stuff to the CMakeInstallDirs.cmake file */
/* Directory for data */
#define DATAFILE_DIR "${CMAKE_INSTALL_PREFIX}/${CMAKE_INSTALL_DATADIR}/${CPACK_PACKAGE_NAME}"

/* Build wsutil with SIMD optimization */
#cmakedefine HAVE_SSE4_2 1

/* Directory where extcap hooks reside */
#define EXTCAP_DIR "${EXTCAP_DIR}"

/* Define to 1 if we want to enable plugins */
#cmakedefine HAVE_PLUGINS 1

/*  Define to 1 if we check hf conflict */
#cmakedefine ENABLE_CHECK_FILTER 1

/* Link Wireshark libraries statically */
#cmakedefine ENABLE_STATIC 1

/* Enable AirPcap */
#cmakedefine HAVE_AIRPCAP 1

/* Define to 1 if you have the <alloca.h> header file. */
#cmakedefine HAVE_ALLOCA_H 1

/* Define to 1 if you have the <arpa/inet.h> header file. */
#cmakedefine HAVE_ARPA_INET_H 1

/* Define to 1 if you have the `bpf_image' function. */
#cmakedefine HAVE_BPF_IMAGE 1

/* Define to use c-ares library */
#cmakedefine HAVE_C_ARES 1

/* Define to 1 if you have the `dladdr' function. */
#cmakedefine HAVE_DLADDR 1

/* Define to 1 if you have the <fcntl.h> header file. */
#cmakedefine HAVE_FCNTL_H 1

/* Define to use the MaxMind DB library */
#cmakedefine HAVE_MAXMINDDB 1

/* Define to 1 if you have the <ifaddrs.h> header file. */
#cmakedefine HAVE_IFADDRS_H 1

/* Define to 1 if you have the `getexecname' function. */
#cmakedefine HAVE_GETEXECNAME 1

/* Define to 1 if you have the `getifaddrs' function. */
#cmakedefine HAVE_GETIFADDRS 1

/* Define if LIBSSH support is enabled */
#cmakedefine HAVE_LIBSSH 1

/* Define if LIBSSH has ssh_userauth_agent() function */
#cmakedefine HAVE_SSH_USERAUTH_AGENT 1

/* Define if you have the 'floorl' function. */
#cmakedefine HAVE_FLOORL 1

/* Define if you have the 'lrint' function. */
#cmakedefine HAVE_LRINT 1

/* Define to 1 if you have the getopt_long function. */
#cmakedefine HAVE_GETOPT_LONG 1

/* Define to 1 if you have the <getopt.h> header file. */
#cmakedefine HAVE_GETOPT_H 1

/* Define to 1 if you have the <grp.h> header file. */
#cmakedefine HAVE_GRP_H 1

/* Define to use heimdal kerberos */
#cmakedefine HAVE_HEIMDAL_KERBEROS 1

/* Define to 1 if you have the `inflatePrime' function. */
#cmakedefine HAVE_INFLATEPRIME 1

/* Define to 1 if you have the `issetugid' function. */
#cmakedefine HAVE_ISSETUGID 1

/* Define to use kerberos */
#cmakedefine HAVE_KERBEROS 1

/* Define to use nghttp2 */
#cmakedefine HAVE_NGHTTP2 1

/* Define to use the libcap library */
#cmakedefine HAVE_LIBCAP 1

/* Define to use GnuTLS library */
#cmakedefine HAVE_LIBGNUTLS 1

/* Enable libnl support */
#cmakedefine HAVE_LIBNL 1

/* libnl version 1 */
#cmakedefine HAVE_LIBNL1 1

/* libnl version 2 */
#cmakedefine HAVE_LIBNL2 1

/* libnl version 3 */
#cmakedefine HAVE_LIBNL3 1

/* Define to use libpcap library */
#cmakedefine HAVE_LIBPCAP 1

/* Define to 1 if you have the `smi' library (-lsmi). */
#cmakedefine HAVE_LIBSMI 1

/* Define to use zlib library */
#cmakedefine HAVE_ZLIB 1

/* Define to use lz4 library */
#cmakedefine HAVE_LZ4 1

/* Define to use snappy library */
#cmakedefine HAVE_SNAPPY 1

/* Define to 1 if you have the <linux/sockios.h> header file. */
#cmakedefine HAVE_LINUX_SOCKIOS_H 1

/* Define to 1 if you have the <linux/if_bonding.h> header file. */
#cmakedefine HAVE_LINUX_IF_BONDING_H 1

/* Define to use Lua */
#cmakedefine HAVE_LUA 1

/* Define to 1 if you have the <lua.h> header file. */
#cmakedefine HAVE_LUA_H 1

/* Define to 1 if you have the <memory.h> header file. */
#cmakedefine HAVE_MEMORY_H 1

/* Define to use MIT kerberos */
#cmakedefine HAVE_MIT_KERBEROS 1

/* Define to 1 if you have the `mkstemps' function. */
#cmakedefine HAVE_MKSTEMPS 1

/* Define to 1 if you have the `mmap' function. */
#cmakedefine HAVE_MMAP 1

/* Define to 1 if you have the `mprotect' function. */
#cmakedefine HAVE_MPROTECT 1

/* Define to 1 if you have the <netdb.h> header file. */
#cmakedefine HAVE_NETDB_H 1

/* Define to 1 if you have the <netinet/in.h> header file. */
#cmakedefine HAVE_NETINET_IN_H 1

/* nl80211.h is new enough */
#cmakedefine HAVE_NL80211 1

/* SET_CHANNEL is supported */
#cmakedefine HAVE_NL80211_CMD_SET_CHANNEL 1

/* SPLIT_WIPHY_DUMP is supported */
#cmakedefine HAVE_NL80211_SPLIT_WIPHY_DUMP 1

/* VHT_CAPABILITY is supported */
#cmakedefine HAVE_NL80211_VHT_CAPABILITY 1

/* Define to 1 if you have macOS frameworks */
#cmakedefine HAVE_MACOS_FRAMEWORKS 1

/* Define to 1 if you have the macOS CFPropertyListCreateWithStream function */
#cmakedefine HAVE_CFPROPERTYLISTCREATEWITHSTREAM 1

/* Define if pcap_breakloop is known */
#cmakedefine HAVE_PCAP_BREAKLOOP 1

/* Define to 1 if you have the `pcap_create' function. */
#cmakedefine HAVE_PCAP_CREATE 1

/* Define to 1 if the capture buffer size can be set. */
#cmakedefine CAN_SET_CAPTURE_BUFFER_SIZE 1

/* Define to 1 if you have the `pcap_datalink_name_to_val' function. */
#cmakedefine HAVE_PCAP_DATALINK_NAME_TO_VAL 1

/* Define to 1 if you have the `pcap_datalink_val_to_description' function. */
#cmakedefine HAVE_PCAP_DATALINK_VAL_TO_DESCRIPTION 1

/* Define to 1 if you have the `pcap_datalink_val_to_name' function. */
#cmakedefine HAVE_PCAP_DATALINK_VAL_TO_NAME 1

/* Define to 1 if you have the `pcap_findalldevs' function and a pcap.h that
   declares pcap_if_t. */
#cmakedefine HAVE_PCAP_FINDALLDEVS 1

/* Define to 1 if you have the `pcap_freecode' function. */
#cmakedefine HAVE_PCAP_FREECODE 1

/* Define to 1 if you have the `pcap_free_datalinks' function. */
#cmakedefine HAVE_PCAP_FREE_DATALINKS 1

/* Define to 1 if you have the `pcap_get_selectable_fd' function. */
#cmakedefine HAVE_PCAP_GET_SELECTABLE_FD 1

/* Define to 1 if you have the `pcap_lib_version' function. */
#cmakedefine HAVE_PCAP_LIB_VERSION 1

/* Define to 1 if you have the `pcap_list_datalinks' function. */
#cmakedefine HAVE_PCAP_LIST_DATALINKS 1

/* Define to 1 if you have the `pcap_open' function. */
#cmakedefine HAVE_PCAP_OPEN 1

/* Define to 1 if you have the `pcap_open_dead' function. */
#cmakedefine HAVE_PCAP_OPEN_DEAD 1

/* Define to 1 if you have libpcap/WinPcap remote capturing support. */
#cmakedefine HAVE_PCAP_REMOTE 1

/* Define to 1 if you have the `pcap_set_datalink' function. */
#cmakedefine HAVE_PCAP_SET_DATALINK 1

/* Define to 1 if you have the `pcap_setsampling' function. */
#cmakedefine HAVE_PCAP_SETSAMPLING 1

/* Define to 1 if you have the `pcap_set_tstamp_precision' function. */
#cmakedefine HAVE_PCAP_SET_TSTAMP_PRECISION 1

/* Define to 1 if you have the `pcap_set_tstamp_type' function. */
#cmakedefine HAVE_PCAP_SET_TSTAMP_TYPE 1

/* Define to 1 if you have the <pwd.h> header file. */
#cmakedefine HAVE_PWD_H 1

/* Define to 1 if you have the optreset variable */
#cmakedefine HAVE_OPTRESET 1

/* Define if sa_len field exists in struct sockaddr */
#cmakedefine HAVE_STRUCT_SOCKADDR_SA_LEN 1

/* Define to 1 if you want to playing SBC by standalone BlueZ SBC library */
#cmakedefine HAVE_SBC 1

/* Define to 1 if you have the SpanDSP library. */
#cmakedefine HAVE_SPANDSP 1

/* Define to 1 if you have the bcg729 library. */
#cmakedefine HAVE_BCG729 1

/* Define to 1 if you have the opus library. */
#cmakedefine HAVE_OPUS 1

/* Define to 1 if you have the lixbml2 library. */
#cmakedefine HAVE_LIBXML2 1

/* Define to 1 if you have the `setresgid' function. */
#cmakedefine HAVE_SETRESGID 1

/* Define to 1 if you have the `setresuid' function. */
#cmakedefine HAVE_SETRESUID 1

/* Define to 1 if you have the WinSparkle library */
#cmakedefine HAVE_SOFTWARE_UPDATE 1

/* Define if you have the 'strptime' function. */
#cmakedefine HAVE_STRPTIME 1

/* Define to 1 if `st_birthtime' is a member of `struct stat'. */
#cmakedefine HAVE_STRUCT_STAT_ST_BIRTHTIME 1

/* Define if st_flags field exists in struct stat */
#cmakedefine HAVE_STRUCT_STAT_ST_FLAGS 1

/* Define to 1 if `__st_birthtime' is a member of `struct stat'. */
#cmakedefine HAVE_STRUCT_STAT___ST_BIRTHTIME 1

/* Define to 1 if you have the <sys/ioctl.h> header file. */
#cmakedefine HAVE_SYS_IOCTL_H 1

/* Define to 1 if you have the <sys/socket.h> header file. */
#cmakedefine HAVE_SYS_SOCKET_H 1

/* Define to 1 if you have the <sys/sockio.h> header file. */
#cmakedefine HAVE_SYS_SOCKIO_H 1

/* Define to 1 if you have the <sys/stat.h> header file. */
#cmakedefine HAVE_SYS_STAT_H 1

/* Define to 1 if you have the <sys/time.h> header file. */
#cmakedefine HAVE_SYS_TIME_H 1

/* Define to 1 if you have the <sys/types.h> header file. */
#cmakedefine HAVE_SYS_TYPES_H 1

/* Define to 1 if you have the <sys/utsname.h> header file. */
#cmakedefine HAVE_SYS_UTSNAME_H 1

/* Define to 1 if you have the <sys/wait.h> header file. */
#cmakedefine HAVE_SYS_WAIT_H 1

/* Define if tm_zone field exists in struct tm */
#cmakedefine HAVE_STRUCT_TM_TM_ZONE 1

/* Define if tzname array exists */
#cmakedefine HAVE_TZNAME 1

/* Define to 1 if you have the <unistd.h> header file. */
#cmakedefine HAVE_UNISTD_H 1

/* Name of package */
#cmakedefine PACKAGE

/* Define to the address where bug reports for this package should be sent. */
#cmakedefine PACKAGE_BUGREPORT

/* Define to the full name of this package. */
#cmakedefine PACKAGE_NAME

/* Define to the full name and version of this package. */
#cmakedefine PACKAGE_STRING

/* Define to the one symbol short name of this package. */
#cmakedefine PACKAGE_TARNAME

/* Define to the version of this package. */
#cmakedefine PACKAGE_VERSION

/* Support for pcapng */
#cmakedefine PCAP_NG_DEFAULT 1

/* Define if we are using version of of the Portaudio library API */
#cmakedefine PORTAUDIO_API_1 1

/* Define if we have QtMultimedia */
#cmakedefine QT_MULTIMEDIA_LIB 1

/* Define if we have QtMacExtras */
#cmakedefine QT_MACEXTRAS_LIB 1

/* Define if we have QtWinExtras */
/* #cmakedefine QT_WINEXTRAS_LIB 1 */

/* Build androiddump with libpcap instead of wireshark stuff */
#cmakedefine ANDROIDDUMP_USE_LIBPCAP 1

/* Large file support */
#cmakedefine _LARGEFILE_SOURCE
#cmakedefine _LARGEFILE64_SOURCE
#cmakedefine _LARGE_FILES
#cmakedefine _FILE_OFFSET_BITS @_FILE_OFFSET_BITS@

/* Define to 1 if `lex' declares `yytext' as a `char *' by default, not a
   `char[]'. */
/* Note: not use in the code */
#cmakedefine YYTEXT_POINTER

#if defined(_WIN32)
   /*
    * Make sure everyone is using the same API and that it's sufficient
    * for our needs.
    * This should match the following:
    * - The <compatibility><application> section in image\wireshark.exe.manifest.in
    * - The GetWindowsVersion parts of packaging\nsis\wireshark.nsi
    * - The VersionNT parts of packaging\wix\Prerequisites.wxi
    */
#  if defined(NTDDI_VERSION)
#    error NTDDI_VERSION already defined.
#  endif
#  define NTDDI_VERSION NTDDI_WIN7
#  if defined(_WIN32_WINNT)
#    error _WIN32_WINNT already defined.
#  endif
#  define _WIN32_WINNT _WIN32_WINNT_WIN7

   /* WpdPack/INclude/pcap/pcap.h checks for "#if defined(WIN32)" */
#  ifndef WIN32
#    define WIN32	1
#  endif

#  if !defined(QT_VERSION) || !defined(_SSIZE_T_DEFINED)
   typedef int ssize_t;
#  endif

   /*
    * Flex (v 2.5.35) uses this symbol to "exclude" unistd.h
    */
#  define YY_NO_UNISTD_H

#  define strncasecmp strnicmp
#  define popen       _popen
#  define pclose      _pclose

#  ifndef __STDC__
#    define __STDC__ 0
#  endif
   /* Use Unicode in Windows runtime functions. */
#  define UNICODE 1
#  define _UNICODE 1

#  define NEED_STRPTIME_H 1
#endif

#if defined(__APPLE__)
   /* This is to trigger the integration of objective-c
    * code builds for removing unnecessary menu entries
    * in 10.12 > and Qt 5.3 >
    */
#  define CMAKE_BUILD 1
#endif

#include <ws_diag_control.h>

#endif /* __CONFIG_H__ */
