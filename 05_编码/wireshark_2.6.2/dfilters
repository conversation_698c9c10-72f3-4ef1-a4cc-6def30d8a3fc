"Ethernet address 00:00:5e:00:53:00" eth.addr == 00:00:5e:00:53:00
"Ethernet type 0x0806 (ARP)" eth.type == 0x0806
"Ethernet broadcast" eth.addr == ff:ff:ff:ff:ff:ff
"No ARP" not arp
"IPv4 only" ip
"IPv4 address *********" ip.addr == *********
"IPv4 address isn't ********* (don't use != for this!)" !(ip.addr == *********)
"IPv6 only" ipv6
"IPv6 address 2001:db8::1" ipv6.addr == 2001:db8::1
"IPX only" ipx
"TCP only" tcp
"UDP only" udp
"Non-DNS" !(udp.port == 53 || tcp.port == 53)
"TCP or UDP port is 80 (HTTP)" tcp.port == 80 || udp.port == 80
"HTTP" http
"No ARP and no DNS" not arp and !(udp.port == 53)
"Non-HTTP and non-SMTP to/from *********" ip.addr == ********* and not tcp.port in {80 25}
