NOTE: this document applies to the Wireshark source releases and
buildbot source tarballs.  It does not apply to source code checked out
directly from Git, as files such as the configuration script are not
checked into Git, but need to be generated from the autoconf and
automake files.

See https://wiki.wireshark.org/Development if you would like to build the
source code checked out directly from Git.

Installation
============

These are installation instructions for Unix and Unix-like systems
that can run the "configure" script in this same directory. These
are not the installation instructions for Windows systems; see
README.windows for those instructions.

0. This is software.  Beware.

1. If you wish to build Wireshark, make sure you have the Qt and GLib
   development packages installed. Try running
   'pkg-config glib-2.0 --modversion' to see if you have GLib 2.x
   installed.  Then try running 'pkg-config Qt5Widgets --modversion'
   to see if you have Qt installed. Wireshark needs version 4.8 or above
   of Qt, although 5.2 and above are strongly recommended. It needs
   version 2.22.0 or above of glib-2.0.  If you need to install or
   re-install GLIB, you can find the packages at:

        https://www.gtk.org

   You can find Qt at:

        https://www.qt.io/download

   If you installed Qt or GLib from binary packages, you may have to
   install corresponding "development" packages; there may be separate
   "user's" and "developer's" packages, with the former not including
   header files and the like.  For example, Red Hat users will need to
   install a "glib2-devel" .rpm.


2. If you wish to build TShark, the line-mode version of Wireshark,
   make sure you have GLib installed.  See note #1 above for instructions
   on checking if you have GLib installed.

3. If you want to capture packets, make sure you have libpcap
   installed.  The latest "official" version can be found at

        http://www.tcpdump.org .

   If you installed libpcap from a binary package, you may have to
   install a "development" package; for example, there's
   apparently a "libpcap0" Debian package, but it just includes a
   shared library, a copyright notice, changelog files, and a
   README.md file - you also need to install a "libpcap-dev" package
   to get header files, a non-shared library, and the man page.
   Similarly, Red Hat users will need to install a "libpcap-devel"
   .rpm to go along with the "libpcap" .rpm.

4. Building Wireshark requires Perl (specifically the pod2man program)
   so that the documentation can be built.

5. Building Wireshark requires Python.

6. Create a build directory separate from the source directory. It can
   be anywhere, but you might run into issues if the path contains
   spaces.

7. Run 'cmake <options> <path/to/the/wireshark/sources>' in your build
   directory. Running 'cmake -LH <path/to/the/wireshark/sources>'
   displays a complete list of options. The "Tool Reference" section of
   Developer's Guide contains general instructions for using CMake. Some
   of the Wireshark-specific options are as follows:

    -G Ninja
        CMake supports many different build systems, including UNIX
        Make, MSBuild, and Ninja. UNIX Make is the default, but Ninja
        tends to be faster.

    -DBUILD_wireshark=OFF
        By default CMake tries to find the Qt libraries so Wireshark,
        the GUI packet analyzer, can be built.  You can disable the
        build of the GUI version of Wireshark with this switch.

    -DBUILD_tshark=OFF
        By default the line-mode packet analyzer, TShark, is built.
        Use this switch to avoid building it.

    -DBUILD_editcap=OFF
        By default the capture-file editing program is built.
        Use this switch to avoid building it.

    -DBUILD_capinfos=OFF
        By default the capture-file statistics reporting pogram
        is built. Use this switch to avoid building it.

    -DBUILD_captype=OFF
        By default the capture-type reporting pogram is built. Use this
        switch to avoid building it.

    -DBUILD_mergecap=OFF
        By default the capture-file merging program is built.
        Use this switch to avoid building it.

    -DBUILD_reordercap=OFF
        By default the capture-file reordering program is built.
        Use this switch to avoid building it.

    -DBUILD_text2pcap=OFF
        By default the hex-dump-to-capture file conversion program
        is built. Use this switch to avoid building it.

    -DBUILD_dftest=OFF
        By default the display-filter-compiler test program is built.
        Use this switch to avoid building it.

    -DBUILD_randpkt=OFF
        By default the program which creates random packet-capture files
        is built. Use this switch to avoid building it.

    -DBUILD_dumpcap=OFF
        By default the network traffic capture program is built.
        Use this switch to avoid building it.

    -DBUILD_rawshark=OFF
        By default the program used to dump and analyze raw libpcap data
        is built. Use this switch to avoid building it.

    -DDUMPCAP_INSTALL_OPTION=suid
    -DDUMPCAP_INSTALL_OPTION=capabilities
        Wireshark and TShark rely on dumpcap for packet capture. Setting
        this flag to "suid" installs dumpcap with setuid root
        permissions, which lets any user on the system capture live
        traffic. If this is not desired, you can restrict dumpcap's
        permissions so that only a single user or group can run it and
        set the "capabilities" flag.

        Running Wireshark or TShark as root is not recommended.

    -DENABLE_CAP=OFF
        By default, if 'cmake' finds libcap (the POSIX capabilities
        library) dumpcap will be built so that if it is installed setuid
        root, it will attempt to retain CAP_NET_RAW and CAP_NET_ADMIN
        before dropping root privileges. Use this option to disable this
        behavior.

    -DENABLE_PCAP=OFF
        If you choose to build a packet analyzer that can analyze
        capture files but cannot capture packets on its own, but you
        *do* have libpcap installed, or if you are trying to build
        Wireshark on a system that doesn't have libpcap installed (in
        which case you have no choice but to build a version that can
        analyze capture files but cannot capture packets on its own),
        use -DENABLE_PCAP=OFF to avoid using libpcap.

    -DENABLE_ZLIB=OFF
        By default, if 'configure' finds zlib (a.k.a, libz), the
        wiretap library will be built so that it can read compressed
        capture files. If you have zlib but do not wish to build
        it into the wiretap library, used by Wireshark, TShark, and
        the capture-file utilities that come in this package, use
        this switch.

    -DENABLE_PLUGINS=OFF
        By default, if your system can support run-time loadable modules,
        the packet analyzers are build with support for plugins.
        Use this switch to build packet analyzers without plugin support.

8. After running 'cmake', you will see a summary of some
   of the options you chose. Ensure that the summary reflects
   what you want. If it doesn't, re-run 'cmake' with new options.

9. Run 'make', or 'ninja' if you chose to create Ninja build files.
   Hopefully, you won't run into any problems.

10. Run './wireshark' or './tshark' or ./dumpcap, and make sure things are
   working. You must have root privileges in order to capture live data.

11./a. Run 'make install'.  If you're running a system that supports
   the RPM packaging systems you can run

        make rpm-package

   or

        ninja rpm-package

   to make an installable package for your system. The installation path
   defaults to /usr/local, so you'll probably want to pass
   -DCMAKE_INSTALL_PREFIX=/usr to CMake.

11/b. If you 're running a system that supports APT (Debian/Ubuntu/etc.)
   run

        dpkg-buildpackage -us -uc -rfakeroot

   in the source directory right after extracting of checking out
   Wireshark's source code. (You don't have to run configure/make/etc.
   prior to running dpkg-buildpackage)


If you have trouble with the build or installation process, you can
find assistance on the wireshark-users and wireshark-dev mailing lists (see
http://www.wireshark.org/lists/ for details) or the Wireshark Q&A site:
https://ask.wireshark.org .
