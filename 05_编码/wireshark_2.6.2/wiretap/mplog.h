/* mplog.h
 *
 * File format support for Micropross mplog files
 * Copyright (c) 2016 by <PERSON> <<EMAIL>>
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */

#ifndef _MPLOG_H
#define _MPLOG_H

#include <glib.h>
#include <wiretap/wtap.h>

wtap_open_return_val mplog_open(wtap *wth, int *err, gchar **err_info);

#endif /* _MPLOG_H */
