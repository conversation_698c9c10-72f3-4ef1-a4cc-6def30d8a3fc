/* nettl.h
 *
 * Wiretap Library
 * Copyright (c) 1998 by <PERSON> <<EMAIL>>
 *
 * Enhancements by <PERSON> <<EMAIL>>
 * Copyright (C) 2003, 2005 Hewlett-Packard Development Company, L.P.
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */

#ifndef __NETTL_H__
#define __NETTL_H__

#include <glib.h>
#include <wiretap/wtap.h>

/* nettl subsystems are defined in /etc/nettlgen.conf */

#define NETTL_SUBSYS_NS_LS_LOGGING	0
#define NETTL_SUBSYS_NS_LS_NFT		1
#define NETTL_SUBSYS_NS_LS_LOOPBACK	2
#define NETTL_SUBSYS_NS_LS_NI		3
#define NETTL_SUBSYS_NS_LS_IPC		4
#define NETTL_SUBSYS_NS_LS_SOCKREGD	5
#define NETTL_SUBSYS_NS_LS_TCP		6
#define NETTL_SUBSYS_NS_LS_PXP		7
#define NETTL_SUBSYS_NS_LS_UDP		8
#define NETTL_SUBSYS_NS_LS_IP		9
#define NETTL_SUBSYS_NS_LS_PROBE	10
#define NETTL_SUBSYS_NS_LS_DRIVER	11
#define NETTL_SUBSYS_NS_LS_RLBD		12
#define NETTL_SUBSYS_NS_LS_BUFS		13
#define NETTL_SUBSYS_NS_LS_CASE21	14
#define NETTL_SUBSYS_NS_LS_ROUTER21	15
#define NETTL_SUBSYS_NS_LS_NFS		16
#define NETTL_SUBSYS_NS_LS_NETISR	17
#define NETTL_SUBSYS_NS_LS_NSE		18
#define NETTL_SUBSYS_NS_LS_STRLOG	19
#define NETTL_SUBSYS_NS_LS_TIRDWR	21
#define NETTL_SUBSYS_NS_LS_TIMOD	22
#define NETTL_SUBSYS_NS_LS_ICMP		23
#define NETTL_SUBSYS_FILTER		26
#define NETTL_SUBSYS_NAME		27
#define NETTL_SUBSYS_IGMP		29
#define NETTL_SUBSYS_SX25L2		34
#define NETTL_SUBSYS_SX25L3		35
#define NETTL_SUBSYS_FTAM_INIT		64
#define NETTL_SUBSYS_FTAM_RESP		65
#define NETTL_SUBSYS_FTAM_VFS		70
#define NETTL_SUBSYS_FTAM_USER		72
#define NETTL_SUBSYS_OTS		90
#define NETTL_SUBSYS_NETWORK		91
#define NETTL_SUBSYS_TRANSPORT		92
#define NETTL_SUBSYS_SESSION		93
#define NETTL_SUBSYS_ACSE_PRES		94
#define NETTL_SUBSYS_SHM		116
#define NETTL_SUBSYS_ACSE_US		119
#define NETTL_SUBSYS_HPS		121
#define NETTL_SUBSYS_CM			122
#define NETTL_SUBSYS_ULA_UTILS		123
#define NETTL_SUBSYS_EM			124
#define NETTL_SUBSYS_HP_APAPORT		189
#define NETTL_SUBSYS_HP_APALACP		190
#define NETTL_SUBSYS_NS_LS_IPV6		244
#define NETTL_SUBSYS_NS_LS_ICMPV6	245
#define NETTL_SUBSYS_NS_LS_TELNET	267
#define NETTL_SUBSYS_NS_LS_SCTP		268

/* Ethernet cards */
#define NETTL_SUBSYS_100VG		37
#define NETTL_SUBSYS_LAN100		164
#define NETTL_SUBSYS_EISA100BT		172
#define NETTL_SUBSYS_BASE100		173
#define NETTL_SUBSYS_GSC100BT		178
#define NETTL_SUBSYS_PCI100BT		179
#define NETTL_SUBSYS_SPP100BT		180
#define NETTL_SUBSYS_GELAN		185
#define NETTL_SUBSYS_BTLAN		210
#define NETTL_SUBSYS_INTL100		233
#define NETTL_SUBSYS_IGELAN		252
#define NETTL_SUBSYS_IETHER		253
#define NETTL_SUBSYS_IXGBE		265
#define NETTL_SUBSYS_ICXGBE		271
#define NETTL_SUBSYS_IEXGBE		275
#define NETTL_SUBSYS_IOCXGBE		277
#define NETTL_SUBSYS_IQXGBE		278

/* FDDI cards */
#define NETTL_SUBSYS_HPPB_FDDI		95
#define NETTL_SUBSYS_EISA_FDDI		174
#define NETTL_SUBSYS_PCI_FDDI		176
#define NETTL_SUBSYS_HSC_FDDI		177

/* Token Ring cards */
#define NETTL_SUBSYS_TOKEN		31
#define NETTL_SUBSYS_PCI_TR		187

/* Accelerated Virtual I/O (AVIO) drivers */
#define NETTL_SUBSYS_HSSN		269
#define NETTL_SUBSYS_IGSSN		270

/* from /usr/include/sys/subsys_id.h */

#define NETTL_HDR_HDRIN                 0x80000000
#define NETTL_HDR_HDROUT                0x40000000
#define NETTL_HDR_PDUIN			0x20000000
#define NETTL_HDR_PDUOUT		0x10000000
#define NETTL_HDR_PROCEDURE_TRACE       0x08000000
#define NETTL_HDR_STATE_TRACE           0x04000000
#define NETTL_HDR_ERROR_TRACE	        0x02000000
#define NETTL_HDR_LOG_TRACE             0x01000000
#define NETTL_HDR_LOOPBACK              0x00800000
#define NETTL_HDR_PTOP                  0x00400000
#define NETTL_HDR_SUBSYSTEM_BITS_MASK   0x000fffff

#define NETTL_HDR_PDU_MASK              0x30000000

wtap_open_return_val nettl_open(wtap *wth, int *err, gchar **err_info);
gboolean nettl_dump_open(wtap_dumper *wdh, int *err);
int nettl_dump_can_write_encap(int encap);

#endif
