/* camins.h
 *
 * File format support for Rabbit Labs CAM Inspector files
 * Copyright (c) 2013 by <PERSON> <<EMAIL>>
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */

#ifndef _CAMINS_H
#define _CAMINS_H

#include <glib.h>
#include <wiretap/wtap.h>

wtap_open_return_val camins_open(wtap *wth, int *err, gchar **err_info _U_);

#endif /* _CAMINS_H */
