This is a very quick and very dirty guide to adding support for new
capture file formats.  If you see any errors or have any improvements,
submit patches - free software is a community effort....

To add the ability to read a new capture file format, you have to:

	add a new WTAP_FILE_ value for the file type to
	"wiretap/wtap.h";

	write an "open" routine that can read the beginning of the
	capture file and figure out if it's in that format or not,
	either by looking at a magic number at the beginning or by using
	some form of heuristic to determine if it's a file of that type
	(if the file format has a magic number, that's what should be
	used);

	write a "read" routine that can read a packet from the file and
	supply the packet length, captured data length, time stamp, and
	packet pseudo-header (if any) and data, and have the "open"
	routine set the "subtype_read" member of the "wtap" structure
	supplied to it to point to that routine;

	write a "seek and read" routine that can seek to a specified
	location in the file for a packet and supply the packet
	pseudo-header (if any) and data, and have the "open" routine set
	the "subtype_seek_read" member of the "wtap" structure to point
	to that routine;

	write a "close" routine, if necessary (if, for example, the
	"open" routine allocates any memory), and set the
	"subtype_close" member of the "wtap" structure to point to it,
	otherwise leave it set to NULL;

	add a pointer to the "open" routine to the "open_routines_base[]"
	table in "wiretap/file_access.c" - if it uses a magic number, put 
	it in the first section of that list, and, if it uses a heuristic, 
	put it in the second section, preferably putting the heuristic
	routines for binary files before the heuristic routines for text
	files;

	add an entry for that file type in the "dump_open_table_base[]" in
	"wiretap/file_access.c", giving a descriptive name, a short name 
	that's convenient to type on a command line (no blanks or capital
	letters, please), common file extensions to open and save, a flag 
	if it can be compressed with gzip (currently unused) and pointers 
	to the "can_write_encap" and "dump_open" routines if writing that 
	file is supported (see below), otherwise just null pointers.

Wiretap applications typically first perform sequential reads through
the capture file and may later do "seek and read" for individual frames. 
The "read" routine should set the variable data_offset to the byte
offset within the capture file from which the "seek and read" routine
will read.  If the capture records consist of:

	capture record header
	pseudo-header (e.g., for ATM)
	frame data

then data_offset should point to the pseudo-header.  The first
sequential read pass will process and store the capture record header
data, but it will not store the pseudo-header.  Note that the 
seek_and_read routine should work with the "random_fh" file handle
of the passed in wtap struct, instead of the "fh" file habndle used
in the normal read routine.

To add the ability to write a new capture file format, you have to:

	add a "can_write_encap" routine that returns an indication of
	whether a given packet encapsulation format is supported by the
	new capture file format;

	add a "dump_open" routine that starts writing a file (writing
	headers, allocating data structures, etc.);

	add a "dump" routine to write a packet to a file, and have the
	"dump_open" routine set the "subtype_write" member of the
	"wtap_dumper" structure passed to it to point to it;

	add a "dump_close" routine, if necessary (if, for example, the
	"dump_open" routine allocates any memory, or if some of the file
	header can be written only after all the packets have been
	written), and have the "dump_open" routine set the
	"subtype_close" member of the "wtap_dumper" structure to point
	to it;

	put pointers to the "can_write_encap" and "dump_open" routines
	in the "dump_open_table_base[]" entry for that file type.

In the wiretap directory, add your source file to CMakelists.txt and
your source and header file to Makefile.am.
