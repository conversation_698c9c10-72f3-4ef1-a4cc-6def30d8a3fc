# DO NOT EDIT THIS FILE!  It was created by <PERSON>shark
@Bad <EMAIL> && !tcp.analysis.window_update@[4718,10030,11796][63479,34695,34695]
@<NAME_EMAIL> != 8 && hsrp.state != 16@[4718,10030,11796][65535,64764,40092]
@Spanning Tree Topology  <EMAIL> == 0x80@[4718,10030,11796][65535,64764,40092]
@<NAME_EMAIL> != 1@[4718,10030,11796][65535,64764,40092]
@ICMP <EMAIL> eq 3 || icmp.type eq 4 || icmp.type eq 5 || icmp.type eq 11 || icmpv6.type eq 1 || icmpv6.type eq 2 || icmpv6.type eq 3 || icmpv6.type eq 4@[4718,10030,11796][47031,63479,29812]
@ARP@arp@[64250,61680,55255][4718,10030,11796]
@ICMP@icmp || icmpv6@[64764,57568,65535][4718,10030,11796]
@TCP <EMAIL> eq 1@[42148,0,0][65535,64764,40092]
@SCTP ABORT@sctp.chunk_type eq ABORT@[42148,0,0][65535,64764,40092]
@TTL low or unexpected@( ! ip.dst == *********/4 && ip.ttl < 5 && !pim && !ospf) || (ip.dst == *********/24 && ip.dst != *********** && ip.ttl != 1 && !(vrrp || carp))@[42148,0,0][60652,61680,60395]
@Checksum <EMAIL>=="Bad" || ip.checksum.status=="Bad" || tcp.checksum.status=="Bad" || udp.checksum.status=="Bad" || sctp.checksum.status=="Bad" || mstp.checksum.status=="Bad" || cdp.checksum.status=="Bad" || edp.checksum.status=="Bad" || wlan.fcs.status=="Bad" || stt.checksum.status=="Bad"@[4718,10030,11796][63479,34695,34695]
@SMB@smb || nbss || nbns || nbipx || ipxsap || netbios@[65278,65535,53456][4718,10030,11796]
@HTTP@http || tcp.port == 80 || http2@[58596,65535,51143][4718,10030,11796]
@IPX@ipx || spx@[65534,58325,58808][4718,10030,11796]
@DCERPC@dcerpc@[51199,38706,65533][4718,10030,11796]
@Routing@hsrp || eigrp || ospf || bgp || cdp || vrrp || carp || gvrp || igmp || ismp@[65534,62325,54808][4718,10030,11796]
@TCP SYN/<EMAIL> & 0x02 || tcp.flags.fin == 1@[41026,41026,41026][4718,10030,11796]
@TCP@tcp@[59345,58980,65535][4718,10030,11796]
@UDP@udp@[56026,61166,65535][4718,10030,11796]
@Broadcast@eth[0] & 1@[65535,65535,65535][47802,48573,46774]
