/**
 * crc7.c
 *
 * Functions and types for CRC checks.
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 *
 *
 * Generated on Wed Jul 11 17:24:30 2012,
 * by pycrc v0.7.10, http://www.tty1.net/pycrc/
 * using the configuration:
 *    Width        = 7
 *    Poly         = 0x45
 *    XorIn        = 0x00
 *    ReflectIn    = False
 *    XorOut       = 0x00
 *    ReflectOut   = False
 *    Algorithm    = table-driven
 *****************************************************************************/
#include "config.h"

#include <glib.h>
#include "crc7.h"     /* include the header file generated with pycrc */

/**
 * Static table used for the table_driven implementation.
 *****************************************************************************/
static const guint8 crc_table[256] = {
    0x00, 0x8a, 0x9e, 0x14, 0xb6, 0x3c, 0x28, 0xa2, 0xe6, 0x6c, 0x78, 0xf2, 0x50, 0xda, 0xce, 0x44,
    0x46, 0xcc, 0xd8, 0x52, 0xf0, 0x7a, 0x6e, 0xe4, 0xa0, 0x2a, 0x3e, 0xb4, 0x16, 0x9c, 0x88, 0x02,
    0x8c, 0x06, 0x12, 0x98, 0x3a, 0xb0, 0xa4, 0x2e, 0x6a, 0xe0, 0xf4, 0x7e, 0xdc, 0x56, 0x42, 0xc8,
    0xca, 0x40, 0x54, 0xde, 0x7c, 0xf6, 0xe2, 0x68, 0x2c, 0xa6, 0xb2, 0x38, 0x9a, 0x10, 0x04, 0x8e,
    0x92, 0x18, 0x0c, 0x86, 0x24, 0xae, 0xba, 0x30, 0x74, 0xfe, 0xea, 0x60, 0xc2, 0x48, 0x5c, 0xd6,
    0xd4, 0x5e, 0x4a, 0xc0, 0x62, 0xe8, 0xfc, 0x76, 0x32, 0xb8, 0xac, 0x26, 0x84, 0x0e, 0x1a, 0x90,
    0x1e, 0x94, 0x80, 0x0a, 0xa8, 0x22, 0x36, 0xbc, 0xf8, 0x72, 0x66, 0xec, 0x4e, 0xc4, 0xd0, 0x5a,
    0x58, 0xd2, 0xc6, 0x4c, 0xee, 0x64, 0x70, 0xfa, 0xbe, 0x34, 0x20, 0xaa, 0x08, 0x82, 0x96, 0x1c,
    0xae, 0x24, 0x30, 0xba, 0x18, 0x92, 0x86, 0x0c, 0x48, 0xc2, 0xd6, 0x5c, 0xfe, 0x74, 0x60, 0xea,
    0xe8, 0x62, 0x76, 0xfc, 0x5e, 0xd4, 0xc0, 0x4a, 0x0e, 0x84, 0x90, 0x1a, 0xb8, 0x32, 0x26, 0xac,
    0x22, 0xa8, 0xbc, 0x36, 0x94, 0x1e, 0x0a, 0x80, 0xc4, 0x4e, 0x5a, 0xd0, 0x72, 0xf8, 0xec, 0x66,
    0x64, 0xee, 0xfa, 0x70, 0xd2, 0x58, 0x4c, 0xc6, 0x82, 0x08, 0x1c, 0x96, 0x34, 0xbe, 0xaa, 0x20,
    0x3c, 0xb6, 0xa2, 0x28, 0x8a, 0x00, 0x14, 0x9e, 0xda, 0x50, 0x44, 0xce, 0x6c, 0xe6, 0xf2, 0x78,
    0x7a, 0xf0, 0xe4, 0x6e, 0xcc, 0x46, 0x52, 0xd8, 0x9c, 0x16, 0x02, 0x88, 0x2a, 0xa0, 0xb4, 0x3e,
    0xb0, 0x3a, 0x2e, 0xa4, 0x06, 0x8c, 0x98, 0x12, 0x56, 0xdc, 0xc8, 0x42, 0xe0, 0x6a, 0x7e, 0xf4,
    0xf6, 0x7c, 0x68, 0xe2, 0x40, 0xca, 0xde, 0x54, 0x10, 0x9a, 0x8e, 0x04, 0xa6, 0x2c, 0x38, 0xb2
};



/**
 * Update the crc value with new data.
 *
 * \param crc      The current crc value.
 * \param data     Pointer to a buffer of \a data_len bytes.
 * \param data_len Number of bytes in the \a data buffer.
 * \return         The updated crc value.
 *****************************************************************************/
guint8 crc7update(guint8 crc, const unsigned char *data, int data_len)
{
    unsigned int tbl_idx;

    while (data_len--) {
        tbl_idx = ((crc >> 0) ^ *data) & 0xff;
        crc = (crc_table[tbl_idx] ^ (crc << (8 - 1))) & (0x7f << 1);

        data++;
    }
    return crc & (0x7f << 1);
}

/*
 * Editor modelines  -  http://www.wireshark.org/tools/modelines.html
 *
 * Local variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * vi: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
