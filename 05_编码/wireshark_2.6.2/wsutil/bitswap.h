/* bitswap.h
 * Macro to bitswap a byte by looking it up in a table
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */

#ifndef __BITSWAP_H__
#define __BITSWAP_H__

#include "ws_symbol_export.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

WS_DLL_PUBLIC void bitswap_buf_inplace(guint8 *buf, size_t len);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* bitswap.h */
