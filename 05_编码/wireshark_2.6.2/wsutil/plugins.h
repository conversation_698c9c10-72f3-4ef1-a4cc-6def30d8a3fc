/* plugins.h
 * definitions for plugins structures
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */

#ifndef __PLUGINS_H__
#define __PLUGINS_H__

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#include <glib.h>
#include <gmodule.h>

#include "ws_symbol_export.h"

typedef void (*plugin_register_func)(void);

typedef void plugins_t;

typedef enum {
    WS_PLUGIN_EPAN,
    WS_PLUGIN_WIRETAP,
    WS_PLUGIN_CODEC
} plugin_type_e;

WS_DLL_PUBLIC plugins_t *plugins_init(plugin_type_e type);

typedef void (*plugin_description_callback)(const char *name, const char *version,
                                            const char *types, const char *filename,
                                            void *user_data);

WS_DLL_PUBLIC void plugins_get_descriptions(plugin_description_callback callback, void *user_data);

WS_DLL_PUBLIC void plugins_dump_all(void);

WS_DLL_PUBLIC int plugins_get_count(void);

WS_DLL_PUBLIC void plugins_cleanup(plugins_t *plugins);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __PLUGINS_H__ */

/*
 * Editor modelines
 *
 * Local Variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * ex: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
