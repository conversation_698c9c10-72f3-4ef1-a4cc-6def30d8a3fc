/* type_util.h
 * Types utility definitions
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */

#ifndef __TYPE_UTIL_H__
#define __TYPE_UTIL_H__

#include <glib.h>
#include "ws_symbol_export.h"

/*
 * guint64 to gdouble conversions taken from gstutils.h of GStreamer project
 *
 * GStreamer
 * Copyright (C) 1999,2000 <PERSON> <<EMAIL>>
 *                    2000 Wim <PERSON>ymans <<EMAIL>>
 *                    2002 <PERSON> <<EMAIL>>
 *
 * gstutils.h: Header for various utility functions
 *
 * GNU GPL v2
 *
 */

WS_DLL_PUBLIC
guint64         type_util_gdouble_to_guint64(gdouble value);
WS_DLL_PUBLIC
gdouble         type_util_guint64_to_gdouble(guint64 value);

#ifdef _WIN32
#define         gdouble_to_guint64(value)   type_util_gdouble_to_guint64(value)
#define         guint64_to_gdouble(value)   type_util_guint64_to_gdouble(value)
#else
#define         gdouble_to_guint64(value)   ((guint64)(value))
#define         guint64_to_gdouble(value)   ((gdouble)(value))
#endif

#endif /* __TYPE_UTIL_H__ */
