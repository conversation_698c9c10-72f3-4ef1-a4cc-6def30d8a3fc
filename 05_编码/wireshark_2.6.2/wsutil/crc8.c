/* crc8.c
 * Implementation CRC-8 declarations and routines
 *
 * 2011 <PERSON> <<EMAIL>>
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */


#include "config.h"

#include <glib.h>
#include <wsutil/crc8.h>

/* @brief Precompiled table for CRC8 values for the polynom 0x2F */
static const guint8 crc8_precompiled_2F[256] =
{
    0x00, 0x2F, 0x5E, 0x71, 0xBC, 0x93, 0xE2, 0xCD,
    0x57, 0x78, 0x09, 0x26, 0xEB, 0xC4, 0xB5, 0x9A,
    0xAE, 0x81, 0xF0, 0xDF, 0x12, 0x3D, 0x4C, 0x63,
    0xF9, 0xD6, 0xA7, 0x88, 0x45, 0x6A, 0x1B, 0x34,
    0x73, 0x5C, 0x2D, 0x02, 0xCF, 0xE0, 0x91, 0xBE,
    0x24, 0x0B, 0x7A, 0x55, 0x98, 0xB7, 0xC6, 0xE9,
    0xDD, 0xF2, 0x83, 0xAC, 0x61, 0x4E, 0x3F, 0x10,
    0x8A, 0xA5, 0xD4, 0xFB, 0x36, 0x19, 0x68, 0x47,
    0xE6, 0xC9, 0xB8, 0x97, 0x5A, 0x75, 0x04, 0x2B,
    0xB1, 0x9E, 0xEF, 0xC0, 0x0D, 0x22, 0x53, 0x7C,
    0x48, 0x67, 0x16, 0x39, 0xF4, 0xDB, 0xAA, 0x85,
    0x1F, 0x30, 0x41, 0x6E, 0xA3, 0x8C, 0xFD, 0xD2,
    0x95, 0xBA, 0xCB, 0xE4, 0x29, 0x06, 0x77, 0x58,
    0xC2, 0xED, 0x9C, 0xB3, 0x7E, 0x51, 0x20, 0x0F,
    0x3B, 0x14, 0x65, 0x4A, 0x87, 0xA8, 0xD9, 0xF6,
    0x6C, 0x43, 0x32, 0x1D, 0xD0, 0xFF, 0x8E, 0xA1,
    0xE3, 0xCC, 0xBD, 0x92, 0x5F, 0x70, 0x01, 0x2E,
    0xB4, 0x9B, 0xEA, 0xC5, 0x08, 0x27, 0x56, 0x79,
    0x4D, 0x62, 0x13, 0x3C, 0xF1, 0xDE, 0xAF, 0x80,
    0x1A, 0x35, 0x44, 0x6B, 0xA6, 0x89, 0xF8, 0xD7,
    0x90, 0xBF, 0xCE, 0xE1, 0x2C, 0x03, 0x72, 0x5D,
    0xC7, 0xE8, 0x99, 0xB6, 0x7B, 0x54, 0x25, 0x0A,
    0x3E, 0x11, 0x60, 0x4F, 0x82, 0xAD, 0xDC, 0xF3,
    0x69, 0x46, 0x37, 0x18, 0xD5, 0xFA, 0x8B, 0xA4,
    0x05, 0x2A, 0x5B, 0x74, 0xB9, 0x96, 0xE7, 0xC8,
    0x52, 0x7D, 0x0C, 0x23, 0xEE, 0xC1, 0xB0, 0x9F,
    0xAB, 0x84, 0xF5, 0xDA, 0x17, 0x38, 0x49, 0x66,
    0xFC, 0xD3, 0xA2, 0x8D, 0x40, 0x6F, 0x1E, 0x31,
    0x76, 0x59, 0x28, 0x07, 0xCA, 0xE5, 0x94, 0xBB,
    0x21, 0x0E, 0x7F, 0x50, 0x9D, 0xB2, 0xC3, 0xEC,
    0xD8, 0xF7, 0x86, 0xA9, 0x64, 0x4B, 0x3A, 0x15,
    0x8F, 0xA0, 0xD1, 0xFE, 0x33, 0x1C, 0x6D, 0x42
};

/* @brief Precompiled table for CRC8 values for the polynom 0x37 */
static const unsigned char crc8_precompiled_37[256] =
{
    0x00, 0x37, 0x6e, 0x59, 0xdc, 0xeb, 0xb2, 0x85,
    0x8f, 0xb8, 0xe1, 0xd6, 0x53, 0x64, 0x3d, 0x0a,
    0x29, 0x1e, 0x47, 0x70, 0xf5, 0xc2, 0x9b, 0xac,
    0xa6, 0x91, 0xc8, 0xff, 0x7a, 0x4d, 0x14, 0x23,
    0x52, 0x65, 0x3c, 0x0b, 0x8e, 0xb9, 0xe0, 0xd7,
    0xdd, 0xea, 0xb3, 0x84, 0x01, 0x36, 0x6f, 0x58,
    0x7b, 0x4c, 0x15, 0x22, 0xa7, 0x90, 0xc9, 0xfe,
    0xf4, 0xc3, 0x9a, 0xad, 0x28, 0x1f, 0x46, 0x71,
    0xa4, 0x93, 0xca, 0xfd, 0x78, 0x4f, 0x16, 0x21,
    0x2b, 0x1c, 0x45, 0x72, 0xf7, 0xc0, 0x99, 0xae,
    0x8d, 0xba, 0xe3, 0xd4, 0x51, 0x66, 0x3f, 0x08,
    0x02, 0x35, 0x6c, 0x5b, 0xde, 0xe9, 0xb0, 0x87,
    0xf6, 0xc1, 0x98, 0xaf, 0x2a, 0x1d, 0x44, 0x73,
    0x79, 0x4e, 0x17, 0x20, 0xa5, 0x92, 0xcb, 0xfc,
    0xdf, 0xe8, 0xb1, 0x86, 0x03, 0x34, 0x6d, 0x5a,
    0x50, 0x67, 0x3e, 0x09, 0x8c, 0xbb, 0xe2, 0xd5,
    0x7f, 0x48, 0x11, 0x26, 0xa3, 0x94, 0xcd, 0xfa,
    0xf0, 0xc7, 0x9e, 0xa9, 0x2c, 0x1b, 0x42, 0x75,
    0x56, 0x61, 0x38, 0x0f, 0x8a, 0xbd, 0xe4, 0xd3,
    0xd9, 0xee, 0xb7, 0x80, 0x05, 0x32, 0x6b, 0x5c,
    0x2d, 0x1a, 0x43, 0x74, 0xf1, 0xc6, 0x9f, 0xa8,
    0xa2, 0x95, 0xcc, 0xfb, 0x7e, 0x49, 0x10, 0x27,
    0x04, 0x33, 0x6a, 0x5d, 0xd8, 0xef, 0xb6, 0x81,
    0x8b, 0xbc, 0xe5, 0xd2, 0x57, 0x60, 0x39, 0x0e,
    0xdb, 0xec, 0xb5, 0x82, 0x07, 0x30, 0x69, 0x5e,
    0x54, 0x63, 0x3a, 0x0d, 0x88, 0xbf, 0xe6, 0xd1,
    0xf2, 0xc5, 0x9c, 0xab, 0x2e, 0x19, 0x40, 0x77,
    0x7d, 0x4a, 0x13, 0x24, 0xa1, 0x96, 0xcf, 0xf8,
    0x89, 0xbe, 0xe7, 0xd0, 0x55, 0x62, 0x3b, 0x0c,
    0x06, 0x31, 0x68, 0x5f, 0xda, 0xed, 0xb4, 0x83,
    0xa0, 0x97, 0xce, 0xf9, 0x7c, 0x4b, 0x12, 0x25,
    0x2f, 0x18, 0x41, 0x76, 0xf3, 0xc4, 0x9d, 0xaa,
};

/* @brief Precompiled table for CRC8 values for the polynom 0x3B */
static const unsigned char crc8_precompiled_3b[256] =
{
    0x00, 0x3b, 0x76, 0x4d, 0xec, 0xd7, 0x9a, 0xa1,
    0xe3, 0xd8, 0x95, 0xae, 0x0f, 0x34, 0x79, 0x42,
    0xfd, 0xc6, 0x8b, 0xb0, 0x11, 0x2a, 0x67, 0x5c,
    0x1e, 0x25, 0x68, 0x53, 0xf2, 0xc9, 0x84, 0xbf,
    0xc1, 0xfa, 0xb7, 0x8c, 0x2d, 0x16, 0x5b, 0x60,
    0x22, 0x19, 0x54, 0x6f, 0xce, 0xf5, 0xb8, 0x83,
    0x3c, 0x07, 0x4a, 0x71, 0xd0, 0xeb, 0xa6, 0x9d,
    0xdf, 0xe4, 0xa9, 0x92, 0x33, 0x08, 0x45, 0x7e,
    0xb9, 0x82, 0xcf, 0xf4, 0x55, 0x6e, 0x23, 0x18,
    0x5a, 0x61, 0x2c, 0x17, 0xb6, 0x8d, 0xc0, 0xfb,
    0x44, 0x7f, 0x32, 0x09, 0xa8, 0x93, 0xde, 0xe5,
    0xa7, 0x9c, 0xd1, 0xea, 0x4b, 0x70, 0x3d, 0x06,
    0x78, 0x43, 0x0e, 0x35, 0x94, 0xaf, 0xe2, 0xd9,
    0x9b, 0xa0, 0xed, 0xd6, 0x77, 0x4c, 0x01, 0x3a,
    0x85, 0xbe, 0xf3, 0xc8, 0x69, 0x52, 0x1f, 0x24,
    0x66, 0x5d, 0x10, 0x2b, 0x8a, 0xb1, 0xfc, 0xc7,
    0x49, 0x72, 0x3f, 0x04, 0xa5, 0x9e, 0xd3, 0xe8,
    0xaa, 0x91, 0xdc, 0xe7, 0x46, 0x7d, 0x30, 0x0b,
    0xb4, 0x8f, 0xc2, 0xf9, 0x58, 0x63, 0x2e, 0x15,
    0x57, 0x6c, 0x21, 0x1a, 0xbb, 0x80, 0xcd, 0xf6,
    0x88, 0xb3, 0xfe, 0xc5, 0x64, 0x5f, 0x12, 0x29,
    0x6b, 0x50, 0x1d, 0x26, 0x87, 0xbc, 0xf1, 0xca,
    0x75, 0x4e, 0x03, 0x38, 0x99, 0xa2, 0xef, 0xd4,
    0x96, 0xad, 0xe0, 0xdb, 0x7a, 0x41, 0x0c, 0x37,
    0xf0, 0xcb, 0x86, 0xbd, 0x1c, 0x27, 0x6a, 0x51,
    0x13, 0x28, 0x65, 0x5e, 0xff, 0xc4, 0x89, 0xb2,
    0x0d, 0x36, 0x7b, 0x40, 0xe1, 0xda, 0x97, 0xac,
    0xee, 0xd5, 0x98, 0xa3, 0x02, 0x39, 0x74, 0x4f,
    0x31, 0x0a, 0x47, 0x7c, 0xdd, 0xe6, 0xab, 0x90,
    0xd2, 0xe9, 0xa4, 0x9f, 0x3e, 0x05, 0x48, 0x73,
    0xcc, 0xf7, 0xba, 0x81, 0x20, 0x1b, 0x56, 0x6d,
    0x2f, 0x14, 0x59, 0x62, 0xc3, 0xf8, 0xb5, 0x8e,
};
/** Calculates a CRC8 checksum for the given buffer with the polynom
 *  stored in the crc_table
 * @param buf a pointer to a buffer of the given length
 * @param len the length of the given buffer
 * @param seed The seed to use.
 * @param crc_table a table storing 256 entries for crc8 checksums
 * @return the CRC8 checksum for the buffer
 */
static guint8 crc8_precompiled(const guint8 *buf, guint32 len, guint8 seed, const guint8 crc_table[])
{
    guint8 crc;

    crc = seed;
    while(len-- > 0)
        crc = crc_table[(guint8)(*buf++) ^ crc];

    return crc;
}

/** Calculates a CRC8 checksum for the given buffer with the polynom
 *  0x2F using the precompiled CRC table
 * @param buf a pointer to a buffer of the given length
 * @param len the length of the given buffer
 * @param seed The seed to use.
 * @return the CRC8 checksum for the buffer
 */
guint8 crc8_0x2F(const guint8 *buf, guint32 len, guint8 seed)
{
    return crc8_precompiled(buf, len, seed, crc8_precompiled_2F);
}

/** Calculates a CRC8 checksum for the given buffer with the polynom
 *  0x37 using the precompiled CRC table
 * @param buf a pointer to a buffer of the given length
 * @param len the length of the given buffer
 * @param seed The seed to use.
 * @return the CRC8 checksum for the buffer
 */
guint8 crc8_0x37(const guint8 *buf, guint32 len, guint8 seed)
{
    guint8 crc = seed;
    while (len-- > 0)
    {
        crc = crc8_precompiled_37[(crc ^ *buf++)];
    }
    return (crc);
}

/** Calculates a CRC8 checksum for the given buffer with the polynom
 *  0x3B using the precompiled CRC table
 * @param buf a pointer to a buffer of the given length
 * @param len the length of the given buffer
 * @param seed The seed to use.
 * @return the CRC8 checksum for the buffer
 */
guint8 crc8_0x3B(const guint8 *buf, guint32 len, guint8 seed)
{
    guint8 crc = seed;
    while (len-- > 0)
    {
        crc = crc8_precompiled_3b[(crc ^ *buf++)];
    }
    return (crc);
}

/*
 * Editor modelines  -  http://www.wireshark.org/tools/modelines.html
 *
 * Local variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * vi: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
