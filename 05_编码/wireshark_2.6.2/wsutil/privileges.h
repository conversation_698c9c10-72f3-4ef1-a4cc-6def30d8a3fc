/* privileges.h
 * Declarations of routines for handling privileges.
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 2006 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */

#ifndef __PRIVILEGES_H__
#define __PRIVILEGES_H__

#include "ws_symbol_export.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Called when the program starts, to enable security features and save
 * whatever credential information we'll need later.
 */
WS_DLL_PUBLIC void init_process_policies(void);

/**
 * Was this program started with special privileges?  get_credential_info()
 * MUST be called before calling this.
 * @return TRUE if the program was started with special privileges,
 * FALSE otherwise.
 */
WS_DLL_PUBLIC gboolean started_with_special_privs(void);

/**
 * Is this program running with special privileges? get_credential_info()
 * MUST be called before calling this.
 * @return TRUE if the program is running with special privileges,
 * FALSE otherwise.
 */
WS_DLL_PUBLIC gboolean running_with_special_privs(void);

/**
 * Permanently relinquish special privileges. get_credential_info()
 * MUST be called before calling this.
 */
WS_DLL_PUBLIC void relinquish_special_privs_perm(void);

/**
 * Get the current username.  String must be g_free()d after use.
 * @return A freshly g_alloc()ed string containing the username,
 * or "UNKNOWN" on failure.
 */
WS_DLL_PUBLIC gchar *get_cur_username(void);

/**
 * Get the current group.  String must be g_free()d after use.
 * @return A freshly g_alloc()ed string containing the group,
 * or "UNKNOWN" on failure.
 */
WS_DLL_PUBLIC gchar *get_cur_groupname(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __PRIVILEGES_H__ */
