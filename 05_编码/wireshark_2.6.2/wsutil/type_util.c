/* type_util.c
 * Types utility routines
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */

#include "config.h"

#include "type_util.h"

/*
 * guint64 to gdouble conversions taken from gstutils.c of GStreamer project
 *
 * GStreamer
 * Copyright (C) 1999,2000 <PERSON> <<EMAIL>>
 *                    2000 Wim <PERSON> <<EMAIL>>
 *                    2002 <PERSON> <<EMAIL>>
 *
 * gstutils.h: Header for various utility functions
 *
 * GNU GPL v2
 *
 */

/* work around error C2520: conversion from unsigned __int64 to double
 * not implemented, use signed __int64
 *
 * These are implemented as functions because on some platforms a 64bit int to
 * double conversion is not defined/implemented.
 */

gdouble
type_util_guint64_to_gdouble(guint64 value)
{
  if (value & G_GUINT64_CONSTANT (0x8000000000000000))
    return (gdouble) ((gint64) value) + (gdouble) 18446744073709551616.;
  else
    return (gdouble) ((gint64) value);
}

guint64
type_util_gdouble_to_guint64(gdouble value)
{
  if (value < (gdouble) 9223372036854775808.)   /* 1 << 63 */
    return ((guint64) ((gint64) value));

  value -= (gdouble) 18446744073709551616.;
  return ((guint64) ((gint64) value));
}

/*
 * Editor modelines  -  http://www.wireshark.org/tools/modelines.html
 *
 * Local Variables:
 * c-basic-offset: 2
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * ex: set shiftwidth=2 tabstop=8 expandtab:
 * :indentSize=2:tabSize=8:noTabs=true:
 */
