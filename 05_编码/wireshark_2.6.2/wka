#
# Well-known addresses.
#
# Wireshark - Network traffic analyzer
# By <PERSON> <gerald [AT] wireshark.org>
# Copyright 1998 <PERSON>
#
# SPDX-License-Identifier: GPL-2.0-or-later
#
# The data below has been assembled from the following sources:
#
# <PERSON>'s "Ethernet Codes Master Page" available from:
# <http://www.cavebear.com/CaveBear/Ethernet/>
# <ftp://ftp.cavebear.com/pub/Ethernet.txt>
#
# Microsoft Windows 2000 Server
# Operating System
# Network Load Balancing Technical Overview
# White Paper
#
00-00-00-00-FE-21	Checkpoint-Uninitialized-Cluster-Member
00-00-0C-07-AC/40	All-HSRP-routers
00-00-5E-00-01/40	IETF-VRRP-VRID
00-BF-00-00-00-00/16	MS-NLB-VirtServer
00-E0-2B-00-00-00	Extreme-EDP
# Extreme Encapsulation Protocol (basically EDP renamed)
00-E0-2B-00-00-01	Extreme-EEP
00-E0-2B-00-00-02	Extreme-ESRP-Client
00-E0-2B-00-00-04	Extreme-EAPS
00-E0-2B-00-00-06	Extreme-EAPS-SL
00-E0-2B-00-00-08	Extreme-ESRP-Master
01-00-0C-00-00/40	ISL-Frame
01-00-0C-CC-CC-CC	CDP/VTP/DTP/PAgP/UDLD
01-00-0C-CC-CC-CD	PVST+
01-00-0C-CD-CD-CD	STP-UplinkFast
01-00-0C-CD-CD-CE	VLAN-bridge
01-00-0C-CD-CD-D0	GBPT
01-00-0C-DD-DD-DD	CGMP
01-00-10-00-00-20	Hughes-Lan-Systems-Terminal-Server-S/W-download
01-00-10-FF-FF-20	Hughes-Lan-Systems-Terminal-Server-S/W-request
01-00-1D-00-00-00	Cabletron-PC-OV-PC-discover-(on-demand)
01-00-1D-00-00-05	Cabletron-PVST-BPDU
01-00-1D-00-00-06	Cabletron-QCSTP-BPDU
01-00-1D-42-00-00	Cabletron-PC-OV-Bridge-discover-(on-demand)
01-00-1D-52-00-00	Cabletron-PC-OV-MMAC-discover-(on-demand)
01-00-3C		Auspex-Systems-(Serverguard)
01-00-5E/25		IPv4mcast
01-00-81-00-00-00	Nortel-Network-Management
01-00-81-00-00-02	Nortel-Network-Management
01-00-81-00-01-00	Nortel-autodiscovery
01-00-81-00-01-01	Nortel-autodiscovery
#
# As per
#
#	http://www.t11.org/ftp/t11/pub/fc/bb-5/08-334v0.pdf
#
# Broadcom "donated" one of their OUIs, 00-10-18, for use for
# Fibre Channel over Ethernet, so we add entries for the
# addresses in that document and a group of addresses for all
# otherwise unlisted 01-10-18-XX-XX-XX addresses.
#
01-10-18-01-00-00	All-FCoE-MACs
01-10-18-01-00-01	All-ENode-MACs
01-10-18-01-00-02	All-FCF-MACs
01-10-18-00-00-00/24	FCoE-group
01-11-1E-00-00-01	EPLv2_SoC
01-11-1E-00-00-02	EPLv2_PRes
01-11-1E-00-00-03	EPLv2_SoA
01-11-1E-00-00-04	EPLv2_ASnd
01-11-1E-00-00-05	EPLv2_AMNI
01-20-25/25		Control-Technology-Inc's-Industrial-Ctrl-Proto.
01-80-24-00-00-00	Kalpana-Etherswitch-every-60-seconds
01-80-C2-00-00-00/44	Spanning-tree-(for-bridges)
01-80-C2-00-00-02	Slow-Protocols
01-80-C2-00-00-03	Nearest customer bridge
01-80-C2-00-00-0E	LLDP_Multicast
01-80-C2-00-00-0F	Nearest non-TPMR bridge
01-80-C2-00-00-10	Bridge-Management
01-80-C2-00-00-11	Load-Server
01-80-C2-00-00-12	Loadable-Device
01-80-C2-00-00-13	IEEE-1905.1-Control
01-80-C2-00-00-14	ISIS-all-level-1-IS's
01-80-C2-00-00-15	ISIS-all-level-2-IS's
01-80-C2-00-00-18	IEEE-802.1B-All-Manager-Stations
01-80-C2-00-00-19	IEEE-802.11aa-groupcast-with-retries
01-80-C2-00-00-1A	IEEE-802.1B-All-Agent-Stations
01-80-C2-00-00-1B	ESIS-all-multicast-capable-ES's
01-80-C2-00-00-1C	ESIS-all-multicast-announcements
01-80-C2-00-00-1D	ESIS-all-multicast-capable-IS's
01-80-C2-00-00-1E	Token-Ring-all-DTR-Concentrators
01-80-C2-00-00-30/45	OAM-Multicast-DA-Class-1
01-80-C2-00-00-38/45	OAM-Multicast-DA-Class-2
01-80-C2-00-00-40	All-RBridges
01-80-C2-00-00-41	All-IS-IS-RBridges
01-80-C2-00-00-42	All-Egress-RBridges
01-80-C2-00-00-45	TRILL-End-Stations
01-80-C2-00-00-46	All-Edge-RBridges
01-80-C2-00-01-00	FDDI-RMT-Directed-Beacon
01-80-C2-00-01-10	FDDI-status-report-frame
01-DD-00-FF-FF-FF	Ungermann-Bass-boot-me-requests
01-DD-01-00-00-00	Ungermann-Bass-Spanning-Tree
01-E0-52-CC-CC-CC	Foundry-DP
# DOCSIS, defined in ANSI SCTE 22-1 2012
01-E0-2F-00-00-01	DOCSIS-CM
01-E0-2F-00-00-02	DOCSIS-CMTS
01-E0-2F-00-00-03	DOCSIS-STP

# Extremenetworks in their infinite wisdom seems to use 02-04-94 (Vendor MAC XOR 02-00-00)
# for their base mac address, thus colliding with MS-NLB 02-04/16 which Microsoft in their
# infinite wisdom decided to use for MS-NLB.
02-04-96-00-00-00/24	ExtremeNetworks

# Microsoft Network Load Balancing (NLB)
# Actually, 02-01-virtualip to 02-20-virtualip will be used from server to rest-of-world
# 02-bf-virtualip will be used from rest-of-world to server
02-BF-00-00-00-00/16	MS-NLB-VirtServer
02-01-00-00-00-00/16	MS-NLB-PhysServer-01
02-02-00-00-00-00/16	MS-NLB-PhysServer-02
02-03-00-00-00-00/16	MS-NLB-PhysServer-03
02-04-00-00-00-00/16	MS-NLB-PhysServer-04
02-05-00-00-00-00/16	MS-NLB-PhysServer-05
02-06-00-00-00-00/16	MS-NLB-PhysServer-06
02-07-00-00-00-00/16	MS-NLB-PhysServer-07
02-08-00-00-00-00/16	MS-NLB-PhysServer-08
02-09-00-00-00-00/16	MS-NLB-PhysServer-09
02-0a-00-00-00-00/16	MS-NLB-PhysServer-10
02-0b-00-00-00-00/16	MS-NLB-PhysServer-11
02-0c-00-00-00-00/16	MS-NLB-PhysServer-12
02-0d-00-00-00-00/16	MS-NLB-PhysServer-13
02-0e-00-00-00-00/16	MS-NLB-PhysServer-14
02-0f-00-00-00-00/16	MS-NLB-PhysServer-15
02-10-00-00-00-00/16	MS-NLB-PhysServer-16
02-11-00-00-00-00/16	MS-NLB-PhysServer-17
02-12-00-00-00-00/16	MS-NLB-PhysServer-18
02-13-00-00-00-00/16	MS-NLB-PhysServer-19
02-14-00-00-00-00/16	MS-NLB-PhysServer-20
02-15-00-00-00-00/16	MS-NLB-PhysServer-21
02-16-00-00-00-00/16	MS-NLB-PhysServer-22
02-17-00-00-00-00/16	MS-NLB-PhysServer-23
02-18-00-00-00-00/16	MS-NLB-PhysServer-24
02-19-00-00-00-00/16	MS-NLB-PhysServer-25
02-1a-00-00-00-00/16	MS-NLB-PhysServer-26
02-1b-00-00-00-00/16	MS-NLB-PhysServer-27
02-1c-00-00-00-00/16	MS-NLB-PhysServer-28
02-1d-00-00-00-00/16	MS-NLB-PhysServer-29
02-1e-00-00-00-00/16	MS-NLB-PhysServer-30
02-1f-00-00-00-00/16	MS-NLB-PhysServer-31
02-20-00-00-00-00/16	MS-NLB-PhysServer-32

#       [ The following block of addresses (03-...) are used by various ]
#       [ standards.  Some (marked [TR?]) are suspected of only being   ]
#       [ used on Token Ring for group addresses of Token Ring specific ]
#       [ functions, reference ISO 8802-5:1995 aka. IEEE 802.5:1995 for ]
#       [ some info.  These in the Ethernet order for this list.  On    ]
#       [ Token Ring they appear reversed.  They should never appear on ]
#       [ Ethernet.  Others, not so marked, are normal reports (may be  ]
#       [ seen on either).
03-00-00-00-00-01	NETBIOS-# [TR?]
03-00-00-00-00-02	Locate-Directory-Server # [TR?]
03-00-00-00-00-04	Synchronous-Bandwidth-Manager-# [TR?]
03-00-00-00-00-08	Configuration-Report-Server-# [TR?]
03-00-00-00-00-10	Ring-Error-Monitor-# [TR?]
03-00-00-00-00-10	(OS/2-1.3-EE+Communications-Manager)
03-00-00-00-00-20	Network-Server-Heartbeat-# [TR?]
03-00-00-00-00-40	(OS/2-1.3-EE+Communications-Manager)
03-00-00-00-00-80	Active-Monitor # [TR?]
03-00-00-00-01-00	OSI-All-IS-Token-Ring-Multicast
03-00-00-00-02-00	OSI-All-ES-Token-Ring-Multicast
03-00-00-00-04-00	LAN-Manager # [TR?]
03-00-00-00-08-00	Ring-Wiring-Concentrator # [TR?]
03-00-00-00-10-00	LAN-Gateway # [TR?]
03-00-00-00-20-00	Ring-Authorization-Server # [TR?]
03-00-00-00-40-00	IMPL-Server # [TR?]
03-00-00-00-80-00	Bridge # [TR?]
03-00-00-20-00-00	IP-Token-Ring-Multicast (RFC1469)
03-00-00-80-00-00	Discovery-Client
03-00-0C-00-00/40	ISL-Frame [TR?]
03-00-C7-00-00-EE	HP (Compaq) ProLiant NIC teaming
03-00-FF-FF-FF-FF	All-Stations-Address
03-BF-00-00-00-00/16	MS-NLB-VirtServer-Multicast
09-00-07-00-00-00/40	AppleTalk-Zone-multicast-addresses
			# only goes through 09-00-07-00-00-FC?
09-00-07-FF-FF-FF	AppleTalk-broadcast-address
09-00-09-00-00-01	HP-Probe
09-00-09-00-00-04	HP-DTC
09-00-0D-00-00-00/24	ICL-Oslan-Multicast
09-00-0D-02-00-00	ICL-Oslan-Service-discover-only-on-boot
09-00-0D-02-0A-38	ICL-Oslan-Service-discover-only-on-boot
09-00-0D-02-0A-39	ICL-Oslan-Service-discover-only-on-boot
09-00-0D-02-0A-3C	ICL-Oslan-Service-discover-only-on-boot
09-00-0D-02-FF-FF	ICL-Oslan-Service-discover-only-on-boot
09-00-0D-09-00-00	ICL-Oslan-Service-discover-as-required
09-00-1E-00-00-00	Apollo-DOMAIN
09-00-2B-00-00-00	DEC-MUMPS?
09-00-2B-00-00-01	DEC-DSM/DDP
09-00-2B-00-00-02	DEC-VAXELN?
09-00-2B-00-00-03	DEC-Lanbridge-Traffic-Monitor-(LTM)
09-00-2B-00-00-04	DEC-MAP-(or-OSI?)-End-System-Hello?
09-00-2B-00-00-05	DEC-MAP-(or-OSI?)-Intermediate-System-Hello?
09-00-2B-00-00-06	DEC-CSMA/CD-Encryption?
09-00-2B-00-00-07	DEC-NetBios-Emulator?
09-00-2B-00-00-0F	DEC-Local-Area-Transport-(LAT)
09-00-2B-00-00-10/44	DEC-Experimental
09-00-2B-01-00-00	DEC-LanBridge-Copy-packets-(All-bridges)
09-00-2B-01-00-01	DEC-LanBridge-Hello-packets-(All-local-bridges)
09-00-2B-02-00-00	DEC-DNA-Level-2-Routing-Layer-routers?
09-00-2B-02-01-00	DEC-DNA-Naming-Service-Advertisement?
09-00-2B-02-01-01	DEC-DNA-Naming-Service-Solicitation?
09-00-2B-02-01-09	DEC-Availability-Manager-for-Distributed-Systems-DECamds
09-00-2B-02-01-02	DEC-Distributed-Time-Service
09-00-2B-03-00-00/32	DEC-default-filtering-by-bridges?
09-00-2B-04-00-00	DEC-Local-Area-System-Transport-(LAST)?
09-00-2B-23-00-00	DEC-Argonaut-Console?
09-00-4C-00-00-00	BICC-802.1-management
09-00-4C-00-00-02	BICC-802.1-management
09-00-4C-00-00-06	BICC-Local-bridge-STA-802.1(D)-Rev6
09-00-4C-00-00-0C	BICC-Remote-bridge-STA-802.1(D)-Rev8
09-00-4C-00-00-0F	BICC-Remote-bridge-ADAPTIVE-ROUTING
09-00-56-FF-00-00/32	Stanford-V-Kernel,-version-6.0
09-00-6A-00-01-00	TOP-NetBIOS.
09-00-77-00-00-00	Retix-Bridge-Local-Management-System
09-00-77-00-00-01	Retix-spanning-tree-bridges
09-00-77-00-00-02	Retix-Bridge-Adaptive-routing
09-00-7C-01-00-01	Vitalink-DLS-Multicast
09-00-7C-01-00-03	Vitalink-DLS-Inlink
09-00-7C-01-00-04	Vitalink-DLS-and-non-DLS-Multicast
09-00-7C-02-00-05	Vitalink-diagnostics
09-00-7C-05-00-01	Vitalink-gateway?
09-00-7C-05-00-02	Vitalink-Network-Validation-Message
09-00-87-80-FF-FF	Xyplex-Terminal-Servers
09-00-87-90-FF-FF	Xyplex-Terminal-Servers
0C-00-0C-00-00/40	ISL-Frame
0D-1E-15-BA-DD-06	HP
20-52-45-43-56-00/40	Receive
20-53-45-4E-44-00/40	Send
33-33-00-00-00-00	IPv6-Neighbor-Discovery
33-33-00-00-00-00/16	IPv6mcast
AA-00-03-00-00-00/32	DEC-UNA
AA-00-03-01-00-00/32	DEC-PROM-AA
AA-00-03-03-00-00/32	DEC-NI20
AB-00-00-01-00-00	DEC-MOP-Dump/Load-Assistance
AB-00-00-02-00-00	DEC-MOP-Remote-Console
AB-00-00-03-00-00	DECNET-Phase-IV-end-node-Hello-packets
AB-00-00-04-00-00	DECNET-Phase-IV-Router-Hello-packets
AB-00-03-00-00-00	DEC-Local-Area-Transport-(LAT)-old
AB-00-04-01-00-00/32	DEC-Local-Area-VAX-Cluster-groups-SCA
CF-00-00-00-00-00	Ethernet-Configuration-Test-protocol-(Loopback)
FF-FF-00-60-00-04	Lantastic
FF-FF-00-40-00-01	Lantastic
FF-FF-01-E0-00-04	Lantastic

FF-FF-FF-FF-FF-FF	Broadcast
