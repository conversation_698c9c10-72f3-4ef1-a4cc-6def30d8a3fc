/* globals.h
 * Global defines, etc.
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 * SPDX-License-Identifier: GPL-2.0-or-later
 */

#ifndef __GLOBALS_H__
#define __GLOBALS_H__

#include "file.h"
#include <epan/timestamp.h>

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

extern capture_file cfile;

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __GLOBALS_H__ */

/*
 * Editor modelines  -  http://www.wireshark.org/tools/modelines.html
 *
 * Local variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * vi: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */
