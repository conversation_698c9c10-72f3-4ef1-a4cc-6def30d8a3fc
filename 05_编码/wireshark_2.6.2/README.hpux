
Note: We *probably* don't support HP-UX any more, at least not in the
sense that you can run `configure; make` or `cmake ... ; make` and
expect everything to work out of the box. At the time of this writing
(August 2017) the most recent version of Wireshark available at the
HP-UX Porting and Archive Centre is 1.10.5 and the most recently
reported HP-UX bug (#6550) was from 2012. The Porting and Archive Centre
provides libraries required to build TShark, and while the GTK+ packages
are current (2.24.31) they are 32-bit only. Recent Qt packages are not
provided.

Contents:

1 - Building wireshark
2 - Building GTK+/GLib with HP's C compiler
3 - nettl support
4 - libpcap on HP-UX
5 - HP-UX patches to fix packet capture problems

1 - Building wireshark

The HP-UX Porting and Archive Centre, at

	http://hpux.connect.org.uk/

(with mirrors in various countries, listed on the Centre's home page;
you may want to choose a mirror closer to you) has ported versions, in
both source and binary form, for Wireshark, as well as for the libpcap,
GLib, GTK+, and zlib libraries that it uses.

The changes they've made appear largely to be compile option changes; if
you've downloaded the source to the latest version of Wireshark (the
version on the Centre's site may not necessarily be the latest version),
it should be able to compile, perhaps with those changes.

They appear to have used HP-UX's "cc" compiler, with the options "-Ae
-O"; there's a comment "Add -Dhpux_9 if building under 9.X".  It may
also build with GCC.

They currently have libpcap 0.6.2; libpcap 0.6.2, and later versions,
include changes to properly open network devices when given the name
reported by the lanscan and ifconfig commands - earlier versions didn't
do this correctly.  Therefore, we strongly suggest you use libpcap 0.6.2
or later, not libpcap 0.5.2.

2 - Building GTK+/GLib with HP's C compiler

By default, HP's C compiler doesn't support "long long int" to provide
64-bit integral data types on 32-bit platforms; the "-Ae" flag must be
supplied to enable extensions such as that.

Wireshark's "configure" script automatically includes that flag if it
detects that the native compiler is being used on HP-UX; however, the
configure scripts for GTK+ and GLib don't do so, which means that 64-bit
integer support won't be enabled.

This may prevent some parts of Wireshark from compiling; in order to get
64-bit integer support in GTK+/GLib, edit all the Makefiles for GTK+ and
GLib, as generated by the GTK+ and GLib "configure" scripts, to add
"-Ae" to all "CFLAGS = " definitions found in those Makefiles.  (If a
Makefile lacks a "CFLAGS = " definition, there's no need to add a
definition that includes "-Ae".)

3 - nettl support

nettl is used on HP-UX to trace various streams based subsystems.  Wireshark
can read nettl files containing raw IP frames (NS_LS_IP, NS_LS_TCP,
NS_LS_UDP, NS_LS_ICMP subsystems), all ethernet/tokenring/fddi driver
level frames (such as BTLAN, BASE100, GELAN, IGELAN subsystems) and LAPB
frames (SX25L2 subsystem).  Use "ioscan -kfClan" to see the driver
names and compare that to /etc/nettlgen.conf to find the nettl subsystem
name for your particular release.

It has been tested with files generated on HP-UX 9.04, 10.20, and 11.x.

Use the following commands to generate a trace (cf. nettl(1M)):

# IP capture:
nettl -tn pduin pduout -e NS_LS_IP -f tracefile
# Driver level capture.  Replace btlan with the name of your interface:
nettl -tn pduin pduout -e btlan -f tracefile
# X25 capture. You must specify an interface :
nettl -tn pduin pduout -e SX25l2 -d /dev/x25_0 -f tracefile
# stop capture. subsystem is NS_LS_IP, btlan, SX25L2 :
nettl -tf -e subsystem

You may have to use "-tn 0x30000000" instead of "-tn pduin pduout"
on old versions of 10.20 and 9.04.

4 - libpcap on HP-UX

If you want to use Wireshark to capture packets, you will have to install
libpcap; binary distributions are, as noted above, available from the
Software Porting And Archive Centre for HP-UX, as well as source code.

Versions of libpcap prior to 0.6 didn't handle HP-UX as well as 0.6 and
later versions do.  You should install the latest version.

The source code is also available from the official home of libpcap and
tcpdump, at

	http://www.tcpdump.org/

if you want a version later than the version available from the Software
Porting And Archive Centre; however, the versions available from
tcpdump.org might not, for example, include support for building libpcap
as a shared library.

5 - HP-UX patches to fix packet capture problems

Note that packet-capture programs such as Wireshark/TShark or tcpdump
may, on HP-UX, not be able to see packets sent from the machine on which
they're running.  Make sure you have a recent "LAN Cummulative/DLPI" patch
installed.

Some articles on groups.google.com discussing this are:

	http://groups.google.com/groups?selm=82ld3v%2480i%241%40mamenchi.zrz.TU-Berlin.DE

which says:

  Newsgroups: comp.sys.hp.hpux
  Subject:  Re: Did someone made tcpdump working on 10.20 ?
  Date: 12/08/1999
  From: Lutz Jaenicke <<EMAIL>>

  In article <82ks5i$5vc$<EMAIL>>, mtsat <<EMAIL>>
  wrote:
   >Hello,
   >
   >I downloaded and compiled tcpdump3.4 a couple of week ago. I tried to use
   >it, but I can only see incoming data, never outgoing.
   >Someone (raj) explained me that a patch was missing, and that this patch
   >must me "patched" (poked) in order to see outbound data in promiscuous mode.
   >Many things to do .... So the question is : did someone has already this
   >"ready to use" PHNE_**** patch ?

   Two things:
   1. You do need a late "LAN products cumulative patch" (e.g.  PHNE_18173
  for   s700/10.20).
   2. You must use
echo 'lanc_outbound_promisc_flag/W1' | /usr/bin/adb -w /stand/vmunix /dev/kmem
     You can insert this e.g. into /sbin/init.d/lan

   Best regards,
   Lutz

and

	http://groups.google.com/groups?selm=88cf4t%24p03%241%40web1.cup.hp.com

which says:

  Newsgroups: comp.sys.hp.hpux
  Subject: Re: tcpdump only shows incoming packets
  Date: 02/15/2000
  From: Rick Jones <<EMAIL>>

  Harald Skotnes <<EMAIL>> wrote:
  > I am running HPUX 11.0 on a C200 hanging on a 100Mb switch. I have
  > compiled libpcap-0.4 an tcpdump-3.4 and it seems to work. But at a
  > closer look I only get to see the incoming packets not the
  > outgoing. I have tried tcpflow-0.12 which also uses libpcap and the
  > same thing happens.  Could someone please give me a hint on how to
  > get this right?

  Search/Read the archives ?-)

  What you are seeing is expected, un-patched, behaviour for an HP-UX
  system.  On 11.00, you need to install the latest lancommon/DLPI
  patches, and then the latest driver patch for the interface(s) in use.
  At that point, a miracle happens and you should start seeing outbound
  traffic.

[That article also mentions the patch that appears below.]

and

	http://groups.google.com/groups?selm=38AA973E.96BE7DF7%40cc.uit.no

which says:

  Newsgroups: comp.sys.hp.hpux
  Subject: Re: tcpdump only shows incoming packets
  Date: 02/16/2000
  From: Harald Skotnes <<EMAIL>>

  Rick Jones wrote:

	...

  > What you are seeing is expected, un-patched, behaviour for an HP-UX
  > system. On 11.00, you need to install the latest lancommon/DLPI
  > patches, and then the latest driver patch for the interface(s) in
  > use. At that point, a miracle happens and you should start seeing
  > outbound traffic.

  Thanks a lot.  I have this problem on several machines running HPUX
  10.20 and 11.00.  The machines where patched up before y2k so did not
  know what to think.  Anyway I have now installed PHNE_19766,
  PHNE_19826, PHNE_20008, PHNE_20735 on the C200 and now I can see the
  outbound traffic too.  Thanks again.

(although those patches may not be the ones to install - there may be
later patches).

And another <NAME_EMAIL>, from Rick Jones:

  Date: Mon, 29 Apr 2002 15:59:55 -0700
  From: Rick Jones
  To: <EMAIL>
  Subject: Re: [tcpdump-workers] I Can't Capture the Outbound Traffic

	...

  http://itrc.hp.com/ would be one place to start in a search for the most
  up-to-date patches for DLPI and the lan driver(s) used on your system (I
  cannot guess because 9000/800 is too generic - one hs to use the "model"
  command these days and/or an ioscan command (see manpage) to guess what
  the drivers (btlan[3456], gelan, etc) might be involved in addition to
  DLPI.

  Another option is to upgrade to 11i as outbound promiscuous mode support
  is there in the base OS, no patches required.

Another posting:

	http://groups.google.com/groups?selm=7d6gvn%24b3%241%40ocean.cup.hp.com

indicates that you need to install the optional STREAMS product to do
captures on HP-UX 9.x:

  Newsgroups: comp.sys.hp.hpux
  Subject:  Re: tcpdump HP/UX 9.x
  Date: 03/22/1999
  From: Rick Jones <<EMAIL>>

  Dave Barr (<EMAIL>) wrote:
  : Has anyone ported tcpdump (or something similar) to HP/UX 9.x?

  I'm reasonably confident that any port of tcpdump to 9.X would require
  the (then optional) STREAMS product.  This would bring DLPI, which is
  what one uses to access interfaces in promiscuous mode.

  I'm not sure that HP even sells the 9.X STREAMS product any longer,
  since HP-UX 9.X is off the pricelist (well, maybe 9.10 for the old 68K
  devices).

  Your best bet is to be up on 10.20 or better if that is at all
  possible.  If your hardware is supported by it, I'd go with HP-UX 11.
  If you want to see the system's own outbound traffic, you'll never get
  that functionality on 9.X, but it might happen at some point for 10.20
  and 11.X.

  rick jones

(as per other messages cited here, the ability to see the system's own
outbound traffic did happen).

Rick Jones reports that HP-UX 11i needs no patches for outbound
promiscuous mode support.

An additional note, from Jost Martin, for HP-UX 10.20:

	Q: How do I get wireshark on HPUX to capture the _outgoing_ packets
	   of an interface
	A: You need to get PHNE_20892,PHNE_20725 and PHCO_10947 (or
	   newer, this is as of 4.4.00) and its dependencies.  Then you can
	   enable the feature as described below:

	Patch Name: PHNE_20892
	Patch Description: s700 10.20 PCI 100Base-T cumulative patch
		To trace the outbound packets, please do the following
		to turn on a global promiscuous switch before running
		the promiscuous applications like snoop or tcpdump:

		adb -w /stand/vmunix /dev/mem
		lanc_outbound_promisc_flag/W 1
		(adb will echo the result showing that the flag has
		been changed)
		$quit
	(Thanks for this part to HP-support, Ratingen)

		The attached hack does this and some security-related stuff
	(<NAME_EMAIL> (Ralf Hildebrandt) who
	posted the security-part some time ago)

		 <<hack_ip_stack>>

		(Don't switch IP-forwarding off, if you need it !)
		Install the hack as /sbin/init.d/hacl_ip_stack (adjust
	permissions !) and make a sequencing-symlink
	/sbin/rc2.d/S350hack_ip_stack pointing to this script.
		Now all this is done on every reboot.

According to Rick Jones, the global promiscuous switch also has to be
turned on for HP-UX 11.00, but not for 11i - and, in fact, the switch
doesn't even exist on 11i.

Here's the "hack_ip_stack" script:

-----------------------------------Cut Here-------------------------------------
#!/sbin/sh
#
# nettune:  hack kernel parms for safety

OKAY=0
ERROR=-1

# /usr/contrib/bin fuer nettune auf Pfad
PATH=/sbin:/usr/sbin:/usr/bin:/usr/contrib/bin
export PATH


##########
#  main  #
##########

case $1 in
   start_msg)
      print "Tune IP-Stack for security"
      exit $OKAY
      ;;

   stop_msg)
      print "This action is not applicable"
      exit $OKAY
      ;;

   stop)
      exit $OKAY
      ;;

   start)
      ;;  # fall through

   *)
      print "USAGE: $0 {start_msg | stop_msg | start | stop}" >&2
      exit $ERROR
      ;;
   esac

###########
#  start  #
###########

#
# tcp-Sequence-Numbers nicht mehr inkrementieren sondern random
# Syn-Flood-Protection an
# ip_forwarding aus
# Source-Routing aus
# Ausgehende Packets an ethereal/tcpdump etc.

/usr/contrib/bin/nettune -s tcp_random_seq 2 || exit $ERROR
/usr/contrib/bin/nettune -s hp_syn_protect 1 || exit $ERROR
/usr/contrib/bin/nettune -s ip_forwarding 0 || exit $ERROR
echo 'ip_block_source_routed/W1' | /usr/bin/adb -w /stand/vmunix /dev/kmem || exit $ERROR
echo 'lanc_outbound_promisc_flag/W 1' | adb -w /stand/vmunix /dev/mem  || exit $ERROR

exit $OKAY
-----------------------------------Cut Here-------------------------------------
