libpcap 0.7.1 and later appear to work on AIX when using AIX's native
BPF; that appears to work better than DLPI does.  Note that you may have
to run AIX's tcpdump, as root, before configuring, building, and
installing libpcap, in order to create the "/dev/bpf" devices and load
the BPF driver.

However, libpcap 0.7.1 doesn't work perfectly with AIX's BPF - it
appears that AIX's BPF devices inform their user that packets were
dropped since the last successful read by returning -1 and setting
"errno" to EFAULT, which libpcap 0.7.1 treats as an error.  The current
CVS version of libpcap ignores EFAULT on AIX; it appears that this fixes
the problem.

Some earlier notes:

The notes about libpcap may not apply, with libpcap 0.7.1 and later, but
they're preserved here for historical reasons.

The notes about glib, gtk+, and Ethereal may not apply, as we're now
using GLib 2.x and GTK+ 2.x, and don't have our own gtkclist.c, but
they're also preserved for historical reasons.

After much work and toil, <PERSON> was able to compile libpcap
and <PERSON><PERSON><PERSON> on AIX 4.3.2.  His odyssey is document in various e-mails
at http://www.ethereal.com/lists/ethereal-dev/199911/

Here are a few excerpts.  Note that, to configure "libpcap" to use DLPI
rather than BPF (which it'll apparently use by default on AIX),
specifying the flag

	--with-pcap=dlpi

to the "configure" script for "libpcap" should do the trick.

The source code changes to Ethereal mentioned below should be in the
current source tree.  The changes to the GLib configure script is in
GLib 1.2.7; the changes for the "-lgdk" problem are probably still
necessary in the current version of GTK+.

Subject: Re: [ethereal-dev] Re: [ethereal-users] Problems compiling 0.7.7 under AIX 4.3.2 
From: Gilbert Ramirez <<EMAIL>> 
Date: Fri, 5 Nov 1999 16:58:17 -0600 
To: Guy Harris <<EMAIL>> 
Cc: Craig Rodrigues <<EMAIL>>, <EMAIL> 


On Fri, Nov 05, 1999 at 01:42:44PM -0600, Guy Harris wrote:
> 
> 
> Hmm.
> 
> Looks suspiciously similar to the previous error; have you tried
> recompiling GTK+ with "xlc_r"?

I believe glib and gtk+ should both be compiled with xlc_r. I haven't
compiled on AIX in a long time, but I think it's because glib is including
pthread stuff, so the re-entrant C library, libc_r, is needed. 


Compiler Invocation

When compiling a multi-threaded program, you should invoke the C compiler
using one of the following commands:

xlc_r
    Invokes the compiler with default language level of ansi.
cc_r
    Invokes the compiler with default language level of extended.


These commands ensure that the adequate options and libraries are used to be
compliant with the X/Open Version 5 Standard. The POSIX Threads
Specification 1003.1c is a subset of the X/Open Specification.

The following libraries are automatically linked with your program when using these commands:

libpthreads.a
	    Threads library.
libc.a
	    Standard C library


For example, the following command compiles the foo.c multi-threaded C source file and produces the foo executable file:

cc_r -o foo foo.c

See the cc command for more information about C For AIX.


--gilbert


To: <EMAIL> 
Subject: [ethereal-dev] AIX: gtk problem solved, now an ethereal problem 
From: Craig Rodrigues <<EMAIL>> 
Date: Mon, 8 Nov 1999 10:46:25 -0500 
Cc: <EMAIL> 


Hi,

After much sweat and toil, I have managed to get gtk 1.2.6 to
compile and not dump core under AIX.  The solutions were to
(1) apply the attached patch to the configure.ac in the glib-1.2.6
subdirectory

(2)  In the file gtk+-1.2.6/gtk/Makefile, add a link flag -lgdk to link
in gdk.

I have submitted (1) to the gtk-devel mailing list where it has been
accepted.  (2) is an uglier problem, but for now, adding -lgdk by hand
seems to work.

Now I have a problem....I compiled gtk, and that works.
I compiled ethereal (after some minor mods), and it starts,
but when I click on Capture -> Start, I get:

"There are no network interfaces that can be opened."

I am running as root, so I don't think permissions are a problem.

Any ideas?

Thanks.
-- 
Craig Rodrigues        
http://www.gis.net/~craigr    
<EMAIL>          

*** configure.ac.old    Thu Oct  7 17:27:43 1999
--- configure.ac        Sun Nov  7 19:34:36 1999
***************
*** 795,809 ****
	  fi
	  if test "$ac_cv_func_getpwuid_r" = "yes"; then
		  AC_MSG_CHECKING(whether getpwuid_r is posix like)
!                       # getpwuid_r(0, NULL, NULL, 0) is the signature on
!                       # solaris, if that is not found, the prog below won't 
!                       # compile, then the posix signature is assumed as 
!                       # the default.
!                       AC_TRY_COMPILE([#include <pwd.h>],
!                               [getpwuid_r(0, NULL, NULL, 0);],
!                               [AC_MSG_RESULT(no)],
!                               [AC_MSG_RESULT(yes)
!                               AC_DEFINE(HAVE_GETPWUID_R_POSIX)])
	  fi
  fi
  if test x"$have_threads" = xposix; then
--- 795,809 ----
	  fi
	  if test "$ac_cv_func_getpwuid_r" = "yes"; then
		  AC_MSG_CHECKING(whether getpwuid_r is posix like)
!                       # The signature for the POSIX version is:
!                       # int getpwuid_r(uid_t, struct passwd *, char *, size_t, struct passwd **)
!                       AC_TRY_COMPILE([#include <pwd.h>
!                                         #include <sys/types.h>
!                                         #include <stdlib.h>],
!                               [getpwuid_r((uid_t)0, NULL, NULL, (size_t)0, NULL);],
!                               [AC_DEFINE(HAVE_GETPWUID_R_POSIX)
!                               AC_MSG_RESULT(yes)],
!                               [AC_MSG_RESULT(no)])
	  fi
  fi
  if test x"$have_threads" = xposix; then



To: <EMAIL> 
Subject: Re: [ethereal-dev] AIX: gtk problem solved, now an ethereal problem 
From: Craig Rodrigues <<EMAIL>> 
Date: Wed, 10 Nov 1999 12:18:47 -0500 



Hi,

OK, I'm getting closer and closer to this working on AIX.

Things I've done:

(1) In a bunch of places in the code I removed '//' style C++ comments
which the IBM C compiler didn't like.

(2) I also found some places in the code like:

enum some_enum {  FOO, BAR, };

IBM C did not like the trailing "," after BAR.

(3) In packet-ipv6.h, IPV6_VERSION is defined, but that is already
defined in <netinet/in.h> on AIX 4.3, so for now I just commented that out.

(4) in packet-afs.c, when it sucks in <netinet/in.h>,  in.h sucks in
<sys/machine.h> which defines LITTLE_ENDIAN.  This conflicts with
LITTLE_ENDIAN in globals.h.  So what I did was, in globals.h, I added:

#ifdef HAVE_NETINET_IN_H
#include <netinet/in.h>
#endif

So after doing all these things, I can compile ethereal and run it.  
I can list the
correct network interfaces on my system: lo0 and en0.  However,
when I start capturing packets on en0, they are all of the protocol type
"TRMAC" and "TR".  The only problem is, I'm not on a Token Ring network.

Any ideas?

No. Time        Source                Destination           Protocol   Info
1 0.000000    0a:30:a1:08:00:45     06:74:60:08:00:5a     TR   Token-Ring Unknown
2 0.210304    0a:30:a1:08:00:45     06:74:60:08:00:5a     TR   Token-Ring Unknown
3 0.926080    0a:30:a1:08:00:45     06:74:60:08:00:5a     TR   Token-Ring Unknown
4 0.4236416   0a:30:a1:08:00:45     06:74:60:08:00:5a     TR   Token-Ring Unknown
5 0.4712064   6f:06:74:60:08:00     5a:8a:30:a1:00:00 TR MAC Unknown Major Vector: 127


---------------------
It turns out that libpcap was using IFT_* numbers instead of DLT_* numbers for
link types. That has been fixed
---------------------


To: <EMAIL> 
Subject: [ethereal-dev] Sucess with libpcap under AIX 
From: Craig Rodrigues <<EMAIL>> 
Date: Sat, 20 Nov 1999 03:34:50 -0500 
Cc: <EMAIL> 


Hi,

I have managed to successfully compile and use the latest
snapshot of libpcap under AIX using DLPI.  bpf is majorly
brain-dead under AIX, and very unsupported.  Rather than
find all the bugs in AIX's bpf, I decided to try using
dlpi, which is officially supported.

The first step is to get the setup right.  To determine if
you have the dlpi driver loaded correctly, type:
strload -q -d dlpi

If the result is:
dlpi: yes

then you are ready to use dlpi.

If you get:
dlpi: no

Then you need to type:
strload -f /etc/dlpi.conf

Check again with strload -q -d dlpi that the dlpi driver is loaded.

I had to make one minor code change to pcap-dlpi.c.  Maybe someone
can explain it to me, because I am not familiar with dlpi or
streams programming.  It took me hours to figure this out, because
I'm not familiar with dlpi.

In pcap-dlpi.c, lines 316-320:
#if !defined(HAVE_HPUX9) && !defined(HAVE_HPUX10_20) && !defined(sinix)
       if (dlbindreq(p->fd, 0, ebuf) < 0 ||
	   dlbindack(p->fd, (char *)buf, ebuf) < 0)
	    goto bad;
#endif

I changed it to:
#if !defined(HAVE_HPUX9) && !defined(HAVE_HPUX10_20) && !defined(sinix)
       if (dlbindreq(p->fd, 1620, ebuf) < 0 ||
	   dlbindack(p->fd, (char *)buf, ebuf) < 0)
	    goto bad;
#endif

I picked the number 1620 out of thin air.  The second parameter
to dlbindreq() sets the value of dl_sap.  This dl_sap
value is then passed along to the DLPI driver through
the DL_BIND_REQ primitive.  I guess that it cannot be 0 under
AIX, but I'm not sure.

If someone knows anything about DLPI, I'd appreciate a clarification.
Basically, I am just using the DLPI specification at:
http://www.opengroup.org/onlinepubs/009638599/ which is pretty good.
The AIX documentation is not so well written.

But basically, after I fixed up pcap-dlpi.c, I managed to get libpcap
working under AIX.  This enabled me to successfully run Ethereal,
ie. all the packets on my Ethernet network correctly showed up
as Ethernet and not Token Ring in the Wireshark screen.

YAY!
-- 
Craig Rodrigues        
http://www.gis.net/~craigr    
<EMAIL>          

Date: Thu, 11 Nov 1999 23:47:02 -0500
From: Craig Rodrigues <<EMAIL>>
To: <EMAIL>
Subject: Re: [ethereal-dev] AIX: gtk problem solved, now an ethereal  problem

On Thu, Nov 11, 1999 at 11:50:23AM -0800, Guy Harris wrote:
> > The only differences between gtkclist.c in the gtk distribution and
> > gtkclist.c in the ethereal distribution relate to the ROW_ELEMENT
> > macro.  It looks like an optimization for retrieving the GList item
> > when the requested row is the last row in the list.
> 
> Yup - as per my other mail, Ethereal does that rather a lot when
> building the CList, and the optimization changes quadratic behavior to
> linear behavior.
> 
> > Any ideas why this causes trouble?
> 
> Mismatches between the layouts of data structures as declared in the
> "gtk/gtk*.h" files in the Wireshark source tree and the layouts as
> declared in the header files in the GTK+ source (either due to header
> file differences - although the header files appear to be identical to
> the GTK+ 1.2.6 ones - or due to compiler behavior differences)?

I tried stepping things through the debugger, and constantly
hit the same segfault inside gdk_string_width(), line 308 of gdkfont.c

Fails on line: switch(font->type),
where *font is: (type = -1, ascent = -1, descent = -1)

Stack trace:
gdk_string_width(font = 0x7caf01a4, string = "../"), line 308 in "gdkfont.c"
gtk_file_selection_populate(fs = 0x20094468, rel_path = "", try_complete = 0), line 1341 in "gtkfilesel.c"
gtk_file_selection_init(filesel = 0x20094468), line 513 in "gtkfilesel.c"
gtk_type_new(0xc315), line 403 in "gtktypeutils.c"
gtk_file_selection_new(title = "Ethereal: Open Capture File"), line 524 in "gtkfilesel.c"
file_open_cmd_cb(0x200640f4, 0x0), line 79 in "file_dlg.c"

Removing gtkclist.o from libui.a and recompiling removed this problem.

Any ideas?  I'm stumped.

-- 
Craig Rodrigues        
http://www.gis.net/~craigr    
<EMAIL>          
