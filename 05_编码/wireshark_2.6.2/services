# This is a local copy of the IANA port-numbers file.
#
# Wireshark uses it to resolve port numbers into human readable
# service names, e.g. TCP port 80 -> http.
#
# It is subject to copyright and being used with IANA's permission:
# http://www.wireshark.org/lists/wireshark-dev/200708/msg00160.html
#
# The original file can be found at:
# http://www.iana.org/assignments/service-names-port-numbers/service-names-port-numbers.csv
#
# The format is the same as that used for services(5). It is allowed to merge
# identical protocols, for example:
#   foo 64/tcp
#   foo 64/udp
# becomes
#   foo 64/tcp/udp
#

tcpmux		1/tcp/udp	# TCP Port Service Multiplexer
compressnet	2/tcp/udp	# Management Utility
compressnet	3/tcp/udp	# Compression Process
rje		5/tcp/udp	# Remote Job Entry
echo		7/tcp/udp	# Echo
discard		9/tcp/udp/sctp/dccp	# Discard
systat		11/tcp/udp	# Active Users
daytime		13/tcp/udp	# Daytime
qotd		17/tcp/udp	# Quote of the Day
msp		18/tcp/udp	# Message Send Protocol (historic)
chargen		19/tcp/udp	# Character Generator
ftp-data	20/tcp/udp/sctp	# File Transfer [Default Data]
ftp		21/tcp/udp/sctp	# File Transfer Protocol [Control]
ssh		22/tcp/udp/sctp	# The Secure Shell (SSH) Protocol
telnet		23/tcp/udp	# Telnet
smtp		25/tcp/udp	# Simple Mail Transfer
nsw-fe		27/tcp/udp	# NSW User System FE
msg-icp		29/tcp/udp	# MSG ICP
msg-auth	31/tcp/udp	# MSG Authentication
dsp		33/tcp/udp	# Display Support Protocol
time		37/tcp/udp	# Time
rap		38/tcp/udp	# Route Access Protocol
rlp		39/tcp/udp	# Resource Location Protocol
graphics	41/tcp/udp	# Graphics
name		42/tcp/udp	# Host Name Server
nicname		43/tcp/udp	# Who Is
mpm-flags	44/tcp/udp	# MPM FLAGS Protocol
mpm		45/tcp/udp	# Message Processing Module [recv]
mpm-snd		46/tcp/udp	# MPM [default send]
auditd		48/tcp/udp	# Digital Audit Daemon
tacacs		49/tcp/udp	# Login Host Protocol (TACACS)
re-mail-ck	50/tcp/udp	# Remote Mail Checking Protocol
xns-time	52/tcp/udp	# XNS Time Protocol
domain		53/tcp/udp	# Domain Name Server
xns-ch		54/tcp/udp	# XNS Clearinghouse
isi-gl		55/tcp/udp	# ISI Graphics Language
xns-auth	56/tcp/udp	# XNS Authentication
xns-mail	58/tcp/udp	# XNS Mail
acas		62/tcp/udp	# ACA Services
whoispp		63/tcp/udp	# whois++
covia		64/tcp/udp	# Communications Integrator (CI)
tacacs-ds	65/tcp/udp	# TACACS-Database Service
sql-net		66/tcp/udp	# Oracle SQL*NET
bootps		67/tcp/udp	# Bootstrap Protocol Server
bootpc		68/tcp/udp	# Bootstrap Protocol Client
tftp		69/tcp/udp	# Trivial File Transfer
gopher		70/tcp/udp	# Gopher
netrjs-1	71/tcp/udp	# Remote Job Service
netrjs-2	72/tcp/udp	# Remote Job Service
netrjs-3	73/tcp/udp	# Remote Job Service
netrjs-4	74/tcp/udp	# Remote Job Service
deos		76/tcp/udp	# Distributed External Object Store
vettcp		78/tcp/udp
finger		79/tcp/udp	# Finger
http		80/tcp/udp/sctp	# World Wide Web HTTP
xfer		82/tcp/udp	# XFER Utility
mit-ml-dev	83/tcp/udp	# MIT ML Device
ctf		84/tcp/udp	# Common Trace Facility
mit-ml-dev	85/tcp/udp	# MIT ML Device
mfcobol		86/tcp/udp	# Micro Focus Cobol
kerberos	88/tcp/udp	# Kerberos
su-mit-tg	89/tcp/udp	# SU/MIT Telnet Gateway
dnsix		90/tcp/udp	# DNSIX Securit Attribute Token Map
mit-dov		91/tcp/udp	# MIT Dover Spooler
npp		92/tcp/udp	# Network Printing Protocol
dcp		93/tcp/udp	# Device Control Protocol
objcall		94/tcp/udp	# Tivoli Object Dispatcher
supdup		95/tcp/udp	# SUPDUP
dixie		96/tcp/udp	# DIXIE Protocol Specification
swift-rvf	97/tcp/udp	# Swift Remote Virtural File Protocol
tacnews		98/tcp/udp	# TAC News
metagram	99/tcp/udp	# Metagram Relay
hostname	101/tcp/udp	# NIC Host Name Server
iso-tsap	102/tcp/udp	# ISO-TSAP Class 0
gppitnp		103/tcp/udp	# Genesis Point-to-Point Trans Net
acr-nema	104/tcp/udp	# ACR-NEMA Digital Imag. & Comm. 300
cso		105/tcp/udp	# CCSO name server protocol
3com-tsmux	106/tcp/udp	# 3COM-TSMUX
rtelnet		107/tcp/udp	# Remote Telnet Service
snagas		108/tcp/udp	# SNA Gateway Access Server
pop2		109/tcp/udp	# Post Office Protocol - Version 2
pop3		110/tcp/udp	# Post Office Protocol - Version 3
sunrpc		111/tcp/udp	# SUN Remote Procedure Call
mcidas		112/tcp/udp	# McIDAS Data Transmission Protocol
ident		113/tcp
auth		113/udp		# Authentication Service
sftp		115/tcp/udp	# Simple File Transfer Protocol
ansanotify	116/tcp/udp	# ANSA REX Notify
uucp-path	117/tcp/udp	# UUCP Path Service
sqlserv		118/tcp/udp	# SQL Services
nntp		119/tcp/udp	# Network News Transfer Protocol
cfdptkt		120/tcp/udp	# CFDPTKT
erpc		121/tcp/udp	# Encore Expedited Remote Pro.Call
smakynet	122/tcp/udp	# SMAKYNET
ntp		123/tcp/udp	# Network Time Protocol
ansatrader	124/tcp/udp	# ANSA REX Trader
locus-map	125/tcp/udp	# Locus PC-Interface Net Map Ser
nxedit		126/tcp/udp	# NXEdit
locus-con	127/tcp/udp	# Locus PC-Interface Conn Server
gss-xlicen	128/tcp/udp	# GSS X License Verification
pwdgen		129/tcp/udp	# Password Generator Protocol
cisco-fna	130/tcp/udp	# cisco FNATIVE
cisco-tna	131/tcp/udp	# cisco TNATIVE
cisco-sys	132/tcp/udp	# cisco SYSMAINT
statsrv		133/tcp/udp	# Statistics Service
ingres-net	134/tcp/udp	# INGRES-NET Service
epmap		135/tcp/udp	# DCE endpoint resolution
profile		136/tcp/udp	# PROFILE Naming System
netbios-ns	137/tcp/udp	# NETBIOS Name Service
netbios-dgm	138/tcp/udp	# NETBIOS Datagram Service
netbios-ssn	139/tcp/udp	# NETBIOS Session Service
emfis-data	140/tcp/udp	# EMFIS Data Service
emfis-cntl	141/tcp/udp	# EMFIS Control Service
bl-idm		142/tcp/udp	# Britton-Lee IDM
imap		143/tcp/udp	# Internet Message Access Protocol
uma		144/tcp/udp	# Universal Management Architecture
uaac		145/tcp/udp	# UAAC Protocol
iso-tp0		146/tcp/udp	# ISO-IP0
iso-ip		147/tcp/udp	# ISO-IP
jargon		148/tcp/udp	# Jargon
aed-512		149/tcp/udp	# AED 512 Emulation Service
sql-net		150/tcp/udp	# SQL-NET
hems		151/tcp/udp	# HEMS
bftp		152/tcp/udp	# Background File Transfer Program
sgmp		153/tcp/udp	# SGMP
netsc-prod	154/tcp/udp	# NETSC
netsc-dev	155/tcp/udp	# NETSC
sqlsrv		156/tcp/udp	# SQL Service
knet-cmp	157/tcp/udp	# KNET/VM Command/Message Protocol
pcmail-srv	158/tcp/udp	# PCMail Server
nss-routing	159/tcp/udp	# NSS-Routing
sgmp-traps	160/tcp/udp	# SGMP-TRAPS
snmp		161/tcp/udp	# SNMP
snmptrap	162/tcp/udp	# SNMPTRAP
cmip-man	163/tcp/udp	# CMIP/TCP Manager
cmip-agent	164/tcp/udp	# CMIP/TCP Agent
xns-courier	165/tcp/udp	# Xerox
s-net		166/tcp/udp	# Sirius Systems
namp		167/tcp/udp	# NAMP
rsvd		168/tcp/udp	# RSVD
send		169/tcp/udp	# SEND
print-srv	170/tcp/udp	# Network PostScript
multiplex	171/tcp/udp	# Network Innovations Multiplex
cl-1		172/tcp/udp	# Network Innovations CL/1
xyplex-mux	173/tcp/udp	# Xyplex
mailq		174/tcp/udp	# MAILQ
vmnet		175/tcp/udp	# VMNET
genrad-mux	176/tcp/udp	# GENRAD-MUX
xdmcp		177/tcp/udp	# X Display Manager Control Protocol
nextstep	178/tcp/udp	# NextStep Window Server
bgp		179/tcp/udp/sctp	# Border Gateway Protocol
ris		180/tcp/udp	# Intergraph
unify		181/tcp/udp	# Unify
audit		182/tcp/udp	# Unisys Audit SITP
ocbinder	183/tcp/udp	# OCBinder
ocserver	184/tcp/udp	# OCServer
remote-kis	185/tcp/udp	# Remote-KIS
kis		186/tcp/udp	# KIS Protocol
aci		187/tcp/udp	# Application Communication Interface
mumps		188/tcp/udp	# Plus Five's MUMPS
qft		189/tcp/udp	# Queued File Transport
gacp		190/tcp/udp	# Gateway Access Control Protocol
prospero	191/tcp/udp	# Prospero Directory Service
osu-nms		192/tcp/udp	# OSU Network Monitoring System
srmp		193/tcp/udp	# Spider Remote Monitoring Protocol
irc		194/tcp/udp	# Internet Relay Chat Protocol
dn6-nlm-aud	195/tcp/udp	# DNSIX Network Level Module Audit
dn6-smm-red	196/tcp/udp	# DNSIX Session Mgt Module Audit Redir
dls		197/tcp/udp	# Directory Location Service
dls-mon		198/tcp/udp	# Directory Location Service Monitor
smux		199/tcp/udp	# SMUX
src		200/tcp/udp	# IBM System Resource Controller
at-rtmp		201/tcp/udp	# AppleTalk Routing Maintenance
at-nbp		202/tcp/udp	# AppleTalk Name Binding
at-3		203/tcp/udp	# AppleTalk Unused
at-echo		204/tcp/udp	# AppleTalk Echo
at-5		205/tcp/udp	# AppleTalk Unused
at-zis		206/tcp/udp	# AppleTalk Zone Information
at-7		207/tcp/udp	# AppleTalk Unused
at-8		208/tcp/udp	# AppleTalk Unused
qmtp		209/tcp/udp	# The Quick Mail Transfer Protocol
z39-50		210/tcp/udp	# ANSI Z39.50
914c-g		211/tcp/udp	# Texas Instruments 914C/G Terminal
anet		212/tcp/udp	# ATEXSSTR
ipx		213/tcp/udp	# IPX
vmpwscs		214/tcp/udp	# VM PWSCS
softpc		215/tcp/udp	# Insignia Solutions
CAIlic		216/tcp/udp	# Computer Associates Int'l License Server
dbase		217/tcp/udp	# dBASE Unix
mpp		218/tcp/udp	# Netix Message Posting Protocol
uarps		219/tcp/udp	# Unisys ARPs
imap3		220/tcp/udp	# Interactive Mail Access Protocol v3
fln-spx		221/tcp/udp	# Berkeley rlogind with SPX auth
rsh-spx		222/tcp/udp	# Berkeley rshd with SPX auth
cdc		223/tcp/udp	# Certificate Distribution Center
masqdialer	224/tcp/udp
direct		242/tcp/udp	# Direct
sur-meas	243/tcp/udp	# Survey Measurement
inbusiness	244/tcp/udp
link		245/tcp/udp	# LINK
dsp3270		246/tcp/udp	# Display Systems Protocol
subntbcst-tftp	247/tcp/udp	# SUBNTBCST_TFTP
bhfhs		248/tcp/udp
rap		256/tcp/udp	# RAP
set		257/tcp/udp	# Secure Electronic Transaction
esro-gen	259/tcp/udp	# Efficient Short Remote Operations
openport	260/tcp/udp	# Openport
nsiiops		261/tcp/udp	# IIOP Name Service over TLS/SSL
arcisdms	262/tcp/udp	# Arcisdms
hdap		263/tcp/udp	# HDAP
bgmp		264/tcp/udp	# BGMP
x-bone-ctl	265/tcp/udp	# X-Bone CTL
sst		266/tcp/udp	# SCSI on ST
td-service	267/tcp/udp	# Tobit David Service Layer
td-replica	268/tcp/udp	# Tobit David Replica
manet		269/tcp/udp	# MANET Protocols
gist		270/udp		# Q-mode encapsulation for GIST messages
pt-tls		271/tcp		# IETF Network Endpoint Assessment (NEA) Posture Transport Protocol over TLS (PT-TLS)
http-mgmt	280/tcp/udp
personal-link	281/tcp/udp	# Personal Link
cableport-ax	282/tcp/udp	# Cable Port A/X
rescap		283/tcp/udp
corerjd		284/tcp/udp
fxp		286/tcp/udp	# FXP Communication
k-block		287/tcp/udp	# K-BLOCK
novastorbakcup	308/tcp/udp	# Novastor Backup
entrusttime	309/tcp/udp	# EntrustTime
bhmds		310/tcp/udp
asip-webadmin	311/tcp/udp	# AppleShare IP WebAdmin
vslmp		312/tcp/udp	# VSLMP
magenta-logic	313/tcp/udp	# Magenta Logic
opalis-robot	314/tcp/udp	# Opalis Robot
dpsi		315/tcp/udp	# DPSI
decauth		316/tcp/udp	# decAuth
zannet		317/tcp/udp	# Zannet
pkix-timestamp	318/tcp/udp	# PKIX TimeStamp
ptp-event	319/tcp/udp	# PTP Event
ptp-general	320/tcp/udp	# PTP General
pip		321/tcp/udp	# PIP
rtsps		322/tcp/udp	# RTSPS
rpki-rtr	323/tcp		# Resource PKI to Router Protocol
rpki-rtr-tls	324/tcp		# Resource PKI to Router Protocol over TLS
texar		333/tcp/udp	# Texar Security Port
pdap		344/tcp/udp	# Prospero Data Access Protocol
pawserv		345/tcp/udp	# Perf Analysis Workbench
zserv		346/tcp/udp	# Zebra server
fatserv		347/tcp/udp	# Fatmen Server
csi-sgwp	348/tcp/udp	# Cabletron Management Protocol
mftp		349/tcp/udp
matip-type-a	350/tcp/udp	# MATIP Type A
matip-type-b	351/tcp/udp	# MATIP Type B
dtag-ste-sb	352/tcp/udp	# DTAG
ndsauth		353/tcp/udp	# NDSAUTH
bh611		354/tcp/udp
datex-asn	355/tcp/udp	# DATEX-ASN
cloanto-net-1	356/tcp/udp	# Cloanto Net 1
bhevent		357/tcp/udp
shrinkwrap	358/tcp/udp	# Shrinkwrap
nsrmp		359/tcp/udp	# Network Security Risk Management Protocol
scoi2odialog	360/tcp/udp
semantix	361/tcp/udp	# Semantix
srssend		362/tcp/udp	# SRS Send
rsvp-tunnel	363/tcp/udp	# RSVP Tunnel
aurora-cmgr	364/tcp/udp	# Aurora CMGR
dtk		365/tcp/udp	# DTK
odmr		366/tcp/udp	# ODMR
mortgageware	367/tcp/udp	# MortgageWare
qbikgdp		368/tcp/udp	# QbikGDP
rpc2portmap	369/tcp/udp
codaauth2	370/tcp/udp
clearcase	371/tcp/udp	# Clearcase
ulistproc	372/tcp/udp	# ListProcessor
legent-1	373/tcp/udp	# Legent Corporation
legent-2	374/tcp/udp	# Legent Corporation
hassle		375/tcp/udp	# Hassle
nip		376/tcp/udp	# Amiga Envoy Network Inquiry Proto
tnETOS		377/tcp/udp	# NEC Corporation
dsETOS		378/tcp/udp	# NEC Corporation
is99c		379/tcp/udp	# TIA/EIA/IS-99 modem client
is99s		380/tcp/udp	# TIA/EIA/IS-99 modem server
hp-collector	381/tcp/udp	# hp performance data collector
hp-managed-node	382/tcp/udp	# hp performance data managed node
hp-alarm-mgr	383/tcp/udp	# hp performance data alarm manager
arns		384/tcp/udp	# A Remote Network Server System
ibm-app		385/tcp/udp	# IBM Application
asa		386/tcp/udp	# ASA Message Router Object Def.
aurp		387/tcp/udp	# Appletalk Update-Based Routing Pro.
unidata-ldm	388/tcp/udp	# Unidata LDM
ldap		389/tcp/udp	# Lightweight Directory Access Protocol
uis		390/tcp/udp	# UIS
synotics-relay	391/tcp/udp	# SynOptics SNMP Relay Port
synotics-broker	392/tcp/udp	# SynOptics Port Broker Port
meta5		393/tcp/udp	# Meta5
embl-ndt	394/tcp/udp	# EMBL Nucleic Data Transfer
netcp		395/tcp/udp	# NetScout Control Protocol
netware-ip	396/tcp/udp	# Novell Netware over IP
mptn		397/tcp/udp	# Multi Protocol Trans. Net.
kryptolan	398/tcp/udp	# Kryptolan
iso-tsap-c2	399/tcp/udp	# ISO Transport Class 2 Non-Control over TCP
osb-sd		400/tcp/udp	# Oracle Secure Backup
ups		401/tcp/udp	# Uninterruptible Power Supply
genie		402/tcp/udp	# Genie Protocol
decap		403/tcp/udp
nced		404/tcp/udp
ncld		405/tcp/udp
imsp		406/tcp/udp	# Interactive Mail Support Protocol
timbuktu	407/tcp/udp	# Timbuktu
prm-sm		408/tcp/udp	# Prospero Resource Manager Sys. Man.
prm-nm		409/tcp/udp	# Prospero Resource Manager Node Man.
decladebug	410/tcp/udp	# DECLadebug Remote Debug Protocol
rmt		411/tcp/udp	# Remote MT Protocol
synoptics-trap	412/tcp/udp	# Trap Convention Port
smsp		413/tcp/udp	# Storage Management Services Protocol
infoseek	414/tcp/udp	# InfoSeek
bnet		415/tcp/udp	# BNet
silverplatter	416/tcp/udp	# Silverplatter
onmux		417/tcp/udp	# Onmux
hyper-g		418/tcp/udp	# Hyper-G
ariel1		419/tcp/udp	# Ariel 1
smpte		420/tcp/udp	# SMPTE
ariel2		421/tcp/udp	# Ariel 2
ariel3		422/tcp/udp	# Ariel 3
opc-job-start	423/tcp/udp	# IBM Operations Planning and Control Start
opc-job-track	424/tcp/udp	# IBM Operations Planning and Control Track
icad-el		425/tcp/udp	# ICAD
smartsdp	426/tcp/udp
svrloc		427/tcp/udp	# Server Location
ocs-cmu		428/tcp/udp	# OCS_CMU
ocs-amu		429/tcp/udp	# OCS_AMU
utmpsd		430/tcp/udp	# UTMPSD
utmpcd		431/tcp/udp	# UTMPCD
iasd		432/tcp/udp	# IASD
nnsp		433/tcp/udp	# NNTP for transit servers (NNSP)
mobileip-agent	434/tcp/udp	# MobileIP-Agent
mobilip-mn	435/tcp/udp	# MobilIP-MN
dna-cml		436/tcp/udp	# DNA-CML
comscm		437/tcp/udp
dsfgw		438/tcp/udp
dasp		439/tcp/udp
sgcp		440/tcp/udp
decvms-sysmgt	441/tcp/udp
cvc-hostd	442/tcp/udp	# cvc_hostd
https		443/tcp/udp/sctp	# http protocol over TLS/SSL
snpp		444/tcp/udp	# Simple Network Paging Protocol
microsoft-ds	445/tcp/udp	# Microsoft-DS
ddm-rdb		446/tcp/udp	# DDM-Remote Relational Database Access
ddm-dfm		447/tcp/udp	# DDM-Distributed File Management
ddm-ssl		448/tcp/udp	# DDM-Remote DB Access Using Secure Sockets
as-servermap	449/tcp/udp	# AS Server Mapper
tserver		450/tcp/udp	# Computer Supported Telecomunication Applications
sfs-smp-net	451/tcp/udp	# Cray Network Semaphore server
sfs-config	452/tcp/udp	# Cray SFS config server
creativeserver	453/tcp/udp	# CreativeServer
contentserver	454/tcp/udp	# ContentServer
creativepartnr	455/tcp/udp	# CreativePartnr
macon-tcp	456/tcp
macon-udp	456/udp
scohelp		457/tcp/udp
appleqtc	458/tcp/udp	# apple quick time
ampr-rcmd	459/tcp/udp
skronk		460/tcp/udp
datasurfsrv	461/tcp/udp	# DataRampSrv
datasurfsrvsec	462/tcp/udp	# DataRampSrvSec
alpes		463/tcp/udp
kpasswd		464/tcp/udp
urd		465/tcp		# URL Rendezvous Directory for SSM
igmpv3lite	465/udp		# IGMP over UDP for SSM
digital-vrc	466/tcp/udp
mylex-mapd	467/tcp/udp
photuris	468/tcp/udp	# proturis
rcp		469/tcp/udp	# Radio Control Protocol
scx-proxy	470/tcp/udp
mondex		471/tcp/udp	# Mondex
ljk-login	472/tcp/udp
hybrid-pop	473/tcp/udp
tn-tl-w1	474/tcp
tn-tl-w2	474/udp
tcpnethaspsrv	475/tcp/udp
tn-tl-fd1	476/tcp/udp
ss7ns		477/tcp/udp
spsc		478/tcp/udp
iafserver	479/tcp/udp
iafdbase	480/tcp/udp
ph		481/tcp/udp	# Ph service
bgs-nsi		482/tcp/udp
ulpnet		483/tcp/udp
integra-sme	484/tcp/udp	# Integra Software Management Environment
powerburst	485/tcp/udp	# Air Soft Power Burst
avian		486/tcp/udp
saft		487/tcp/udp	# saft Simple Asynchronous File Transfer
gss-http	488/tcp/udp
nest-protocol	489/tcp/udp
micom-pfs	490/tcp/udp
go-login	491/tcp/udp
ticf-1		492/tcp/udp	# Transport Independent Convergence for FNA
ticf-2		493/tcp/udp	# Transport Independent Convergence for FNA
pov-ray		494/tcp/udp	# POV-Ray
intecourier	495/tcp/udp
pim-rp-disc	496/tcp/udp	# PIM-RP-DISC
retrospect	497/tcp/udp	# Retrospect backup and restore service
siam		498/tcp/udp
iso-ill		499/tcp/udp	# ISO ILL Protocol
isakmp		500/tcp/udp
stmf		501/tcp/udp	# STMF
mbap		502/tcp/udp	# Modbus Application Protocol
intrinsa	503/tcp/udp	# Intrinsa
citadel		504/tcp/udp
mailbox-lm	505/tcp/udp
ohimsrv		506/tcp/udp
crs		507/tcp/udp
xvttp		508/tcp/udp
snare		509/tcp/udp
fcp		510/tcp/udp	# FirstClass Protocol
passgo		511/tcp/udp	# PassGo
exec		512/tcp		# remote process execution; authentication performed using passwords and UNIX login names
comsat		512/udp
login		513/tcp		# remote login a la telnet; automatic authentication performed based on priviledged port numbers and distributed data bases which identify "authentication domains"
who		513/udp		# maintains data bases showing who's logged in to machines on a local net and the load average of the machine
shell		514/tcp		# cmd like exec, but automatic authentication is performed as for login server
syslog		514/udp
printer		515/tcp/udp	# spooler
videotex	516/tcp/udp
talk		517/tcp/udp	# like tenex link, but across machine - unfortunately, doesn't use link protocol (this is actually just a rendezvous port from which a tcp connection is established)
ntalk		518/tcp/udp
utime		519/tcp/udp	# unixtime
efs		520/tcp		# extended file name server
router		520/udp		# local routing process (on site); uses variant of Xerox NS routing information protocol - RIP
ripng		521/tcp/udp
ulp		522/tcp/udp	# ULP
ibm-db2		523/tcp/udp	# IBM-DB2
ncp		524/tcp/udp	# NCP
timed		525/tcp/udp	# timeserver
tempo		526/tcp/udp	# newdate
stx		527/tcp/udp	# Stock IXChange
custix		528/tcp/udp	# Customer IXChange
irc-serv	529/tcp/udp	# IRC-SERV
courier		530/tcp/udp	# rpc
conference	531/tcp/udp	# chat
netnews		532/tcp/udp	# readnews
netwall		533/tcp/udp	# for emergency broadcasts
windream	534/tcp/udp	# windream Admin
iiop		535/tcp/udp
opalis-rdv	536/tcp/udp
nmsp		537/tcp/udp	# Networked Media Streaming Protocol
gdomap		538/tcp/udp
apertus-ldp	539/tcp/udp	# Apertus Technologies Load Determination
uucp		540/tcp/udp	# uucpd
uucp-rlogin	541/tcp/udp
commerce	542/tcp/udp
klogin		543/tcp/udp
kshell		544/tcp/udp	# krcmd
appleqtcsrvr	545/tcp/udp
dhcpv6-client	546/tcp/udp	# DHCPv6 Client
dhcpv6-server	547/tcp/udp	# DHCPv6 Server
afpovertcp	548/tcp/udp	# AFP over TCP
idfp		549/tcp/udp	# IDFP
new-rwho	550/tcp/udp	# new-who
cybercash	551/tcp/udp
devshr-nts	552/tcp/udp	# DeviceShare
pirp		553/tcp/udp
rtsp		554/tcp/udp	# Real Time Streaming Protocol (RTSP)
dsf		555/tcp/udp
remotefs	556/tcp/udp	# rfs server
openvms-sysipc	557/tcp/udp
sdnskmp		558/tcp/udp	# SDNSKMP
teedtap		559/tcp/udp	# TEEDTAP
rmonitor	560/tcp/udp	# rmonitord
monitor		561/tcp/udp
chshell		562/tcp/udp	# chcmd
nntps		563/tcp/udp	# nntp protocol over TLS/SSL (was snntp)
9pfs		564/tcp/udp	# plan 9 file service
whoami		565/tcp/udp
streettalk	566/tcp/udp
banyan-rpc	567/tcp/udp
ms-shuttle	568/tcp/udp	# microsoft shuttle
ms-rome		569/tcp/udp	# microsoft rome
meter		570/tcp/udp	# demon
meter		571/tcp/udp	# udemon
sonar		572/tcp/udp
banyan-vip	573/tcp/udp
ftp-agent	574/tcp/udp	# FTP Software Agent System
vemmi		575/tcp/udp	# VEMMI
ipcd		576/tcp/udp
vnas		577/tcp/udp
ipdd		578/tcp/udp
decbsrv		579/tcp/udp
sntp-heartbeat	580/tcp/udp	# SNTP HEARTBEAT
bdp		581/tcp/udp	# Bundle Discovery Protocol
scc-security	582/tcp/udp	# SCC Security
philips-vc	583/tcp/udp	# Philips Video-Conferencing
keyserver	584/tcp/udp	# Key Server
password-chg	586/tcp/udp	# Password Change
submission	587/tcp/udp	# Message Submission
cal		588/tcp/udp	# CAL
eyelink		589/tcp/udp	# EyeLink
tns-cml		590/tcp/udp	# TNS CML
http-alt	591/tcp/udp	# FileMaker, Inc. - HTTP Alternate (see Port 80)
eudora-set	592/tcp/udp	# Eudora Set
http-rpc-epmap	593/tcp/udp	# HTTP RPC Ep Map
tpip		594/tcp/udp	# TPIP
cab-protocol	595/tcp/udp	# CAB Protocol
smsd		596/tcp/udp	# SMSD
ptcnameservice	597/tcp/udp	# PTC Name Service
sco-websrvrmg3	598/tcp/udp	# SCO Web Server Manager 3
acp		599/tcp/udp	# Aeolon Core Protocol
ipcserver	600/tcp/udp	# Sun IPC server
syslog-conn	601/tcp/udp	# Reliable Syslog Service
xmlrpc-beep	602/tcp/udp	# XML-RPC over BEEP
idxp		603/tcp/udp	# IDXP
tunnel		604/tcp/udp	# TUNNEL
soap-beep	605/tcp/udp	# SOAP over BEEP
urm		606/tcp/udp	# Cray Unified Resource Manager
nqs		607/tcp/udp
sift-uft	608/tcp/udp	# Sender-Initiated/Unsolicited File Transfer
npmp-trap	609/tcp/udp
npmp-local	610/tcp/udp
npmp-gui	611/tcp/udp
hmmp-ind	612/tcp/udp	# HMMP Indication
hmmp-op		613/tcp/udp	# HMMP Operation
sshell		614/tcp/udp	# SSLshell
sco-inetmgr	615/tcp/udp	# Internet Configuration Manager
sco-sysmgr	616/tcp/udp	# SCO System Administration Server
sco-dtmgr	617/tcp/udp	# SCO Desktop Administration Server
dei-icda	618/tcp/udp	# DEI-ICDA
compaq-evm	619/tcp/udp	# Compaq EVM
sco-websrvrmgr	620/tcp/udp	# SCO WebServer Manager
escp-ip		621/tcp/udp	# ESCP
collaborator	622/tcp/udp	# Collaborator
oob-ws-http	623/tcp		# DMTF out-of-band web services management protocol
asf-rmcp	623/udp		# ASF Remote Management and Control Protocol
cryptoadmin	624/tcp/udp	# Crypto Admin
dec-dlm		625/tcp/udp	# DEC DLM
asia		626/tcp/udp	# ASIA
passgo-tivoli	627/tcp/udp	# PassGo Tivoli
qmqp		628/tcp/udp	# QMQP
3com-amp3	629/tcp/udp	# 3Com AMP3
rda		630/tcp/udp	# RDA
ipp		631/tcp/udp	# IPP (Internet Printing Protocol)
bmpp		632/tcp/udp
servstat	633/tcp/udp	# Service Status update (Sterling Software)
ginad		634/tcp/udp
rlzdbase	635/tcp/udp	# RLZ DBase
ldaps		636/tcp/udp	# ldap protocol over TLS/SSL (was sldap)
lanserver	637/tcp/udp
mcns-sec	638/tcp/udp
msdp		639/tcp/udp	# MSDP
entrust-sps	640/tcp/udp
repcmd		641/tcp/udp
esro-emsdp	642/tcp/udp	# ESRO-EMSDP V1.3
sanity		643/tcp/udp	# SANity
dwr		644/tcp/udp
pssc		645/tcp/udp	# PSSC
ldp		646/tcp/udp	# LDP
dhcp-failover	647/tcp/udp	# DHCP Failover
rrp		648/tcp/udp	# Registry Registrar Protocol (RRP)
cadview-3d	649/tcp/udp	# Cadview-3d - streaming 3d models over the internet
obex		650/tcp/udp	# OBEX
ieee-mms	651/tcp/udp	# IEEE MMS
hello-port	652/tcp/udp	# HELLO_PORT
repscmd		653/tcp/udp	# RepCmd
aodv		654/tcp/udp	# AODV
tinc		655/tcp/udp	# TINC
spmp		656/tcp/udp	# SPMP
rmc		657/tcp/udp	# RMC
tenfold		658/tcp/udp	# TenFold
mac-srvr-admin	660/tcp/udp	# MacOS Server Admin
hap		661/tcp/udp	# HAP
pftp		662/tcp/udp	# PFTP
purenoise	663/tcp/udp	# PureNoise
oob-ws-https	664/tcp		# DMTF out-of-band secure web services management protocol
asf-secure-rmcp	664/udp		# ASF Secure Remote Management and Control Protocol
sun-dr		665/tcp/udp	# Sun DR
mdqs		666/tcp/udp
disclose	667/tcp/udp	# campaign contribution disclosures - SDR Technologies
mecomm		668/tcp/udp	# MeComm
meregister	669/tcp/udp	# MeRegister
vacdsm-sws	670/tcp/udp	# VACDSM-SWS
vacdsm-app	671/tcp/udp	# VACDSM-APP
vpps-qua	672/tcp/udp	# VPPS-QUA
cimplex		673/tcp/udp	# CIMPLEX
acap		674/tcp/udp	# ACAP
dctp		675/tcp/udp	# DCTP
vpps-via	676/tcp/udp	# VPPS Via
vpp		677/tcp/udp	# Virtual Presence Protocol
ggf-ncp		678/tcp/udp	# GNU Generation Foundation NCP
mrm		679/tcp/udp	# MRM
entrust-aaas	680/tcp/udp
entrust-aams	681/tcp/udp
xfr		682/tcp/udp	# XFR
corba-iiop	683/tcp/udp	# CORBA IIOP
corba-iiop-ssl	684/tcp/udp	# CORBA IIOP SSL
mdc-portmapper	685/tcp/udp	# MDC Port Mapper
hcp-wismar	686/tcp/udp	# Hardware Control Protocol Wismar
asipregistry	687/tcp/udp
realm-rusd	688/tcp/udp	# ApplianceWare managment protocol
nmap		689/tcp/udp	# NMAP
vatp		690/tcp/udp	# Velneo Application Transfer Protocol
msexch-routing	691/tcp/udp	# MS Exchange Routing
hyperwave-isp	692/tcp/udp	# Hyperwave-ISP
connendp	693/tcp/udp	# almanid Connection Endpoint
ha-cluster	694/tcp/udp
ieee-mms-ssl	695/tcp/udp	# IEEE-MMS-SSL
rushd		696/tcp/udp	# RUSHD
uuidgen		697/tcp/udp	# UUIDGEN
olsr		698/tcp/udp	# OLSR
accessnetwork	699/tcp/udp	# Access Network
epp		700/tcp/udp	# Extensible Provisioning Protocol
lmp		701/tcp/udp	# Link Management Protocol (LMP)
iris-beep	702/tcp/udp	# IRIS over BEEP
elcsd		704/tcp/udp	# errlog copy/server daemon
agentx		705/tcp/udp	# AgentX
silc		706/tcp/udp	# SILC
borland-dsj	707/tcp/udp	# Borland DSJ
entrust-kmsh	709/tcp/udp	# Entrust Key Management Service Handler
entrust-ash	710/tcp/udp	# Entrust Administration Service Handler
cisco-tdp	711/tcp/udp	# Cisco TDP
tbrpf		712/tcp/udp	# TBRPF
iris-xpc	713/tcp/udp	# IRIS over XPC
iris-xpcs	714/tcp/udp	# IRIS over XPCS
iris-lwz	715/tcp/udp	# IRIS-LWZ
pana		716/udp		# PANA Messages
netviewdm1	729/tcp/udp	# IBM NetView DM/6000 Server/Client
netviewdm2	730/tcp/udp	# IBM NetView DM/6000 send/tcp
netviewdm3	731/tcp/udp	# IBM NetView DM/6000 receive/tcp
netgw		741/tcp/udp	# netGW
netrcs		742/tcp/udp	# Network based Rev. Cont. Sys.
flexlm		744/tcp/udp	# Flexible License Manager
fujitsu-dev	747/tcp/udp	# Fujitsu Device Control
ris-cm		748/tcp/udp	# Russell Info Sci Calendar Manager
kerberos-adm	749/tcp/udp	# kerberos administration
rfile		750/tcp
loadav		750/udp
pump		751/tcp/udp
qrh		752/tcp/udp
rrh		753/tcp/udp
tell		754/tcp/udp	# send
nlogin		758/tcp/udp
con		759/tcp/udp
ns		760/tcp/udp
rxe		761/tcp/udp
quotad		762/tcp/udp
cycleserv	763/tcp/udp
omserv		764/tcp/udp
webster		765/tcp/udp
phonebook	767/tcp/udp	# phone
vid		769/tcp/udp
cadlock		770/tcp/udp
rtip		771/tcp/udp
cycleserv2	772/tcp/udp
submit		773/tcp
notify		773/udp
rpasswd		774/tcp
acmaint-dbd	774/udp
entomb		775/tcp
acmaint-transd	775/udp
wpages		776/tcp/udp
multiling-http	777/tcp/udp	# Multiling HTTP
wpgs		780/tcp/udp
mdbs-daemon	800/tcp/udp
device		801/tcp/udp
mbap-s		802/tcp/udp	# Modbus Application Protocol Secure
fcp-udp		810/tcp/udp	# FCP
itm-mcell-s	828/tcp/udp
pkix-3-ca-ra	829/tcp/udp	# PKIX-3 CA/RA
netconf-ssh	830/tcp/udp	# NETCONF over SSH
netconf-beep	831/tcp/udp	# NETCONF over BEEP
netconfsoaphttp	832/tcp/udp	# NETCONF for SOAP over HTTPS
netconfsoapbeep	833/tcp/udp	# NETCONF for SOAP over BEEP
dhcp-failover2	847/tcp/udp	# dhcp-failover 2
gdoi		848/tcp/udp	# GDOI
domain-s	853/tcp/udp	# DNS query-response protocol run over TLS/DTLS
dlep		854/tcp/udp	# Dynamic Link Exchange Protocol (DLEP)
iscsi		860/tcp/udp	# iSCSI
owamp-control	861/tcp/udp	# OWAMP-Control
twamp-control	862/tcp/udp	# Two-way Active Measurement Protocol (TWAMP) Control
rsync		873/tcp/udp
iclcnet-locate	886/tcp/udp	# ICL coNETion locate server
iclcnet-svinfo	887/tcp/udp	# ICL coNETion server info
accessbuilder	888/tcp/udp	# AccessBuilder
omginitialrefs	900/tcp/udp	# OMG Initial Refs
smpnameres	901/tcp/udp	# SMPNAMERES
ideafarm-door	902/tcp/udp	# self documenting Telnet Door
ideafarm-panic	903/tcp/udp	# self documenting Telnet Panic Door
kink		910/tcp/udp	# Kerberized Internet Negotiation of Keys (KINK)
xact-backup	911/tcp/udp
apex-mesh	912/tcp/udp	# APEX relay-relay service
apex-edge	913/tcp/udp	# APEX endpoint-relay service
rndc		953/tcp		# BIND9 remote name daemon controller
ftps-data	989/tcp/udp	# ftp protocol, data, over TLS/SSL
ftps		990/tcp/udp	# ftp protocol, control, over TLS/SSL
nas		991/tcp/udp	# Netnews Administration System
telnets		992/tcp/udp	# telnet protocol over TLS/SSL
imaps		993/tcp/udp	# IMAP over TLS protocol
pop3s		995/tcp/udp	# POP3 over TLS protocol
vsinet		996/tcp/udp
maitrd		997/tcp/udp
busboy		998/tcp
puparp		998/udp
garcon		999/tcp
applix		999/udp		# Applix ac
cadlock2	1000/tcp/udp
webpush		1001/tcp	# HTTP Web Push
surf		1010/tcp/udp
exp1		1021/tcp/udp/sctp/dccp	# RFC3692-style Experiment 1
exp2		1022/tcp/udp/sctp/dccp	# RFC3692-style Experiment 2
blackjack	1025/tcp/udp	# network blackjack
cap		1026/tcp/udp	# Calendar Access Protocol
6a44		1027/udp	# IPv6 Behind NAT44 CPEs
solid-mux	1029/tcp/udp	# Solid Mux Server
netinfo-local	1033/tcp/udp	# local netinfo port
activesync	1034/tcp/udp	# ActiveSync Notifications
mxxrlogin	1035/tcp/udp	# MX-XR RPC
nsstp		1036/tcp/udp	# Nebula Secure Segment Transfer Protocol
ams		1037/tcp/udp	# AMS
mtqp		1038/tcp/udp	# Message Tracking Query Protocol
sbl		1039/tcp/udp	# Streamlined Blackhole
netarx		1040/tcp/udp	# Netarx Netcare
danf-ak2	1041/tcp/udp	# AK2 Product
afrog		1042/tcp/udp	# Subnet Roaming
boinc-client	1043/tcp/udp	# BOINC Client Control
dcutility	1044/tcp/udp	# Dev Consortium Utility
fpitp		1045/tcp/udp	# Fingerprint Image Transfer Protocol
wfremotertm	1046/tcp/udp	# WebFilter Remote Monitor
neod1		1047/tcp/udp	# Sun's NEO Object Request Broker
neod2		1048/tcp/udp	# Sun's NEO Object Request Broker
td-postman	1049/tcp/udp	# Tobit David Postman VPMN
cma		1050/tcp/udp	# CORBA Management Agent
optima-vnet	1051/tcp/udp	# Optima VNET
ddt		1052/tcp/udp	# Dynamic DNS Tools
remote-as	1053/tcp/udp	# Remote Assistant (RA)
brvread		1054/tcp/udp	# BRVREAD
ansyslmd	1055/tcp/udp	# ANSYS - License Manager
vfo		1056/tcp/udp	# VFO
startron	1057/tcp/udp	# STARTRON
nim		1058/tcp/udp
nimreg		1059/tcp/udp
polestar	1060/tcp/udp	# POLESTAR
kiosk		1061/tcp/udp	# KIOSK
veracity	1062/tcp/udp	# Veracity
kyoceranetdev	1063/tcp/udp	# KyoceraNetDev
jstel		1064/tcp/udp	# JSTEL
syscomlan	1065/tcp/udp	# SYSCOMLAN
fpo-fns		1066/tcp/udp	# FPO-FNS
instl-boots	1067/tcp/udp	# Installation Bootstrap Proto. Serv.
instl-bootc	1068/tcp/udp	# Installation Bootstrap Proto. Cli.
cognex-insight	1069/tcp/udp	# COGNEX-INSIGHT
gmrupdateserv	1070/tcp/udp	# GMRUpdateSERV
bsquare-voip	1071/tcp/udp	# BSQUARE-VOIP
cardax		1072/tcp/udp	# CARDAX
bridgecontrol	1073/tcp/udp	# Bridge Control
warmspotMgmt	1074/tcp/udp	# Warmspot Management Protocol
rdrmshc		1075/tcp/udp	# RDRMSHC
dab-sti-c	1076/tcp/udp	# DAB STI-C
imgames		1077/tcp/udp	# IMGames
avocent-proxy	1078/tcp/udp	# Avocent Proxy Protocol
asprovatalk	1079/tcp/udp	# ASPROVATalk
socks		1080/tcp/udp	# Socks
pvuniwien	1081/tcp/udp	# PVUNIWIEN
amt-esd-prot	1082/tcp/udp	# AMT-ESD-PROT
ansoft-lm-1	1083/tcp/udp	# Anasoft License Manager
ansoft-lm-2	1084/tcp/udp	# Anasoft License Manager
webobjects	1085/tcp/udp	# Web Objects
cplscrambler-lg	1086/tcp/udp	# CPL Scrambler Logging
cplscrambler-in	1087/tcp/udp	# CPL Scrambler Internal
cplscrambler-al	1088/tcp/udp	# CPL Scrambler Alarm Log
ff-annunc	1089/tcp/udp	# FF Annunciation
ff-fms		1090/tcp/udp	# FF Fieldbus Message Specification
ff-sm		1091/tcp/udp	# FF System Management
obrpd		1092/tcp/udp	# Open Business Reporting Protocol
proofd		1093/tcp/udp	# PROOFD
rootd		1094/tcp/udp	# ROOTD
nicelink	1095/tcp/udp	# NICELink
cnrprotocol	1096/tcp/udp	# Common Name Resolution Protocol
sunclustermgr	1097/tcp/udp	# Sun Cluster Manager
rmiactivation	1098/tcp/udp	# RMI Activation
rmiregistry	1099/tcp/udp	# RMI Registry
mctp		1100/tcp/udp	# MCTP
pt2-discover	1101/tcp/udp	# PT2-DISCOVER
adobeserver-1	1102/tcp/udp	# ADOBE SERVER 1
adobeserver-2	1103/tcp/udp	# ADOBE SERVER 2
xrl		1104/tcp/udp	# XRL
ftranhc		1105/tcp/udp	# FTRANHC
isoipsigport-1	1106/tcp/udp	# ISOIPSIGPORT-1
isoipsigport-2	1107/tcp/udp	# ISOIPSIGPORT-2
ratio-adp	1108/tcp/udp
webadmstart	1110/tcp	# Start web admin server
nfsd-keepalive	1110/udp	# Client status info
lmsocialserver	1111/tcp/udp	# LM Social Server
icp		1112/tcp/udp	# Intelligent Communication Protocol
ltp-deepspace	1113/tcp/udp/dccp	# Licklider Transmission Protocol
mini-sql	1114/tcp/udp	# Mini SQL
ardus-trns	1115/tcp/udp	# ARDUS Transfer
ardus-cntl	1116/tcp/udp	# ARDUS Control
ardus-mtrns	1117/tcp/udp	# ARDUS Multicast Transfer
sacred		1118/tcp/udp	# SACRED
bnetgame	1119/tcp/udp	# Battle.net Chat/Game Protocol
bnetfile	1120/tcp/udp	# Battle.net File Transfer Protocol
rmpp		1121/tcp/udp	# Datalode RMPP
availant-mgr	1122/tcp/udp
murray		1123/tcp/udp	# Murray
hpvmmcontrol	1124/tcp/udp	# HP VMM Control
hpvmmagent	1125/tcp/udp	# HP VMM Agent
hpvmmdata	1126/tcp/udp	# HP VMM Agent
kwdb-commn	1127/tcp/udp	# KWDB Remote Communication
saphostctrl	1128/tcp/udp	# SAPHostControl over SOAP/HTTP
saphostctrls	1129/tcp/udp	# SAPHostControl over SOAP/HTTPS
casp		1130/tcp/udp	# CAC App Service Protocol
caspssl		1131/tcp/udp	# CAC App Service Protocol Encripted
kvm-via-ip	1132/tcp/udp	# KVM-via-IP Management Service
dfn		1133/tcp/udp	# Data Flow Network
aplx		1134/tcp/udp	# MicroAPL APLX
omnivision	1135/tcp/udp	# OmniVision Communication Service
hhb-gateway	1136/tcp/udp	# HHB Gateway Control
trim		1137/tcp/udp	# TRIM Workgroup Service
encrypted-admin	1138/tcp/udp	# encrypted admin requests
evm		1139/tcp/udp	# Enterprise Virtual Manager
autonoc		1140/tcp/udp	# AutoNOC Network Operations Protocol
mxomss		1141/tcp/udp	# User Message Service
edtools		1142/tcp/udp	# User Discovery Service
imyx		1143/tcp/udp	# Infomatryx Exchange
fuscript	1144/tcp/udp	# Fusion Script
x9-icue		1145/tcp/udp	# X9 iCue Show Control
audit-transfer	1146/tcp/udp
capioverlan	1147/tcp/udp	# CAPIoverLAN
elfiq-repl	1148/tcp/udp	# Elfiq Replication Service
bvtsonar	1149/tcp/udp	# BlueView Sonar Service
blaze		1150/tcp/udp	# Blaze File Server
unizensus	1151/tcp/udp	# Unizensus Login Server
winpoplanmess	1152/tcp/udp	# Winpopup LAN Messenger
c1222-acse	1153/tcp/udp	# ANSI C12.22 Port
resacommunity	1154/tcp/udp	# Community Service
nfa		1155/tcp/udp	# Network File Access
iascontrol-oms	1156/tcp/udp	# iasControl OMS
iascontrol	1157/tcp/udp	# Oracle iASControl
dbcontrol-oms	1158/tcp/udp	# dbControl OMS
oracle-oms	1159/tcp/udp	# Oracle OMS
olsv		1160/tcp/udp	# DB Lite Mult-User Server
health-polling	1161/tcp/udp	# Health Polling
health-trap	1162/tcp/udp	# Health Trap
sddp		1163/tcp/udp	# SmartDialer Data Protocol
qsm-proxy	1164/tcp/udp	# QSM Proxy Service
qsm-gui		1165/tcp/udp	# QSM GUI Service
qsm-remote	1166/tcp/udp	# QSM RemoteExec
cisco-ipsla	1167/tcp/udp/sctp	# Cisco IP SLAs Control Protocol
vchat		1168/tcp/udp	# VChat Conference Service
tripwire	1169/tcp/udp	# TRIPWIRE
atc-lm		1170/tcp/udp	# AT+C License Manager
atc-appserver	1171/tcp/udp	# AT+C FmiApplicationServer
dnap		1172/tcp/udp	# DNA Protocol
d-cinema-rrp	1173/tcp/udp	# D-Cinema Request-Response
fnet-remote-ui	1174/tcp/udp	# FlashNet Remote Admin
dossier		1175/tcp/udp	# Dossier Server
indigo-server	1176/tcp/udp	# Indigo Home Server
dkmessenger	1177/tcp/udp	# DKMessenger Protocol
sgi-storman	1178/tcp/udp	# SGI Storage Manager
b2n		1179/tcp/udp	# Backup To Neighbor
mc-client	1180/tcp/udp	# Millicent Client Proxy
3comnetman	1181/tcp/udp	# 3Com Net Management
accelenet	1182/tcp	# AcceleNet Control
accelenet-data	1182/udp	# AcceleNet Data
llsurfup-http	1183/tcp/udp	# LL Surfup HTTP
llsurfup-https	1184/tcp/udp	# LL Surfup HTTPS
catchpole	1185/tcp/udp	# Catchpole port
mysql-cluster	1186/tcp/udp	# MySQL Cluster Manager
alias		1187/tcp/udp	# Alias Service
hp-webadmin	1188/tcp/udp	# HP Web Admin
unet		1189/tcp/udp	# Unet Connection
commlinx-avl	1190/tcp/udp	# CommLinx GPS / AVL System
gpfs		1191/tcp/udp	# General Parallel File System
caids-sensor	1192/tcp/udp	# caids sensors channel
fiveacross	1193/tcp/udp	# Five Across Server
openvpn		1194/tcp/udp	# OpenVPN
rsf-1		1195/tcp/udp	# RSF-1 clustering
netmagic	1196/tcp/udp	# Network Magic
carrius-rshell	1197/tcp/udp	# Carrius Remote Access
cajo-discovery	1198/tcp/udp	# cajo reference discovery
dmidi		1199/tcp/udp	# DMIDI
scol		1200/tcp/udp	# SCOL
nucleus-sand	1201/tcp/udp	# Nucleus Sand Database Server
caiccipc	1202/tcp/udp
ssslic-mgr	1203/tcp/udp	# License Validation
ssslog-mgr	1204/tcp/udp	# Log Request Listener
accord-mgc	1205/tcp/udp	# Accord-MGC
anthony-data	1206/tcp/udp	# Anthony Data
metasage	1207/tcp/udp	# MetaSage
seagull-ais	1208/tcp/udp	# SEAGULL AIS
ipcd3		1209/tcp/udp	# IPCD3
eoss		1210/tcp/udp	# EOSS
groove-dpp	1211/tcp/udp	# Groove DPP
lupa		1212/tcp/udp
mpc-lifenet	1213/tcp/udp	# Medtronic/Physio-Control LIFENET
kazaa		1214/tcp/udp	# KAZAA
scanstat-1	1215/tcp/udp	# scanSTAT 1.0
etebac5		1216/tcp/udp	# ETEBAC 5
hpss-ndapi	1217/tcp/udp	# HPSS NonDCE Gateway
aeroflight-ads	1218/tcp/udp	# AeroFlight-ADs
aeroflight-ret	1219/tcp/udp	# AeroFlight-Ret
qt-serveradmin	1220/tcp/udp	# QT SERVER ADMIN
sweetware-apps	1221/tcp/udp	# SweetWARE Apps
nerv		1222/tcp/udp	# SNI R&D network
tgp		1223/tcp/udp	# TrulyGlobal Protocol
vpnz		1224/tcp/udp	# VPNz
slinkysearch	1225/tcp/udp	# SLINKYSEARCH
stgxfws		1226/tcp/udp	# STGXFWS
dns2go		1227/tcp/udp	# DNS2Go
florence	1228/tcp/udp	# FLORENCE
zented		1229/tcp/udp	# ZENworks Tiered Electronic Distribution
periscope	1230/tcp/udp	# Periscope
menandmice-lpm	1231/tcp/udp
first-defense	1232/tcp/udp	# Remote systems monitoring
univ-appserver	1233/tcp/udp	# Universal App Server
search-agent	1234/tcp/udp	# Infoseek Search Agent
mosaicsyssvc1	1235/tcp/udp
bvcontrol	1236/tcp/udp
tsdos390	1237/tcp/udp
hacl-qs		1238/tcp/udp
nmsd		1239/tcp/udp	# NMSD
instantia	1240/tcp/udp	# Instantia
nessus		1241/tcp/udp
nmasoverip	1242/tcp/udp	# NMAS over IP
serialgateway	1243/tcp/udp	# SerialGateway
isbconference1	1244/tcp/udp
isbconference2	1245/tcp/udp
payrouter	1246/tcp/udp
visionpyramid	1247/tcp/udp	# VisionPyramid
hermes		1248/tcp/udp
mesavistaco	1249/tcp/udp	# Mesa Vista Co
swldy-sias	1250/tcp/udp
servergraph	1251/tcp/udp
bspne-pcc	1252/tcp/udp
q55-pcc		1253/tcp/udp
de-noc		1254/tcp/udp
de-cache-query	1255/tcp/udp
de-server	1256/tcp/udp
shockwave2	1257/tcp/udp	# Shockwave 2
opennl		1258/tcp/udp	# Open Network Library
opennl-voice	1259/tcp/udp	# Open Network Library Voice
ibm-ssd		1260/tcp/udp
mpshrsv		1261/tcp/udp
qnts-orb	1262/tcp/udp	# QNTS-ORB
dka		1263/tcp/udp
prat		1264/tcp/udp	# PRAT
dssiapi		1265/tcp/udp	# DSSIAPI
dellpwrappks	1266/tcp/udp	# DELLPWRAPPKS
epc		1267/tcp/udp	# eTrust Policy Compliance
propel-msgsys	1268/tcp/udp	# PROPEL-MSGSYS
watilapp	1269/tcp/udp	# WATiLaPP
opsmgr		1270/tcp/udp	# Microsoft Operations Manager
excw		1271/tcp/udp	# eXcW
cspmlockmgr	1272/tcp/udp	# CSPMLockMgr
emc-gateway	1273/tcp/udp	# EMC-Gateway
t1distproc	1274/tcp/udp
ivcollector	1275/tcp/udp
miva-mqs	1277/tcp/udp	# mqs
dellwebadmin-1	1278/tcp/udp	# Dell Web Admin 1
dellwebadmin-2	1279/tcp/udp	# Dell Web Admin 2
pictrography	1280/tcp/udp	# Pictrography
healthd		1281/tcp/udp
emperion	1282/tcp/udp	# Emperion
productinfo	1283/tcp/udp	# Product Information
iee-qfx		1284/tcp/udp	# IEE-QFX
neoiface	1285/tcp/udp
netuitive	1286/tcp/udp
routematch	1287/tcp/udp	# RouteMatch Com
navbuddy	1288/tcp/udp	# NavBuddy
jwalkserver	1289/tcp/udp	# JWalkServer
winjaserver	1290/tcp/udp	# WinJaServer
seagulllms	1291/tcp/udp	# SEAGULLLMS
dsdn		1292/tcp/udp
pkt-krb-ipsec	1293/tcp/udp	# PKT-KRB-IPSec
cmmdriver	1294/tcp/udp	# CMMdriver
ehtp		1295/tcp/udp	# End-by-Hop Transmission Protocol
dproxy		1296/tcp/udp
sdproxy		1297/tcp/udp
lpcp		1298/tcp/udp
hp-sci		1299/tcp/udp
h323hostcallsc	1300/tcp/udp	# H.323 Secure Call Control Signalling
ci3-software-1	1301/tcp/udp	# CI3-Software-1
ci3-software-2	1302/tcp/udp	# CI3-Software-2
sftsrv		1303/tcp/udp
boomerang	1304/tcp/udp	# Boomerang
pe-mike		1305/tcp/udp
re-conn-proto	1306/tcp/udp	# RE-Conn-Proto
pacmand		1307/tcp/udp	# Pacmand
odsi		1308/tcp/udp	# Optical Domain Service Interconnect (ODSI)
jtag-server	1309/tcp/udp	# JTAG server
husky		1310/tcp/udp	# Husky
rxmon		1311/tcp/udp	# RxMon
sti-envision	1312/tcp/udp	# STI Envision
bmc-patroldb	1313/tcp/udp	# BMC_PATROLDB
pdps		1314/tcp/udp	# Photoscript Distributed Printing System
els		1315/tcp/udp	# E.L.S., Event Listener Service
exbit-escp	1316/tcp/udp	# Exbit-ESCP
vrts-ipcserver	1317/tcp/udp
krb5gatekeeper	1318/tcp/udp
amx-icsp	1319/tcp/udp	# AMX-ICSP
amx-axbnet	1320/tcp/udp	# AMX-AXBNET
pip		1321/tcp/udp	# PIP
novation	1322/tcp/udp	# Novation
brcd		1323/tcp/udp
delta-mcp	1324/tcp/udp
dx-instrument	1325/tcp/udp	# DX-Instrument
wimsic		1326/tcp/udp	# WIMSIC
ultrex		1327/tcp/udp	# Ultrex
ewall		1328/tcp/udp	# EWALL
netdb-export	1329/tcp/udp
streetperfect	1330/tcp/udp	# StreetPerfect
intersan	1331/tcp/udp
pcia-rxp-b	1332/tcp/udp	# PCIA RXP-B
passwrd-policy	1333/tcp/udp	# Password Policy
writesrv	1334/tcp/udp
digital-notary	1335/tcp/udp	# Digital Notary Protocol
ischat		1336/tcp/udp	# Instant Service Chat
menandmice-dns	1337/tcp/udp	# menandmice DNS
wmc-log-svc	1338/tcp/udp	# WMC-log-svr
kjtsiteserver	1339/tcp/udp
naap		1340/tcp/udp	# NAAP
qubes		1341/tcp/udp	# QuBES
esbroker	1342/tcp/udp	# ESBroker
re101		1343/tcp/udp
icap		1344/tcp/udp	# ICAP
vpjp		1345/tcp/udp	# VPJP
alta-ana-lm	1346/tcp/udp	# Alta Analytics License Manager
bbn-mmc		1347/tcp/udp	# multi media conferencing
bbn-mmx		1348/tcp/udp	# multi media conferencing
sbook		1349/tcp/udp	# Registration Network Protocol
editbench	1350/tcp/udp	# Registration Network Protocol
equationbuilder	1351/tcp/udp	# Digital Tool Works (MIT)
lotusnote	1352/tcp/udp	# Lotus Note
relief		1353/tcp/udp	# Relief Consulting
XSIP-network	1354/tcp/udp	# Five Across XSIP Network
intuitive-edge	1355/tcp/udp	# Intuitive Edge
cuillamartin	1356/tcp/udp	# CuillaMartin Company
pegboard	1357/tcp/udp	# Electronic PegBoard
connlcli	1358/tcp/udp	# CONNLCLI
ftsrv		1359/tcp/udp	# FTSRV
mimer		1360/tcp/udp	# MIMER
linx		1361/tcp/udp	# LinX
timeflies	1362/tcp/udp	# TimeFlies
ndm-requester	1363/tcp/udp	# Network DataMover Requester
ndm-server	1364/tcp/udp	# Network DataMover Server
adapt-sna	1365/tcp/udp	# Network Software Associates
netware-csp	1366/tcp/udp	# Novell NetWare Comm Service Platform
dcs		1367/tcp/udp	# DCS
screencast	1368/tcp/udp	# ScreenCast
gv-us		1369/tcp/udp	# GlobalView to Unix Shell
us-gv		1370/tcp/udp	# Unix Shell to GlobalView
fc-cli		1371/tcp/udp	# Fujitsu Config Protocol
fc-ser		1372/tcp/udp	# Fujitsu Config Protocol
chromagrafx	1373/tcp/udp	# Chromagrafx
molly		1374/tcp/udp	# EPI Software Systems
bytex		1375/tcp/udp	# Bytex
ibm-pps		1376/tcp/udp	# IBM Person to Person Software
cichlid		1377/tcp/udp	# Cichlid License Manager
elan		1378/tcp/udp	# Elan License Manager
dbreporter	1379/tcp/udp	# Integrity Solutions
telesis-licman	1380/tcp/udp	# Telesis Network License Manager
apple-licman	1381/tcp/udp	# Apple Network License Manager
udt-os		1382/tcp/udp	# udt_os
gwha		1383/tcp/udp	# GW Hannaway Network License Manager
os-licman	1384/tcp/udp	# Objective Solutions License Manager
atex-elmd	1385/tcp/udp	# Atex Publishing License Manager
checksum	1386/tcp/udp	# CheckSum License Manager
cadsi-lm	1387/tcp/udp	# Computer Aided Design Software Inc LM
objective-dbc	1388/tcp/udp	# Objective Solutions DataBase Cache
iclpv-dm	1389/tcp/udp	# Document Manager
iclpv-sc	1390/tcp/udp	# Storage Controller
iclpv-sas	1391/tcp/udp	# Storage Access Server
iclpv-pm	1392/tcp/udp	# Print Manager
iclpv-nls	1393/tcp/udp	# Network Log Server
iclpv-nlc	1394/tcp/udp	# Network Log Client
iclpv-wsm	1395/tcp/udp	# PC Workstation Manager software
dvl-activemail	1396/tcp/udp	# DVL Active Mail
audio-activmail	1397/tcp/udp	# Audio Active Mail
video-activmail	1398/tcp/udp	# Video Active Mail
cadkey-licman	1399/tcp/udp	# Cadkey License Manager
cadkey-tablet	1400/tcp/udp	# Cadkey Tablet Daemon
goldleaf-licman	1401/tcp/udp	# Goldleaf License Manager
prm-sm-np	1402/tcp/udp	# Prospero Resource Manager
prm-nm-np	1403/tcp/udp	# Prospero Resource Manager
igi-lm		1404/tcp/udp	# Infinite Graphics License Manager
ibm-res		1405/tcp/udp	# IBM Remote Execution Starter
netlabs-lm	1406/tcp/udp	# NetLabs License Manager
tibet-server	1407/tcp	# TIBET Data Server
sophia-lm	1408/tcp/udp	# Sophia License Manager
here-lm		1409/tcp/udp	# Here License Manager
hiq		1410/tcp/udp	# HiQ License Manager
af		1411/tcp/udp	# AudioFile
innosys		1412/tcp/udp	# InnoSys
innosys-acl	1413/tcp/udp	# Innosys-ACL
ibm-mqseries	1414/tcp/udp	# IBM MQSeries
dbstar		1415/tcp/udp	# DBStar
novell-lu6-2	1416/tcp/udp	# Novell LU6.2
timbuktu-srv1	1417/tcp/udp	# Timbuktu Service 1 Port
timbuktu-srv2	1418/tcp/udp	# Timbuktu Service 2 Port
timbuktu-srv3	1419/tcp/udp	# Timbuktu Service 3 Port
timbuktu-srv4	1420/tcp/udp	# Timbuktu Service 4 Port
gandalf-lm	1421/tcp/udp	# Gandalf License Manager
autodesk-lm	1422/tcp/udp	# Autodesk License Manager
essbase		1423/tcp/udp	# Essbase Arbor Software
hybrid		1424/tcp/udp	# Hybrid Encryption Protocol
zion-lm		1425/tcp/udp	# Zion Software License Manager
sais		1426/tcp/udp	# Satellite-data Acquisition System 1
mloadd		1427/tcp/udp	# mloadd monitoring tool
informatik-lm	1428/tcp/udp	# Informatik License Manager
nms		1429/tcp/udp	# Hypercom NMS
tpdu		1430/tcp/udp	# Hypercom TPDU
rgtp		1431/tcp/udp	# Reverse Gossip Transport
blueberry-lm	1432/tcp/udp	# Blueberry Software License Manager
ms-sql-s	1433/tcp/udp	# Microsoft-SQL-Server
ms-sql-m	1434/tcp/udp	# Microsoft-SQL-Monitor
ibm-cics	1435/tcp/udp	# IBM CICS
saism		1436/tcp/udp	# Satellite-data Acquisition System 2
tabula		1437/tcp/udp	# Tabula
eicon-server	1438/tcp/udp	# Eicon Security Agent/Server
eicon-x25	1439/tcp/udp	# Eicon X25/SNA Gateway
eicon-slp	1440/tcp/udp	# Eicon Service Location Protocol
cadis-1		1441/tcp/udp	# Cadis License Management
cadis-2		1442/tcp/udp	# Cadis License Management
ies-lm		1443/tcp/udp	# Integrated Engineering Software
marcam-lm	1444/tcp/udp	# Marcam License Management
proxima-lm	1445/tcp/udp	# Proxima License Manager
ora-lm		1446/tcp/udp	# Optical Research Associates License Manager
apri-lm		1447/tcp/udp	# Applied Parallel Research LM
oc-lm		1448/tcp/udp	# OpenConnect License Manager
peport		1449/tcp/udp	# PEport
dwf		1450/tcp/udp	# Tandem Distributed Workbench Facility
infoman		1451/tcp/udp	# IBM Information Management
gtegsc-lm	1452/tcp/udp	# GTE Government Systems License Man
genie-lm	1453/tcp/udp	# Genie License Manager
interhdl-elmd	1454/tcp/udp	# interHDL License Manager
esl-lm		1455/tcp/udp	# ESL License Manager
dca		1456/tcp/udp	# DCA
valisys-lm	1457/tcp/udp	# Valisys License Manager
nrcabq-lm	1458/tcp/udp	# Nichols Research Corp.
proshare1	1459/tcp/udp	# Proshare Notebook Application
proshare2	1460/tcp/udp	# Proshare Notebook Application
ibm-wrless-lan	1461/tcp/udp	# IBM Wireless LAN
world-lm	1462/tcp/udp	# World License Manager
nucleus		1463/tcp/udp	# Nucleus
msl-lmd		1464/tcp/udp	# MSL License Manager
pipes		1465/tcp/udp	# Pipes Platform
oceansoft-lm	1466/tcp/udp	# Ocean Software License Manager
csdmbase	1467/tcp/udp	# CSDMBASE
csdm		1468/tcp/udp	# CSDM
aal-lm		1469/tcp/udp	# Active Analysis Limited License Manager
uaiact		1470/tcp/udp	# Universal Analytics
csdmbase	1471/tcp/udp
csdm		1472/tcp/udp
openmath	1473/tcp/udp	# OpenMath
telefinder	1474/tcp/udp	# Telefinder
taligent-lm	1475/tcp/udp	# Taligent License Manager
clvm-cfg	1476/tcp/udp
ms-sna-server	1477/tcp/udp
ms-sna-base	1478/tcp/udp
dberegister	1479/tcp/udp
pacerforum	1480/tcp/udp	# PacerForum
airs		1481/tcp/udp	# AIRS
miteksys-lm	1482/tcp/udp	# Miteksys License Manager
afs		1483/tcp/udp	# AFS License Manager
confluent	1484/tcp/udp	# Confluent License Manager
lansource	1485/tcp/udp	# LANSource
nms-topo-serv	1486/tcp/udp	# nms_topo_serv
localinfosrvr	1487/tcp/udp	# LocalInfoSrvr
docstor		1488/tcp/udp	# DocStor
dmdocbroker	1489/tcp/udp
insitu-conf	1490/tcp/udp
stone-design-1	1492/tcp/udp
netmap-lm	1493/tcp/udp	# netmap_lm
ica		1494/tcp/udp
cvc		1495/tcp/udp
liberty-lm	1496/tcp/udp
rfx-lm		1497/tcp/udp
sybase-sqlany	1498/tcp/udp	# Sybase SQL Any
fhc		1499/tcp/udp	# Federico Heinz Consultora
vlsi-lm		1500/tcp/udp	# VLSI License Manager
saiscm		1501/tcp/udp	# Satellite-data Acquisition System 3
shivadiscovery	1502/tcp/udp	# Shiva
imtc-mcs	1503/tcp/udp	# Databeam
evb-elm		1504/tcp/udp	# EVB Software Engineering License Manager
funkproxy	1505/tcp/udp	# Funk Software, Inc.
utcd		1506/tcp/udp	# Universal Time daemon (utcd)
symplex		1507/tcp/udp
diagmond	1508/tcp/udp
robcad-lm	1509/tcp/udp	# Robcad, Ltd. License Manager
mvx-lm		1510/tcp/udp	# Midland Valley Exploration Ltd. Lic. Man.
3l-l1		1511/tcp/udp
wins		1512/tcp/udp	# Microsoft's Windows Internet Name Service
fujitsu-dtc	1513/tcp/udp	# Fujitsu Systems Business of America, Inc
fujitsu-dtcns	1514/tcp/udp	# Fujitsu Systems Business of America, Inc
ifor-protocol	1515/tcp/udp
vpad		1516/tcp/udp	# Virtual Places Audio data
vpac		1517/tcp/udp	# Virtual Places Audio control
vpvd		1518/tcp/udp	# Virtual Places Video data
vpvc		1519/tcp/udp	# Virtual Places Video control
atm-zip-office	1520/tcp/udp
ncube-lm	1521/tcp/udp	# nCube License Manager
ricardo-lm	1522/tcp/udp	# Ricardo North America License Manager
cichild-lm	1523/tcp/udp	# cichild
ingreslock	1524/tcp/udp	# ingres
orasrv		1525/tcp/udp	# oracle
pdap-np		1526/tcp/udp	# Prospero Data Access Prot non-priv
tlisrv		1527/tcp/udp	# oracle
ngr-t		1528/udp	# NGR transport protocol for mobile ad-hoc networks
coauthor	1529/tcp/udp	# oracle
rap-service	1530/tcp/udp
rap-listen	1531/tcp/udp
miroconnect	1532/tcp/udp
virtual-places	1533/tcp/udp	# Virtual Places Software
micromuse-lm	1534/tcp/udp
ampr-info	1535/tcp/udp
ampr-inter	1536/tcp/udp
sdsc-lm		1537/tcp/udp	# isi-lm
3ds-lm		1538/tcp/udp
intellistor-lm	1539/tcp/udp	# Intellistor License Manager
rds		1540/tcp/udp
rds2		1541/tcp/udp
gridgen-elmd	1542/tcp/udp
simba-cs	1543/tcp/udp
aspeclmd	1544/tcp/udp
vistium-share	1545/tcp/udp
abbaccuray	1546/tcp/udp
laplink		1547/tcp/udp
axon-lm		1548/tcp/udp	# Axon License Manager
shivahose	1549/tcp	# Shiva Hose
shivasound	1549/udp	# Shiva Sound
3m-image-lm	1550/tcp/udp	# Image Storage license manager 3M Company
hecmtl-db	1551/tcp/udp	# HECMTL-DB
pciarray	1552/tcp/udp
sna-cs		1553/tcp/udp
caci-lm		1554/tcp/udp	# CACI Products Company License Manager
livelan		1555/tcp/udp
veritas-pbx	1556/tcp/udp	# VERITAS Private Branch Exchange
arbortext-lm	1557/tcp/udp	# ArborText License Manager
xingmpeg	1558/tcp/udp
web2host	1559/tcp/udp
asci-val	1560/tcp/udp	# ASCI-RemoteSHADOW
facilityview	1561/tcp/udp
pconnectmgr	1562/tcp/udp
cadabra-lm	1563/tcp/udp	# Cadabra License Manager
pay-per-view	1564/tcp/udp	# Pay-Per-View
winddlb		1565/tcp/udp	# WinDD
corelvideo	1566/tcp/udp	# CORELVIDEO
jlicelmd	1567/tcp/udp
tsspmap		1568/tcp/udp
ets		1569/tcp/udp
orbixd		1570/tcp/udp
rdb-dbs-disp	1571/tcp/udp	# Oracle Remote Data Base
chip-lm		1572/tcp/udp	# Chipcom License Manager
itscomm-ns	1573/tcp/udp
mvel-lm		1574/tcp/udp
oraclenames	1575/tcp/udp
moldflow-lm	1576/tcp/udp	# Moldflow License Manager
hypercube-lm	1577/tcp/udp
jacobus-lm	1578/tcp/udp	# Jacobus License Manager
ioc-sea-lm	1579/tcp/udp
tn-tl-r1	1580/tcp
tn-tl-r2	1580/udp
mil-2045-47001	1581/tcp/udp	# MIL-2045-47001
msims		1582/tcp/udp	# MSIMS
simbaexpress	1583/tcp/udp
tn-tl-fd2	1584/tcp/udp
intv		1585/tcp/udp
ibm-abtact	1586/tcp/udp
pra-elmd	1587/tcp/udp	# pra_elmd
triquest-lm	1588/tcp/udp
vqp		1589/tcp/udp	# VQP
gemini-lm	1590/tcp/udp
ncpm-pm		1591/tcp/udp
commonspace	1592/tcp/udp
mainsoft-lm	1593/tcp/udp
sixtrak		1594/tcp/udp
radio		1595/tcp/udp
radio-sm	1596/tcp
radio-bc	1596/udp
orbplus-iiop	1597/tcp/udp
picknfs		1598/tcp/udp
simbaservices	1599/tcp/udp
issd		1600/tcp/udp
aas		1601/tcp/udp
inspect		1602/tcp/udp
picodbc		1603/tcp/udp	# pickodbc
icabrowser	1604/tcp/udp
slp		1605/tcp/udp	# Salutation Manager (Salutation Protocol)
slm-api		1606/tcp/udp	# Salutation Manager (SLM-API)
stt		1607/tcp/udp
smart-lm	1608/tcp/udp	# Smart Corp. License Manager
isysg-lm	1609/tcp/udp
taurus-wh	1610/tcp/udp
ill		1611/tcp/udp	# Inter Library Loan
netbill-trans	1612/tcp/udp	# NetBill Transaction Server
netbill-keyrep	1613/tcp/udp	# NetBill Key Repository
netbill-cred	1614/tcp/udp	# NetBill Credential Server
netbill-auth	1615/tcp/udp	# NetBill Authorization Server
netbill-prod	1616/tcp/udp	# NetBill Product Server
nimrod-agent	1617/tcp/udp	# Nimrod Inter-Agent Communication
skytelnet	1618/tcp/udp
xs-openstorage	1619/tcp/udp
faxportwinport	1620/tcp/udp
softdataphone	1621/tcp/udp
ontime		1622/tcp/udp
jaleosnd	1623/tcp/udp
udp-sr-port	1624/tcp/udp
svs-omagent	1625/tcp/udp
shockwave	1626/tcp/udp	# Shockwave
t128-gateway	1627/tcp/udp	# T.128 Gateway
lontalk-norm	1628/tcp/udp	# LonTalk normal
lontalk-urgnt	1629/tcp/udp	# LonTalk urgent
oraclenet8cman	1630/tcp/udp	# Oracle Net8 Cman
visitview	1631/tcp/udp	# Visit view
pammratc	1632/tcp/udp	# PAMMRATC
pammrpc		1633/tcp/udp	# PAMMRPC
loaprobe	1634/tcp/udp	# Log On America Probe
edb-server1	1635/tcp/udp	# EDB Server 1
isdc		1636/tcp/udp	# ISP shared public data control
islc		1637/tcp/udp	# ISP shared local data control
ismc		1638/tcp/udp	# ISP shared management control
cert-initiator	1639/tcp/udp
cert-responder	1640/tcp/udp
invision	1641/tcp/udp	# InVision
isis-am		1642/tcp/udp
isis-ambc	1643/tcp/udp
saiseh		1644/tcp/udp	# Satellite-data Acquisition System 4
sightline	1645/tcp/udp	# SightLine
sa-msg-port	1646/tcp/udp
rsap		1647/tcp/udp
concurrent-lm	1648/tcp/udp
kermit		1649/tcp/udp
nkd		1650/tcp/udp	# nkdn
shiva-confsrvr	1651/tcp/udp	# shiva_confsrvr
xnmp		1652/tcp/udp
alphatech-lm	1653/tcp/udp
stargatealerts	1654/tcp/udp
dec-mbadmin	1655/tcp/udp
dec-mbadmin-h	1656/tcp/udp
fujitsu-mmpdc	1657/tcp/udp
sixnetudr	1658/tcp/udp
sg-lm		1659/tcp/udp	# Silicon Grail License Manager
skip-mc-gikreq	1660/tcp/udp
netview-aix-1	1661/tcp/udp
netview-aix-2	1662/tcp/udp
netview-aix-3	1663/tcp/udp
netview-aix-4	1664/tcp/udp
netview-aix-5	1665/tcp/udp
netview-aix-6	1666/tcp/udp
netview-aix-7	1667/tcp/udp
netview-aix-8	1668/tcp/udp
netview-aix-9	1669/tcp/udp
netview-aix-10	1670/tcp/udp
netview-aix-11	1671/tcp/udp
netview-aix-12	1672/tcp/udp
proshare-mc-1	1673/tcp/udp	# Intel Proshare Multicast
proshare-mc-2	1674/tcp/udp	# Intel Proshare Multicast
pdp		1675/tcp/udp	# Pacific Data Products
netcomm1	1676/tcp
netcomm2	1676/udp
groupwise	1677/tcp/udp
prolink		1678/tcp/udp
darcorp-lm	1679/tcp/udp
microcom-sbp	1680/tcp/udp
sd-elmd		1681/tcp/udp
lanyon-lantern	1682/tcp/udp
ncpm-hip	1683/tcp/udp
snaresecure	1684/tcp/udp	# SnareSecure
n2nremote	1685/tcp/udp
cvmon		1686/tcp/udp
nsjtp-ctrl	1687/tcp/udp
nsjtp-data	1688/tcp/udp
firefox		1689/tcp/udp
ng-umds		1690/tcp/udp
empire-empuma	1691/tcp/udp
sstsys-lm	1692/tcp/udp
rrirtr		1693/tcp/udp
rrimwm		1694/tcp/udp
rrilwm		1695/tcp/udp
rrifmm		1696/tcp/udp
rrisat		1697/tcp/udp
rsvp-encap-1	1698/tcp/udp	# RSVP-ENCAPSULATION-1
rsvp-encap-2	1699/tcp/udp	# RSVP-ENCAPSULATION-2
mps-raft	1700/tcp/udp
l2f		1701/tcp/udp
deskshare	1702/tcp/udp
hb-engine	1703/tcp/udp
bcs-broker	1704/tcp/udp
slingshot	1705/tcp/udp
jetform		1706/tcp/udp
vdmplay		1707/tcp/udp
gat-lmd		1708/tcp/udp
centra		1709/tcp/udp
impera		1710/tcp/udp
pptconference	1711/tcp/udp
registrar	1712/tcp/udp	# resource monitoring service
conferencetalk	1713/tcp/udp	# ConferenceTalk
sesi-lm		1714/tcp/udp
houdini-lm	1715/tcp/udp
xmsg		1716/tcp/udp
fj-hdnet	1717/tcp/udp
h323gatedisc	1718/tcp/udp	# H.323 Multicast Gatekeeper Discover
h323gatestat	1719/tcp/udp	# H.323 Unicast Gatekeeper Signaling
h323hostcall	1720/tcp/udp/sctp	# H.323 Call Control Signalling
caicci		1721/tcp/udp
hks-lm		1722/tcp/udp	# HKS License Manager
pptp		1723/tcp/udp
csbphonemaster	1724/tcp/udp
iden-ralp	1725/tcp/udp
iberiagames	1726/tcp/udp	# IBERIAGAMES
winddx		1727/tcp/udp
telindus	1728/tcp/udp	# TELINDUS
citynl		1729/tcp/udp	# CityNL License Management
roketz		1730/tcp/udp
msiccp		1731/tcp/udp	# MSICCP
proxim		1732/tcp/udp
siipat		1733/tcp/udp	# SIMS - SIIPAT Protocol for Alarm Transmission
cambertx-lm	1734/tcp/udp	# Camber Corporation License Management
privatechat	1735/tcp/udp	# PrivateChat
street-stream	1736/tcp/udp
ultimad		1737/tcp/udp
gamegen1	1738/tcp/udp	# GameGen1
webaccess	1739/tcp/udp
encore		1740/tcp/udp
cisco-net-mgmt	1741/tcp/udp
3Com-nsd	1742/tcp/udp
cinegrfx-lm	1743/tcp/udp	# Cinema Graphics License Manager
ncpm-ft		1744/tcp/udp
remote-winsock	1745/tcp/udp
ftrapid-1	1746/tcp/udp
ftrapid-2	1747/tcp/udp
oracle-em1	1748/tcp/udp
aspen-services	1749/tcp/udp
sslp		1750/tcp/udp	# Simple Socket Library's PortMaster
swiftnet	1751/tcp/udp	# SwiftNet
lofr-lm		1752/tcp/udp	# Leap of Faith Research License Manager
predatar-comms	1753/tcp	# Predatar Comms Service
oracle-em2	1754/tcp/udp
ms-streaming	1755/tcp/udp
capfast-lmd	1756/tcp/udp
cnhrp		1757/tcp/udp
tftp-mcast	1758/tcp/udp
spss-lm		1759/tcp/udp	# SPSS License Manager
www-ldap-gw	1760/tcp/udp
cft-0		1761/tcp/udp
cft-1		1762/tcp/udp
cft-2		1763/tcp/udp
cft-3		1764/tcp/udp
cft-4		1765/tcp/udp
cft-5		1766/tcp/udp
cft-6		1767/tcp/udp
cft-7		1768/tcp/udp
bmc-net-adm	1769/tcp/udp
bmc-net-svc	1770/tcp/udp
vaultbase	1771/tcp/udp
essweb-gw	1772/tcp/udp	# EssWeb Gateway
kmscontrol	1773/tcp/udp	# KMSControl
global-dtserv	1774/tcp/udp
vdab		1775/tcp	# data interchange between visual processing containers
femis		1776/tcp/udp	# Federal Emergency Management Information System
powerguardian	1777/tcp/udp
prodigy-intrnet	1778/tcp/udp	# prodigy-internet
pharmasoft	1779/tcp/udp
dpkeyserv	1780/tcp/udp
answersoft-lm	1781/tcp/udp
hp-hcip		1782/tcp/udp
finle-lm	1784/tcp/udp	# Finle License Manager
windlm		1785/tcp/udp	# Wind River Systems License Manager
funk-logger	1786/tcp/udp
funk-license	1787/tcp/udp
psmond		1788/tcp/udp
hello		1789/tcp/udp
nmsp		1790/tcp/udp	# Narrative Media Streaming Protocol
ea1		1791/tcp/udp	# EA1
ibm-dt-2	1792/tcp/udp
rsc-robot	1793/tcp/udp
cera-bcm	1794/tcp/udp
dpi-proxy	1795/tcp/udp
vocaltec-admin	1796/tcp/udp	# Vocaltec Server Administration
uma		1797/tcp/udp	# UMA
etp		1798/tcp/udp	# Event Transfer Protocol
netrisk		1799/tcp/udp	# NETRISK
ansys-lm	1800/tcp/udp	# ANSYS-License manager
msmq		1801/tcp/udp	# Microsoft Message Que
concomp1	1802/tcp/udp	# ConComp1
hp-hcip-gwy	1803/tcp/udp	# HP-HCIP-GWY
enl		1804/tcp/udp	# ENL
enl-name	1805/tcp/udp	# ENL-Name
musiconline	1806/tcp/udp	# Musiconline
fhsp		1807/tcp/udp	# Fujitsu Hot Standby Protocol
oracle-vp2	1808/tcp/udp	# Oracle-VP2
oracle-vp1	1809/tcp/udp	# Oracle-VP1
jerand-lm	1810/tcp/udp	# Jerand License Manager
scientia-sdb	1811/tcp/udp	# Scientia-SDB
radius		1812/tcp/udp	# RADIUS
radius-acct	1813/tcp/udp	# RADIUS Accounting
tdp-suite	1814/tcp/udp	# TDP Suite
mmpft		1815/tcp/udp	# MMPFT
harp		1816/tcp/udp	# HARP
rkb-oscs	1817/tcp/udp	# RKB-OSCS
etftp		1818/tcp/udp	# Enhanced Trivial File Transfer Protocol
plato-lm	1819/tcp/udp	# Plato License Manager
mcagent		1820/tcp/udp
donnyworld	1821/tcp/udp
es-elmd		1822/tcp/udp
unisys-lm	1823/tcp/udp	# Unisys Natural Language License Manager
metrics-pas	1824/tcp/udp
direcpc-video	1825/tcp/udp	# DirecPC Video
ardt		1826/tcp/udp	# ARDT
asi		1827/tcp/udp	# ASI
itm-mcell-u	1828/tcp/udp
optika-emedia	1829/tcp/udp	# Optika eMedia
net8-cman	1830/tcp/udp	# Oracle Net8 CMan Admin
myrtle		1831/tcp/udp	# Myrtle
tht-treasure	1832/tcp/udp	# ThoughtTreasure
udpradio	1833/tcp/udp
ardusuni	1834/tcp/udp	# ARDUS Unicast
ardusmul	1835/tcp/udp	# ARDUS Multicast
ste-smsc	1836/tcp/udp
csoft1		1837/tcp/udp
talnet		1838/tcp/udp	# TALNET
netopia-vo1	1839/tcp/udp
netopia-vo2	1840/tcp/udp
netopia-vo3	1841/tcp/udp
netopia-vo4	1842/tcp/udp
netopia-vo5	1843/tcp/udp
direcpc-dll	1844/tcp/udp	# DirecPC-DLL
altalink	1845/tcp/udp
tunstall-pnc	1846/tcp/udp	# Tunstall PNC
slp-notify	1847/tcp/udp	# SLP Notification
fjdocdist	1848/tcp/udp
alpha-sms	1849/tcp/udp	# ALPHA-SMS
gsi		1850/tcp/udp	# GSI
ctcd		1851/tcp/udp
virtual-time	1852/tcp/udp	# Virtual Time
vids-avtp	1853/tcp/udp	# VIDS-AVTP
buddy-draw	1854/tcp/udp	# Buddy Draw
fiorano-rtrsvc	1855/tcp/udp	# Fiorano RtrSvc
fiorano-msgsvc	1856/tcp/udp	# Fiorano MsgSvc
datacaptor	1857/tcp/udp	# DataCaptor
privateark	1858/tcp/udp	# PrivateArk
gammafetchsvr	1859/tcp/udp	# Gamma Fetcher Server
sunscalar-svc	1860/tcp/udp	# SunSCALAR Services
lecroy-vicp	1861/tcp/udp	# LeCroy VICP
mysql-cm-agent	1862/tcp/udp	# MySQL Cluster Manager Agent
msnp		1863/tcp/udp	# MSNP
paradym-31port	1864/tcp/udp	# Paradym 31 Port
entp		1865/tcp/udp	# ENTP
swrmi		1866/tcp/udp
udrive		1867/tcp/udp	# UDRIVE
viziblebrowser	1868/tcp/udp	# VizibleBrowser
transact	1869/tcp/udp	# TransAct
sunscalar-dns	1870/tcp/udp	# SunSCALAR DNS Service
canocentral0	1871/tcp/udp	# Cano Central 0
canocentral1	1872/tcp/udp	# Cano Central 1
fjmpjps		1873/tcp/udp	# Fjmpjps
fjswapsnp	1874/tcp/udp	# Fjswapsnp
westell-stats	1875/tcp/udp
ewcappsrv	1876/tcp/udp
hp-webqosdb	1877/tcp/udp
drmsmc		1878/tcp/udp
nettgain-nms	1879/tcp/udp	# NettGain NMS
vsat-control	1880/tcp/udp	# Gilat VSAT Control
ibm-mqseries2	1881/tcp/udp	# IBM WebSphere MQ Everyplace
ecsqdmn		1882/tcp/udp	# CA eTrust Common Services
mqtt		1883/tcp/udp	# Message Queuing Telemetry Transport Protocol
idmaps		1884/tcp/udp	# Internet Distance Map Svc
vrtstrapserver	1885/tcp/udp	# Veritas Trap Server
leoip		1886/tcp/udp	# Leonardo over IP
filex-lport	1887/tcp/udp	# FileX Listening Port
ncconfig	1888/tcp/udp	# NC Config Port
unify-adapter	1889/tcp/udp	# Unify Web Adapter Service
wilkenlistener	1890/tcp/udp	# wilkenListener
childkey-notif	1891/tcp/udp	# ChildKey Notification
childkey-ctrl	1892/tcp/udp	# ChildKey Control
elad		1893/tcp/udp	# ELAD Protocol
o2server-port	1894/tcp/udp	# O2Server Port
b-novative-ls	1896/tcp/udp	# b-novative license server
metaagent	1897/tcp/udp	# MetaAgent
cymtec-port	1898/tcp/udp	# Cymtec secure management
mc2studios	1899/tcp/udp	# MC2Studios
ssdp		1900/tcp/udp	# SSDP
fjicl-tep-a	1901/tcp/udp	# Fujitsu ICL Terminal Emulator Program A
fjicl-tep-b	1902/tcp/udp	# Fujitsu ICL Terminal Emulator Program B
linkname	1903/tcp/udp	# Local Link Name Resolution
fjicl-tep-c	1904/tcp/udp	# Fujitsu ICL Terminal Emulator Program C
sugp		1905/tcp/udp	# Secure UP.Link Gateway Protocol
tpmd		1906/tcp/udp	# TPortMapperReq
intrastar	1907/tcp/udp	# IntraSTAR
dawn		1908/tcp/udp	# Dawn
global-wlink	1909/tcp/udp	# Global World Link
ultrabac	1910/tcp/udp	# UltraBac Software communications port
mtp		1911/tcp/udp	# Starlight Networks Multimedia Transport Protocol
rhp-iibp	1912/tcp/udp
armadp		1913/tcp/udp
elm-momentum	1914/tcp/udp	# Elm-Momentum
facelink	1915/tcp/udp	# FACELINK
persona		1916/tcp/udp	# Persoft Persona
noagent		1917/tcp/udp	# nOAgent
can-nds		1918/tcp/udp	# IBM Tivole Directory Service - NDS
can-dch		1919/tcp/udp	# IBM Tivoli Directory Service - DCH
can-ferret	1920/tcp/udp	# IBM Tivoli Directory Service - FERRET
noadmin		1921/tcp/udp	# NoAdmin
tapestry	1922/tcp/udp	# Tapestry
spice		1923/tcp/udp	# SPICE
xiip		1924/tcp/udp	# XIIP
discovery-port	1925/tcp/udp	# Surrogate Discovery Port
egs		1926/tcp/udp	# Evolution Game Server
videte-cipc	1927/tcp/udp	# Videte CIPC Port
emsd-port	1928/tcp/udp	# Expnd Maui Srvr Dscovr
bandwiz-system	1929/tcp/udp	# Bandwiz System - Server
driveappserver	1930/tcp/udp	# Drive AppServer
amdsched	1931/tcp/udp	# AMD SCHED
ctt-broker	1932/tcp/udp	# CTT Broker
xmapi		1933/tcp/udp	# IBM LM MT Agent
xaapi		1934/tcp/udp	# IBM LM Appl Agent
macromedia-fcs	1935/tcp/udp	# Macromedia Flash Communications Server MX
jetcmeserver	1936/tcp/udp	# JetCmeServer Server Port
jwserver	1937/tcp/udp	# JetVWay Server Port
jwclient	1938/tcp/udp	# JetVWay Client Port
jvserver	1939/tcp/udp	# JetVision Server Port
jvclient	1940/tcp/udp	# JetVision Client Port
dic-aida	1941/tcp/udp	# DIC-Aida
res		1942/tcp/udp	# Real Enterprise Service
beeyond-media	1943/tcp/udp	# Beeyond Media
close-combat	1944/tcp/udp
dialogic-elmd	1945/tcp/udp
tekpls		1946/tcp/udp
sentinelsrm	1947/tcp/udp	# SentinelSRM
eye2eye		1948/tcp/udp
ismaeasdaqlive	1949/tcp/udp	# ISMA Easdaq Live
ismaeasdaqtest	1950/tcp/udp	# ISMA Easdaq Test
bcs-lmserver	1951/tcp/udp
mpnjsc		1952/tcp/udp
rapidbase	1953/tcp/udp	# Rapid Base
abr-api		1954/tcp/udp	# ABR-API (diskbridge)
abr-secure	1955/tcp/udp	# ABR-Secure Data (diskbridge)
vrtl-vmf-ds	1956/tcp/udp	# Vertel VMF DS
unix-status	1957/tcp/udp
dxadmind	1958/tcp/udp	# CA Administration Daemon
simp-all	1959/tcp/udp	# SIMP Channel
nasmanager	1960/tcp/udp	# Merit DAC NASmanager
bts-appserver	1961/tcp/udp	# BTS APPSERVER
biap-mp		1962/tcp/udp	# BIAP-MP
webmachine	1963/tcp/udp	# WebMachine
solid-e-engine	1964/tcp/udp	# SOLID E ENGINE
tivoli-npm	1965/tcp/udp	# Tivoli NPM
slush		1966/tcp/udp	# Slush
sns-quote	1967/tcp/udp	# SNS Quote
lipsinc		1968/tcp/udp	# LIPSinc
lipsinc1	1969/tcp/udp	# LIPSinc 1
netop-rc	1970/tcp/udp	# NetOp Remote Control
netop-school	1971/tcp/udp	# NetOp School
intersys-cache	1972/tcp/udp	# Cache
dlsrap		1973/tcp/udp	# Data Link Switching Remote Access Protocol
drp		1974/tcp/udp	# DRP
tcoflashagent	1975/tcp/udp	# TCO Flash Agent
tcoregagent	1976/tcp/udp	# TCO Reg Agent
tcoaddressbook	1977/tcp/udp	# TCO Address Book
unisql		1978/tcp/udp	# UniSQL
unisql-java	1979/tcp/udp	# UniSQL Java
pearldoc-xact	1980/tcp/udp	# PearlDoc XACT
p2pq		1981/tcp/udp	# p2pQ
estamp		1982/tcp/udp	# Evidentiary Timestamp
lhtp		1983/tcp/udp	# Loophole Test Protocol
bb		1984/tcp/udp	# BB
hsrp		1985/tcp/udp	# Hot Standby Router Protocol
licensedaemon	1986/tcp/udp	# cisco license management
tr-rsrb-p1	1987/tcp/udp	# cisco RSRB Priority 1 port
tr-rsrb-p2	1988/tcp/udp	# cisco RSRB Priority 2 port
tr-rsrb-p3	1989/tcp/udp	# cisco RSRB Priority 3 port
stun-p1		1990/tcp/udp	# cisco STUN Priority 1 port
stun-p2		1991/tcp/udp	# cisco STUN Priority 2 port
stun-p3		1992/tcp/udp	# cisco STUN Priority 3 port
snmp-tcp-port	1993/tcp/udp	# cisco SNMP TCP port
stun-port	1994/tcp/udp	# cisco serial tunnel port
perf-port	1995/tcp/udp	# cisco perf port
tr-rsrb-port	1996/tcp/udp	# cisco Remote SRB port
gdp-port	1997/tcp/udp	# cisco Gateway Discovery Protocol
x25-svc-port	1998/tcp/udp	# cisco X.25 service (XOT)
tcp-id-port	1999/tcp/udp	# cisco identification port
cisco-sccp	2000/tcp/udp	# Cisco SCCP
dc		2001/tcp
wizard		2001/udp	# curry
globe		2002/tcp/udp
brutus		2003/tcp/udp	# Brutus Server
mailbox		2004/tcp
emce		2004/udp	# CCWS mm conf
berknet		2005/tcp
oracle		2005/udp
invokator	2006/tcp
raid-cd		2006/udp	# raid
dectalk		2007/tcp
raid-am		2007/udp
conf		2008/tcp
terminaldb	2008/udp
news		2009/tcp
whosockami	2009/udp
search		2010/tcp
pipe-server	2010/udp
raid-cc		2011/tcp	# raid
servserv	2011/udp
ttyinfo		2012/tcp
raid-ac		2012/udp
raid-am		2013/tcp
raid-cd		2013/udp
troff		2014/tcp
raid-sf		2014/udp
cypress		2015/tcp
raid-cs		2015/udp
bootserver	2016/tcp/udp
cypress-stat	2017/tcp
bootclient	2017/udp
terminaldb	2018/tcp
rellpack	2018/udp
whosockami	2019/tcp
about		2019/udp
xinupageserver	2020/tcp/udp
servexec	2021/tcp
xinuexpansion1	2021/udp
down		2022/tcp
xinuexpansion2	2022/udp
xinuexpansion3	2023/tcp/udp
xinuexpansion4	2024/tcp/udp
ellpack		2025/tcp
xribs		2025/udp
scrabble	2026/tcp/udp
shadowserver	2027/tcp/udp
submitserver	2028/tcp/udp
hsrpv6		2029/tcp/udp	# Hot Standby Router Protocol IPv6
device2		2030/tcp/udp
mobrien-chat	2031/tcp/udp
blackboard	2032/tcp/udp
glogger		2033/tcp/udp
scoremgr	2034/tcp/udp
imsldoc		2035/tcp/udp
e-dpnet		2036/tcp/udp	# Ethernet WS DP network
applus		2037/tcp/udp	# APplus Application Server
objectmanager	2038/tcp/udp
prizma		2039/tcp/udp	# Prizma Monitoring Service
lam		2040/tcp/udp
interbase	2041/tcp/udp
isis		2042/tcp/udp
isis-bcast	2043/tcp/udp
rimsl		2044/tcp/udp
cdfunc		2045/tcp/udp
sdfunc		2046/tcp/udp
dls		2047/tcp/udp
dls-monitor	2048/tcp/udp
nfs		2049/tcp/udp/sctp	# Network File System - Sun Microsystems
av-emb-config	2050/tcp/udp	# Avaya EMB Config Port
epnsdp		2051/tcp/udp	# EPNSDP
clearvisn	2052/tcp/udp	# clearVisn Services Port
lot105-ds-upd	2053/tcp/udp	# Lot105 DSuper Updates
weblogin	2054/tcp/udp	# Weblogin Port
iop		2055/tcp/udp	# Iliad-Odyssey Protocol
omnisky		2056/tcp/udp	# OmniSky Port
rich-cp		2057/tcp/udp	# Rich Content Protocol
newwavesearch	2058/tcp/udp	# NewWaveSearchables RMI
bmc-messaging	2059/tcp/udp	# BMC Messaging Service
teleniumdaemon	2060/tcp/udp	# Telenium Daemon IF
netmount	2061/tcp/udp	# NetMount
icg-swp		2062/tcp/udp	# ICG SWP Port
icg-bridge	2063/tcp/udp	# ICG Bridge Port
icg-iprelay	2064/tcp/udp	# ICG IP Relay Port
dlsrpn		2065/tcp/udp	# Data Link Switch Read Port Number
aura		2066/tcp/udp	# AVM USB Remote Architecture
dlswpn		2067/tcp/udp	# Data Link Switch Write Port Number
avauthsrvprtcl	2068/tcp/udp	# Avocent AuthSrv Protocol
event-port	2069/tcp/udp	# HTTP Event Port
ah-esp-encap	2070/tcp/udp	# AH and ESP Encapsulated in UDP packet
acp-port	2071/tcp/udp	# Axon Control Protocol
msync		2072/tcp/udp	# GlobeCast mSync
gxs-data-port	2073/tcp/udp	# DataReel Database Socket
vrtl-vmf-sa	2074/tcp/udp	# Vertel VMF SA
newlixengine	2075/tcp/udp	# Newlix ServerWare Engine
newlixconfig	2076/tcp/udp	# Newlix JSPConfig
tsrmagt		2077/tcp/udp	# Old Tivoli Storage Manager
tpcsrvr		2078/tcp/udp	# IBM Total Productivity Center Server
idware-router	2079/tcp/udp	# IDWARE Router Port
autodesk-nlm	2080/tcp/udp	# Autodesk NLM (FLEXlm)
kme-trap-port	2081/tcp/udp	# KME PRINTER TRAP PORT
infowave	2082/tcp/udp	# Infowave Mobility Server
radsec		2083/tcp/udp	# Secure Radius Service
sunclustergeo	2084/tcp/udp	# SunCluster Geographic
ada-cip		2085/tcp/udp	# ADA Control
gnunet		2086/tcp/udp	# GNUnet
eli		2087/tcp/udp	# ELI - Event Logging Integration
ip-blf		2088/tcp/udp	# IP Busy Lamp Field
sep		2089/tcp/udp	# Security Encapsulation Protocol - SEP
lrp		2090/tcp/udp	# Load Report Protocol
prp		2091/tcp/udp	# PRP
descent3	2092/tcp/udp	# Descent 3
nbx-cc		2093/tcp/udp	# NBX CC
nbx-au		2094/tcp/udp	# NBX AU
nbx-ser		2095/tcp/udp	# NBX SER
nbx-dir		2096/tcp/udp	# NBX DIR
jetformpreview	2097/tcp/udp	# Jet Form Preview
dialog-port	2098/tcp/udp	# Dialog Port
h2250-annex-g	2099/tcp/udp	# H.225.0 Annex G Signalling
amiganetfs	2100/tcp/udp	# Amiga Network Filesystem
rtcm-sc104	2101/tcp/udp
zephyr-srv	2102/tcp/udp	# Zephyr server
zephyr-clt	2103/tcp/udp	# Zephyr serv-hm connection
zephyr-hm	2104/tcp/udp	# Zephyr hostmanager
minipay		2105/tcp/udp	# MiniPay
mzap		2106/tcp/udp	# MZAP
bintec-admin	2107/tcp/udp	# BinTec Admin
comcam		2108/tcp/udp	# Comcam
ergolight	2109/tcp/udp	# Ergolight
umsp		2110/tcp/udp	# UMSP
dsatp		2111/tcp/udp	# OPNET Dynamic Sampling Agent Transaction Protocol
idonix-metanet	2112/tcp/udp	# Idonix MetaNet
hsl-storm	2113/tcp/udp	# HSL StoRM
ariascribe	2114/tcp/udp	# Classical Music Meta-Data Access and Enhancement
kdm		2115/tcp/udp	# Key Distribution Manager
ccowcmr		2116/tcp/udp	# CCOWCMR
mentaclient	2117/tcp/udp	# MENTACLIENT
mentaserver	2118/tcp/udp	# MENTASERVER
gsigatekeeper	2119/tcp/udp	# GSIGATEKEEPER
qencp		2120/tcp/udp	# Quick Eagle Networks CP
scientia-ssdb	2121/tcp/udp	# SCIENTIA-SSDB
caupc-remote	2122/tcp/udp	# CauPC Remote Control
gtp-control	2123/tcp/udp	# GTP-Control Plane (3GPP)
elatelink	2124/tcp/udp	# ELATELINK
lockstep	2125/tcp/udp	# LOCKSTEP
pktcable-cops	2126/tcp/udp	# PktCable-COPS
index-pc-wb	2127/tcp/udp	# INDEX-PC-WB
net-steward	2128/tcp/udp	# Net Steward Control
cs-live		2129/tcp/udp	# cs-live.com
xds		2130/tcp/udp	# XDS
avantageb2b	2131/tcp/udp	# Avantageb2b
solera-epmap	2132/tcp/udp	# SoleraTec End Point Map
zymed-zpp	2133/tcp/udp	# ZYMED-ZPP
avenue		2134/tcp/udp	# AVENUE
gris		2135/tcp/udp	# Grid Resource Information Server
appworxsrv	2136/tcp/udp	# APPWORXSRV
connect		2137/tcp/udp	# CONNECT
unbind-cluster	2138/tcp/udp	# UNBIND-CLUSTER
ias-auth	2139/tcp/udp	# IAS-AUTH
ias-reg		2140/tcp/udp	# IAS-REG
ias-admind	2141/tcp/udp	# IAS-ADMIND
tdmoip		2142/tcp/udp	# TDM OVER IP
lv-jc		2143/tcp/udp	# Live Vault Job Control
lv-ffx		2144/tcp/udp	# Live Vault Fast Object Transfer
lv-pici		2145/tcp/udp	# Live Vault Remote Diagnostic Console Support
lv-not		2146/tcp/udp	# Live Vault Admin Event Notification
lv-auth		2147/tcp/udp	# Live Vault Authentication
veritas-ucl	2148/tcp/udp	# VERITAS UNIVERSAL COMMUNICATION LAYER
acptsys		2149/tcp/udp	# ACPTSYS
dynamic3d	2150/tcp/udp	# DYNAMIC3D
docent		2151/tcp/udp	# DOCENT
gtp-user	2152/tcp/udp	# GTP-User Plane (3GPP)
ctlptc		2153/tcp/udp	# Control Protocol
stdptc		2154/tcp/udp	# Standard Protocol
brdptc		2155/tcp/udp	# Bridge Protocol
trp		2156/tcp/udp	# Talari Reliable Protocol
xnds		2157/tcp/udp	# Xerox Network Document Scan Protocol
touchnetplus	2158/tcp/udp	# TouchNetPlus Service
gdbremote	2159/tcp/udp	# GDB Remote Debug Port
apc-2160	2160/tcp/udp	# APC 2160
apc-2161	2161/tcp/udp	# APC 2161
navisphere	2162/tcp/udp	# Navisphere
navisphere-sec	2163/tcp/udp	# Navisphere Secure
ddns-v3		2164/tcp/udp	# Dynamic DNS Version 3
x-bone-api	2165/tcp/udp	# X-Bone API
iwserver	2166/tcp/udp
raw-serial	2167/tcp/udp	# Raw Async Serial Link
easy-soft-mux	2168/tcp/udp	# easy-soft Multiplexer
brain		2169/tcp/udp	# Backbone for Academic Information Notification (BRAIN)
eyetv		2170/tcp/udp	# EyeTV Server Port
msfw-storage	2171/tcp/udp	# MS Firewall Storage
msfw-s-storage	2172/tcp/udp	# MS Firewall SecureStorage
msfw-replica	2173/tcp/udp	# MS Firewall Replication
msfw-array	2174/tcp/udp	# MS Firewall Intra Array
airsync		2175/tcp/udp	# Microsoft Desktop AirSync Protocol
rapi		2176/tcp/udp	# Microsoft ActiveSync Remote API
qwave		2177/tcp/udp	# qWAVE Bandwidth Estimate
bitspeer	2178/tcp/udp	# Peer Services for BITS
vmrdp		2179/tcp/udp	# Microsoft RDP for virtual machines
mc-gt-srv	2180/tcp/udp	# Millicent Vendor Gateway Server
eforward	2181/tcp/udp
cgn-stat	2182/tcp/udp	# CGN status
cgn-config	2183/tcp/udp	# Code Green configuration
nvd		2184/tcp/udp	# NVD User
onbase-dds	2185/tcp/udp	# OnBase Distributed Disk Services
gtaua		2186/tcp/udp	# Guy-Tek Automated Update Applications
ssmc		2187/tcp	# Sepehr System Management Control
ssmd		2187/udp	# Sepehr System Management Data
radware-rpm	2188/tcp	# Radware Resource Pool Manager
radware-rpm-s	2189/tcp	# Secure Radware Resource Pool Manager
tivoconnect	2190/tcp/udp	# TiVoConnect Beacon
tvbus		2191/tcp/udp	# TvBus Messaging
asdis		2192/tcp/udp	# ASDIS software management
drwcs		2193/tcp/udp	# Dr.Web Enterprise Management Service
mnp-exchange	2197/tcp/udp	# MNP data exchange
onehome-remote	2198/tcp/udp	# OneHome Remote Access
onehome-help	2199/tcp/udp	# OneHome Service Port
ici		2200/tcp/udp	# ICI
ats		2201/tcp/udp	# Advanced Training System Program
imtc-map	2202/tcp/udp	# Int. Multimedia Teleconferencing Cosortium
b2-runtime	2203/tcp/udp	# b2 Runtime Protocol
b2-license	2204/tcp/udp	# b2 License Server
jps		2205/tcp/udp	# Java Presentation Server
hpocbus		2206/tcp/udp	# HP OpenCall bus
hpssd		2207/tcp/udp	# HP Status and Services
hpiod		2208/tcp/udp	# HP I/O Backend
rimf-ps		2209/tcp/udp	# HP RIM for Files Portal Service
noaaport	2210/tcp/udp	# NOAAPORT Broadcast Network
emwin		2211/tcp/udp	# EMWIN
leecoposserver	2212/tcp/udp	# LeeCO POS Server Service
kali		2213/tcp/udp	# Kali
rpi		2214/tcp/udp	# RDQ Protocol Interface
ipcore		2215/tcp/udp	# IPCore.co.za GPRS
vtu-comms	2216/tcp/udp	# VTU data service
gotodevice	2217/tcp/udp	# GoToDevice Device Management
bounzza		2218/tcp/udp	# Bounzza IRC Proxy
netiq-ncap	2219/tcp/udp	# NetIQ NCAP Protocol
netiq		2220/tcp/udp	# NetIQ End2End
ethernet-ip-s	2221/tcp/udp	# EtherNet/IP over TLS
EtherNet-IP-1	2222/tcp/udp	# EtherNet/IP I/O
rockwell-csp2	2223/tcp/udp	# Rockwell CSP2
efi-mg		2224/tcp/udp	# Easy Flexible Internet/Multiplayer Games
rcip-itu	2225/tcp/sctp	# Resource Connection Initiation Protocol
di-drm		2226/tcp/udp	# Digital Instinct DRM
di-msg		2227/tcp/udp	# DI Messaging Service
ehome-ms	2228/tcp/udp	# eHome Message Server
datalens	2229/tcp/udp	# DataLens Service
queueadm	2230/tcp/udp	# MetaSoft Job Queue Administration Service
wimaxasncp	2231/tcp/udp	# WiMAX ASN Control Plane Protocol
ivs-video	2232/tcp/udp	# IVS Video default
infocrypt	2233/tcp/udp	# INFOCRYPT
directplay	2234/tcp/udp	# DirectPlay
sercomm-wlink	2235/tcp/udp	# Sercomm-WLink
nani		2236/tcp/udp	# Nani
optech-port1-lm	2237/tcp/udp	# Optech Port1 License Manager
aviva-sna	2238/tcp/udp	# AVIVA SNA SERVER
imagequery	2239/tcp/udp	# Image Query
recipe		2240/tcp/udp	# RECIPe
ivsd		2241/tcp/udp	# IVS Daemon
foliocorp	2242/tcp/udp	# Folio Remote Server
magicom		2243/tcp/udp	# Magicom Protocol
nmsserver	2244/tcp/udp	# NMS Server
hao		2245/tcp/udp	# HaO
pc-mta-addrmap	2246/tcp/udp	# PacketCable MTA Addr Map
antidotemgrsvr	2247/tcp/udp	# Antidote Deployment Manager Service
ums		2248/tcp/udp	# User Management Service
rfmp		2249/tcp/udp	# RISO File Manager Protocol
remote-collab	2250/tcp/udp
dif-port	2251/tcp/udp	# Distributed Framework Port
njenet-ssl	2252/tcp/udp	# NJENET using SSL
dtv-chan-req	2253/tcp/udp	# DTV Channel Request
seispoc		2254/tcp/udp	# Seismic P.O.C. Port
vrtp		2255/tcp/udp	# VRTP - ViRtue Transfer Protocol
pcc-mfp		2256/tcp/udp	# PCC MFP
simple-tx-rx	2257/tcp/udp	# simple text/file transfer
rcts		2258/tcp/udp	# Rotorcraft Communications Test System
apc-2260	2260/tcp/udp	# APC 2260
comotionmaster	2261/tcp/udp	# CoMotion Master Server
comotionback	2262/tcp/udp	# CoMotion Backup Server
ecwcfg		2263/tcp/udp	# ECweb Configuration Service
apx500api-1	2264/tcp/udp	# Audio Precision Apx500 API Port 1
apx500api-2	2265/tcp/udp	# Audio Precision Apx500 API Port 2
mfserver	2266/tcp/udp	# M-Files Server
ontobroker	2267/tcp/udp	# OntoBroker
amt		2268/tcp/udp	# AMT
mikey		2269/tcp/udp	# MIKEY
starschool	2270/tcp/udp	# starSchool
mmcals		2271/tcp/udp	# Secure Meeting Maker Scheduling
mmcal		2272/tcp/udp	# Meeting Maker Scheduling
mysql-im	2273/tcp/udp	# MySQL Instance Manager
pcttunnell	2274/tcp/udp	# PCTTunneller
ibridge-data	2275/tcp/udp	# iBridge Conferencing
ibridge-mgmt	2276/tcp/udp	# iBridge Management
bluectrlproxy	2277/tcp/udp	# Bt device control proxy
s3db		2278/tcp/udp	# Simple Stacked Sequences Database
xmquery		2279/tcp/udp
lnvpoller	2280/tcp/udp	# LNVPOLLER
lnvconsole	2281/tcp/udp	# LNVCONSOLE
lnvalarm	2282/tcp/udp	# LNVALARM
lnvstatus	2283/tcp/udp	# LNVSTATUS
lnvmaps		2284/tcp/udp	# LNVMAPS
lnvmailmon	2285/tcp/udp	# LNVMAILMON
nas-metering	2286/tcp/udp	# NAS-Metering
dna		2287/tcp/udp	# DNA
netml		2288/tcp/udp	# NETML
dict-lookup	2289/tcp/udp	# Lookup dict server
sonus-logging	2290/tcp/udp	# Sonus Logging Services
eapsp		2291/tcp/udp	# EPSON Advanced Printer Share Protocol
mib-streaming	2292/tcp/udp	# Sonus Element Management Services
npdbgmngr	2293/tcp/udp	# Network Platform Debug Manager
konshus-lm	2294/tcp/udp	# Konshus License Manager (FLEX)
advant-lm	2295/tcp/udp	# Advant License Manager
theta-lm	2296/tcp/udp	# Theta License Manager (Rainbow)
d2k-datamover1	2297/tcp/udp	# D2K DataMover 1
d2k-datamover2	2298/tcp/udp	# D2K DataMover 2
pc-telecommute	2299/tcp/udp	# PC Telecommute
cvmmon		2300/tcp/udp	# CVMMON
cpq-wbem	2301/tcp/udp	# Compaq HTTP
binderysupport	2302/tcp/udp	# Bindery Support
proxy-gateway	2303/tcp/udp	# Proxy Gateway
attachmate-uts	2304/tcp/udp	# Attachmate UTS
mt-scaleserver	2305/tcp/udp	# MT ScaleServer
tappi-boxnet	2306/tcp/udp	# TAPPI BoxNet
pehelp		2307/tcp/udp
sdhelp		2308/tcp/udp
sdserver	2309/tcp/udp	# SD Server
sdclient	2310/tcp/udp	# SD Client
messageservice	2311/tcp/udp	# Message Service
wanscaler	2312/tcp/udp	# WANScaler Communication Service
iapp		2313/tcp/udp	# IAPP (Inter Access Point Protocol)
cr-websystems	2314/tcp/udp	# CR WebSystems
precise-sft	2315/tcp/udp	# Precise Sft.
sent-lm		2316/tcp/udp	# SENT License Manager
attachmate-g32	2317/tcp/udp	# Attachmate G32
cadencecontrol	2318/tcp/udp	# Cadence Control
infolibria	2319/tcp/udp	# InfoLibria
siebel-ns	2320/tcp/udp	# Siebel NS
rdlap		2321/tcp/udp	# RDLAP
ofsd		2322/tcp/udp
3d-nfsd		2323/tcp/udp
cosmocall	2324/tcp/udp	# Cosmocall
ansysli		2325/tcp/udp	# ANSYS Licensing Interconnect
idcp		2326/tcp/udp	# IDCP
xingcsm		2327/tcp/udp
netrix-sftm	2328/tcp/udp	# Netrix SFTM
nvd		2329/tcp/udp	# NVD
tscchat		2330/tcp/udp	# TSCCHAT
agentview	2331/tcp/udp	# AGENTVIEW
rcc-host	2332/tcp/udp	# RCC Host
snapp		2333/tcp/udp	# SNAPP
ace-client	2334/tcp/udp	# ACE Client Auth
ace-proxy	2335/tcp/udp	# ACE Proxy
appleugcontrol	2336/tcp/udp	# Apple UG Control
ideesrv		2337/tcp/udp
norton-lambert	2338/tcp/udp	# Norton Lambert
3com-webview	2339/tcp/udp	# 3Com WebView
wrs-registry	2340/tcp/udp	# WRS Registry
xiostatus	2341/tcp/udp	# XIO Status
manage-exec	2342/tcp/udp	# Seagate Manage Exec
nati-logos	2343/tcp/udp
fcmsys		2344/tcp/udp
dbm		2345/tcp/udp
redstorm-join	2346/tcp/udp	# Game Connection Port
redstorm-find	2347/tcp/udp	# Game Announcement and Location
redstorm-info	2348/tcp/udp	# Information to query for game status
redstorm-diag	2349/tcp/udp	# Diagnostics Port
psbserver	2350/tcp/udp	# Pharos Booking Server
psrserver	2351/tcp/udp
pslserver	2352/tcp/udp
pspserver	2353/tcp/udp
psprserver	2354/tcp/udp
psdbserver	2355/tcp/udp
gxtelmd		2356/tcp/udp	# GXT License Managemant
unihub-server	2357/tcp/udp	# UniHub Server
futrix		2358/tcp/udp	# Futrix
flukeserver	2359/tcp/udp	# FlukeServer
nexstorindltd	2360/tcp/udp	# NexstorIndLtd
tl1		2361/tcp/udp	# TL1
digiman		2362/tcp/udp
mediacntrlnfsd	2363/tcp/udp	# Media Central NFSD
oi-2000		2364/tcp/udp	# OI-2000
dbref		2365/tcp/udp
qip-login	2366/tcp/udp
service-ctrl	2367/tcp/udp	# Service Control
opentable	2368/tcp/udp	# OpenTable
l3-hbmon	2370/tcp/udp	# L3-HBMon
hp-rda		2371/tcp	# HP Remote Device Access
lanmessenger	2372/tcp/udp	# LanMessenger
remographlm	2373/tcp	# Remograph License Manager
hydra		2374/tcp	# Hydra RPC
docker		2375/tcp	# Docker REST API (plain text)
docker-s	2376/tcp	# Docker REST API (ssl)
swarm		2377/tcp	# RPC interface for Docker Swarm
etcd-client	2379/tcp	# etcd client communication
etcd-server	2380/tcp	# etcd server to server communication
compaq-https	2381/tcp/udp	# Compaq HTTPS
ms-olap3	2382/tcp/udp	# Microsoft OLAP
ms-olap4	2383/tcp/udp	# Microsoft OLAP
sd-request	2384/tcp	# SD-REQUEST
sd-capacity	2384/udp	# SD-CAPACITY
sd-data		2385/tcp/udp	# SD-DATA
virtualtape	2386/tcp/udp	# Virtual Tape
vsamredirector	2387/tcp/udp	# VSAM Redirector
mynahautostart	2388/tcp/udp	# MYNAH AutoStart
ovsessionmgr	2389/tcp/udp	# OpenView Session Mgr
rsmtp		2390/tcp/udp	# RSMTP
3com-net-mgmt	2391/tcp/udp	# 3COM Net Management
tacticalauth	2392/tcp/udp	# Tactical Auth
ms-olap1	2393/tcp/udp	# MS OLAP 1
ms-olap2	2394/tcp/udp	# MS OLAP 2
lan900-remote	2395/tcp/udp	# LAN900 Remote
wusage		2396/tcp/udp	# Wusage
ncl		2397/tcp/udp	# NCL
orbiter		2398/tcp/udp	# Orbiter
fmpro-fdal	2399/tcp/udp	# FileMaker, Inc. - Data Access Layer
opequus-server	2400/tcp/udp	# OpEquus Server
cvspserver	2401/tcp/udp
taskmaster2000	2402/tcp/udp	# TaskMaster 2000 Server
taskmaster2000	2403/tcp/udp	# TaskMaster 2000 Web
iec-104		2404/tcp/udp	# IEC 60870-5-104 process control over IP
trc-netpoll	2405/tcp/udp	# TRC Netpoll
jediserver	2406/tcp/udp	# JediServer
orion		2407/tcp/udp	# Orion
railgun-webaccl	2408/tcp	# CloudFlare Railgun Web Acceleration Protocol
sns-protocol	2409/tcp/udp	# SNS Protocol
vrts-registry	2410/tcp/udp	# VRTS Registry
netwave-ap-mgmt	2411/tcp/udp	# Netwave AP Management
cdn		2412/tcp/udp	# CDN
orion-rmi-reg	2413/tcp/udp
beeyond		2414/tcp/udp	# Beeyond
codima-rtp	2415/tcp/udp	# Codima Remote Transaction Protocol
rmtserver	2416/tcp/udp	# RMT Server
composit-server	2417/tcp/udp	# Composit Server
cas		2418/tcp/udp
attachmate-s2s	2419/tcp/udp	# Attachmate S2S
dslremote-mgmt	2420/tcp/udp	# DSL Remote Management
g-talk		2421/tcp/udp	# G-Talk
crmsbits	2422/tcp/udp	# CRMSBITS
rnrp		2423/tcp/udp	# RNRP
kofax-svr	2424/tcp/udp	# KOFAX-SVR
fjitsuappmgr	2425/tcp/udp	# Fujitsu App Manager
vcmp		2426/tcp/udp	# VeloCloud MultiPath Protocol
mgcp-gateway	2427/tcp/udp	# Media Gateway Control Protocol Gateway
ott		2428/tcp/udp	# One Way Trip Time
ft-role		2429/tcp/udp	# FT-ROLE
venus		2430/tcp/udp
venus-se	2431/tcp/udp
codasrv		2432/tcp/udp
codasrv-se	2433/tcp/udp
pxc-epmap	2434/tcp/udp
optilogic	2435/tcp/udp	# OptiLogic
topx		2436/tcp/udp	# TOP/X
unicontrol	2437/tcp/udp	# UniControl
msp		2438/tcp/udp	# MSP
sybasedbsynch	2439/tcp/udp	# SybaseDBSynch
spearway	2440/tcp/udp	# Spearway Lockers
pvsw-inet	2441/tcp/udp	# Pervasive I*net Data Server
netangel	2442/tcp/udp	# Netangel
powerclientcsf	2443/tcp/udp	# PowerClient Central Storage Facility
btpp2sectrans	2444/tcp/udp	# BT PP2 Sectrans
dtn1		2445/tcp/udp	# DTN1
bues-service	2446/tcp/udp	# bues_service
ovwdb		2447/tcp/udp	# OpenView NNM daemon
hpppssvr	2448/tcp/udp	# hpppsvr
ratl		2449/tcp/udp	# RATL
netadmin	2450/tcp/udp
netchat		2451/tcp/udp
snifferclient	2452/tcp/udp	# SnifferClient
madge-ltd	2453/tcp/udp
indx-dds	2454/tcp/udp	# IndX-DDS
wago-io-system	2455/tcp/udp	# WAGO-IO-SYSTEM
altav-remmgt	2456/tcp/udp
rapido-ip	2457/tcp/udp	# Rapido_IP
griffin		2458/tcp/udp
community	2459/tcp/udp	# Community
ms-theater	2460/tcp/udp
qadmifoper	2461/tcp/udp
qadmifevent	2462/tcp/udp
lsi-raid-mgmt	2463/tcp/udp	# LSI RAID Management
direcpc-si	2464/tcp/udp	# DirecPC SI
lbm		2465/tcp/udp	# Load Balance Management
lbf		2466/tcp/udp	# Load Balance Forwarding
high-criteria	2467/tcp/udp	# High Criteria
qip-msgd	2468/tcp/udp	# qip_msgd
mti-tcs-comm	2469/tcp/udp	# MTI-TCS-COMM
taskman-port	2470/tcp/udp
seaodbc		2471/tcp/udp	# SeaODBC
c3		2472/tcp/udp	# C3
aker-cdp	2473/tcp/udp	# Aker-cdp
vitalanalysis	2474/tcp/udp	# Vital Analysis
ace-server	2475/tcp/udp	# ACE Server
ace-svr-prop	2476/tcp/udp	# ACE Server Propagation
ssm-cvs		2477/tcp/udp	# SecurSight Certificate Valifation Service
ssm-cssps	2478/tcp/udp	# SecurSight Authentication Server (SSL)
ssm-els		2479/tcp/udp	# SecurSight Event Logging Server (SSL)
powerexchange	2480/tcp/udp	# Informatica PowerExchange Listener
giop		2481/tcp/udp	# Oracle GIOP
giop-ssl	2482/tcp/udp	# Oracle GIOP SSL
ttc		2483/tcp/udp	# Oracle TTC
ttc-ssl		2484/tcp/udp	# Oracle TTC SSL
netobjects1	2485/tcp/udp	# Net Objects1
netobjects2	2486/tcp/udp	# Net Objects2
pns		2487/tcp/udp	# Policy Notice Service
moy-corp	2488/tcp/udp	# Moy Corporation
tsilb		2489/tcp/udp	# TSILB
qip-qdhcp	2490/tcp/udp	# qip_qdhcp
conclave-cpp	2491/tcp/udp	# Conclave CPP
groove		2492/tcp/udp	# GROOVE
talarian-mqs	2493/tcp/udp	# Talarian MQS
bmc-ar		2494/tcp/udp	# BMC AR
fast-rem-serv	2495/tcp/udp	# Fast Remote Services
dirgis		2496/tcp/udp	# DIRGIS
quaddb		2497/tcp/udp	# Quad DB
odn-castraq	2498/tcp/udp	# ODN-CasTraq
unicontrol	2499/tcp/udp	# UniControl
rtsserv		2500/tcp/udp	# Resource Tracking system server
rtsclient	2501/tcp/udp	# Resource Tracking system client
kentrox-prot	2502/tcp/udp	# Kentrox Protocol
nms-dpnss	2503/tcp/udp	# NMS-DPNSS
wlbs		2504/tcp/udp	# WLBS
ppcontrol	2505/tcp/udp	# PowerPlay Control
jbroker		2506/tcp/udp
spock		2507/tcp/udp
jdatastore	2508/tcp/udp	# JDataStore
fjmpss		2509/tcp/udp
fjappmgrbulk	2510/tcp/udp
metastorm	2511/tcp/udp	# Metastorm
citrixima	2512/tcp/udp	# Citrix IMA
citrixadmin	2513/tcp/udp	# Citrix ADMIN
facsys-ntp	2514/tcp/udp	# Facsys NTP
facsys-router	2515/tcp/udp	# Facsys Router
maincontrol	2516/tcp/udp	# Main Control
call-sig-trans	2517/tcp/udp	# H.323 Annex E Call Control Signalling Transport
willy		2518/tcp/udp	# Willy
globmsgsvc	2519/tcp/udp
pvsw		2520/tcp/udp	# Pervasive Listener
adaptecmgr	2521/tcp/udp	# Adaptec Manager
windb		2522/tcp/udp	# WinDb
qke-llc-v3	2523/tcp/udp	# Qke LLC V.3
optiwave-lm	2524/tcp/udp	# Optiwave License Management
ms-v-worlds	2525/tcp/udp	# MS V-Worlds
ema-sent-lm	2526/tcp/udp	# EMA License Manager
iqserver	2527/tcp/udp	# IQ Server
ncr-ccl		2528/tcp/udp	# NCR CCL
utsftp		2529/tcp/udp	# UTS FTP
vrcommerce	2530/tcp/udp	# VR Commerce
ito-e-gui	2531/tcp/udp	# ITO-E GUI
ovtopmd		2532/tcp/udp	# OVTOPMD
snifferserver	2533/tcp/udp	# SnifferServer
combox-web-acc	2534/tcp/udp	# Combox Web Access
madcap		2535/tcp/udp	# MADCAP
btpp2audctr1	2536/tcp/udp
upgrade		2537/tcp/udp	# Upgrade Protocol
vnwk-prapi	2538/tcp/udp
vsiadmin	2539/tcp/udp	# VSI Admin
lonworks	2540/tcp/udp	# LonWorks
lonworks2	2541/tcp/udp	# LonWorks2
udrawgraph	2542/tcp/udp	# uDraw(Graph)
reftek		2543/tcp/udp	# REFTEK
novell-zen	2544/tcp/udp	# Management Daemon Refresh
sis-emt		2545/tcp/udp
vytalvaultbrtp	2546/tcp/udp
vytalvaultvsmp	2547/tcp/udp
vytalvaultpipe	2548/tcp/udp
ipass		2549/tcp/udp	# IPASS
ads		2550/tcp/udp	# ADS
isg-uda-server	2551/tcp/udp	# ISG UDA Server
call-logging	2552/tcp/udp	# Call Logging
efidiningport	2553/tcp/udp
vcnet-link-v10	2554/tcp/udp	# VCnet-Link v10
compaq-wcp	2555/tcp/udp	# Compaq WCP
nicetec-nmsvc	2556/tcp/udp
nicetec-mgmt	2557/tcp/udp
pclemultimedia	2558/tcp/udp	# PCLE Multi Media
lstp		2559/tcp/udp	# LSTP
labrat		2560/tcp/udp
mosaixcc	2561/tcp/udp	# MosaixCC
delibo		2562/tcp/udp	# Delibo
cti-redwood	2563/tcp/udp	# CTI Redwood
hp-3000-telnet	2564/tcp/udp	# HP 3000 NS/VT block mode telnet
coord-svr	2565/tcp/udp	# Coordinator Server
pcs-pcw		2566/tcp/udp
clp		2567/tcp/udp	# Cisco Line Protocol
spamtrap	2568/tcp/udp	# SPAM TRAP
sonuscallsig	2569/tcp/udp	# Sonus Call Signal
hs-port		2570/tcp/udp	# HS Port
cecsvc		2571/tcp/udp	# CECSVC
ibp		2572/tcp/udp	# IBP
trustestablish	2573/tcp/udp	# Trust Establish
blockade-bpsp	2574/tcp/udp	# Blockade BPSP
hl7		2575/tcp/udp	# HL7
tclprodebugger	2576/tcp/udp	# TCL Pro Debugger
scipticslsrvr	2577/tcp/udp	# Scriptics Lsrvr
rvs-isdn-dcp	2578/tcp/udp	# RVS ISDN DCP
mpfoncl		2579/tcp/udp
tributary	2580/tcp/udp	# Tributary
argis-te	2581/tcp/udp	# ARGIS TE
argis-ds	2582/tcp/udp	# ARGIS DS
mon		2583/tcp/udp	# MON
cyaserv		2584/tcp/udp
netx-server	2585/tcp/udp	# NETX Server
netx-agent	2586/tcp/udp	# NETX Agent
masc		2587/tcp/udp	# MASC
privilege	2588/tcp/udp	# Privilege
quartus-tcl	2589/tcp/udp
idotdist	2590/tcp/udp
maytagshuffle	2591/tcp/udp	# Maytag Shuffle
netrek		2592/tcp/udp
mns-mail	2593/tcp/udp	# MNS Mail Notice Service
dts		2594/tcp/udp	# Data Base Server
worldfusion1	2595/tcp/udp	# World Fusion 1
worldfusion2	2596/tcp/udp	# World Fusion 2
homesteadglory	2597/tcp/udp	# Homestead Glory
citriximaclient	2598/tcp/udp	# Citrix MA Client
snapd		2599/tcp/udp	# Snap Discovery
hpstgmgr	2600/tcp/udp	# HPSTGMGR
discp-client	2601/tcp/udp
discp-server	2602/tcp/udp
servicemeter	2603/tcp/udp	# Service Meter
nsc-ccs		2604/tcp/udp	# NSC CCS
nsc-posa	2605/tcp/udp	# NSC POSA
netmon		2606/tcp/udp	# Dell Netmon
connection	2607/tcp/udp	# Dell Connection
wag-service	2608/tcp/udp	# Wag Service
system-monitor	2609/tcp/udp	# System Monitor
versa-tek	2610/tcp/udp	# VersaTek
lionhead	2611/tcp/udp	# LIONHEAD
qpasa-agent	2612/tcp/udp	# Qpasa Agent
smntubootstrap	2613/tcp/udp	# SMNTUBootstrap
neveroffline	2614/tcp/udp	# Never Offline
firepower	2615/tcp/udp
appswitch-emp	2616/tcp/udp
cmadmin		2617/tcp/udp	# Clinical Context Managers
priority-e-com	2618/tcp/udp	# Priority E-Com
bruce		2619/tcp/udp
lpsrecommender	2620/tcp/udp	# LPSRecommender
miles-apart	2621/tcp/udp	# Miles Apart Jukebox Server
metricadbc	2622/tcp/udp	# MetricaDBC
lmdp		2623/tcp/udp	# LMDP
aria		2624/tcp/udp	# Aria
blwnkl-port	2625/tcp/udp	# Blwnkl Port
gbjd816		2626/tcp/udp
moshebeeri	2627/tcp/udp	# Moshe Beeri
dict		2628/tcp/udp	# DICT
sitaraserver	2629/tcp/udp	# Sitara Server
sitaramgmt	2630/tcp/udp	# Sitara Management
sitaradir	2631/tcp/udp	# Sitara Dir
irdg-post	2632/tcp/udp	# IRdg Post
interintelli	2633/tcp/udp	# InterIntelli
pk-electronics	2634/tcp/udp	# PK Electronics
backburner	2635/tcp/udp	# Back Burner
solve		2636/tcp/udp	# Solve
imdocsvc	2637/tcp/udp	# Import Document Service
sybaseanywhere	2638/tcp/udp	# Sybase Anywhere
aminet		2639/tcp/udp	# AMInet
ami-control	2640/tcp/udp	# Alcorn McBride Inc protocol used for device control
hdl-srv		2641/tcp/udp	# HDL Server
tragic		2642/tcp/udp	# Tragic
gte-samp	2643/tcp/udp	# GTE-SAMP
travsoft-ipx-t	2644/tcp/udp	# Travsoft IPX Tunnel
novell-ipx-cmd	2645/tcp/udp	# Novell IPX CMD
and-lm		2646/tcp/udp	# AND License Manager
syncserver	2647/tcp/udp	# SyncServer
upsnotifyprot	2648/tcp/udp	# Upsnotifyprot
vpsipport	2649/tcp/udp	# VPSIPPORT
eristwoguns	2650/tcp/udp
ebinsite	2651/tcp/udp	# EBInSite
interpathpanel	2652/tcp/udp	# InterPathPanel
sonus		2653/tcp/udp	# Sonus
corel-vncadmin	2654/tcp/udp	# Corel VNC Admin
unglue		2655/tcp/udp	# UNIX Nt Glue
kana		2656/tcp/udp	# Kana
sns-dispatcher	2657/tcp/udp	# SNS Dispatcher
sns-admin	2658/tcp/udp	# SNS Admin
sns-query	2659/tcp/udp	# SNS Query
gcmonitor	2660/tcp/udp	# GC Monitor
olhost		2661/tcp/udp	# OLHOST
bintec-capi	2662/tcp/udp	# BinTec-CAPI
bintec-tapi	2663/tcp/udp	# BinTec-TAPI
patrol-mq-gm	2664/tcp/udp	# Patrol for MQ GM
patrol-mq-nm	2665/tcp/udp	# Patrol for MQ NM
extensis	2666/tcp/udp
alarm-clock-s	2667/tcp/udp	# Alarm Clock Server
alarm-clock-c	2668/tcp/udp	# Alarm Clock Client
toad		2669/tcp/udp	# TOAD
tve-announce	2670/tcp/udp	# TVE Announce
newlixreg	2671/tcp/udp
nhserver	2672/tcp/udp
firstcall42	2673/tcp/udp	# First Call 42
ewnn		2674/tcp/udp
ttc-etap	2675/tcp/udp	# TTC ETAP
simslink	2676/tcp/udp	# SIMSLink
gadgetgate1way	2677/tcp/udp	# Gadget Gate 1 Way
gadgetgate2way	2678/tcp/udp	# Gadget Gate 2 Way
syncserverssl	2679/tcp/udp	# Sync Server SSL
pxc-sapxom	2680/tcp/udp
mpnjsomb	2681/tcp/udp
ncdloadbalance	2683/tcp/udp	# NCDLoadBalance
mpnjsosv	2684/tcp/udp
mpnjsocl	2685/tcp/udp
mpnjsomg	2686/tcp/udp
pq-lic-mgmt	2687/tcp/udp
md-cg-http	2688/tcp/udp	# md-cf-http
fastlynx	2689/tcp/udp	# FastLynx
hp-nnm-data	2690/tcp/udp	# HP NNM Embedded Database
itinternet	2691/tcp/udp	# ITInternet ISM Server
admins-lms	2692/tcp/udp	# Admins LMS
pwrsevent	2694/tcp/udp
vspread		2695/tcp/udp	# VSPREAD
unifyadmin	2696/tcp/udp	# Unify Admin
oce-snmp-trap	2697/tcp/udp	# Oce SNMP Trap Port
mck-ivpip	2698/tcp/udp	# MCK-IVPIP
csoft-plusclnt	2699/tcp/udp	# Csoft Plus Client
tqdata		2700/tcp/udp
sms-rcinfo	2701/tcp/udp	# SMS RCINFO
sms-xfer	2702/tcp/udp	# SMS XFER
sms-chat	2703/tcp/udp	# SMS CHAT
sms-remctrl	2704/tcp/udp	# SMS REMCTRL
sds-admin	2705/tcp/udp	# SDS Admin
ncdmirroring	2706/tcp/udp	# NCD Mirroring
emcsymapiport	2707/tcp/udp	# EMCSYMAPIPORT
banyan-net	2708/tcp/udp	# Banyan-Net
supermon	2709/tcp/udp	# Supermon
sso-service	2710/tcp/udp	# SSO Service
sso-control	2711/tcp/udp	# SSO Control
aocp		2712/tcp/udp	# Axapta Object Communication Protocol
raventbs	2713/tcp/udp	# Raven Trinity Broker Service
raventdm	2714/tcp/udp	# Raven Trinity Data Mover
hpstgmgr2	2715/tcp/udp	# HPSTGMGR2
inova-ip-disco	2716/tcp/udp	# Inova IP Disco
pn-requester	2717/tcp/udp	# PN REQUESTER
pn-requester2	2718/tcp/udp	# PN REQUESTER 2
scan-change	2719/tcp/udp	# Scan & Change
wkars		2720/tcp/udp
smart-diagnose	2721/tcp/udp	# Smart Diagnose
proactivesrvr	2722/tcp/udp	# Proactive Server
watchdog-nt	2723/tcp/udp	# WatchDog NT Protocol
qotps		2724/tcp/udp
msolap-ptp2	2725/tcp/udp	# MSOLAP PTP2
tams		2726/tcp/udp	# TAMS
mgcp-callagent	2727/tcp/udp	# Media Gateway Control Protocol Call Agent
sqdr		2728/tcp/udp	# SQDR
tcim-control	2729/tcp/udp	# TCIM Control
nec-raidplus	2730/tcp/udp	# NEC RaidPlus
fyre-messanger	2731/tcp/udp	# Fyre Messanger
g5m		2732/tcp/udp	# G5M
signet-ctf	2733/tcp/udp	# Signet CTF
ccs-software	2734/tcp/udp	# CCS Software
netiq-mc	2735/tcp/udp	# NetIQ Monitor Console
radwiz-nms-srv	2736/tcp/udp	# RADWIZ NMS SRV
srp-feedback	2737/tcp/udp	# SRP Feedback
ndl-tcp-ois-gw	2738/tcp/udp	# NDL TCP-OSI Gateway
tn-timing	2739/tcp/udp	# TN Timing
alarm		2740/tcp/udp	# Alarm
tsb		2741/tcp/udp	# TSB
tsb2		2742/tcp/udp	# TSB2
murx		2743/tcp/udp
honyaku		2744/tcp/udp
urbisnet	2745/tcp/udp	# URBISNET
cpudpencap	2746/tcp/udp	# CPUDPENCAP
fjippol-swrly	2747/tcp/udp
fjippol-polsvr	2748/tcp/udp
fjippol-cnsl	2749/tcp/udp
fjippol-port1	2750/tcp/udp
fjippol-port2	2751/tcp/udp
rsisysaccess	2752/tcp/udp	# RSISYS ACCESS
de-spot		2753/tcp/udp
apollo-cc	2754/tcp/udp	# APOLLO CC
expresspay	2755/tcp/udp	# Express Pay
simplement-tie	2756/tcp/udp
cnrp		2757/tcp/udp	# CNRP
apollo-status	2758/tcp/udp	# APOLLO Status
apollo-gms	2759/tcp/udp	# APOLLO GMS
sabams		2760/tcp/udp	# Saba MS
dicom-iscl	2761/tcp/udp	# DICOM ISCL
dicom-tls	2762/tcp/udp	# DICOM TLS
desktop-dna	2763/tcp/udp	# Desktop DNA
data-insurance	2764/tcp/udp	# Data Insurance
qip-audup	2765/tcp/udp
compaq-scp	2766/tcp/udp	# Compaq SCP
uadtc		2767/tcp/udp	# UADTC
uacs		2768/tcp/udp	# UACS
exce		2769/tcp/udp	# eXcE
veronica	2770/tcp/udp	# Veronica
vergencecm	2771/tcp/udp	# Vergence CM
auris		2772/tcp/udp
rbakcup1	2773/tcp/udp	# RBackup Remote Backup
rbakcup2	2774/tcp/udp	# RBackup Remote Backup
smpp		2775/tcp/udp	# SMPP
ridgeway1	2776/tcp/udp	# Ridgeway Systems & Software
ridgeway2	2777/tcp/udp	# Ridgeway Systems & Software
gwen-sonya	2778/tcp/udp	# Gwen-Sonya
lbc-sync	2779/tcp/udp	# LBC Sync
lbc-control	2780/tcp/udp	# LBC Control
whosells	2781/tcp/udp
everydayrc	2782/tcp/udp
aises		2783/tcp/udp	# AISES
www-dev		2784/tcp/udp	# world wide web - development
aic-np		2785/tcp/udp
aic-oncrpc	2786/tcp/udp	# aic-oncrpc - Destiny MCD database
piccolo		2787/tcp/udp	# piccolo - Cornerstone Software
fryeserv	2788/tcp/udp	# NetWare Loadable Module - Seagate Software
media-agent	2789/tcp/udp	# Media Agent
plgproxy	2790/tcp/udp	# PLG Proxy
mtport-regist	2791/tcp/udp	# MT Port Registrator
f5-globalsite	2792/tcp/udp
initlsmsad	2793/tcp/udp
livestats	2795/tcp/udp	# LiveStats
ac-tech		2796/tcp/udp
esp-encap	2797/tcp/udp
tmesis-upshot	2798/tcp/udp	# TMESIS-UPShot
icon-discover	2799/tcp/udp	# ICON Discover
acc-raid	2800/tcp/udp	# ACC RAID
igcp		2801/tcp/udp	# IGCP
veritas-tcp1	2802/tcp	# Veritas TCP1
veritas-udp1	2802/udp	# Veritas UDP1
btprjctrl	2803/tcp/udp
dvr-esm		2804/tcp/udp	# March Networks Digital Video Recorders and Enterprise Service Manager products
wta-wsp-s	2805/tcp/udp	# WTA WSP-S
cspuni		2806/tcp/udp
cspmulti	2807/tcp/udp
j-lan-p		2808/tcp/udp	# J-LAN-P
corbaloc	2809/tcp/udp	# CORBA LOC
netsteward	2810/tcp/udp	# Active Net Steward
gsiftp		2811/tcp/udp	# GSI FTP
atmtcp		2812/tcp/udp
llm-pass	2813/tcp/udp
llm-csv		2814/tcp/udp
lbc-measure	2815/tcp/udp	# LBC Measurement
lbc-watchdog	2816/tcp/udp	# LBC Watchdog
nmsigport	2817/tcp/udp	# NMSig Port
rmlnk		2818/tcp/udp
fc-faultnotify	2819/tcp/udp	# FC Fault Notification
univision	2820/tcp/udp	# UniVision
vrts-at-port	2821/tcp/udp	# VERITAS Authentication Service
ka0wuc		2822/tcp/udp
cqg-netlan	2823/tcp/udp	# CQG Net/LAN
cqg-netlan-1	2824/tcp/udp	# CQG Net/LAN 1
slc-systemlog	2826/tcp/udp
slc-ctrlrloops	2827/tcp/udp
itm-lm		2828/tcp/udp	# ITM License Manager
silkp1		2829/tcp/udp
silkp2		2830/tcp/udp
silkp3		2831/tcp/udp
silkp4		2832/tcp/udp
glishd		2833/tcp/udp
evtp		2834/tcp/udp	# EVTP
evtp-data	2835/tcp/udp	# EVTP-DATA
catalyst	2836/tcp/udp
repliweb	2837/tcp/udp	# Repliweb
starbot		2838/tcp/udp	# Starbot
nmsigport	2839/tcp/udp	# NMSigPort
l3-exprt	2840/tcp/udp
l3-ranger	2841/tcp/udp
l3-hawk		2842/tcp/udp
pdnet		2843/tcp/udp	# PDnet
bpcp-poll	2844/tcp/udp	# BPCP POLL
bpcp-trap	2845/tcp/udp	# BPCP TRAP
aimpp-hello	2846/tcp/udp	# AIMPP Hello
aimpp-port-req	2847/tcp/udp	# AIMPP Port Req
amt-blc-port	2848/tcp/udp	# AMT-BLC-PORT
fxp		2849/tcp/udp	# FXP
metaconsole	2850/tcp/udp	# MetaConsole
webemshttp	2851/tcp/udp
bears-01	2852/tcp/udp
ispipes		2853/tcp/udp	# ISPipes
infomover	2854/tcp/udp	# InfoMover
msrp		2855/tcp	# MSRP over TCP
cesdinv		2856/tcp/udp
simctlp		2857/tcp/udp	# SimCtIP
ecnp		2858/tcp/udp	# ECNP
activememory	2859/tcp/udp	# Active Memory
dialpad-voice1	2860/tcp/udp	# Dialpad Voice 1
dialpad-voice2	2861/tcp/udp	# Dialpad Voice 2
ttg-protocol	2862/tcp/udp	# TTG Protocol
sonardata	2863/tcp/udp	# Sonar Data
astromed-main	2864/tcp/udp	# main 5001 cmd
pit-vpn		2865/tcp/udp
iwlistener	2866/tcp/udp
esps-portal	2867/tcp/udp
npep-messaging	2868/tcp/udp	# Norman Proprietaqry Events Protocol
icslap		2869/tcp/udp	# ICSLAP
daishi		2870/tcp/udp
msi-selectplay	2871/tcp/udp	# MSI Select Play
radix		2872/tcp/udp	# RADIX
dxmessagebase1	2874/tcp/udp	# DX Message Base Transport Protocol
dxmessagebase2	2875/tcp/udp	# DX Message Base Transport Protocol
sps-tunnel	2876/tcp/udp	# SPS Tunnel
bluelance	2877/tcp/udp	# BLUELANCE
aap		2878/tcp/udp	# AAP
ucentric-ds	2879/tcp/udp
synapse		2880/tcp/udp	# Synapse Transport
ndsp		2881/tcp/udp	# NDSP
ndtp		2882/tcp/udp	# NDTP
ndnp		2883/tcp/udp	# NDNP
flashmsg	2884/tcp/udp	# Flash Msg
topflow		2885/tcp/udp	# TopFlow
responselogic	2886/tcp/udp	# RESPONSELOGIC
aironetddp	2887/tcp/udp	# aironet
spcsdlobby	2888/tcp/udp	# SPCSDLOBBY
rsom		2889/tcp/udp	# RSOM
cspclmulti	2890/tcp/udp	# CSPCLMULTI
cinegrfx-elmd	2891/tcp/udp	# CINEGRFX-ELMD License Manager
snifferdata	2892/tcp/udp	# SNIFFERDATA
vseconnector	2893/tcp/udp	# VSECONNECTOR
abacus-remote	2894/tcp/udp	# ABACUS-REMOTE
natuslink	2895/tcp/udp	# NATUS LINK
ecovisiong6-1	2896/tcp/udp	# ECOVISIONG6-1
citrix-rtmp	2897/tcp/udp	# Citrix RTMP
appliance-cfg	2898/tcp/udp	# APPLIANCE-CFG
powergemplus	2899/tcp/udp	# POWERGEMPLUS
quicksuite	2900/tcp/udp	# QUICKSUITE
allstorcns	2901/tcp/udp	# ALLSTORCNS
netaspi		2902/tcp/udp	# NET ASPI
suitcase	2903/tcp/udp	# SUITCASE
m2ua		2904/tcp/udp/sctp	# M2UA
m3ua		2905/tcp/sctp	# M3UA
caller9		2906/tcp/udp	# CALLER9
webmethods-b2b	2907/tcp/udp	# WEBMETHODS B2B
mao		2908/tcp/udp
funk-dialout	2909/tcp/udp	# Funk Dialout
tdaccess	2910/tcp/udp	# TDAccess
blockade	2911/tcp/udp	# Blockade
epicon		2912/tcp/udp	# Epicon
boosterware	2913/tcp/udp	# Booster Ware
gamelobby	2914/tcp/udp	# Game Lobby
tksocket	2915/tcp/udp	# TK Socket
elvin-server	2916/tcp/udp	# Elvin Server
elvin-client	2917/tcp/udp	# Elvin Client
kastenchasepad	2918/tcp/udp	# Kasten Chase Pad
roboer		2919/tcp/udp	# roboER
roboeda		2920/tcp/udp	# roboEDA
cesdcdman	2921/tcp/udp	# CESD Contents Delivery Management
cesdcdtrn	2922/tcp/udp	# CESD Contents Delivery Data Transfer
wta-wsp-wtp-s	2923/tcp/udp	# WTA-WSP-WTP-S
precise-vip	2924/tcp/udp	# PRECISE-VIP
mobile-file-dl	2926/tcp/udp	# MOBILE-FILE-DL
unimobilectrl	2927/tcp/udp	# UNIMOBILECTRL
redstone-cpss	2928/tcp/udp	# REDSTONE-CPSS
amx-webadmin	2929/tcp/udp	# AMX-WEBADMIN
amx-weblinx	2930/tcp/udp	# AMX-WEBLINX
circle-x	2931/tcp/udp	# Circle-X
incp		2932/tcp/udp	# INCP
4-tieropmgw	2933/tcp/udp	# 4-TIER OPM GW
4-tieropmcli	2934/tcp/udp	# 4-TIER OPM CLI
qtp		2935/tcp/udp	# QTP
otpatch		2936/tcp/udp	# OTPatch
pnaconsult-lm	2937/tcp/udp	# PNACONSULT-LM
sm-pas-1	2938/tcp/udp	# SM-PAS-1
sm-pas-2	2939/tcp/udp	# SM-PAS-2
sm-pas-3	2940/tcp/udp	# SM-PAS-3
sm-pas-4	2941/tcp/udp	# SM-PAS-4
sm-pas-5	2942/tcp/udp	# SM-PAS-5
ttnrepository	2943/tcp/udp	# TTNRepository
megaco-h248	2944/tcp/udp/sctp	# Megaco H-248
h248-binary	2945/tcp/udp/sctp	# H248 Binary
fjsvmpor	2946/tcp/udp	# FJSVmpor
gpsd		2947/tcp/udp	# GPS Daemon request/response protocol
wap-push	2948/tcp/udp	# WAP PUSH
wap-pushsecure	2949/tcp/udp	# WAP PUSH SECURE
esip		2950/tcp/udp	# ESIP
ottp		2951/tcp/udp	# OTTP
mpfwsas		2952/tcp/udp	# MPFWSAS
ovalarmsrv	2953/tcp/udp	# OVALARMSRV
ovalarmsrv-cmd	2954/tcp/udp	# OVALARMSRV-CMD
csnotify	2955/tcp/udp	# CSNOTIFY
ovrimosdbman	2956/tcp/udp	# OVRIMOSDBMAN
jmact5		2957/tcp/udp	# JAMCT5
jmact6		2958/tcp/udp	# JAMCT6
rmopagt		2959/tcp/udp	# RMOPAGT
dfoxserver	2960/tcp/udp	# DFOXSERVER
boldsoft-lm	2961/tcp/udp	# BOLDSOFT-LM
iph-policy-cli	2962/tcp/udp	# IPH-POLICY-CLI
iph-policy-adm	2963/tcp/udp	# IPH-POLICY-ADM
bullant-srap	2964/tcp/udp	# BULLANT SRAP
bullant-rap	2965/tcp/udp	# BULLANT RAP
idp-infotrieve	2966/tcp/udp	# IDP-INFOTRIEVE
ssc-agent	2967/tcp/udp	# SSC-AGENT
enpp		2968/tcp/udp	# ENPP
essp		2969/tcp/udp	# ESSP
index-net	2970/tcp/udp	# INDEX-NET
netclip		2971/tcp/udp	# NetClip clipboard daemon
pmsm-webrctl	2972/tcp/udp	# PMSM Webrctl
svnetworks	2973/tcp/udp	# SV Networks
signal		2974/tcp/udp	# Signal
fjmpcm		2975/tcp/udp	# Fujitsu Configuration Management Service
cns-srv-port	2976/tcp/udp	# CNS Server Port
ttc-etap-ns	2977/tcp/udp	# TTCs Enterprise Test Access Protocol - NS
ttc-etap-ds	2978/tcp/udp	# TTCs Enterprise Test Access Protocol - DS
h263-video	2979/tcp/udp	# H.263 Video Streaming
wimd		2980/tcp/udp	# Instant Messaging Service
mylxamport	2981/tcp/udp	# MYLXAMPORT
iwb-whiteboard	2982/tcp/udp	# IWB-WHITEBOARD
netplan		2983/tcp/udp	# NETPLAN
hpidsadmin	2984/tcp/udp	# HPIDSADMIN
hpidsagent	2985/tcp/udp	# HPIDSAGENT
stonefalls	2986/tcp/udp	# STONEFALLS
identify	2987/tcp/udp
hippad		2988/tcp/udp	# HIPPA Reporting Protocol
zarkov		2989/tcp/udp	# ZARKOV Intelligent Agent Communication
boscap		2990/tcp/udp	# BOSCAP
wkstn-mon	2991/tcp/udp	# WKSTN-MON
avenyo		2992/tcp/udp	# Avenyo Server
veritas-vis1	2993/tcp/udp	# VERITAS VIS1
veritas-vis2	2994/tcp/udp	# VERITAS VIS2
idrs		2995/tcp/udp	# IDRS
vsixml		2996/tcp/udp
rebol		2997/tcp/udp	# REBOL
realsecure	2998/tcp/udp	# Real Secure
remoteware-un	2999/tcp/udp	# RemoteWare Unassigned
hbci		3000/tcp/udp	# HBCI
origo-native	3001/tcp	# OrigoDB Server Native Interface
exlm-agent	3002/tcp/udp	# EXLM Agent
cgms		3003/tcp/udp	# CGMS
csoftragent	3004/tcp/udp	# Csoft Agent
geniuslm	3005/tcp/udp	# Genius License Manager
ii-admin	3006/tcp/udp	# Instant Internet Admin
lotusmtap	3007/tcp/udp	# Lotus Mail Tracking Agent Protocol
midnight-tech	3008/tcp/udp	# Midnight Technologies
pxc-ntfy	3009/tcp/udp	# PXC-NTFY
gw		3010/tcp	# Telerate Workstation
ping-pong	3010/udp	# Telerate Workstation
trusted-web	3011/tcp/udp	# Trusted Web
twsdss		3012/tcp/udp	# Trusted Web Client
gilatskysurfer	3013/tcp/udp	# Gilat Sky Surfer
broker-service	3014/tcp/udp	# Broker Service
nati-dstp	3015/tcp/udp	# NATI DSTP
notify-srvr	3016/tcp/udp	# Notify Server
event-listener	3017/tcp/udp	# Event Listener
srvc-registry	3018/tcp/udp	# Service Registry
resource-mgr	3019/tcp/udp	# Resource Manager
cifs		3020/tcp/udp	# CIFS
agriserver	3021/tcp/udp	# AGRI Server
csregagent	3022/tcp/udp	# CSREGAGENT
magicnotes	3023/tcp/udp
nds-sso		3024/tcp/udp	# NDS_SSO
arepa-raft	3025/tcp/udp	# Arepa Raft
agri-gateway	3026/tcp/udp	# AGRI Gateway
LiebDevMgmt-C	3027/tcp/udp	# LiebDevMgmt_C
LiebDevMgmt-DM	3028/tcp/udp	# LiebDevMgmt_DM
LiebDevMgmt-A	3029/tcp/udp	# LiebDevMgmt_A
arepa-cas	3030/tcp/udp	# Arepa Cas
eppc		3031/tcp/udp	# Remote AppleEvents/PPC Toolbox
redwood-chat	3032/tcp/udp	# Redwood Chat
pdb		3033/tcp/udp	# PDB
osmosis-aeea	3034/tcp/udp	# Osmosis / Helix (R) AEEA Port
fjsv-gssagt	3035/tcp/udp	# FJSV gssagt
hagel-dump	3036/tcp/udp	# Hagel DUMP
hp-san-mgmt	3037/tcp/udp	# HP SAN Mgmt
santak-ups	3038/tcp/udp	# Santak UPS
cogitate	3039/tcp/udp	# Cogitate, Inc.
tomato-springs	3040/tcp/udp	# Tomato Springs
di-traceware	3041/tcp/udp
journee		3042/tcp/udp
brp		3043/tcp/udp	# Broadcast Routing Protocol
epp		3044/tcp/udp	# EndPoint Protocol
responsenet	3045/tcp/udp	# ResponseNet
di-ase		3046/tcp/udp
hlserver	3047/tcp/udp	# Fast Security HL Server
pctrader	3048/tcp/udp	# Sierra Net PC Trader
nsws		3049/tcp/udp	# NSWS
gds-db		3050/tcp/udp	# gds_db
galaxy-server	3051/tcp/udp	# Galaxy Server
apc-3052	3052/tcp/udp	# APC 3052
dsom-server	3053/tcp/udp
amt-cnf-prot	3054/tcp/udp	# AMT CNF PROT
policyserver	3055/tcp/udp	# Policy Server
cdl-server	3056/tcp/udp	# CDL Server
goahead-fldup	3057/tcp/udp	# GoAhead FldUp
videobeans	3058/tcp/udp
qsoft		3059/tcp/udp
interserver	3060/tcp/udp
cautcpd		3061/tcp/udp
ncacn-ip-tcp	3062/tcp/udp
ncadg-ip-udp	3063/tcp/udp
rprt		3064/tcp/udp	# Remote Port Redirector
slinterbase	3065/tcp/udp
netattachsdmp	3066/tcp/udp	# NETATTACHSDMP
fjhpjp		3067/tcp/udp	# FJHPJP
ls3bcast	3068/tcp/udp	# ls3 Broadcast
ls3		3069/tcp/udp
mgxswitch	3070/tcp/udp	# MGXSWITCH
xplat-replicate	3071/tcp	# Crossplatform replication protocol
csd-monitor	3072/tcp/udp	# ContinuStor Monitor Port
vcrp		3073/tcp/udp	# Very simple chatroom prot
xbox		3074/tcp/udp	# Xbox game port
orbix-locator	3075/tcp/udp	# Orbix 2000 Locator
orbix-config	3076/tcp/udp	# Orbix 2000 Config
orbix-loc-ssl	3077/tcp/udp	# Orbix 2000 Locator SSL
orbix-cfg-ssl	3078/tcp/udp	# Orbix 2000 Locator SSL
lv-frontpanel	3079/tcp/udp	# LV Front Panel
stm-pproc	3080/tcp/udp	# stm_pproc
tl1-lv		3081/tcp/udp	# TL1-LV
tl1-raw		3082/tcp/udp	# TL1-RAW
tl1-telnet	3083/tcp/udp	# TL1-TELNET
itm-mccs	3084/tcp/udp	# ITM-MCCS
pcihreq		3085/tcp/udp	# PCIHReq
jdl-dbkitchen	3086/tcp/udp	# JDL-DBKitchen
asoki-sma	3087/tcp/udp	# Asoki SMA
xdtp		3088/tcp/udp	# eXtensible Data Transfer Protocol
ptk-alink	3089/tcp/udp	# ParaTek Agent Linking
stss		3090/tcp/udp	# Senforce Session Services
1ci-smcs	3091/tcp/udp	# 1Ci Server Management
rapidmq-center	3093/tcp/udp	# Jiiva RapidMQ Center
rapidmq-reg	3094/tcp/udp	# Jiiva RapidMQ Registry
panasas		3095/tcp/udp	# Panasas rendezvous port
ndl-aps		3096/tcp/udp	# Active Print Server Port
itu-bicc-stc	3097/sctp	# ITU-T Q.1902.1/Q.2150.3
umm-port	3098/tcp/udp	# Universal Message Manager
chmd		3099/tcp/udp	# CHIPSY Machine Daemon
opcon-xps	3100/tcp/udp	# OpCon/xps
hp-pxpib	3101/tcp/udp	# HP PolicyXpert PIB Server
slslavemon	3102/tcp/udp	# SoftlinK Slave Mon Port
autocuesmi	3103/tcp/udp	# Autocue SMI Protocol
autocuelog	3104/tcp	# Autocue Logger Protocol
autocuetime	3104/udp	# Autocue Time Service
cardbox		3105/tcp/udp	# Cardbox
cardbox-http	3106/tcp/udp	# Cardbox HTTP
business	3107/tcp/udp	# Business protocol
geolocate	3108/tcp/udp	# Geolocate protocol
personnel	3109/tcp/udp	# Personnel protocol
sim-control	3110/tcp/udp	# simulator control port
wsynch		3111/tcp/udp	# Web Synchronous Services
ksysguard	3112/tcp/udp	# KDE System Guard
cs-auth-svr	3113/tcp/udp	# CS-Authenticate Svr Port
ccmad		3114/tcp/udp	# CCM AutoDiscover
mctet-master	3115/tcp/udp	# MCTET Master
mctet-gateway	3116/tcp/udp	# MCTET Gateway
mctet-jserv	3117/tcp/udp	# MCTET Jserv
pkagent		3118/tcp/udp	# PKAgent
d2000kernel	3119/tcp/udp	# D2000 Kernel Port
d2000webserver	3120/tcp/udp	# D2000 Webserver Port
pcmk-remote	3121/tcp	# The pacemaker remote (pcmk-remote) service extends high availability functionality outside of the Linux cluster into remote nodes.
vtr-emulator	3122/tcp/udp	# MTI VTR Emulator port
edix		3123/tcp/udp	# EDI Translation Protocol
beacon-port	3124/tcp/udp	# Beacon Port
a13-an		3125/tcp/udp	# A13-AN Interface
ctx-bridge	3127/tcp/udp	# CTX Bridge Port
ndl-aas		3128/tcp/udp	# Active API Server Port
netport-id	3129/tcp/udp	# NetPort Discovery Port
icpv2		3130/tcp/udp	# ICPv2
netbookmark	3131/tcp/udp	# Net Book Mark
ms-rule-engine	3132/tcp/udp	# Microsoft Business Rule Engine Update Service
prism-deploy	3133/tcp/udp	# Prism Deploy User Port
ecp		3134/tcp/udp	# Extensible Code Protocol
peerbook-port	3135/tcp/udp	# PeerBook Port
grubd		3136/tcp/udp	# Grub Server Port
rtnt-1		3137/tcp/udp	# rtnt-1 data packets
rtnt-2		3138/tcp/udp	# rtnt-2 data packets
incognitorv	3139/tcp/udp	# Incognito Rendez-Vous
ariliamulti	3140/tcp/udp	# Arilia Multiplexor
vmodem		3141/tcp/udp	# VMODEM
rdc-wh-eos	3142/tcp/udp	# RDC WH EOS
seaview		3143/tcp/udp	# Sea View
tarantella	3144/tcp/udp	# Tarantella
csi-lfap	3145/tcp/udp	# CSI-LFAP
bears-02	3146/tcp/udp
rfio		3147/tcp/udp	# RFIO
nm-game-admin	3148/tcp/udp	# NetMike Game Administrator
nm-game-server	3149/tcp/udp	# NetMike Game Server
nm-asses-admin	3150/tcp/udp	# NetMike Assessor Administrator
nm-assessor	3151/tcp/udp	# NetMike Assessor
feitianrockey	3152/tcp/udp	# FeiTian Port
s8-client-port	3153/tcp/udp	# S8Cargo Client Port
ccmrmi		3154/tcp/udp	# ON RMI Registry
jpegmpeg	3155/tcp/udp	# JpegMpeg Port
indura		3156/tcp/udp	# Indura Collector
e3consultants	3157/tcp/udp	# CCC Listener Port
stvp		3158/tcp/udp	# SmashTV Protocol
navegaweb-port	3159/tcp/udp	# NavegaWeb Tarification
tip-app-server	3160/tcp/udp	# TIP Application Server
doc1lm		3161/tcp/udp	# DOC1 License Manager
sflm		3162/tcp/udp	# SFLM
res-sap		3163/tcp/udp	# RES-SAP
imprs		3164/tcp/udp	# IMPRS
newgenpay	3165/tcp/udp	# Newgenpay Engine Service
sossecollector	3166/tcp/udp	# Quest Spotlight Out-Of-Process Collector
nowcontact	3167/tcp/udp	# Now Contact Public Server
poweronnud	3168/tcp/udp	# Now Up-to-Date Public Server
serverview-as	3169/tcp/udp	# SERVERVIEW-AS
serverview-asn	3170/tcp/udp	# SERVERVIEW-ASN
serverview-gf	3171/tcp/udp	# SERVERVIEW-GF
serverview-rm	3172/tcp/udp	# SERVERVIEW-RM
serverview-icc	3173/tcp/udp	# SERVERVIEW-ICC
armi-server	3174/tcp/udp	# ARMI Server
t1-e1-over-ip	3175/tcp/udp	# T1_E1_Over_IP
ars-master	3176/tcp/udp	# ARS Master
phonex-port	3177/tcp/udp	# Phonex Protocol
radclientport	3178/tcp/udp	# Radiance UltraEdge Port
h2gf-w-2m	3179/tcp/udp	# H2GF W.2m Handover prot.
mc-brk-srv	3180/tcp/udp	# Millicent Broker Server
bmcpatrolagent	3181/tcp/udp	# BMC Patrol Agent
bmcpatrolrnvu	3182/tcp/udp	# BMC Patrol Rendezvous
cops-tls	3183/tcp/udp	# COPS/TLS
apogeex-port	3184/tcp/udp	# ApogeeX Port
smpppd		3185/tcp/udp	# SuSE Meta PPPD
iiw-port	3186/tcp/udp	# IIW Monitor User Port
odi-port	3187/tcp/udp	# Open Design Listen Port
brcm-comm-port	3188/tcp/udp	# Broadcom Port
pcle-infex	3189/tcp/udp	# Pinnacle Sys InfEx Port
csvr-proxy	3190/tcp/udp	# ConServR Proxy
csvr-sslproxy	3191/tcp/udp	# ConServR SSL Proxy
firemonrcc	3192/tcp/udp	# FireMon Revision Control
spandataport	3193/tcp/udp	# SpanDataPort
magbind		3194/tcp/udp	# Rockstorm MAG protocol
ncu-1		3195/tcp/udp	# Network Control Unit
ncu-2		3196/tcp/udp	# Network Control Unit
embrace-dp-s	3197/tcp/udp	# Embrace Device Protocol Server
embrace-dp-c	3198/tcp/udp	# Embrace Device Protocol Client
dmod-workspace	3199/tcp/udp	# DMOD WorkSpace
tick-port	3200/tcp/udp	# Press-sense Tick Port
cpq-tasksmart	3201/tcp/udp	# CPQ-TaskSmart
intraintra	3202/tcp/udp	# IntraIntra
netwatcher-mon	3203/tcp/udp	# Network Watcher Monitor
netwatcher-db	3204/tcp/udp	# Network Watcher DB Access
isns		3205/tcp/udp	# iSNS Server Port
ironmail	3206/tcp/udp	# IronMail POP Proxy
vx-auth-port	3207/tcp/udp	# Veritas Authentication Port
pfu-prcallback	3208/tcp/udp	# PFU PR Callback
netwkpathengine	3209/tcp/udp	# HP OpenView Network Path Engine Server
flamenco-proxy	3210/tcp/udp	# Flamenco Networks Proxy
avsecuremgmt	3211/tcp/udp	# Avocent Secure Management
surveyinst	3212/tcp/udp	# Survey Instrument
neon24x7	3213/tcp/udp	# NEON 24X7 Mission Control
jmq-daemon-1	3214/tcp/udp	# JMQ Daemon Port 1
jmq-daemon-2	3215/tcp/udp	# JMQ Daemon Port 2
ferrari-foam	3216/tcp/udp	# Ferrari electronic FOAM
unite		3217/tcp/udp	# Unified IP & Telecom Environment
smartpackets	3218/tcp/udp	# EMC SmartPackets
wms-messenger	3219/tcp/udp	# WMS Messenger
xnm-ssl		3220/tcp/udp	# XML NM over SSL
xnm-clear-text	3221/tcp/udp	# XML NM over TCP
glbp		3222/tcp/udp	# Gateway Load Balancing Pr
digivote	3223/tcp/udp	# DIGIVOTE (R) Vote-Server
aes-discovery	3224/tcp/udp	# AES Discovery Port
fcip-port	3225/tcp/udp	# FCIP
isi-irp		3226/tcp/udp	# ISI Industry Software IRP
dwnmshttp	3227/tcp/udp	# DiamondWave NMS Server
dwmsgserver	3228/tcp/udp	# DiamondWave MSG Server
global-cd-port	3229/tcp/udp	# Global CD Port
sftdst-port	3230/tcp/udp	# Software Distributor Port
vidigo		3231/tcp/udp	# VidiGo communication (previous was: Delta Solutions Direct)
mdtp		3232/tcp/udp	# MDT port
whisker		3233/tcp/udp	# WhiskerControl main port
alchemy		3234/tcp/udp	# Alchemy Server
mdap-port	3235/tcp/udp	# MDAP port
apparenet-ts	3236/tcp/udp	# appareNet Test Server
apparenet-tps	3237/tcp/udp	# appareNet Test Packet Sequencer
apparenet-as	3238/tcp/udp	# appareNet Analysis Server
apparenet-ui	3239/tcp/udp	# appareNet User Interface
triomotion	3240/tcp/udp	# Trio Motion Control Port
sysorb		3241/tcp/udp	# SysOrb Monitoring Server
sdp-id-port	3242/tcp/udp	# Session Description ID
timelot		3243/tcp/udp	# Timelot Port
onesaf		3244/tcp/udp	# OneSAF
vieo-fe		3245/tcp/udp	# VIEO Fabric Executive
dvt-system	3246/tcp/udp	# DVT SYSTEM PORT
dvt-data	3247/tcp/udp	# DVT DATA LINK
procos-lm	3248/tcp/udp	# PROCOS LM
ssp		3249/tcp/udp	# State Sync Protocol
hicp		3250/tcp/udp	# HMS hicp port
sysscanner	3251/tcp/udp	# Sys Scanner
dhe		3252/tcp/udp	# DHE port
pda-data	3253/tcp/udp	# PDA Data
pda-sys		3254/tcp/udp	# PDA System
semaphore	3255/tcp/udp	# Semaphore Connection Port
cpqrpm-agent	3256/tcp/udp	# Compaq RPM Agent Port
cpqrpm-server	3257/tcp/udp	# Compaq RPM Server Port
ivecon-port	3258/tcp/udp	# Ivecon Server Port
epncdp2		3259/tcp/udp	# Epson Network Common Devi
iscsi-target	3260/tcp/udp	# iSCSI port
winshadow	3261/tcp/udp	# winShadow
necp		3262/tcp/udp	# NECP
ecolor-imager	3263/tcp/udp	# E-Color Enterprise Imager
ccmail		3264/tcp/udp	# cc:mail/lotus
altav-tunnel	3265/tcp/udp	# Altav Tunnel
ns-cfg-server	3266/tcp/udp	# NS CFG Server
ibm-dial-out	3267/tcp/udp	# IBM Dial Out
msft-gc		3268/tcp/udp	# Microsoft Global Catalog
msft-gc-ssl	3269/tcp/udp	# Microsoft Global Catalog with LDAP/SSL
verismart	3270/tcp/udp	# Verismart
csoft-prev	3271/tcp/udp	# CSoft Prev Port
user-manager	3272/tcp/udp	# Fujitsu User Manager
sxmp		3273/tcp/udp	# Simple Extensible Multiplexed Protocol
ordinox-server	3274/tcp/udp	# Ordinox Server
samd		3275/tcp/udp	# SAMD
maxim-asics	3276/tcp/udp	# Maxim ASICs
awg-proxy	3277/tcp/udp	# AWG Proxy
lkcmserver	3278/tcp/udp	# LKCM Server
admind		3279/tcp/udp
vs-server	3280/tcp/udp	# VS Server
sysopt		3281/tcp/udp	# SYSOPT
datusorb	3282/tcp/udp	# Datusorb
Apple-Remote-De	3283/tcp/udp	# Net Assistant
4talk		3284/tcp/udp	# 4Talk
plato		3285/tcp/udp	# Plato
e-net		3286/tcp/udp	# E-Net
directvdata	3287/tcp/udp	# DIRECTVDATA
cops		3288/tcp/udp	# COPS
enpc		3289/tcp/udp	# ENPC
caps-lm		3290/tcp/udp	# CAPS LOGISTICS TOOLKIT - LM
sah-lm		3291/tcp/udp	# S A Holditch & Associates - LM
cart-o-rama	3292/tcp/udp	# Cart O Rama
fg-fps		3293/tcp/udp
fg-gip		3294/tcp/udp
dyniplookup	3295/tcp/udp	# Dynamic IP Lookup
rib-slm		3296/tcp/udp	# Rib License Manager
cytel-lm	3297/tcp/udp	# Cytel License Manager
deskview	3298/tcp/udp	# DeskView
pdrncs		3299/tcp/udp
ceph		3300/tcp	# Ceph monitor
mcs-fastmail	3302/tcp/udp	# MCS Fastmail
opsession-clnt	3303/tcp/udp	# OP Session Client
opsession-srvr	3304/tcp/udp	# OP Session Server
odette-ftp	3305/tcp/udp	# ODETTE-FTP
mysql		3306/tcp/udp	# MySQL
opsession-prxy	3307/tcp/udp	# OP Session Proxy
tns-server	3308/tcp/udp	# TNS Server
tns-adv		3309/tcp/udp	# TNS ADV
dyna-access	3310/tcp/udp	# Dyna Access
mcns-tel-ret	3311/tcp/udp	# MCNS Tel Ret
appman-server	3312/tcp/udp	# Application Management Server
uorb		3313/tcp/udp	# Unify Object Broker
uohost		3314/tcp/udp	# Unify Object Host
cdid		3315/tcp/udp	# CDID
aicc-cmi	3316/tcp/udp	# AICC/CMI
vsaiport	3317/tcp/udp	# VSAI PORT
ssrip		3318/tcp/udp	# Swith to Swith Routing Information Protocol
sdt-lmd		3319/tcp/udp	# SDT License Manager
officelink2000	3320/tcp/udp	# Office Link 2000
vnsstr		3321/tcp/udp	# VNSSTR
sftu		3326/tcp/udp	# SFTU
bbars		3327/tcp/udp	# BBARS
egptlm		3328/tcp/udp	# Eaglepoint License Manager
hp-device-disc	3329/tcp/udp	# HP Device Disc
mcs-calypsoicf	3330/tcp/udp	# MCS Calypso ICF
mcs-messaging	3331/tcp/udp	# MCS Messaging
mcs-mailsvr	3332/tcp/udp	# MCS Mail Server
dec-notes	3333/tcp/udp	# DEC Notes
directv-web	3334/tcp/udp	# Direct TV Webcasting
directv-soft	3335/tcp/udp	# Direct TV Software Updates
directv-tick	3336/tcp/udp	# Direct TV Tickers
directv-catlg	3337/tcp/udp	# Direct TV Data Catalog
anet-b		3338/tcp/udp	# OMF data b
anet-l		3339/tcp/udp	# OMF data l
anet-m		3340/tcp/udp	# OMF data m
anet-h		3341/tcp/udp	# OMF data h
webtie		3342/tcp/udp	# WebTIE
ms-cluster-net	3343/tcp/udp	# MS Cluster Net
bnt-manager	3344/tcp/udp	# BNT Manager
influence	3345/tcp/udp	# Influence
trnsprntproxy	3346/tcp/udp	# Trnsprnt Proxy
phoenix-rpc	3347/tcp/udp	# Phoenix RPC
pangolin-laser	3348/tcp/udp	# Pangolin Laser
chevinservices	3349/tcp/udp	# Chevin Services
findviatv	3350/tcp/udp	# FINDVIATV
btrieve		3351/tcp/udp	# Btrieve port
ssql		3352/tcp/udp	# Scalable SQL
fatpipe		3353/tcp/udp	# FATPIPE
suitjd		3354/tcp/udp	# SUITJD
ordinox-dbase	3355/tcp/udp	# Ordinox Dbase
upnotifyps	3356/tcp/udp	# UPNOTIFYPS
adtech-test	3357/tcp/udp	# Adtech Test IP
mpsysrmsvr	3358/tcp/udp	# Mp Sys Rmsvr
wg-netforce	3359/tcp/udp	# WG NetForce
kv-server	3360/tcp/udp	# KV Server
kv-agent	3361/tcp/udp	# KV Agent
dj-ilm		3362/tcp/udp	# DJ ILM
nati-vi-server	3363/tcp/udp	# NATI Vi Server
creativeserver	3364/tcp/udp	# Creative Server
contentserver	3365/tcp/udp	# Content Server
creativepartnr	3366/tcp/udp	# Creative Partner
tip2		3372/tcp/udp	# TIP 2
lavenir-lm	3373/tcp/udp	# Lavenir License Manager
cluster-disc	3374/tcp/udp	# Cluster Disc
vsnm-agent	3375/tcp/udp	# VSNM Agent
cdbroker	3376/tcp/udp	# CD Broker
cogsys-lm	3377/tcp/udp	# Cogsys Network License Manager
wsicopy		3378/tcp/udp	# WSICOPY
socorfs		3379/tcp/udp	# SOCORFS
sns-channels	3380/tcp/udp	# SNS Channels
geneous		3381/tcp/udp	# Geneous
fujitsu-neat	3382/tcp/udp	# Fujitsu Network Enhanced Antitheft function
esp-lm		3383/tcp/udp	# Enterprise Software Products License Manager
hp-clic		3384/tcp/udp	# Cluster Management Services
qnxnetman	3385/tcp/udp
gprs-data	3386/tcp	# GPRS Data
gprs-sig	3386/udp	# GPRS SIG
backroomnet	3387/tcp/udp	# Back Room Net
cbserver	3388/tcp/udp	# CB Server
ms-wbt-server	3389/tcp/udp	# MS WBT Server
dsc		3390/tcp/udp	# Distributed Service Coordinator
savant		3391/tcp/udp	# SAVANT
efi-lm		3392/tcp/udp	# EFI License Management
d2k-tapestry1	3393/tcp/udp	# D2K Tapestry Client to Server
d2k-tapestry2	3394/tcp/udp	# D2K Tapestry Server to Server
dyna-lm		3395/tcp/udp	# Dyna License Manager (Elam)
printer-agent	3396/tcp/udp	# Printer Agent
cloanto-lm	3397/tcp/udp	# Cloanto License Manager
mercantile	3398/tcp/udp	# Mercantile
csms		3399/tcp/udp	# CSMS
csms2		3400/tcp/udp	# CSMS2
filecast	3401/tcp/udp
fxaengine-net	3402/tcp/udp	# FXa Engine Network Port
nokia-ann-ch1	3405/tcp/udp	# Nokia Announcement ch 1
nokia-ann-ch2	3406/tcp/udp	# Nokia Announcement ch 2
ldap-admin	3407/tcp/udp	# LDAP admin server port
BESApi		3408/tcp/udp	# BES Api Port
networklens	3409/tcp/udp	# NetworkLens Event Port
networklenss	3410/tcp/udp	# NetworkLens SSL Event
biolink-auth	3411/tcp/udp	# BioLink Authenteon server
xmlblaster	3412/tcp/udp	# xmlBlaster
svnet		3413/tcp/udp	# SpecView Networking
wip-port	3414/tcp/udp	# BroadCloud WIP Port
bcinameservice	3415/tcp/udp	# BCI Name Service
commandport	3416/tcp/udp	# AirMobile IS Command Port
csvr		3417/tcp/udp	# ConServR file translation
rnmap		3418/tcp/udp	# Remote nmap
softaudit	3419/tcp/udp	# Isogon SoftAudit
ifcp-port	3420/tcp/udp	# iFCP User Port
bmap		3421/tcp/udp	# Bull Apprise portmapper
rusb-sys-port	3422/tcp/udp	# Remote USB System Port
xtrm		3423/tcp/udp	# xTrade Reliable Messaging
xtrms		3424/tcp/udp	# xTrade over TLS/SSL
agps-port	3425/tcp/udp	# AGPS Access Port
arkivio		3426/tcp/udp	# Arkivio Storage Protocol
websphere-snmp	3427/tcp/udp	# WebSphere SNMP
twcss		3428/tcp/udp	# 2Wire CSS
gcsp		3429/tcp/udp	# GCSP user port
ssdispatch	3430/tcp/udp	# Scott Studios Dispatch
ndl-als		3431/tcp/udp	# Active License Server Port
osdcp		3432/tcp/udp	# Secure Device Protocol
opnet-smp	3433/tcp/udp	# OPNET Service Management Platform
opencm		3434/tcp/udp	# OpenCM Server
pacom		3435/tcp/udp	# Pacom Security User Port
gc-config	3436/tcp/udp	# GuardControl Exchange Protocol
autocueds	3437/tcp/udp	# Autocue Directory Service
spiral-admin	3438/tcp/udp	# Spiralcraft Admin
hri-port	3439/tcp/udp	# HRI Interface Port
ans-console	3440/tcp/udp	# Net Steward Mgmt Console
connect-client	3441/tcp/udp	# OC Connect Client
connect-server	3442/tcp/udp	# OC Connect Server
ov-nnm-websrv	3443/tcp/udp	# OpenView Network Node Manager WEB Server
denali-server	3444/tcp/udp	# Denali Server
monp		3445/tcp/udp	# Media Object Network
3comfaxrpc	3446/tcp/udp	# 3Com FAX RPC port
directnet	3447/tcp/udp	# DirectNet IM System
dnc-port	3448/tcp/udp	# Discovery and Net Config
hotu-chat	3449/tcp/udp	# HotU Chat
castorproxy	3450/tcp/udp	# CAStorProxy
asam		3451/tcp/udp	# ASAM Services
sabp-signal	3452/tcp/udp	# SABP-Signalling Protocol
pscupd		3453/tcp/udp	# PSC Update
mira		3454/tcp/udp	# Apple Remote Access Protocol
prsvp		3455/tcp/udp	# RSVP Port
vat		3456/tcp/udp	# VAT default data
vat-control	3457/tcp/udp	# VAT default control
d3winosfi	3458/tcp/udp	# D3WinOSFI
integral	3459/tcp/udp	# TIP Integral
edm-manager	3460/tcp/udp	# EDM Manger
edm-stager	3461/tcp/udp	# EDM Stager
edm-std-notify	3462/tcp/udp	# EDM STD Notify
edm-adm-notify	3463/tcp/udp	# EDM ADM Notify
edm-mgr-sync	3464/tcp/udp	# EDM MGR Sync
edm-mgr-cntrl	3465/tcp/udp	# EDM MGR Cntrl
workflow	3466/tcp/udp	# WORKFLOW
rcst		3467/tcp/udp	# RCST
ttcmremotectrl	3468/tcp/udp	# TTCM Remote Controll
pluribus	3469/tcp/udp	# Pluribus
jt400		3470/tcp/udp
jt400-ssl	3471/tcp/udp
jaugsremotec-1	3472/tcp/udp	# JAUGS N-G Remotec 1
jaugsremotec-2	3473/tcp/udp	# JAUGS N-G Remotec 2
ttntspauto	3474/tcp/udp	# TSP Automation
genisar-port	3475/tcp/udp	# Genisar Comm Port
nppmp		3476/tcp/udp	# NVIDIA Mgmt Protocol
ecomm		3477/tcp/udp	# eComm link port
stun		3478/tcp/udp	# Session Traversal Utilities for NAT (STUN) port
twrpc		3479/tcp/udp	# 2Wire RPC
plethora	3480/tcp/udp	# Secure Virtual Workspace
cleanerliverc	3481/tcp/udp	# CleanerLive remote ctrl
vulture		3482/tcp/udp	# Vulture Monitoring System
slim-devices	3483/tcp/udp	# Slim Devices Protocol
gbs-stp		3484/tcp/udp	# GBS SnapTalk Protocol
celatalk	3485/tcp/udp	# CelaTalk
ifsf-hb-port	3486/tcp/udp	# IFSF Heartbeat Port
ltctcp		3487/tcp	# LISA TCP Transfer Channel
ltcudp		3487/udp	# LISA UDP Transfer Channel
fs-rh-srv	3488/tcp/udp	# FS Remote Host Server
dtp-dia		3489/tcp/udp	# DTP/DIA
colubris	3490/tcp/udp	# Colubris Management Port
swr-port	3491/tcp/udp	# SWR Port
tvdumtray-port	3492/tcp/udp	# TVDUM Tray Port
nut		3493/tcp/udp	# Network UPS Tools
ibm3494		3494/tcp/udp	# IBM 3494
seclayer-tcp	3495/tcp/udp	# securitylayer over tcp
seclayer-tls	3496/tcp/udp	# securitylayer over tls
ipether232port	3497/tcp/udp	# ipEther232Port
dashpas-port	3498/tcp/udp	# DASHPAS user port
sccip-media	3499/tcp/udp	# SccIP Media
rtmp-port	3500/tcp/udp	# RTMP Port
isoft-p2p	3501/tcp/udp	# iSoft-P2P
avinstalldisc	3502/tcp/udp	# Avocent Install Discovery
lsp-ping	3503/tcp/udp	# MPLS LSP-echo Port
ironstorm	3504/tcp/udp	# IronStorm game server
ccmcomm		3505/tcp/udp	# CCM communications port
apc-3506	3506/tcp/udp	# APC 3506
nesh-broker	3507/tcp/udp	# Nesh Broker Port
interactionweb	3508/tcp/udp	# Interaction Web
vt-ssl		3509/tcp/udp	# Virtual Token SSL Port
xss-port	3510/tcp/udp	# XSS Port
webmail-2	3511/tcp/udp	# WebMail/2
aztec		3512/tcp/udp	# Aztec Distribution Port
arcpd		3513/tcp/udp	# Adaptec Remote Protocol
must-p2p	3514/tcp/udp	# MUST Peer to Peer
must-backplane	3515/tcp/udp	# MUST Backplane
smartcard-port	3516/tcp/udp	# Smartcard Port
802-11-iapp	3517/tcp/udp	# IEEE 802.11 WLANs WG IAPP
artifact-msg	3518/tcp/udp	# Artifact Message Server
nvmsgd		3519/tcp	# Netvion Messenger Port
galileo		3519/udp	# Netvion Galileo Port
galileolog	3520/tcp/udp	# Netvion Galileo Log Port
mc3ss		3521/tcp/udp	# Telequip Labs MC3SS
nssocketport	3522/tcp/udp	# DO over NSSocketPort
odeumservlink	3523/tcp/udp	# Odeum Serverlink
ecmport		3524/tcp/udp	# ECM Server port
eisport		3525/tcp/udp	# EIS Server port
starquiz-port	3526/tcp/udp	# starQuiz Port
beserver-msg-q	3527/tcp/udp	# VERITAS Backup Exec Server
jboss-iiop	3528/tcp/udp	# JBoss IIOP
jboss-iiop-ssl	3529/tcp/udp	# JBoss IIOP/SSL
gf		3530/tcp/udp	# Grid Friendly
joltid		3531/tcp/udp	# Joltid
raven-rmp	3532/tcp/udp	# Raven Remote Management Control
raven-rdp	3533/tcp/udp	# Raven Remote Management Data
urld-port	3534/tcp/udp	# URL Daemon Port
ms-la		3535/tcp/udp	# MS-LA
snac		3536/tcp/udp	# SNAC
ni-visa-remote	3537/tcp/udp	# Remote NI-VISA port
ibm-diradm	3538/tcp/udp	# IBM Directory Server
ibm-diradm-ssl	3539/tcp/udp	# IBM Directory Server SSL
pnrp-port	3540/tcp/udp	# PNRP User Port
voispeed-port	3541/tcp/udp	# VoiSpeed Port
hacl-monitor	3542/tcp/udp	# HA cluster monitor
qftest-lookup	3543/tcp/udp	# qftest Lookup Port
teredo		3544/tcp/udp	# Teredo Port
camac		3545/tcp/udp	# CAMAC equipment
symantec-sim	3547/tcp/udp	# Symantec SIM
interworld	3548/tcp/udp	# Interworld
tellumat-nms	3549/tcp/udp	# Tellumat MDR NMS
ssmpp		3550/tcp/udp	# Secure SMPP
apcupsd		3551/tcp/udp	# Apcupsd Information Port
taserver	3552/tcp/udp	# TeamAgenda Server Port
rbr-discovery	3553/tcp/udp	# Red Box Recorder ADP
questnotify	3554/tcp/udp	# Quest Notification Server
razor		3555/tcp/udp	# Vipul's Razor
sky-transport	3556/tcp/udp	# Sky Transport Protocol
personalos-001	3557/tcp/udp	# PersonalOS Comm Port
mcp-port	3558/tcp/udp	# MCP user port
cctv-port	3559/tcp/udp	# CCTV control port
iniserve-port	3560/tcp/udp	# INIServe port
bmc-onekey	3561/tcp/udp	# BMC-OneKey
sdbproxy	3562/tcp/udp	# SDBProxy
watcomdebug	3563/tcp/udp	# Watcom Debug
esimport	3564/tcp/udp	# Electromed SIM port
m2pa		3565/tcp/sctp	# M2PA
quest-data-hub	3566/tcp	# Quest Data Hub
dof-eps		3567/tcp/udp	# DOF Protocol Stack
dof-tunnel-sec	3568/tcp/udp	# DOF Secure Tunnel
mbg-ctrl	3569/tcp/udp	# Meinberg Control Service
mccwebsvr-port	3570/tcp/udp	# MCC Web Server Port
megardsvr-port	3571/tcp/udp	# MegaRAID Server Port
megaregsvrport	3572/tcp/udp	# Registration Server Port
tag-ups-1	3573/tcp/udp	# Advantage Group UPS Suite
dmaf-server	3574/tcp	# DMAF Server
dmaf-caster	3574/udp	# DMAF Caster
ccm-port	3575/tcp/udp	# Coalsere CCM Port
cmc-port	3576/tcp/udp	# Coalsere CMC Port
config-port	3577/tcp/udp	# Configuration Port
data-port	3578/tcp/udp	# Data Port
ttat3lb		3579/tcp/udp	# Tarantella Load Balancing
nati-svrloc	3580/tcp/udp	# NATI-ServiceLocator
kfxaclicensing	3581/tcp/udp	# Ascent Capture Licensing
press		3582/tcp/udp	# PEG PRESS Server
canex-watch	3583/tcp/udp	# CANEX Watch System
u-dbap		3584/tcp/udp	# U-DBase Access Protocol
emprise-lls	3585/tcp/udp	# Emprise License Server
emprise-lsc	3586/tcp/udp	# License Server Console
p2pgroup	3587/tcp/udp	# Peer to Peer Grouping
sentinel	3588/tcp/udp	# Sentinel Server
isomair		3589/tcp/udp
wv-csp-sms	3590/tcp/udp	# WV CSP SMS Binding
gtrack-server	3591/tcp/udp	# LOCANIS G-TRACK Server
gtrack-ne	3592/tcp/udp	# LOCANIS G-TRACK NE Port
bpmd		3593/tcp/udp	# BP Model Debugger
mediaspace	3594/tcp/udp	# MediaSpace
shareapp	3595/tcp/udp	# ShareApp
iw-mmogame	3596/tcp/udp	# Illusion Wireless MMOG
a14		3597/tcp/udp	# A14 (AN-to-SC/MM)
a15		3598/tcp/udp	# A15 (AN-to-AN)
quasar-server	3599/tcp/udp	# Quasar Accounting Server
trap-daemon	3600/tcp/udp	# text relay-answer
visinet-gui	3601/tcp/udp	# Visinet Gui
infiniswitchcl	3602/tcp/udp	# InfiniSwitch Mgr Client
int-rcv-cntrl	3603/tcp/udp	# Integrated Rcvr Control
bmc-jmx-port	3604/tcp/udp	# BMC JMX Port
comcam-io	3605/tcp/udp	# ComCam IO Port
splitlock	3606/tcp/udp	# Splitlock Server
precise-i3	3607/tcp/udp	# Precise I3
trendchip-dcp	3608/tcp/udp	# Trendchip control protocol
cpdi-pidas-cm	3609/tcp/udp	# CPDI PIDAS Connection Mon
echonet		3610/tcp/udp	# ECHONET
six-degrees	3611/tcp/udp	# Six Degrees Port
hp-dataprotect	3612/tcp/udp	# HP Data Protector
alaris-disc	3613/tcp/udp	# Alaris Device Discovery
sigma-port	3614/tcp/udp	# Satchwell Sigma
start-network	3615/tcp/udp	# Start Messaging Network
cd3o-protocol	3616/tcp/udp	# cd3o Control Protocol
sharp-server	3617/tcp/udp	# ATI SHARP Logic Engine
aairnet-1	3618/tcp/udp	# AAIR-Network 1
aairnet-2	3619/tcp/udp	# AAIR-Network 2
ep-pcp		3620/tcp/udp	# EPSON Projector Control Port
ep-nsp		3621/tcp/udp	# EPSON Network Screen Port
ff-lr-port	3622/tcp/udp	# FF LAN Redundancy Port
haipe-discover	3623/tcp/udp	# HAIPIS Dynamic Discovery
dist-upgrade	3624/tcp/udp	# Distributed Upgrade Port
volley		3625/tcp/udp	# Volley
bvcdaemon-port	3626/tcp/udp	# bvControl Daemon
jamserverport	3627/tcp/udp	# Jam Server Port
ept-machine	3628/tcp/udp	# EPT Machine Interface
escvpnet	3629/tcp/udp	# ESC/VP.net
cs-remote-db	3630/tcp/udp	# C&S Remote Database Port
cs-services	3631/tcp/udp	# C&S Web Services Port
distcc		3632/tcp/udp	# distributed compiler
wacp		3633/tcp/udp	# Wyrnix AIS port
hlibmgr		3634/tcp/udp	# hNTSP Library Manager
sdo		3635/tcp/udp	# Simple Distributed Objects
servistaitsm	3636/tcp/udp	# SerVistaITSM
scservp		3637/tcp/udp	# Customer Service Port
ehp-backup	3638/tcp/udp	# EHP Backup Protocol
xap-ha		3639/tcp/udp	# Extensible Automation
netplay-port1	3640/tcp/udp	# Netplay Port 1
netplay-port2	3641/tcp/udp	# Netplay Port 2
juxml-port	3642/tcp/udp	# Juxml Replication port
audiojuggler	3643/tcp/udp	# AudioJuggler
ssowatch	3644/tcp/udp
cyc		3645/tcp/udp	# Cyc
xss-srv-port	3646/tcp/udp	# XSS Server Port
splitlock-gw	3647/tcp/udp	# Splitlock Gateway
fjcp		3648/tcp/udp	# Fujitsu Cooperation Port
nmmp		3649/tcp/udp	# Nishioka Miyuki Msg Protocol
prismiq-plugin	3650/tcp/udp	# PRISMIQ VOD plug-in
xrpc-registry	3651/tcp/udp	# XRPC Registry
vxcrnbuport	3652/tcp/udp	# VxCR NBU Default Port
tsp		3653/tcp/udp	# Tunnel Setup Protocol
vaprtm		3654/tcp/udp	# VAP RealTime Messenger
abatemgr	3655/tcp/udp	# ActiveBatch Exec Agent
abatjss		3656/tcp/udp	# ActiveBatch Job Scheduler
immedianet-bcn	3657/tcp/udp	# ImmediaNet Beacon
ps-ams		3658/tcp/udp	# PlayStation AMS (Secure)
apple-sasl	3659/tcp/udp	# Apple SASL
can-nds-ssl	3660/tcp/udp	# IBM Tivoli Directory Service using SSL
can-ferret-ssl	3661/tcp/udp	# IBM Tivoli Directory Service using SSL
pserver		3662/tcp/udp
dtp		3663/tcp/udp	# DIRECWAY Tunnel Protocol
ups-engine	3664/tcp/udp	# UPS Engine Port
ent-engine	3665/tcp/udp	# Enterprise Engine Port
eserver-pap	3666/tcp/udp	# IBM eServer PAP
infoexch	3667/tcp/udp	# IBM Information Exchange
dell-rm-port	3668/tcp/udp	# Dell Remote Management
casanswmgmt	3669/tcp/udp	# CA SAN Switch Management
smile		3670/tcp/udp	# SMILE TCP/UDP Interface
efcp		3671/tcp/udp	# e Field Control (EIBnet)
lispworks-orb	3672/tcp/udp	# LispWorks ORB
mediavault-gui	3673/tcp/udp	# Openview Media Vault GUI
wininstall-ipc	3674/tcp/udp	# WinINSTALL IPC Port
calltrax	3675/tcp/udp	# CallTrax Data Port
va-pacbase	3676/tcp/udp	# VisualAge Pacbase server
roverlog	3677/tcp/udp	# RoverLog IPC
ipr-dglt	3678/tcp/udp	# DataGuardianLT
Escale-(Newton	3679/tcp/udp	# Newton Dock
npds-tracker	3680/tcp/udp	# NPDS Tracker
bts-x73		3681/tcp/udp	# BTS X73 Port
cas-mapi	3682/tcp/udp	# EMC SmartPackets-MAPI
bmc-ea		3683/tcp/udp	# BMC EDV/EA
faxstfx-port	3684/tcp/udp	# FAXstfX
dsx-agent	3685/tcp/udp	# DS Expert Agent
tnmpv2		3686/tcp/udp	# Trivial Network Management
simple-push	3687/tcp/udp
simple-push-s	3688/tcp/udp	# simple-push Secure
daap		3689/tcp/udp	# Digital Audio Access Protocol (iTunes)
svn		3690/tcp/udp	# Subversion
magaya-network	3691/tcp/udp	# Magaya Network Port
intelsync	3692/tcp/udp	# Brimstone IntelSync
easl		3693/tcp	# Emergency Automatic Structure Lockdown System
bmc-data-coll	3695/tcp/udp	# BMC Data Collection
telnetcpcd	3696/tcp/udp	# Telnet Com Port Control
nw-license	3697/tcp/udp	# NavisWorks License System
sagectlpanel	3698/tcp/udp	# SAGECTLPANEL
kpn-icw		3699/tcp/udp	# Internet Call Waiting
lrs-paging	3700/tcp/udp	# LRS NetPage
netcelera	3701/tcp/udp	# NetCelera
ws-discovery	3702/tcp/udp	# Web Service Discovery
adobeserver-3	3703/tcp/udp	# Adobe Server 3
adobeserver-4	3704/tcp/udp	# Adobe Server 4
adobeserver-5	3705/tcp/udp	# Adobe Server 5
rt-event	3706/tcp/udp	# Real-Time Event Port
rt-event-s	3707/tcp/udp	# Real-Time Event Secure Port
sun-as-iiops	3708/tcp/udp	# Sun App Svr - Naming
ca-idms		3709/tcp/udp	# CA-IDMS Server
portgate-auth	3710/tcp/udp	# PortGate Authentication
edb-server2	3711/tcp/udp	# EBD Server 2
sentinel-ent	3712/tcp/udp	# Sentinel Enterprise
tftps		3713/tcp/udp	# TFTP over TLS
delos-dms	3714/tcp/udp	# DELOS Direct Messaging
anoto-rendezv	3715/tcp/udp	# Anoto Rendezvous Port
wv-csp-sms-cir	3716/tcp/udp	# WV CSP SMS CIR Channel
wv-csp-udp-cir	3717/tcp/udp	# WV CSP UDP/IP CIR Channel
opus-services	3718/tcp/udp	# OPUS Server Port
itelserverport	3719/tcp/udp	# iTel Server Port
ufastro-instr	3720/tcp/udp	# UF Astro. Instr. Services
xsync		3721/tcp/udp	# Xsync
xserveraid	3722/tcp/udp	# Xserve RAID
sychrond	3723/tcp/udp	# Sychron Service Daemon
blizwow		3724/tcp/udp	# World of Warcraft
na-er-tip	3725/tcp/udp	# Netia NA-ER Port
array-manager	3726/tcp/udp	# Xyratex Array Manager
e-mdu		3727/tcp/udp	# Ericsson Mobile Data Unit
e-woa		3728/tcp/udp	# Ericsson Web on Air
fksp-audit	3729/tcp/udp	# Fireking Audit Port
client-ctrl	3730/tcp/udp	# Client Control
smap		3731/tcp/udp	# Service Manager
m-wnn		3732/tcp/udp	# Mobile Wnn
multip-msg	3733/tcp/udp	# Multipuesto Msg Port
synel-data	3734/tcp/udp	# Synel Data Collection Port
pwdis		3735/tcp/udp	# Password Distribution
rs-rmi		3736/tcp/udp	# RealSpace RMI
xpanel		3737/tcp	# XPanel Daemon
versatalk	3738/tcp/udp	# versaTalk Server Port
launchbird-lm	3739/tcp/udp	# Launchbird LicenseManager
heartbeat	3740/tcp/udp	# Heartbeat Protocol
wysdma		3741/tcp/udp	# WysDM Agent
cst-port	3742/tcp/udp	# CST - Configuration & Service Tracker
ipcs-command	3743/tcp/udp	# IP Control Systems Ltd.
sasg		3744/tcp/udp	# SASG
gw-call-port	3745/tcp/udp	# GWRTC Call Port
linktest	3746/tcp/udp	# LXPRO.COM LinkTest
linktest-s	3747/tcp/udp	# LXPRO.COM LinkTest SSL
webdata		3748/tcp/udp	# webData
cimtrak		3749/tcp/udp	# CimTrak
cbos-ip-port	3750/tcp/udp	# CBOS/IP ncapsalation port
gprs-cube	3751/tcp/udp	# CommLinx GPRS Cube
vipremoteagent	3752/tcp/udp	# Vigil-IP RemoteAgent
nattyserver	3753/tcp/udp	# NattyServer Port
timestenbroker	3754/tcp/udp	# TimesTen Broker Port
sas-remote-hlp	3755/tcp/udp	# SAS Remote Help Server
canon-capt	3756/tcp/udp	# Canon CAPT Port
grf-port	3757/tcp/udp	# GRF Server Port
apw-registry	3758/tcp/udp	# apw RMI registry
exapt-lmgr	3759/tcp/udp	# Exapt License Manager
adtempusclient	3760/tcp/udp	# adTempus Client
gsakmp		3761/tcp/udp	# gsakmp port
gbs-smp		3762/tcp/udp	# GBS SnapMail Protocol
xo-wave		3763/tcp/udp	# XO Wave Control Port
mni-prot-rout	3764/tcp/udp	# MNI Protected Routing
rtraceroute	3765/tcp/udp	# Remote Traceroute
sitewatch-s	3766/tcp	# SSL e-watch sitewatch server
listmgr-port	3767/tcp/udp	# ListMGR Port
rblcheckd	3768/tcp/udp	# rblcheckd server daemon
haipe-otnk	3769/tcp/udp	# HAIPE Network Keying
cindycollab	3770/tcp/udp	# Cinderella Collaboration
paging-port	3771/tcp/udp	# RTP Paging Port
ctp		3772/tcp/udp	# Chantry Tunnel Protocol
ctdhercules	3773/tcp/udp
zicom		3774/tcp/udp	# ZICOM
ispmmgr		3775/tcp/udp	# ISPM Manager Port
dvcprov-port	3776/tcp/udp	# Device Provisioning Port
jibe-eb		3777/tcp/udp	# Jibe EdgeBurst
c-h-it-port	3778/tcp/udp	# Cutler-Hammer IT Port
cognima		3779/tcp/udp	# Cognima Replication
nnp		3780/tcp/udp	# Nuzzler Network Protocol
abcvoice-port	3781/tcp/udp	# ABCvoice server port
iso-tp0s	3782/tcp/udp	# Secure ISO TP0 port
bim-pem		3783/tcp/udp	# Impact Mgr./PEM Gateway
bfd-control	3784/tcp/udp	# BFD Control Protocol
bfd-echo	3785/tcp/udp	# BFD Echo Protocol
upstriggervsw	3786/tcp/udp	# VSW Upstrigger port
fintrx		3787/tcp/udp	# Fintrx
isrp-port	3788/tcp/udp	# SPACEWAY Routing port
remotedeploy	3789/tcp/udp	# RemoteDeploy Administration Port [July 2003]
quickbooksrds	3790/tcp/udp	# QuickBooks RDS
tvnetworkvideo	3791/tcp/udp	# TV NetworkVideo Data port
sitewatch	3792/tcp/udp	# e-Watch Corporation SiteWatch
dcsoftware	3793/tcp/udp	# DataCore Software
jaus		3794/tcp/udp	# JAUS Robots
myblast		3795/tcp/udp	# myBLAST Mekentosj port
spw-dialer	3796/tcp/udp	# Spaceway Dialer
idps		3797/tcp/udp
minilock	3798/tcp/udp	# Minilock
radius-dynauth	3799/tcp/udp	# RADIUS Dynamic Authorization
pwgpsi		3800/tcp/udp	# Print Services Interface
ibm-mgr		3801/tcp/udp	# ibm manager service
vhd		3802/tcp/udp	# VHD
soniqsync	3803/tcp/udp	# SoniqSync
iqnet-port	3804/tcp/udp	# Harman IQNet Port
tcpdataserver	3805/tcp/udp	# ThorGuard Server Port
wsmlb		3806/tcp/udp	# Remote System Manager
spugna		3807/tcp/udp	# SpuGNA Communication Port
sun-as-iiops-ca	3808/tcp/udp	# Sun App Svr-IIOPClntAuth
apocd		3809/tcp/udp	# Java Desktop System Configuration Agent
wlanauth	3810/tcp/udp	# WLAN AS server
amp		3811/tcp/udp	# AMP
neto-wol-server	3812/tcp/udp	# netO WOL Server
rap-ip		3813/tcp/udp	# Rhapsody Interface Protocol
neto-dcs	3814/tcp/udp	# netO DCS
lansurveyorxml	3815/tcp/udp	# LANsurveyor XML
sunlps-http	3816/tcp/udp	# Sun Local Patch Server
tapeware	3817/tcp/udp	# Yosemite Tech Tapeware
crinis-hb	3818/tcp/udp	# Crinis Heartbeat
epl-slp		3819/tcp/udp	# EPL Sequ Layer Protocol
scp		3820/tcp/udp	# Siemens AuD SCP
pmcp		3821/tcp/udp	# ATSC PMCP Standard
acp-discovery	3822/tcp/udp	# Compute Pool Discovery
acp-conduit	3823/tcp/udp	# Compute Pool Conduit
acp-policy	3824/tcp/udp	# Compute Pool Policy
ffserver	3825/tcp/udp	# Antera FlowFusion Process Simulation
warmux		3826/tcp/udp	# WarMUX game server
netmpi		3827/tcp/udp	# Netadmin Systems MPI service
neteh		3828/tcp/udp	# Netadmin Systems Event Handler
neteh-ext	3829/tcp/udp	# Netadmin Systems Event Handler External
cernsysmgmtagt	3830/tcp/udp	# Cerner System Management Agent
dvapps		3831/tcp/udp	# Docsvault Application Service
xxnetserver	3832/tcp/udp	# xxNETserver
aipn-auth	3833/tcp/udp	# AIPN LS Authentication
spectardata	3834/tcp/udp	# Spectar Data Stream Service
spectardb	3835/tcp/udp	# Spectar Database Rights Service
markem-dcp	3836/tcp/udp	# MARKEM NEXTGEN DCP
mkm-discovery	3837/tcp/udp	# MARKEM Auto-Discovery
sos		3838/tcp/udp	# Scito Object Server
amx-rms		3839/tcp/udp	# AMX Resource Management Suite
flirtmitmir	3840/tcp/udp	# www.FlirtMitMir.de
shiprush-db-svr	3841/tcp	# ShipRush Database Server
nhci		3842/tcp/udp	# NHCI status port
quest-agent	3843/tcp/udp	# Quest Common Agent
rnm		3844/tcp/udp	# RNM
v-one-spp	3845/tcp/udp	# V-ONE Single Port Proxy
an-pcp		3846/tcp/udp	# Astare Network PCP
msfw-control	3847/tcp/udp	# MS Firewall Control
item		3848/tcp/udp	# IT Environmental Monitor
spw-dnspreload	3849/tcp/udp	# SPACEWAY DNS Preload
qtms-bootstrap	3850/tcp/udp	# QTMS Bootstrap Protocol
spectraport	3851/tcp/udp	# SpectraTalk Port
sse-app-config	3852/tcp/udp	# SSE App Configuration
sscan		3853/tcp/udp	# SONY scanning protocol
stryker-com	3854/tcp/udp	# Stryker Comm Port
opentrac	3855/tcp/udp	# OpenTRAC
informer	3856/tcp/udp	# INFORMER
trap-port	3857/tcp/udp	# Trap Port
trap-port-mom	3858/tcp/udp	# Trap Port MOM
nav-port	3859/tcp/udp	# Navini Port
sasp		3860/tcp/udp	# Server/Application State Protocol (SASP)
winshadow-hd	3861/tcp/udp	# winShadow Host Discovery
giga-pocket	3862/tcp/udp	# GIGA-POCKET
asap-tcp	3863/tcp	# asap tcp port
asap-udp	3863/udp	# asap udp port
asap-sctp	3863/sctp
asap-tcp-tls	3864/tcp	# asap/tls tcp port
asap-sctp-tls	3864/sctp	# asap-sctp/tls
xpl		3865/tcp/udp	# xpl automation protocol
dzdaemon	3866/tcp/udp	# Sun SDViz DZDAEMON Port
dzoglserver	3867/tcp/udp	# Sun SDViz DZOGLSERVER Port
diameter	3868/tcp/sctp	# DIAMETER
ovsam-mgmt	3869/tcp/udp	# hp OVSAM MgmtServer Disco
ovsam-d-agent	3870/tcp/udp	# hp OVSAM HostAgent Disco
avocent-adsap	3871/tcp/udp	# Avocent DS Authorization
oem-agent	3872/tcp/udp	# OEM Agent
fagordnc	3873/tcp/udp
sixxsconfig	3874/tcp/udp	# SixXS Configuration
pnbscada	3875/tcp/udp	# PNBSCADA
dl-agent	3876/tcp/udp	# DirectoryLockdown Agent
xmpcr-interface	3877/tcp/udp	# XMPCR Interface Port
fotogcad	3878/tcp/udp	# FotoG CAD interface
appss-lm	3879/tcp/udp	# appss license manager
igrs		3880/tcp/udp	# IGRS
idac		3881/tcp/udp	# Data Acquisition and Control
msdts1		3882/tcp/udp	# DTS Service Port
vrpn		3883/tcp/udp	# VR Peripheral Network
softrack-meter	3884/tcp/udp	# SofTrack Metering
topflow-ssl	3885/tcp/udp	# TopFlow SSL
nei-management	3886/tcp/udp	# NEI management port
ciphire-data	3887/tcp/udp	# Ciphire Data Transport
ciphire-serv	3888/tcp/udp	# Ciphire Services
dandv-tester	3889/tcp/udp	# D and V Tester Control Port
ndsconnect	3890/tcp/udp	# Niche Data Server Connect
rtc-pm-port	3891/tcp/udp	# Oracle RTC-PM port
pcc-image-port	3892/tcp/udp	# PCC-image-port
cgi-starapi	3893/tcp/udp	# CGI StarAPI Server
syam-agent	3894/tcp/udp	# SyAM Agent Port
syam-smc	3895/tcp/udp	# SyAm SMC Service Port
sdo-tls		3896/tcp/udp	# Simple Distributed Objects over TLS
sdo-ssh		3897/tcp/udp	# Simple Distributed Objects over SSH
senip		3898/tcp/udp	# IAS, Inc. SmartEye NET Internet Protocol
itv-control	3899/tcp/udp	# ITV Port
udt-os		3900/tcp/udp	# Unidata UDT OS
nimsh		3901/tcp/udp	# NIM Service Handler
nimaux		3902/tcp/udp	# NIMsh Auxiliary Port
charsetmgr	3903/tcp/udp	# CharsetMGR
omnilink-port	3904/tcp/udp	# Arnet Omnilink Port
mupdate		3905/tcp/udp	# Mailbox Update (MUPDATE) protocol
topovista-data	3906/tcp/udp	# TopoVista elevation data
imoguia-port	3907/tcp/udp	# Imoguia Port
hppronetman	3908/tcp/udp	# HP Procurve NetManagement
surfcontrolcpa	3909/tcp/udp	# SurfControl CPA
prnrequest	3910/tcp/udp	# Printer Request Port
prnstatus	3911/tcp/udp	# Printer Status Port
gbmt-stars	3912/tcp/udp	# Global Maintech Stars
listcrt-port	3913/tcp/udp	# ListCREATOR Port
listcrt-port-2	3914/tcp/udp	# ListCREATOR Port 2
agcat		3915/tcp/udp	# Auto-Graphics Cataloging
wysdmc		3916/tcp/udp	# WysDM Controller
aftmux		3917/tcp/udp	# AFT multiplex port
pktcablemmcops	3918/tcp/udp	# PacketCableMultimediaCOPS
hyperip		3919/tcp/udp	# HyperIP
exasoftport1	3920/tcp/udp	# Exasoft IP Port
herodotus-net	3921/tcp/udp	# Herodotus Net
sor-update	3922/tcp/udp	# Soronti Update Port
symb-sb-port	3923/tcp/udp	# Symbian Service Broker
mpl-gprs-port	3924/tcp/udp	# MPL_GPRS_PORT
zmp		3925/tcp/udp	# Zoran Media Port
winport		3926/tcp/udp	# WINPort
natdataservice	3927/tcp/udp	# ScsTsr
netboot-pxe	3928/tcp/udp	# PXE NetBoot Manager
smauth-port	3929/tcp/udp	# AMS Port
syam-webserver	3930/tcp/udp	# Syam Web Server Port
msr-plugin-port	3931/tcp/udp	# MSR Plugin Port
dyn-site	3932/tcp/udp	# Dynamic Site System
plbserve-port	3933/tcp/udp	# PL/B App Server User Port
sunfm-port	3934/tcp/udp	# PL/B File Manager Port
sdp-portmapper	3935/tcp/udp	# SDP Port Mapper Protocol
mailprox	3936/tcp/udp	# Mailprox
dvbservdsc	3937/tcp/udp	# DVB Service Discovery
dbcontrol-agent	3938/tcp/udp	# Oracle dbControl Agent po
aamp		3939/tcp/udp	# Anti-virus Application Management Port
xecp-node	3940/tcp/udp	# XeCP Node Service
homeportal-web	3941/tcp/udp	# Home Portal Web Server
srdp		3942/tcp/udp	# satellite distribution
tig		3943/tcp/udp	# TetraNode Ip Gateway
sops		3944/tcp/udp	# S-Ops Management
emcads		3945/tcp/udp	# EMCADS Server Port
backupedge	3946/tcp/udp	# BackupEDGE Server
ccp		3947/tcp/udp	# Connect and Control Protocol for Consumer, Commercial, and Industrial Electronic Devices
apdap		3948/tcp/udp	# Anton Paar Device Administration Protocol
drip		3949/tcp/udp	# Dynamic Routing Information Protocol
namemunge	3950/tcp/udp	# Name Munging
pwgippfax	3951/tcp/udp	# PWG IPP Facsimile
i3-sessionmgr	3952/tcp/udp	# I3 Session Manager
xmlink-connect	3953/tcp/udp	# Eydeas XMLink Connect
adrep		3954/tcp/udp	# AD Replication RPC
p2pcommunity	3955/tcp/udp	# p2pCommunity
gvcp		3956/tcp/udp	# GigE Vision Control
mqe-broker	3957/tcp/udp	# MQEnterprise Broker
mqe-agent	3958/tcp/udp	# MQEnterprise Agent
treehopper	3959/tcp/udp	# Tree Hopper Networking
bess		3960/tcp/udp	# Bess Peer Assessment
proaxess	3961/tcp/udp	# ProAxess Server
sbi-agent	3962/tcp/udp	# SBI Agent Protocol
thrp		3963/tcp/udp	# Teran Hybrid Routing Protocol
sasggprs	3964/tcp/udp	# SASG GPRS
ati-ip-to-ncpe	3965/tcp/udp	# Avanti IP to NCPE API
bflckmgr	3966/tcp/udp	# BuildForge Lock Manager
ppsms		3967/tcp/udp	# PPS Message Service
ianywhere-dbns	3968/tcp/udp	# iAnywhere DBNS
landmarks	3969/tcp/udp	# Landmark Messages
lanrevagent	3970/tcp/udp	# LANrev Agent
lanrevserver	3971/tcp/udp	# LANrev Server
iconp		3972/tcp/udp	# ict-control Protocol
progistics	3973/tcp/udp	# ConnectShip Progistics
citysearch	3974/tcp/udp	# Remote Applicant Tracking Service
airshot		3975/tcp/udp	# Air Shot
opswagent	3976/tcp/udp	# Server Automation Agent
opswmanager	3977/tcp/udp	# Opsware Manager
secure-cfg-svr	3978/tcp/udp	# Secured Configuration Server
smwan		3979/tcp/udp	# Smith Micro Wide Area Network Service
acms		3980/tcp/udp	# Aircraft Cabin Management System
starfish	3981/tcp/udp	# Starfish System Admin
eis		3982/tcp/udp	# ESRI Image Server
eisp		3983/tcp/udp	# ESRI Image Service
mapper-nodemgr	3984/tcp/udp	# MAPPER network node manager
mapper-mapethd	3985/tcp/udp	# MAPPER TCP/IP server
mapper-ws-ethd	3986/tcp/udp	# MAPPER workstation server
centerline	3987/tcp/udp	# Centerline
dcs-config	3988/tcp/udp	# DCS Configuration Port
bv-queryengine	3989/tcp/udp	# BindView-Query Engine
bv-is		3990/tcp/udp	# BindView-IS
bv-smcsrv	3991/tcp/udp	# BindView-SMCServer
bv-ds		3992/tcp/udp	# BindView-DirectoryServer
bv-agent	3993/tcp/udp	# BindView-Agent
iss-mgmt-ssl	3995/tcp/udp	# ISS Management Svcs SSL
abcsoftware	3996/tcp/udp	# abcsoftware-01
agentsease-db	3997/tcp/udp	# aes_db
dnx		3998/tcp/udp	# Distributed Nagios Executor Service
nvcnet		3999/tcp/udp	# Norman distributes scanning service
terabase	4000/tcp/udp	# Terabase
newoak		4001/tcp/udp	# NewOak
pxc-spvr-ft	4002/tcp/udp
pxc-splr-ft	4003/tcp/udp
pxc-roid	4004/tcp/udp
pxc-pin		4005/tcp/udp
pxc-spvr	4006/tcp/udp
pxc-splr	4007/tcp/udp
netcheque	4008/tcp/udp	# NetCheque accounting
chimera-hwm	4009/tcp/udp	# Chimera HWM
samsung-unidex	4010/tcp/udp	# Samsung Unidex
altserviceboot	4011/tcp/udp	# Alternate Service Boot
pda-gate	4012/tcp/udp	# PDA Gate
acl-manager	4013/tcp/udp	# ACL Manager
taiclock	4014/tcp/udp	# TAICLOCK
talarian-mcast1	4015/tcp/udp	# Talarian Mcast
talarian-mcast2	4016/tcp/udp	# Talarian Mcast
talarian-mcast3	4017/tcp/udp	# Talarian Mcast
talarian-mcast4	4018/tcp/udp	# Talarian Mcast
talarian-mcast5	4019/tcp/udp	# Talarian Mcast
trap		4020/tcp/udp	# TRAP Port
nexus-portal	4021/tcp/udp	# Nexus Portal
dnox		4022/tcp/udp	# DNOX
esnm-zoning	4023/tcp/udp	# ESNM Zoning Port
tnp1-port	4024/tcp/udp	# TNP1 User Port
partimage	4025/tcp/udp	# Partition Image Port
as-debug	4026/tcp/udp	# Graphical Debug Server
bxp		4027/tcp/udp	# bitxpress
dtserver-port	4028/tcp/udp	# DTServer Port
ip-qsig		4029/tcp/udp	# IP Q signaling protocol
jdmn-port	4030/tcp/udp	# Accell/JSP Daemon Port
suucp		4031/tcp/udp	# UUCP over SSL
vrts-auth-port	4032/tcp/udp	# VERITAS Authorization Service
sanavigator	4033/tcp/udp	# SANavigator Peer Port
ubxd		4034/tcp/udp	# Ubiquinox Daemon
wap-push-http	4035/tcp/udp	# WAP Push OTA-HTTP port
wap-push-https	4036/tcp/udp	# WAP Push OTA-HTTP secure
ravehd		4037/tcp/udp	# RaveHD network control
fazzt-ptp	4038/tcp/udp	# Fazzt Point-To-Point
fazzt-admin	4039/tcp/udp	# Fazzt Administration
yo-main		4040/tcp/udp	# Yo.net main service
houston		4041/tcp/udp	# Rocketeer-Houston
ldxp		4042/tcp/udp	# LDXP
nirp		4043/tcp/udp	# Neighbour Identity Resolution
ltp		4044/tcp/udp	# Location Tracking Protocol
npp		4045/tcp/udp	# Network Paging Protocol
acp-proto	4046/tcp/udp	# Accounting Protocol
ctp-state	4047/tcp/udp	# Context Transfer Protocol
wafs		4049/tcp/udp	# Wide Area File Services
cisco-wafs	4050/tcp/udp	# Wide Area File Services
cppdp		4051/tcp/udp	# Cisco Peer to Peer Distribution Protocol
interact	4052/tcp/udp	# VoiceConnect Interact
ccu-comm-1	4053/tcp/udp	# CosmoCall Universe Communications Port 1
ccu-comm-2	4054/tcp/udp	# CosmoCall Universe Communications Port 2
ccu-comm-3	4055/tcp/udp	# CosmoCall Universe Communications Port 3
lms		4056/tcp/udp	# Location Message Service
wfm		4057/tcp/udp	# Servigistics WFM server
kingfisher	4058/tcp/udp	# Kingfisher protocol
dlms-cosem	4059/tcp/udp	# DLMS/COSEM
dsmeter-iatc	4060/tcp/udp	# DSMETER Inter-Agent Transfer Channel
ice-location	4061/tcp/udp	# Ice Location Service (TCP)
ice-slocation	4062/tcp/udp	# Ice Location Service (SSL)
ice-router	4063/tcp/udp	# Ice Firewall Traversal Service (TCP)
ice-srouter	4064/tcp/udp	# Ice Firewall Traversal Service (SSL)
avanti-cdp	4065/tcp/udp	# Avanti Common Data
pmas		4066/tcp/udp	# Performance Measurement and Analysis
idp		4067/tcp/udp	# Information Distribution Protocol
ipfltbcst	4068/tcp/udp	# IP Fleet Broadcast
minger		4069/tcp/udp	# Minger Email Address Validation Service
tripe		4070/tcp/udp	# Trivial IP Encryption (TrIPE)
aibkup		4071/tcp/udp	# Automatically Incremental Backup
zieto-sock	4072/tcp/udp	# Zieto Socket Communications
iRAPP		4073/tcp/udp	# Interactive Remote Application Pairing Protocol
cequint-cityid	4074/tcp/udp	# Cequint City ID UI trigger
perimlan	4075/tcp/udp	# ISC Alarm Message Service
seraph		4076/tcp/udp	# Seraph DCS
ascomalarm	4077/udp	# Ascom IP Alarming
cssp		4078/tcp	# Coordinated Security Service Protocol
santools	4079/tcp/udp	# SANtools Diagnostic Server
lorica-in	4080/tcp/udp	# Lorica inside facing
lorica-in-sec	4081/tcp/udp	# Lorica inside facing (SSL)
lorica-out	4082/tcp/udp	# Lorica outside facing
lorica-out-sec	4083/tcp/udp	# Lorica outside facing (SSL)
fortisphere-vm	4084/udp	# Fortisphere VM Service
ezmessagesrv	4085/tcp	# EZNews Newsroom Message Service
ftsync		4086/udp	# Firewall/NAT state table synchronization
applusservice	4087/tcp	# APplus Service
npsp		4088/tcp	# Noah Printing Service Protocol
opencore	4089/tcp/udp	# OpenCORE Remote Control Service
omasgport	4090/tcp/udp	# OMA BCAST Service Guide
ewinstaller	4091/tcp/udp	# EminentWare Installer
ewdgs		4092/tcp/udp	# EminentWare DGS
pvxpluscs	4093/tcp/udp	# Pvx Plus CS Host
sysrqd		4094/tcp/udp	# sysrq daemon
xtgui		4095/tcp/udp	# xtgui information service
bre		4096/tcp/udp	# BRE (Bridge Relay Element)
patrolview	4097/tcp/udp	# Patrol View
drmsfsd		4098/tcp/udp
dpcp		4099/tcp/udp	# DPCP
igo-incognito	4100/tcp/udp	# IGo Incognito Data Port
brlp-0		4101/tcp/udp	# Braille protocol
brlp-1		4102/tcp/udp	# Braille protocol
brlp-2		4103/tcp/udp	# Braille protocol
brlp-3		4104/tcp/udp	# Braille protocol
shofar		4105/tcp/udp	# Shofar
synchronite	4106/tcp/udp	# Synchronite
j-ac		4107/tcp/udp	# JDL Accounting LAN Service
accel		4108/tcp/udp	# ACCEL
izm		4109/tcp/udp	# Instantiated Zero-control Messaging
g2tag		4110/tcp/udp	# G2 RFID Tag Telemetry Data
xgrid		4111/tcp/udp	# Xgrid
apple-vpns-rp	4112/tcp/udp	# Apple VPN Server Reporting Protocol
aipn-reg	4113/tcp/udp	# AIPN LS Registration
jomamqmonitor	4114/tcp/udp	# JomaMQMonitor
cds		4115/tcp/udp	# CDS Transfer Agent
smartcard-tls	4116/tcp/udp	# smartcard-TLS
hillrserv	4117/tcp/udp	# Hillr Connection Manager
netscript	4118/tcp/udp	# Netadmin Systems NETscript service
assuria-slm	4119/tcp/udp	# Assuria Log Manager
minirem		4120/tcp	# MiniRem Remote Telemetry and Control
e-builder	4121/tcp/udp	# e-Builder Application Communication
fprams		4122/tcp/udp	# Fiber Patrol Alarm Service
z-wave		4123/tcp/udp	# Z-Wave Protocol
tigv2		4124/tcp/udp	# Rohill TetraNode Ip Gateway v2
opsview-envoy	4125/tcp/udp	# Opsview Envoy
ddrepl		4126/tcp/udp	# Data Domain Replication Service
unikeypro	4127/tcp/udp	# NetUniKeyServer
nufw		4128/tcp/udp	# NuFW decision delegation protocol
nuauth		4129/tcp/udp	# NuFW authentication protocol
fronet		4130/tcp/udp	# FRONET message protocol
stars		4131/tcp/udp	# Global Maintech Stars
nuts-dem	4132/tcp/udp	# NUTS Daemon
nuts-bootp	4133/tcp/udp	# NUTS Bootp Server
nifty-hmi	4134/tcp/udp	# NIFTY-Serve HMI protocol
cl-db-attach	4135/tcp/udp	# Classic Line Database Server Attach
cl-db-request	4136/tcp/udp	# Classic Line Database Server Request
cl-db-remote	4137/tcp/udp	# Classic Line Database Server Remote
nettest		4138/tcp/udp
thrtx		4139/tcp/udp	# Imperfect Networks Server
cedros-fds	4140/tcp/udp	# Cedros Fraud Detection System
oirtgsvc	4141/tcp/udp	# Workflow Server
oidocsvc	4142/tcp/udp	# Document Server
oidsr		4143/tcp/udp	# Document Replication
vvr-control	4145/tcp/udp	# VVR Control
tgcconnect	4146/tcp/udp	# TGCConnect Beacon
vrxpservman	4147/tcp/udp	# Multum Service Manager
hhb-handheld	4148/tcp/udp	# HHB Handheld Client
agslb		4149/tcp/udp	# A10 GSLB Service
PowerAlert-nsa	4150/tcp/udp	# PowerAlert Network Shutdown Agent
menandmice-noh	4151/tcp/udp	# Men & Mice Remote Control
idig-mux	4152/tcp/udp	# iDigTech Multiplex
mbl-battd	4153/tcp/udp	# MBL Remote Battery Monitoring
atlinks		4154/tcp/udp	# atlinks device discovery
bzr		4155/tcp/udp	# Bazaar version control system
stat-results	4156/tcp/udp	# STAT Results
stat-scanner	4157/tcp/udp	# STAT Scanner Control
stat-cc		4158/tcp/udp	# STAT Command Center
nss		4159/tcp/udp	# Network Security Service
jini-discovery	4160/tcp/udp	# Jini Discovery
omscontact	4161/tcp/udp	# OMS Contact
omstopology	4162/tcp/udp	# OMS Topology
silverpeakpeer	4163/tcp/udp	# Silver Peak Peer Protocol
silverpeakcomm	4164/tcp/udp	# Silver Peak Communication Protocol
altcp		4165/tcp/udp	# ArcLink over Ethernet
joost		4166/tcp/udp	# Joost Peer to Peer Protocol
ddgn		4167/tcp/udp	# DeskDirect Global Network
pslicser	4168/tcp/udp	# PrintSoft License Server
iadt		4169/tcp	# Automation Drive Interface Transport
iadt-disc	4169/udp	# Internet ADT Discovery Protocol
d-cinema-csp	4170/tcp	# SMPTE Content Synchonization Protocol
ml-svnet	4171/tcp	# Maxlogic Supervisor Communication
pcoip		4172/tcp/udp	# PC over IP
mma-discovery	4173/udp	# MMA Device Discovery
smcluster	4174/tcp	# StorMagic Cluster Services
sm-disc		4174/udp	# StorMagic Discovery
bccp		4175/tcp	# Brocade Cluster Communication Protocol
tl-ipcproxy	4176/tcp	# Translattice Cluster IPC Proxy
wello		4177/tcp/udp	# Wello P2P pubsub service
storman		4178/tcp/udp	# StorMan
MaxumSP		4179/tcp/udp	# Maxum Services
httpx		4180/tcp/udp	# HTTPX
macbak		4181/tcp/udp	# MacBak
pcptcpservice	4182/tcp/udp	# Production Company Pro TCP Service
cyborgnet	4183/tcp/udp	# CyborgNet communications protocol
universe-suite	4184/tcp/udp	# UNIVERSE SUITE MESSAGE SERVICE
wcpp		4185/tcp/udp	# Woven Control Plane Protocol
boxbackupstore	4186/tcp	# Box Backup Store Service
csc-proxy	4187/tcp	# Cascade Proxy
vatata		4188/tcp/udp	# Vatata Peer to Peer Protocol
pcep		4189/tcp	# Path Computation Element Communication Protocol
sieve		4190/tcp	# ManageSieve Protocol
dsmipv6		4191/udp	# Dual Stack MIPv6 NAT Traversal
azeti		4192/tcp	# Azeti Agent Service
azeti-bd	4192/udp	# azeti blinddate
pvxplusio	4193/tcp	# PxPlus remote file srvr
hctl		4197/tcp/udp	# Harman HControl Protocol
eims-admin	4199/tcp/udp	# EIMS ADMIN
corelccam	4300/tcp/udp	# Corel CCam
d-data		4301/tcp/udp	# Diagnostic Data
d-data-control	4302/tcp/udp	# Diagnostic Data Control
srcp		4303/tcp/udp	# Simple Railroad Command Protocol
owserver	4304/tcp/udp	# One-Wire Filesystem Server
batman		4305/tcp/udp	# better approach to mobile ad-hoc networking
pinghgl		4306/tcp/udp	# Hellgate London
trueconf	4307/tcp/udp	# TrueConf Videoconference Service
compx-lockview	4308/tcp/udp	# CompX-LockView
dserver		4309/tcp/udp	# Exsequi Appliance Discovery
mirrtex		4310/tcp/udp	# Mir-RT exchange service
p6ssmc		4311/tcp	# P6R Secure Server Management Console
pscl-mgt	4312/tcp	# Parascale Membership Manager
perrla		4313/tcp	# PERRLA User Services
choiceview-agt	4314/tcp	# ChoiceView Agent
choiceview-clt	4316/tcp	# ChoiceView Client
fdt-rcatp	4320/tcp/udp	# FDT Remote Categorization Protocol
rwhois		4321/tcp/udp	# Remote Who Is
trim-event	4322/tcp/udp	# TRIM Event Service
trim-ice	4323/tcp/udp	# TRIM ICE Service
geognosisman	4325/tcp/udp	# Cadcorp GeognoSIS Manager Service
geognosis	4326/tcp/udp	# Cadcorp GeognoSIS Service
jaxer-web	4327/tcp/udp	# Jaxer Web Protocol
jaxer-manager	4328/tcp/udp	# Jaxer Manager Command Protocol
publiqare-sync	4329/tcp	# PubliQare Distributed Environment Synchronisation Engine
dey-sapi	4330/tcp	# DEY Storage Administration REST API
ktickets-rest	4331/tcp	# ktickets REST API for event management and ticketing systems (embedded POS devices)
ahsp		4333/tcp/udp/sctp	# ArrowHead Service Protocol (AHSP)
netconf-ch-ssh	4334/tcp	# NETCONF Call Home (SSH)
netconf-ch-tls	4335/tcp	# NETCONF Call Home (TLS)
restconf-ch-tls	4336/tcp	# RESTCONF Call Home (TLS)
gaia		4340/tcp/udp	# Gaia Connector Protocol
lisp-data	4341/tcp/udp	# LISP Data Packets
lisp-cons	4342/tcp	# LISP-CONS Control
lisp-control	4342/udp	# LISP Control Packets
unicall		4343/tcp/udp	# UNICALL
vinainstall	4344/tcp/udp	# VinaInstall
m4-network-as	4345/tcp/udp	# Macro 4 Network AS
elanlm		4346/tcp/udp	# ELAN LM
lansurveyor	4347/tcp/udp	# LAN Surveyor
itose		4348/tcp/udp	# ITOSE
fsportmap	4349/tcp/udp	# File System Port Map
net-device	4350/tcp/udp	# Net Device
plcy-net-svcs	4351/tcp/udp	# PLCY Net Services
pjlink		4352/tcp/udp	# Projector Link
f5-iquery	4353/tcp/udp	# F5 iQuery
qsnet-trans	4354/tcp/udp	# QSNet Transmitter
qsnet-workst	4355/tcp/udp	# QSNet Workstation
qsnet-assist	4356/tcp/udp	# QSNet Assistant
qsnet-cond	4357/tcp/udp	# QSNet Conductor
qsnet-nucl	4358/tcp/udp	# QSNet Nucleus
omabcastltkm	4359/tcp/udp	# OMA BCAST Long-Term Key Messages
matrix-vnet	4360/tcp	# Matrix VNet Communication Protocol
nacnl		4361/udp	# NavCom Discovery and Control Port
afore-vdp-disc	4362/udp	# AFORE vNode Discovery protocol
shadowstream	4366/udp	# ShadowStream System
wxbrief		4368/tcp/udp	# WeatherBrief Direct
epmd		4369/tcp/udp	# Erlang Port Mapper Daemon
elpro-tunnel	4370/tcp/udp	# ELPRO V2 Protocol Tunnel
l2c-control	4371/tcp	# LAN2CAN Control
l2c-disc	4371/udp	# LAN2CAN Discovery
l2c-data	4372/tcp/udp	# LAN2CAN Data
remctl		4373/tcp/udp	# Remote Authenticated Command Service
psi-ptt		4374/tcp	# PSI Push-to-Talk Protocol
tolteces	4375/tcp/udp	# Toltec EasyShare
bip		4376/tcp/udp	# BioAPI Interworking
cp-spxsvr	4377/tcp/udp	# Cambridge Pixel SPx Server
cp-spxdpy	4378/tcp/udp	# Cambridge Pixel SPx Display
ctdb		4379/tcp/udp	# CTDB
xandros-cms	4389/tcp/udp	# Xandros Community Management Service
wiegand		4390/tcp/udp	# Physical Access Control
apwi-imserver	4391/tcp	# American Printware IMServer Protocol
apwi-rxserver	4392/tcp	# American Printware RXServer Protocol
apwi-rxspooler	4393/tcp	# American Printware RXSpooler Protocol
apwi-disc	4394/udp	# American Printware Discovery
omnivisionesx	4395/tcp/udp	# OmniVision communication for Virtual environments
fly		4396/tcp	# Fly Object Space
ds-srv		4400/tcp/udp	# ASIGRA Services
ds-srvr		4401/tcp/udp	# ASIGRA Televaulting DS-System Service
ds-clnt		4402/tcp/udp	# ASIGRA Televaulting DS-Client Service
ds-user		4403/tcp/udp	# ASIGRA Televaulting DS-Client Monitoring/Management
ds-admin	4404/tcp/udp	# ASIGRA Televaulting DS-System Monitoring/Management
ds-mail		4405/tcp/udp	# ASIGRA Televaulting Message Level Restore service
ds-slp		4406/tcp/udp	# ASIGRA Televaulting DS-Sleeper Service
nacagent	4407/tcp	# Network Access Control Agent
slscc		4408/tcp	# SLS Technology Control Centre
netcabinet-com	4409/tcp	# Net-Cabinet comunication
itwo-server	4410/tcp	# RIB iTWO Application Server
found		4411/tcp	# Found Messaging Protocol
smallchat	4412/udp	# SmallChat
avi-nms		4413/tcp	# AVI Systems NMS
avi-nms-disc	4413/udp	# AVI Systems NMS
updog		4414/tcp	# Updog Monitoring and Status Framework
brcd-vr-req	4415/tcp	# Brocade Virtual Router Request
pjj-player	4416/tcp	# PJJ Media Player
pjj-player-disc	4416/udp	# PJJ Media Player discovery
workflowdir	4417/tcp	# Workflow Director Communication
axysbridge	4418/udp	# AXYS communication protocol
cbp		4419/tcp	# Colnod Binary Protocol
nvme		4420/tcp/udp	# NVM Express over Fabrics storage access
scaleft		4421/tcp	# Multi-Platform Remote Management for Cloud Infrastructure
tsepisp		4422/tcp	# TSEP Installation Service Protocol
thingkit	4423/tcp	# thingkit secure mesh
netrockey6	4425/tcp/udp	# NetROCKEY6 SMART Plus Service
beacon-port-2	4426/tcp/udp	# SMARTS Beacon Port
drizzle		4427/tcp	# Drizzle database server
omviserver	4428/tcp	# OMV-Investigation Server-Client
omviagent	4429/tcp	# OMV Investigation Agent-Server
rsqlserver	4430/tcp/udp	# REAL SQL Server
wspipe		4431/tcp	# adWISE Pipe
l-acoustics	4432/tcp/udp	# L-ACOUSTICS management
vop		4433/tcp	# Versile Object Protocol
netblox		4441/udp	# Netblox Protocol
saris		4442/tcp/udp	# Saris
pharos		4443/tcp/udp	# Pharos
krb524		4444/tcp/udp	# KRB524
upnotifyp	4445/tcp/udp	# UPNOTIFYP
n1-fwp		4446/tcp/udp	# N1-FWP
n1-rmgmt	4447/tcp/udp	# N1-RMGMT
asc-slmd	4448/tcp/udp	# ASC Licence Manager
privatewire	4449/tcp/udp	# PrivateWire
camp		4450/tcp/udp	# Common ASCII Messaging Protocol
ctisystemmsg	4451/tcp/udp	# CTI System Msg
ctiprogramload	4452/tcp/udp	# CTI Program Load
nssalertmgr	4453/tcp/udp	# NSS Alert Manager
nssagentmgr	4454/tcp/udp	# NSS Agent Manager
prchat-user	4455/tcp/udp	# PR Chat User
prchat-server	4456/tcp/udp	# PR Chat Server
prRegister	4457/tcp/udp	# PR Register
mcp		4458/tcp/udp	# Matrix Configuration Protocol
hpssmgmt	4484/tcp/udp	# hpssmgmt service
assyst-dr	4485/tcp	# Assyst Data Repository Service
icms		4486/tcp/udp	# Integrated Client Message Service
prex-tcp	4487/tcp	# Protocol for Remote Execution over TCP
awacs-ice	4488/tcp/udp	# Apple Wide Area Connectivity Service ICE Bootstrap
ipsec-nat-t	4500/tcp/udp	# IPsec NAT-Traversal
a25-fap-fgw	4502/sctp	# A25 (FAP-FGW)
armagetronad	4534/udp	# Armagetron Advanced Game Server
ehs		4535/tcp/udp	# Event Heap Server
ehs-ssl		4536/tcp/udp	# Event Heap Server SSL
wssauthsvc	4537/tcp/udp	# WSS Security Service
swx-gate	4538/tcp/udp	# Software Data Exchange Gateway
worldscores	4545/tcp/udp	# WorldScores
sf-lm		4546/tcp/udp	# SF License Manager (Sentinel)
lanner-lm	4547/tcp/udp	# Lanner License Manager
synchromesh	4548/tcp/udp	# Synchromesh
aegate		4549/tcp/udp	# Aegate PMR Service
gds-adppiw-db	4550/tcp/udp	# Perman I Interbase Server
ieee-mih	4551/tcp/udp	# MIH Services
menandmice-mon	4552/tcp/udp	# Men and Mice Monitoring
icshostsvc	4553/tcp	# ICS host services
msfrs		4554/tcp/udp	# MS FRS Replication
rsip		4555/tcp/udp	# RSIP Port
dtn-bundle	4556/tcp/udp/dccp	# DTN Bundle TCP CL Protocol
mtcevrunqss	4557/udp	# Marathon everRun Quorum Service Server
mtcevrunqman	4558/udp	# Marathon everRun Quorum Service Manager
hylafax		4559/tcp/udp	# HylaFAX
amahi-anywhere	4563/tcp	# Amahi Anywhere
kwtc		4566/tcp/udp	# Kids Watch Time Control Service
tram		4567/tcp/udp	# TRAM
bmc-reporting	4568/tcp/udp	# BMC Reporting
iax		4569/tcp/udp	# Inter-Asterisk eXchange
deploymentmap	4570/tcp	# Service to distribute and update within a site deployment information for Oracle Communications Suite
cardifftec-back	4573/tcp	# A port for communication between a server and client for a custom backup system
rid		4590/tcp	# RID over HTTP/TLS
l3t-at-an	4591/tcp/udp	# HRPD L3T (AT-AN)
hrpd-ith-at-an	4592/udp	# HRPD-ITH (AT-AN)
ipt-anri-anri	4593/tcp/udp	# IPT (ANRI-ANRI)
ias-session	4594/tcp/udp	# IAS-Session (ANRI-ANRI)
ias-paging	4595/tcp/udp	# IAS-Paging (ANRI-ANRI)
ias-neighbor	4596/tcp/udp	# IAS-Neighbor (ANRI-ANRI)
a21-an-1xbs	4597/tcp/udp	# A21 (AN-1xBS)
a16-an-an	4598/tcp/udp	# A16 (AN-AN)
a17-an-an	4599/tcp/udp	# A17 (AN-AN)
piranha1	4600/tcp/udp	# Piranha1
piranha2	4601/tcp/udp	# Piranha2
mtsserver	4602/tcp	# EAX MTS Server
menandmice-upg	4603/tcp	# Men & Mice Upgrade Agent
irp		4604/tcp	# Identity Registration Protocol
sixchat		4605/tcp	# Direct End to End Secure Chat Protocol
ventoso		4621/udp	# Bidirectional single port remote radio VOIP and Control stream
playsta2-app	4658/tcp/udp	# PlayStation2 App Port
playsta2-lob	4659/tcp/udp	# PlayStation2 Lobby Port
smaclmgr	4660/tcp/udp
kar2ouche	4661/tcp/udp	# Kar2ouche Peer location service
oms		4662/tcp/udp	# OrbitNet Message Service
noteit		4663/tcp/udp	# Note It! Message Service
ems		4664/tcp/udp	# Rimage Messaging Server
contclientms	4665/tcp/udp	# Container Client Message Service
eportcomm	4666/tcp/udp	# E-Port Message Service
mmacomm		4667/tcp/udp	# MMA Comm Services
mmaeds		4668/tcp/udp	# MMA EDS Service
eportcommdata	4669/tcp/udp	# E-Port Data Service
light		4670/tcp/udp	# Light packets transfer protocol
acter		4671/tcp/udp	# Bull RSF action server
rfa		4672/tcp/udp	# remote file access server
cxws		4673/tcp/udp	# CXWS Operations
appiq-mgmt	4674/tcp/udp	# AppIQ Agent Management
dhct-status	4675/tcp/udp	# BIAP Device Status
dhct-alerts	4676/tcp/udp	# BIAP Generic Alert
bcs		4677/tcp/udp	# Business Continuity Servi
traversal	4678/tcp/udp	# boundary traversal
mgesupervision	4679/tcp/udp	# MGE UPS Supervision
mgemanagement	4680/tcp/udp	# MGE UPS Management
parliant	4681/tcp/udp	# Parliant Telephony System
finisar		4682/tcp/udp
spike		4683/tcp/udp	# Spike Clipboard Service
rfid-rp1	4684/tcp/udp	# RFID Reader Protocol 1.0
autopac		4685/tcp/udp	# Autopac Protocol
msp-os		4686/tcp/udp	# Manina Service Protocol
nst		4687/tcp/udp	# Network Scanner Tool FTP
mobile-p2p	4688/tcp/udp	# Mobile P2P Service
altovacentral	4689/tcp/udp	# Altova DatabaseCentral
prelude		4690/tcp/udp	# Prelude IDS message proto
mtn		4691/tcp/udp	# monotone Netsync Protocol
conspiracy	4692/tcp/udp	# Conspiracy messaging
netxms-agent	4700/tcp/udp	# NetXMS Agent
netxms-mgmt	4701/tcp/udp	# NetXMS Management
netxms-sync	4702/tcp/udp	# NetXMS Server Synchronization
npqes-test	4703/tcp	# Network Performance Quality Evaluation System Test Service
assuria-ins	4704/tcp	# Assuria Insider
trinity-dist	4711/tcp/udp/sctp	# Trinity Trust Network Node Communication
truckstar	4725/tcp/udp	# TruckStar Service
a26-fap-fgw	4726/udp	# A26 (FAP-FGW)
fcis		4727/tcp	# F-Link Client Information Service
fcis-disc	4727/udp	# F-Link Client Information Service Discovery
capmux		4728/tcp/udp	# CA Port Multiplexer
gsmtap		4729/udp	# GSM Interface Tap
gearman		4730/tcp/udp	# Gearman Job Queue System
remcap		4731/tcp	# Remote Capture Protocol
ohmtrigger	4732/udp	# OHM server trigger
resorcs		4733/tcp	# RES Orchestration Catalog Services
ipdr-sp		4737/tcp/udp	# IPDR/SP
solera-lpn	4738/tcp/udp	# SoleraTec Locator
ipfix		4739/tcp/udp/sctp	# IP Flow Info Export
ipfixs		4740/tcp/sctp/udp	# ipfix protocol over TLS
lumimgrd	4741/tcp/udp	# Luminizer Manager
sicct		4742/tcp	# SICCT
sicct-sdp	4742/udp	# SICCT Service Discovery Protocol
openhpid	4743/tcp/udp	# openhpi HPI service
ifsp		4744/tcp/udp	# Internet File Synchronization Protocol
fmp		4745/tcp/udp	# Funambol Mobile Push
intelliadm-disc	4746/udp	# IntelliAdmin Discovery
buschtrommel	4747/udp	# peer-to-peer file exchange protocol
profilemac	4749/tcp/udp	# Profile for Mac
ssad		4750/tcp/udp	# Simple Service Auto Discovery
spocp		4751/tcp/udp	# Simple Policy Control Protocol
snap		4752/tcp/udp	# Simple Network Audio Protocol
simon		4753/tcp	# Simple Invocation of Methods Over Network (SIMON)
simon-disc	4753/udp	# Simple Invocation of Methods Over Network (SIMON) Discovery
gre-in-udp	4754/udp	# GRE-in-UDP Encapsulation
gre-udp-dtls	4755/udp	# GRE-in-UDP Encapsulation with DTLS
RDCenter	4756/tcp	# Reticle Decision Center
converge	4774/tcp	# Converge RPC
bfd-multi-ctl	4784/tcp/udp	# BFD Multihop Control
cncp		4785/udp	# Cisco Nexus Control Protocol
smart-install	4786/tcp	# Smart Install Service
sia-ctrl-plane	4787/tcp	# Service Insertion Architecture (SIA) Control-Plane
xmcp		4788/tcp	# eXtensible Messaging Client Protocol
vxlan		4789/udp	# Virtual eXtensible Local Area Network (VXLAN)
vxlan-gpe	4790/udp	# Generic Protocol Extension for Virtual eXtensible Local Area Network (VXLAN)
roce		4791/udp	# IP Routable RocE
iims		4800/tcp/udp	# Icona Instant Messenging System
iwec		4801/tcp/udp	# Icona Web Embedded Chat
ilss		4802/tcp/udp	# Icona License System Server
notateit	4803/tcp	# Notateit Messaging
notateit-disc	4803/udp	# Notateit Messaging Discovery
aja-ntv4-disc	4804/udp	# AJA ntv4 Video System Discovery
htcp		4827/tcp/udp	# HTCP
varadero-0	4837/tcp/udp	# Varadero-0
varadero-1	4838/tcp/udp	# Varadero-1
varadero-2	4839/tcp/udp	# Varadero-2
opcua-tcp	4840/tcp	# OPC UA Connection Protocol
opcua-udp	4840/udp	# OPC UA Multicast Datagram Protocol
quosa		4841/tcp/udp	# QUOSA Virtual Library Service
gw-asv		4842/tcp/udp	# nCode ICE-flow Library AppServer
opcua-tls	4843/tcp/udp	# OPC UA TCP Protocol over TLS/SSL
gw-log		4844/tcp/udp	# nCode ICE-flow Library LogServer
wcr-remlib	4845/tcp/udp	# WordCruncher Remote Library Service
contamac-icm	4846/tcp/udp	# Contamac ICM Service
wfc		4847/tcp/udp	# Web Fresh Communication
appserv-http	4848/tcp/udp	# App Server - Admin HTTP
appserv-https	4849/tcp/udp	# App Server - Admin HTTPS
sun-as-nodeagt	4850/tcp/udp	# Sun App Server - NA
derby-repli	4851/tcp/udp	# Apache Derby Replication
unify-debug	4867/tcp/udp	# Unify Debugger
phrelay		4868/tcp/udp	# Photon Relay
phrelaydbg	4869/tcp/udp	# Photon Relay Debug
cc-tracking	4870/tcp/udp	# Citcom Tracking Service
wired		4871/tcp/udp	# Wired
tritium-can	4876/tcp/udp	# Tritium CAN Bus Bridge Service
lmcs		4877/tcp/udp	# Lighting Management Control System
inst-discovery	4878/udp	# Agilent Instrument Discovery
wsdl-event	4879/tcp	# WSDL Event Receiver
hislip		4880/tcp	# IVI High-Speed LAN Instrument Protocol
socp-t		4881/udp	# SOCP Time Synchronization Protocol
socp-c		4882/udp	# SOCP Control Protocol
wmlserver	4883/tcp	# Meier-Phelps License Server
hivestor	4884/tcp/udp	# HiveStor Distributed File System
abbs		4885/tcp/udp	# ABBS
xcap-portal	4888/tcp	# xcap code analysis portal public user access
xcap-control	4889/tcp	# xcap code analysis portal cluster control and administration
lyskom		4894/tcp/udp	# LysKOM Protocol A
radmin-port	4899/tcp/udp	# RAdmin Port
hfcs		4900/tcp/udp	# HFSQL Client/Server Database Engine
flr-agent	4901/tcp	# FileLocator Remote Search Agent
magiccontrol	4902/tcp	# magicCONROL RF and Data Interface
lutap		4912/tcp	# Technicolor LUT Access Protocol
lutcp		4913/tcp	# LUTher Control Protocol
bones		4914/tcp/udp	# Bones Remote Control
frcs		4915/tcp	# Fibics Remote Control Service
an-signaling	4936/udp	# Signal protocol port for autonomic networking
atsc-mh-ssc	4937/udp	# ATSC-M/H Service Signaling Channel
eq-office-4940	4940/tcp/udp	# Equitrac Office
eq-office-4941	4941/tcp/udp	# Equitrac Office
eq-office-4942	4942/tcp/udp	# Equitrac Office
munin		4949/tcp/udp	# Munin Graphing Framework
sybasesrvmon	4950/tcp/udp	# Sybase Server Monitor
pwgwims		4951/tcp/udp	# PWG WIMS
sagxtsds	4952/tcp/udp	# SAG Directory Server
dbsyncarbiter	4953/tcp	# Synchronization Arbiter
ccss-qmm	4969/tcp/udp	# CCSS QMessageMonitor
ccss-qsm	4970/tcp/udp	# CCSS QSystemMonitor
burp		4971/tcp	# BackUp and Restore Program
ctxs-vpp	4980/udp	# Citrix Virtual Path
webyast		4984/tcp	# WebYast
gerhcs		4985/tcp	# GER HC Standard
mrip		4986/tcp/udp	# Model Railway Interface Program
smar-se-port1	4987/tcp/udp	# SMAR Ethernet Port 1
smar-se-port2	4988/tcp/udp	# SMAR Ethernet Port 2
parallel	4989/tcp/udp	# Parallel for GAUSS (tm)
busycal		4990/tcp/udp	# BusySync Calendar Synch. Protocol
vrt		4991/tcp/udp	# VITA Radio Transport
hfcs-manager	4999/tcp/udp	# HFSQL Client/Server Database Engine Manager
commplex-main	5000/tcp/udp
commplex-link	5001/tcp/udp
rfe		5002/tcp/udp	# radio free ethernet
fmpro-internal	5003/tcp/udp	# FileMaker, Inc. - Proprietary transport
avt-profile-1	5004/tcp/udp/dccp	# RTP media data
avt-profile-2	5005/tcp/udp/dccp	# RTP control protocol
wsm-server	5006/tcp/udp
wsm-server-ssl	5007/tcp/udp
synapsis-edge	5008/tcp/udp	# Synapsis EDGE
winfs		5009/tcp/udp	# Microsoft Windows Filesystem
telelpathstart	5010/tcp/udp	# TelepathStart
telelpathattack	5011/tcp/udp	# TelepathAttack
nsp		5012/tcp/udp	# NetOnTap Service
fmpro-v6	5013/tcp/udp	# FileMaker, Inc. - Proprietary transport
onpsocket	5014/udp	# Overlay Network Protocol
fmwp		5015/tcp	# FileMaker, Inc. - Web publishing
zenginkyo-1	5020/tcp/udp
zenginkyo-2	5021/tcp/udp
mice		5022/tcp/udp	# mice server
htuilsrv	5023/tcp/udp	# Htuil Server for PLD2
scpi-telnet	5024/tcp/udp	# SCPI-TELNET
scpi-raw	5025/tcp/udp	# SCPI-RAW
strexec-d	5026/tcp/udp	# Storix I/O daemon (data)
strexec-s	5027/tcp/udp	# Storix I/O daemon (stat)
qvr		5028/tcp	# Quiqum Virtual Relais
infobright	5029/tcp/udp	# Infobright Database Server
surfpass	5030/tcp/udp	# SurfPass
dmp		5031/udp	# Direct Message Protocol
signacert-agent	5032/tcp	# SignaCert Enterprise Trust Server Agent
jtnetd-server	5033/tcp	# Janstor Secure Data
jtnetd-status	5034/tcp	# Janstor Status
asnaacceler8db	5042/tcp/udp
swxadmin	5043/tcp/udp	# ShopWorX Administration
lxi-evntsvc	5044/tcp/udp	# LXI Event Service
osp		5045/tcp	# Open Settlement Protocol
vpm-udp		5046/udp	# Vishay PM UDP Service
iscape		5047/udp	# iSCAPE Data Broadcasting
texai		5048/tcp	# Texai Message Service
ivocalize	5049/tcp/udp	# iVocalize Web Conference
mmcc		5050/tcp/udp	# multimedia conference control tool
ita-agent	5051/tcp/udp	# ITA Agent
ita-manager	5052/tcp/udp	# ITA Manager
rlm		5053/tcp	# RLM License Server
rlm-disc	5053/udp	# RLM Discovery Server
rlm-admin	5054/tcp	# RLM administrative interface
unot		5055/tcp/udp	# UNOT
intecom-ps1	5056/tcp/udp	# Intecom Pointspan 1
intecom-ps2	5057/tcp/udp	# Intecom Pointspan 2
locus-disc	5058/udp	# Locus Discovery
sds		5059/tcp/udp	# SIP Directory Services
sip		5060/tcp/udp/sctp	# SIP
sips		5061/tcp/udp/sctp	# SIP-TLS
na-localise	5062/tcp/udp	# Localisation access
csrpc		5063/tcp	# centrify secure RPC
ca-1		5064/tcp/udp	# Channel Access 1
ca-2		5065/tcp/udp	# Channel Access 2
stanag-5066	5066/tcp/udp	# STANAG-5066-SUBNET-INTF
authentx	5067/tcp/udp	# Authentx Service
bitforestsrv	5068/tcp	# Bitforest Data Service
i-net-2000-npr	5069/tcp/udp	# I/Net 2000-NPR
vtsas		5070/tcp/udp	# VersaTrans Server Agent Service
powerschool	5071/tcp/udp	# PowerSchool
ayiya		5072/tcp/udp	# Anything In Anything
tag-pm		5073/tcp/udp	# Advantage Group Port Mgr
alesquery	5074/tcp/udp	# ALES Query
pvaccess	5075/tcp	# Experimental Physics and Industrial Control System
pixelpusher	5078/udp	# PixelPusher pixel data
cp-spxrpts	5079/udp	# Cambridge Pixel SPx Reports
onscreen	5080/tcp/udp	# OnScreen Data Collection Service
sdl-ets		5081/tcp/udp	# SDL - Ent Trans Server
qcp		5082/tcp/udp	# Qpur Communication Protocol
qfp		5083/tcp/udp	# Qpur File Protocol
llrp		5084/tcp/udp	# EPCglobal Low-Level Reader Protocol
encrypted-llrp	5085/tcp/udp	# EPCglobal Encrypted LLRP
aprigo-cs	5086/tcp	# Aprigo Collection Service
biotic		5087/tcp	# BIOTIC - Binary Internet of Things Interoperable Communication
car		5090/sctp	# Candidate AR
cxtp		5091/sctp	# Context Transfer Protocol
magpie		5092/udp	# Magpie Binary
sentinel-lm	5093/tcp/udp	# Sentinel LM
hart-ip		5094/tcp/udp	# HART-IP
sentlm-srv2srv	5099/tcp/udp	# SentLM Srv2Srv
socalia		5100/tcp/udp	# Socalia service mux
talarian-tcp	5101/tcp	# Talarian_TCP
talarian-udp	5101/udp	# Talarian_UDP
oms-nonsecure	5102/tcp/udp	# Oracle OMS non-secure
actifio-c2c	5103/tcp	# Actifio C2C
tinymessage	5104/udp	# TinyMessage
hughes-ap	5105/udp	# Hughes Association Protocol
actifioudsagent	5106/tcp	# Actifio UDS Agent
actifioreplic	5107/tcp	# Disk to Disk replication between Actifio Clusters
taep-as-svc	5111/tcp/udp	# TAEP AS service
pm-cmdsvr	5112/tcp/udp	# PeerMe Msg Cmd Service
ev-services	5114/tcp	# Enterprise Vault Services
autobuild	5115/tcp	# Symantec Autobuild Service
emb-proj-cmd	5116/udp	# EPSON Projecter Image Transfer
gradecam	5117/tcp	# GradeCam Image Processing
barracuda-bbs	5120/tcp/udp	# Barracuda Backup Protocol
nbt-pc		5133/tcp/udp	# Policy Commander
ppactivation	5134/tcp	# PP ActivationServer
erp-scale	5135/tcp	# ERP-Scale
minotaur-sa	5136/udp	# Minotaur SA
ctsd		5137/tcp/udp	# MyCTS server port
rmonitor-secure	5145/tcp/udp	# RMONITOR SECURE
social-alarm	5146/tcp	# Social Alarm Service
atmp		5150/tcp/udp	# Ascend Tunnel Management Protocol
esri-sde	5151/tcp/udp	# ESRI SDE Instance
sde-discovery	5152/tcp/udp	# ESRI SDE Instance Discovery
bzflag		5154/tcp/udp	# BZFlag game server
asctrl-agent	5155/tcp/udp	# Oracle asControl Agent
rugameonline	5156/tcp	# Russian Online Game
mediat		5157/tcp	# Mediat Remote Object Exchange
snmpssh		5161/tcp	# SNMP over SSH Transport Model
snmpssh-trap	5162/tcp	# SNMP Notification over SSH Transport Model
sbackup		5163/tcp	# Shadow Backup
vpa		5164/tcp	# Virtual Protocol Adapter
vpa-disc	5164/udp	# Virtual Protocol Adapter Discovery
ife-icorp	5165/tcp/udp	# ife_1corp
winpcs		5166/tcp/udp	# WinPCS Service Connection
scte104		5167/tcp/udp	# SCTE104 Connection
scte30		5168/tcp/udp	# SCTE30 Connection
pcoip-mgmt	5172/tcp	# PC over IP Endpoint Management
aol		5190/tcp/udp	# America-Online
aol-1		5191/tcp/udp	# AmericaOnline1
aol-2		5192/tcp/udp	# AmericaOnline2
aol-3		5193/tcp/udp	# AmericaOnline3
cpscomm		5194/tcp	# CipherPoint Config Service
ampl-lic	5195/tcp	# The protocol is used by a license server and client programs to control use of program licenses that float to networked machines
ampl-tableproxy	5196/tcp	# The protocol is used by two programs that exchange "table" data used in the AMPL modeling language
tunstall-lwp	5197/tcp	# Tunstall Lone worker device interface
targus-getdata	5200/tcp/udp	# TARGUS GetData
targus-getdata1	5201/tcp/udp	# TARGUS GetData 1
targus-getdata2	5202/tcp/udp	# TARGUS GetData 2
targus-getdata3	5203/tcp/udp	# TARGUS GetData 3
nomad		5209/tcp	# Nomad Device Video Transfer
noteza		5215/tcp/sctp	# NOTEZA Data Safety Service
3exmp		5221/tcp	# 3eTI Extensible Management Protocol for OAMP
xmpp-client	5222/tcp	# XMPP Client Connection
hpvirtgrp	5223/tcp/udp	# HP Virtual Machine Group Management
hpvirtctrl	5224/tcp/udp	# HP Virtual Machine Console Operations
hp-server	5225/tcp/udp	# HP Server
hp-status	5226/tcp/udp	# HP Status
perfd		5227/tcp/udp	# HP System Performance Metric Service
hpvroom		5228/tcp	# HP Virtual Room Service
jaxflow		5229/tcp	# Netflow/IPFIX/sFlow Collector and Forwarder Management
jaxflow-data	5230/tcp	# JaxMP RealFlow application and protocol data
crusecontrol	5231/tcp	# Remote Control of Scan Software for Cruse Scanners
csedaemon	5232/tcp	# Cruse Scanning System Service
enfs		5233/tcp	# Etinnae Network File Service
eenet		5234/tcp/udp	# EEnet communications
galaxy-network	5235/tcp/udp	# Galaxy Network Service
padl2sim	5236/tcp/udp
mnet-discovery	5237/tcp/udp	# m-net discovery
downtools	5245/tcp	# DownTools Control Protocol
downtools-disc	5245/udp	# DownTools Discovery Protocol
capwap-control	5246/udp	# CAPWAP Control Protocol
capwap-data	5247/udp	# CAPWAP Data Protocol
caacws		5248/tcp/udp	# CA Access Control Web Service
caaclang2	5249/tcp/udp	# CA AC Lang Service
soagateway	5250/tcp/udp	# soaGateway
caevms		5251/tcp/udp	# CA eTrust VM Service
movaz-ssc	5252/tcp/udp	# Movaz SSC
kpdp		5253/tcp	# Kohler Power Device Protocol
logcabin	5254/tcp	# LogCabin storage service
3com-njack-1	5264/tcp/udp	# 3Com Network Jack Port 1
3com-njack-2	5265/tcp/udp	# 3Com Network Jack Port 2
xmpp-server	5269/tcp	# XMPP Server Connection
cartographerxmp	5270/tcp/udp	# Cartographer XMP
cuelink		5271/tcp	# StageSoft CueLink messaging
cuelink-disc	5271/udp	# StageSoft CueLink discovery
pk		5272/tcp/udp	# PK
xmpp-bosh	5280/tcp	# Bidirectional-streams Over Synchronous HTTP (BOSH)
undo-lm		5281/tcp	# Undo License Manager
transmit-port	5282/tcp/udp	# Marimba Transmitter Port
presence	5298/tcp/udp	# XMPP Link-Local Messaging
nlg-data	5299/tcp/udp	# NLG Data Service
hacl-hb		5300/tcp/udp	# HA cluster heartbeat
hacl-gs		5301/tcp/udp	# HA cluster general services
hacl-cfg	5302/tcp/udp	# HA cluster configuration
hacl-probe	5303/tcp/udp	# HA cluster probing
hacl-local	5304/tcp/udp	# HA Cluster Commands
hacl-test	5305/tcp/udp	# HA Cluster Test
sun-mc-grp	5306/tcp/udp	# Sun MC Group
sco-aip		5307/tcp/udp	# SCO AIP
cfengine	5308/tcp/udp	# CFengine
jprinter	5309/tcp/udp	# J Printer
outlaws		5310/tcp/udp	# Outlaws
permabit-cs	5312/tcp/udp	# Permabit Client-Server
rrdp		5313/tcp/udp	# Real-time & Reliable Data
opalis-rbt-ipc	5314/tcp/udp
hacl-poll	5315/tcp/udp	# HA Cluster UDP Polling
hpbladems	5316/tcp	# HPBladeSystem Monitor Service
hpdevms		5317/tcp	# HP Device Monitor Service
pkix-cmc	5318/tcp	# PKIX Certificate Management using CMS (CMC)
bsfserver-zn	5320/tcp	# Webservices-based Zn interface of BSF
bsfsvr-zn-ssl	5321/tcp	# Webservices-based Zn interface of BSF over SSL
kfserver	5343/tcp/udp	# Sculptor Database Server
xkotodrcp	5344/tcp/udp	# xkoto DRCP
stuns		5349/tcp/udp	# STUN over TLS
pcp-multicast	5350/udp	# Port Control Protocol Multicast
pcp		5351/udp	# Port Control Protocol
dns-llq		5352/tcp/udp	# DNS Long-Lived Queries
mdns		5353/tcp/udp	# Multicast DNS
mdnsresponder	5354/tcp/udp	# Multicast DNS Responder IPC
llmnr		5355/tcp/udp	# LLMNR
ms-smlbiz	5356/tcp/udp	# Microsoft Small Business
wsdapi		5357/tcp/udp	# Web Services for Devices
wsdapi-s	5358/tcp/udp	# WS for Devices Secured
ms-alerter	5359/tcp/udp	# Microsoft Alerter
ms-sideshow	5360/tcp/udp	# Protocol for Windows SideShow
ms-s-sideshow	5361/tcp/udp	# Secure Protocol for Windows SideShow
serverwsd2	5362/tcp/udp	# Microsoft Windows Server WSD2 Service
net-projection	5363/tcp/udp	# Windows Network Projection
kdnet		5364/udp	# Microsoft Kernel Debugger
stresstester	5397/tcp/udp	# StressTester(tm) Injector
elektron-admin	5398/tcp/udp	# Elektron Administration
securitychase	5399/tcp/udp	# SecurityChase
excerpt		5400/tcp/udp	# Excerpt Search
excerpts	5401/tcp/udp	# Excerpt Search Secure
mftp		5402/tcp/udp	# OmniCast MFTP
hpoms-ci-lstn	5403/tcp/udp	# HPOMS-CI-LSTN
hpoms-dps-lstn	5404/tcp/udp	# HPOMS-DPS-LSTN
netsupport	5405/tcp/udp	# NetSupport
systemics-sox	5406/tcp/udp	# Systemics Sox
foresyte-clear	5407/tcp/udp	# Foresyte-Clear
foresyte-sec	5408/tcp/udp	# Foresyte-Sec
salient-dtasrv	5409/tcp/udp	# Salient Data Server
salient-usrmgr	5410/tcp/udp	# Salient User Manager
actnet		5411/tcp/udp	# ActNet
continuus	5412/tcp/udp	# Continuus
wwiotalk	5413/tcp/udp	# WWIOTALK
statusd		5414/tcp/udp	# StatusD
ns-server	5415/tcp/udp	# NS Server
sns-gateway	5416/tcp/udp	# SNS Gateway
sns-agent	5417/tcp/udp	# SNS Agent
mcntp		5418/tcp/udp	# MCNTP
dj-ice		5419/tcp/udp	# DJ-ICE
cylink-c	5420/tcp/udp	# Cylink-C
netsupport2	5421/tcp/udp	# Net Support 2
salient-mux	5422/tcp/udp	# Salient MUX
virtualuser	5423/tcp/udp	# VIRTUALUSER
beyond-remote	5424/tcp/udp	# Beyond Remote
br-channel	5425/tcp/udp	# Beyond Remote Command Channel
devbasic	5426/tcp/udp	# DEVBASIC
sco-peer-tta	5427/tcp/udp	# SCO-PEER-TTA
telaconsole	5428/tcp/udp	# TELACONSOLE
base		5429/tcp/udp	# Billing and Accounting System Exchange
radec-corp	5430/tcp/udp	# RADEC CORP
park-agent	5431/tcp/udp	# PARK AGENT
postgresql	5432/tcp/udp	# PostgreSQL Database
pyrrho		5433/tcp/udp	# Pyrrho DBMS
sgi-arrayd	5434/tcp/udp	# SGI Array Services Daemon
sceanics	5435/tcp/udp	# SCEANICS situation and action notification
pmip6-cntl	5436/udp
pmip6-data	5437/udp
spss		5443/tcp/udp	# Pearson HTTPS
smbdirect	5445/tcp/sctp	# Server Message Block over Remote Direct Memory Access
tiepie		5450/tcp	# TiePie engineering data acquisition
tiepie-disc	5450/udp	# TiePie engineering data acquisition (discovery)
surebox		5453/tcp/udp	# SureBox
apc-5454	5454/tcp/udp	# APC 5454
apc-5455	5455/tcp/udp	# APC 5455
apc-5456	5456/tcp/udp	# APC 5456
silkmeter	5461/tcp/udp	# SILKMETER
ttl-publisher	5462/tcp/udp	# TTL Publisher
ttlpriceproxy	5463/tcp/udp	# TTL Price Proxy
quailnet	5464/tcp/udp	# Quail Networks Object Broker
netops-broker	5465/tcp/udp	# NETOPS-BROKER
apsolab-col	5470/tcp	# The Apsolab company's data collection protocol (native api)
apsolab-cols	5471/tcp	# The Apsolab company's secure data collection protocol (native api)
apsolab-tag	5472/tcp	# The Apsolab company's dynamic tag protocol
apsolab-tags	5473/tcp	# The Apsolab company's secure dynamic tag protocol
apsolab-rpc	5474/udp	# The Apsolab company's status query protocol
apsolab-data	5475/tcp	# The Apsolab company's data retrieval protocol
fcp-addr-srvr1	5500/tcp/udp
fcp-addr-srvr2	5501/tcp/udp
fcp-srvr-inst1	5502/tcp/udp
fcp-srvr-inst2	5503/tcp/udp
fcp-cics-gw1	5504/tcp/udp
checkoutdb	5505/tcp/udp	# Checkout Database
amc		5506/tcp/udp	# Amcom Mobile Connect
psl-management	5507/tcp	# PowerSysLab Electrical Management
cbus		5550/tcp	# Model Railway control using the CBUS message protocol
sgi-eventmond	5553/tcp/udp	# SGI Eventmond Port
sgi-esphttp	5554/tcp/udp	# SGI ESP HTTP
personal-agent	5555/tcp/udp	# Personal Agent
freeciv		5556/tcp/udp	# Freeciv gameplay
farenet		5557/tcp	# Sandlab FARENET
hpe-dp-bura	5565/tcp	# HPE Advanced BURA
westec-connect	5566/tcp	# Westec Connect
dof-dps-mc-sec	5567/tcp/udp	# DOF Protocol Stack Multicast/Secure Transport
sdt		5568/tcp/udp	# Session Data Transport Multicast
rdmnet-ctrl	5569/tcp	# PLASA E1.33, Remote Device Management (RDM) controller status notifications
rdmnet-device	5569/udp	# PLASA E1.33, Remote Device Management (RDM) messages
sdmmp		5573/tcp/udp	# SAS Domain Management Messaging Protocol
lsi-bobcat	5574/tcp	# SAS IO Forwarding
ora-oap		5575/tcp	# Oracle Access Protocol
fdtracks	5579/tcp	# FleetDisplay Tracking Service
tmosms0		5580/tcp/udp	# T-Mobile SMS Protocol Message 0
tmosms1		5581/tcp/udp	# T-Mobile SMS Protocol Message 1
fac-restore	5582/tcp/udp	# T-Mobile SMS Protocol Message 3
tmo-icon-sync	5583/tcp/udp	# T-Mobile SMS Protocol Message 2
bis-web		5584/tcp/udp	# BeInSync-Web
bis-sync	5585/tcp/udp	# BeInSync-sync
att-mt-sms	5586/tcp	# Planning to send mobile terminated SMS to the specific port so that the SMS is not visible to the client
ininmessaging	5597/tcp/udp	# inin secure messaging
mctfeed		5598/tcp/udp	# MCT Market Data Feed
esinstall	5599/tcp/udp	# Enterprise Security Remote Install
esmmanager	5600/tcp/udp	# Enterprise Security Manager
esmagent	5601/tcp/udp	# Enterprise Security Agent
a1-msc		5602/tcp/udp	# A1-MSC
a1-bs		5603/tcp/udp	# A1-BS
a3-sdunode	5604/tcp/udp	# A3-SDUNode
a4-sdunode	5605/tcp/udp	# A4-SDUNode
efr		5618/tcp	# Fiscal Registering Protocol
ninaf		5627/tcp/udp	# Node Initiated Network Association Forma
htrust		5628/tcp/udp	# HTrust API
symantec-sfdb	5629/tcp/udp	# Symantec Storage Foundation for Database
precise-comm	5630/tcp/udp	# PreciseCommunication
pcanywheredata	5631/tcp/udp	# pcANYWHEREdata
pcanywherestat	5632/tcp/udp	# pcANYWHEREstat
beorl		5633/tcp/udp	# BE Operations Request Listener
xprtld		5634/tcp/udp	# SF Message Service
sfmsso		5635/tcp	# SFM Authentication Subsystem
sfm-db-server	5636/tcp	# SFMdb - SFM DB server
cssc		5637/tcp	# Symantec CSSC
flcrs		5638/tcp	# Symantec Fingerprint Lookup and Container Reference Service
ics		5639/tcp	# Symantec Integrity Checking Service
vfmobile	5646/tcp	# Ventureforth Mobile
nrpe		5666/tcp	# Nagios Remote Plugin Executor
filemq		5670/tcp	# ZeroMQ file publish-subscribe protocol
zre-disc	5670/udp	# Local area discovery and messaging over ZeroMQ
amqps		5671/tcp/udp	# amqp protocol over TLS/SSL
amqp		5672/tcp/udp/sctp	# AMQP
jms		5673/tcp/udp	# JACL Message Server
hyperscsi-port	5674/tcp/udp	# HyperSCSI Port
v5ua		5675/tcp/udp/sctp	# V5UA application port
raadmin		5676/tcp/udp	# RA Administration
questdb2-lnchr	5677/tcp/udp	# Quest Central DB2 Launchr
rrac		5678/tcp/udp	# Remote Replication Agent Connection
dccm		5679/tcp/udp	# Direct Cable Connect Manager
auriga-router	5680/tcp/udp	# Auriga Router Service
ncxcp		5681/tcp/udp	# Net-coneX Control Protocol
brightcore	5682/udp	# BrightCore control & data transfer exchange
coap		5683/tcp/udp	# Constrained Application Protocol (CoAP)
coaps		5684/tcp/udp	# Constrained Application Protocol (CoAP)
gog-multiplayer	5687/udp	# GOG multiplayer game protocol
ggz		5688/tcp/udp	# GGZ Gaming Zone
qmvideo		5689/tcp/udp	# QM video network management protocol
rbsystem	5693/tcp	# Robert Bosch Data Transfer
kmip		5696/tcp	# Key Management Interoperability Protocol
supportassist	5700/tcp	# Dell SupportAssist data center management
storageos	5705/tcp	# StorageOS REST API
proshareaudio	5713/tcp/udp	# proshare conf audio
prosharevideo	5714/tcp/udp	# proshare conf video
prosharedata	5715/tcp/udp	# proshare conf data
prosharerequest	5716/tcp/udp	# proshare conf request
prosharenotify	5717/tcp/udp	# proshare conf notify
dpm		5718/tcp/udp	# DPM Communication Server
dpm-agent	5719/tcp/udp	# DPM Agent Coordinator
ms-licensing	5720/tcp/udp	# MS-Licensing
dtpt		5721/tcp/udp	# Desktop Passthru Service
msdfsr		5722/tcp/udp	# Microsoft DFS Replication Service
omhs		5723/tcp/udp	# Operations Manager - Health Service
omsdk		5724/tcp/udp	# Operations Manager - SDK Service
ms-ilm		5725/tcp	# Microsoft Identity Lifecycle Manager
ms-ilm-sts	5726/tcp	# Microsoft Lifecycle Manager Secure Token Service
asgenf		5727/tcp	# ASG Event Notification Framework
io-dist-data	5728/tcp	# Dist. I/O Comm. Service Data and Control
io-dist-group	5728/udp	# Dist. I/O Comm. Service Group Membership
openmail	5729/tcp/udp	# Openmail User Agent Layer
unieng		5730/tcp/udp	# Steltor's calendar access
ida-discover1	5741/tcp/udp	# IDA Discover Port 1
ida-discover2	5742/tcp/udp	# IDA Discover Port 2
watchdoc-pod	5743/tcp/udp	# Watchdoc NetPOD Protocol
watchdoc	5744/tcp/udp	# Watchdoc Server
fcopy-server	5745/tcp/udp
fcopys-server	5746/tcp/udp
tunatic		5747/tcp/udp	# Wildbits Tunatic
tunalyzer	5748/tcp/udp	# Wildbits Tunalyzer
rscd		5750/tcp/udp	# Bladelogic Agent Service
openmailg	5755/tcp/udp	# OpenMail Desk Gateway server
x500ms		5757/tcp/udp	# OpenMail X.500 Directory Server
openmailns	5766/tcp/udp	# OpenMail NewMail Server
s-openmail	5767/tcp/udp	# OpenMail Suer Agent Layer (Secure)
openmailpxy	5768/tcp/udp	# OpenMail CMTS Server
spramsca	5769/tcp/udp	# x509solutions Internal CA
spramsd		5770/tcp/udp	# x509solutions Secure Data
netagent	5771/tcp/udp	# NetAgent
dali-port	5777/tcp/udp	# DALI Port
vts-rpc		5780/tcp	# Visual Tag System RPC
3par-evts	5781/tcp/udp	# 3PAR Event Reporting Service
3par-mgmt	5782/tcp/udp	# 3PAR Management Service
3par-mgmt-ssl	5783/tcp/udp	# 3PAR Management Service with SSL
ibar		5784/udp	# Cisco Interbox Application Redundancy
3par-rcopy	5785/tcp/udp	# 3PAR Inform Remote Copy
cisco-redu	5786/udp	# redundancy notification
waascluster	5787/udp	# Cisco WAAS Cluster Protocol
xtreamx		5793/tcp/udp	# XtreamX Supervised Peer message
spdp		5794/udp	# Simple Peered Discovery Protocol
icmpd		5813/tcp/udp	# ICMPD
spt-automation	5814/tcp/udp	# Support Automation
shiprush-d-ch	5841/tcp	# Z-firm ShipRush interface for web access and bidirectional data
reversion	5842/tcp	# Reversion Backup/Restore
wherehoo	5859/tcp/udp	# WHEREHOO
ppsuitemsg	5863/tcp/udp	# PlanetPress Suite Messeng
diameters	5868/tcp/sctp	# Diameter over TLS/TCP
jute		5883/tcp	# Javascript Unit Test Environment
rfb		5900/tcp/udp	# Remote Framebuffer
cm		5910/tcp/udp/sctp	# Context Management
cpdlc		5911/tcp/udp/sctp	# Controller Pilot Data Link Communication
fis		5912/tcp/udp/sctp	# Flight Information Services
ads-c		5913/tcp/udp/sctp	# Automatic Dependent Surveillance
indy		5963/tcp/udp	# Indy Application Server
mppolicy-v5	5968/tcp/udp
mppolicy-mgr	5969/tcp/udp
couchdb		5984/tcp/udp	# CouchDB
wsman		5985/tcp/udp	# WBEM WS-Management HTTP
wsmans		5986/tcp/udp	# WBEM WS-Management HTTP over TLS/SSL
wbem-rmi	5987/tcp/udp	# WBEM RMI
wbem-http	5988/tcp/udp	# WBEM CIM-XML (HTTP)
wbem-https	5989/tcp/udp	# WBEM CIM-XML (HTTPS)
wbem-exp-https	5990/tcp/udp	# WBEM Export HTTPS
nuxsl		5991/tcp/udp	# NUXSL
consul-insight	5992/tcp/udp	# Consul InSight Security
cim-rs		5993/tcp	# DMTF WBEM CIM REST
cvsup		5999/tcp/udp	# CVSup
x11		6000-6063/tcp/udp	# X Window System
ndl-ahp-svc	6064/tcp/udp	# NDL-AHP-SVC
winpharaoh	6065/tcp/udp	# WinPharaoh
ewctsp		6066/tcp/udp	# EWCTSP
gsmp-ancp	6068/tcp	# GSMP/ANCP
trip		6069/tcp/udp	# TRIP
messageasap	6070/tcp/udp	# Messageasap
ssdtp		6071/tcp/udp	# SSDTP
diagnose-proc	6072/tcp/udp	# DIAGNOSE-PROC
directplay8	6073/tcp/udp	# DirectPlay8
max		6074/tcp/udp	# Microsoft Max
dpm-acm		6075/tcp	# Microsoft DPM Access Control Manager
msft-dpm-cert	6076/tcp	# Microsoft DPM WCF Certificates
iconstructsrv	6077/tcp	# iConstruct Server
gue		6080/udp	# Generic UDP Encapsulation
geneve		6081/udp	# Generic Network Virtualization Encapsulation (Geneve)
p25cai		6082/udp	# APCO Project 25 Common Air Interface - UDP encapsulation
miami-bcast	6083/udp	# telecomsoftware miami broadcast
reload-config	6084/tcp	# Peer to Peer Infrastructure Configuration
konspire2b	6085/tcp/udp	# konspire2b p2p network
pdtp		6086/tcp/udp	# PDTP P2P
ldss		6087/tcp/udp	# Local Download Sharing Service
doglms		6088/tcp	# SuperDog License Manager
doglms-notify	6088/udp	# SuperDog License Manager Notifier
raxa-mgmt	6099/tcp	# RAXA Management
synchronet-db	6100/tcp/udp	# SynchroNet-db
synchronet-rtc	6101/tcp/udp	# SynchroNet-rtc
synchronet-upd	6102/tcp/udp	# SynchroNet-upd
rets		6103/tcp/udp	# RETS
dbdb		6104/tcp/udp	# DBDB
primaserver	6105/tcp/udp	# Prima Server
mpsserver	6106/tcp/udp	# MPS Server
etc-control	6107/tcp/udp	# ETC Control
sercomm-scadmin	6108/tcp/udp	# Sercomm-SCAdmin
globecast-id	6109/tcp/udp	# GLOBECAST-ID
softcm		6110/tcp/udp	# HP SoftBench CM
spc		6111/tcp/udp	# HP SoftBench Sub-Process Control
dtspcd		6112/tcp/udp	# Desk-Top Sub-Process Control Daemon
dayliteserver	6113/tcp	# Daylite Server
wrspice		6114/tcp	# WRspice IPC Service
xic		6115/tcp	# Xic IPC Service
xtlserv		6116/tcp	# XicTools License Manager Service
daylitetouch	6117/tcp	# Daylite Touch Sync
tipc		6118/udp	# Transparent Inter Process Communication
spdy		6121/tcp	# SPDY for a faster web
bex-webadmin	6122/tcp/udp	# Backup Express Web Server
backup-express	6123/tcp/udp	# Backup Express
pnbs		6124/tcp/udp	# Phlexible Network Backup Service
damewaremobgtwy	6130/tcp	# The DameWare Mobile Gateway Service
nbt-wol		6133/tcp/udp	# New Boundary Tech WOL
pulsonixnls	6140/tcp/udp	# Pulsonix Network License Service
meta-corp	6141/tcp/udp	# Meta Corporation License Manager
aspentec-lm	6142/tcp/udp	# Aspen Technology License Manager
watershed-lm	6143/tcp/udp	# Watershed License Manager
statsci1-lm	6144/tcp/udp	# StatSci License Manager - 1
statsci2-lm	6145/tcp/udp	# StatSci License Manager - 2
lonewolf-lm	6146/tcp/udp	# Lone Wolf Systems License Manager
montage-lm	6147/tcp/udp	# Montage License Manager
ricardo-lm	6148/tcp/udp	# Ricardo North America License Manager
tal-pod		6149/tcp/udp
efb-aci		6159/tcp	# EFB Application Control Interface
ecmp		6160/tcp	# Emerson Extensible Control and Management Protocol
ecmp-data	6160/udp	# Emerson Extensible Control and Management Protocol Data
patrol-ism	6161/tcp/udp	# PATROL Internet Srv Mgr
patrol-coll	6162/tcp/udp	# PATROL Collector
pscribe		6163/tcp/udp	# Precision Scribe Cnx Port
lm-x		6200/tcp/udp	# LM-X License Manager by X-Formation
thermo-calc	6201/udp	# Management of service nodes in a processing grid for thermodynamic calculations
qmtps		6209/tcp/udp	# QMTP over TLS
radmind		6222/tcp/udp	# Radmind Access Protocol
jeol-nsdtp-1	6241/tcp	# JEOL Network Services Data Transport Protocol 1
jeol-nsddp-1	6241/udp	# JEOL Network Services Dynamic Discovery Protocol 1
jeol-nsdtp-2	6242/tcp	# JEOL Network Services Data Transport Protocol 2
jeol-nsddp-2	6242/udp	# JEOL Network Services Dynamic Discovery Protocol 2
jeol-nsdtp-3	6243/tcp	# JEOL Network Services Data Transport Protocol 3
jeol-nsddp-3	6243/udp	# JEOL Network Services Dynamic Discovery Protocol 3
jeol-nsdtp-4	6244/tcp	# JEOL Network Services Data Transport Protocol 4
jeol-nsddp-4	6244/udp	# JEOL Network Services Dynamic Discovery Protocol 4
tl1-raw-ssl	6251/tcp/udp	# TL1 Raw Over SSL/TLS
tl1-ssh		6252/tcp/udp	# TL1 over SSH
crip		6253/tcp/udp	# CRIP
gld		6267/tcp	# GridLAB-D User Interface
grid		6268/tcp/udp	# Grid Authentication
grid-alt	6269/tcp/udp	# Grid Authentication Alt
bmc-grx		6300/tcp/udp	# BMC GRX
bmc-ctd-ldap	6301/tcp/udp	# BMC CONTROL-D LDAP SERVER
ufmp		6306/tcp/udp	# Unified Fabric Management Protocol
scup		6315/tcp	# Sensor Control Unit Protocol
scup-disc	6315/udp	# Sensor Control Unit Protocol Discovery Protocol
abb-escp	6316/tcp/udp	# Ethernet Sensor Communications Protocol
nav-data-cmd	6317/tcp	# Navtech Radar Sensor Data Command
nav-data	6317/udp	# Navtech Radar Sensor Data
repsvc		6320/tcp/udp	# Double-Take Replication Service
emp-server1	6321/tcp/udp	# Empress Software Connectivity Server 1
emp-server2	6322/tcp/udp	# Empress Software Connectivity Server 2
hrd-ncs		6324/tcp	# HR Device Network Configuration Service
hrd-ns-disc	6324/udp	# HR Device Network service
dt-mgmtsvc	6325/tcp	# Double-Take Management Service
dt-vra		6326/tcp	# Double-Take Virtual Recovery Assistant
sflow		6343/tcp/udp	# sFlow traffic monitoring
streletz	6344/tcp	# Argus-Spectr security and fire-prevention systems service
gnutella-svc	6346/tcp/udp
gnutella-rtr	6347/tcp/udp
adap		6350/tcp/udp	# App Discovery and Access Protocol
pmcs		6355/tcp/udp	# PMCS applications
metaedit-mu	6360/tcp/udp	# MetaEdit+ Multi-User
ndn		6363/udp	# Named Data Networking
metaedit-se	6370/tcp/udp	# MetaEdit+ Server Administration
redis		6379/tcp	# An advanced key-value cache and store
metatude-mds	6382/tcp/udp	# Metatude Dialogue Server
clariion-evr01	6389/tcp/udp
metaedit-ws	6390/tcp/udp	# MetaEdit+ WebService API
faxcomservice	6417/tcp/udp	# Faxcom Message Service
syserverremote	6418/tcp	# SYserver remote commands
svdrp		6419/tcp	# Simple VDR Protocol
svdrp-disc	6419/udp	# Simple VDR Protocol Discovery
nim-vdrshell	6420/tcp/udp	# NIM_VDRShell
nim-wan		6421/tcp/udp	# NIM_WAN
pgbouncer	6432/tcp	# PgBouncer
tarp		6442/tcp	# Transitory Application Request Protocol
sun-sr-https	6443/tcp/udp	# Service Registry Default HTTPS Domain
sge-qmaster	6444/tcp/udp	# Grid Engine Qmaster Service
sge-execd	6445/tcp/udp	# Grid Engine Execution Service
mysql-proxy	6446/tcp/udp	# MySQL Proxy
skip-cert-recv	6455/tcp/udp	# SKIP Certificate Receive
skip-cert-send	6456/tcp/udp	# SKIP Certificate Send
ieee11073-20701	6464/tcp/udp	# Port assignment for medical device communication in accordance to IEEE 11073-20701
lvision-lm	6471/tcp/udp	# LVision License Manager
sun-sr-http	6480/tcp/udp	# Service Registry Default HTTP Domain
servicetags	6481/tcp/udp	# Service Tags
ldoms-mgmt	6482/tcp/udp	# Logical Domains Management Interface
SunVTS-RMI	6483/tcp/udp
sun-sr-jms	6484/tcp/udp	# Service Registry Default JMS Domain
sun-sr-iiop	6485/tcp/udp	# Service Registry Default IIOP Domain
sun-sr-iiops	6486/tcp/udp	# Service Registry Default IIOPS Domain
sun-sr-iiop-aut	6487/tcp/udp	# Service Registry Default IIOPAuth Domain
sun-sr-jmx	6488/tcp/udp	# Service Registry Default JMX Domain
sun-sr-admin	6489/tcp/udp	# Service Registry Default Admin Domain
boks		6500/tcp/udp	# BoKS Master
boks-servc	6501/tcp/udp	# BoKS Servc
boks-servm	6502/tcp/udp	# BoKS Servm
boks-clntd	6503/tcp/udp	# BoKS Clntd
badm-priv	6505/tcp/udp	# BoKS Admin Private Port
badm-pub	6506/tcp/udp	# BoKS Admin Public Port
bdir-priv	6507/tcp/udp	# BoKS Dir Server, Private Port
bdir-pub	6508/tcp/udp	# BoKS Dir Server, Public Port
mgcs-mfp-port	6509/tcp/udp	# MGCS-MFP Port
mcer-port	6510/tcp/udp	# MCER Port
dccp-udp	6511/udp	# Datagram Congestion Control Protocol Encapsulation for NAT Traversal
netconf-tls	6513/tcp	# NETCONF over TLS
syslog-tls	6514/tcp/udp/dccp	# Syslog over TLS
elipse-rec	6515/tcp/udp	# Elipse RPC Protocol
lds-distrib	6543/tcp/udp	# lds_distrib
lds-dump	6544/tcp/udp	# LDS Dump Service
apc-6547	6547/tcp/udp	# APC 6547
apc-6548	6548/tcp/udp	# APC 6548
apc-6549	6549/tcp/udp	# APC 6549
fg-sysupdate	6550/tcp/udp
sum		6551/tcp/udp	# Software Update Manager
xdsxdm		6558/tcp/udp
sane-port	6566/tcp/udp	# SANE Control Port
canit-store	6568/tcp	# CanIt Storage Manager
rp-reputation	6568/udp	# Roaring Penguin IP Address Reputation Collection
affiliate	6579/tcp/udp	# Affiliate
parsec-master	6580/tcp/udp	# Parsec Masterserver
parsec-peer	6581/tcp/udp	# Parsec Peer-to-Peer
parsec-game	6582/tcp/udp	# Parsec Gameserver
joaJewelSuite	6583/tcp/udp	# JOA Jewel Suite
mshvlm		6600/tcp	# Microsoft Hyper-V Live Migration
mstmg-sstp	6601/tcp	# Microsoft Threat Management Gateway SSTP
wsscomfrmwk	6602/tcp	# Windows WSS Communication Framework
odette-ftps	6619/tcp/udp	# ODETTE-FTP over TLS/SSL
kftp-data	6620/tcp/udp	# Kerberos V5 FTP Data
kftp		6621/tcp/udp	# Kerberos V5 FTP Control
mcftp		6622/tcp/udp	# Multicast FTP
ktelnet		6623/tcp/udp	# Kerberos V5 Telnet
datascaler-db	6624/tcp	# DataScaler database
datascaler-ctl	6625/tcp	# DataScaler control
wago-service	6626/tcp/udp	# WAGO Service and Update
nexgen		6627/tcp/udp	# Allied Electronics NeXGen
afesc-mc	6628/tcp/udp	# AFE Stock Channel M/C
nexgen-aux	6629/tcp/udp	# Secondary, (non ANDI) multi-protocol multi-function interface to the Allied ANDI-based family of forecourt controllers
mxodbc-connect	6632/tcp	# eGenix mxODBC Connect
cisco-vpath-tun	6633/udp	# Cisco vPath Services Overlay
mpls-pm		6634/udp	# MPLS Performance Measurement out-of-band response
mpls-udp	6635/udp	# Encapsulate MPLS packets in UDP tunnels.
mpls-udp-dtls	6636/udp	# Encapsulate MPLS packets in UDP tunnels with DTLS.
ovsdb		6640/tcp	# Open vSwitch Database protocol
openflow	6653/tcp/udp	# OpenFlow
pcs-sf-ui-man	6655/tcp	# PC SOFT - Software factory UI/manager
emgmsg		6656/tcp	# Emergency Message Control Service
palcom-disc	6657/udp	# PalCom Discovery
ircu		6665-6669/tcp	# IRCU
vocaltec-gold	6670/tcp/udp	# Vocaltec Global Online Directory
p4p-portal	6671/tcp/udp	# P4P Portal Service
vision-server	6672/tcp/udp	# vision_server
vision-elmd	6673/tcp/udp	# vision_elmd
vfbp		6678/tcp	# Viscount Freedom Bridge Protocol
vfbp-disc	6678/udp	# Viscount Freedom Bridge Discovery
osaut		6679/tcp/udp	# Osorno Automation
clever-ctrace	6687/tcp	# CleverView for cTrace Message Service
clever-tcpip	6688/tcp	# CleverView for TCP/IP Message Service
tsa		6689/tcp/udp	# Tofino Security Appliance
cleverdetect	6690/tcp	# CLEVERDetect Message Service
babel		6696/udp	# Babel Routing Protocol
ircs-u		6697/tcp	# Internet Relay Chat via TLS/SSL
kti-icad-srvr	6701/tcp/udp	# KTI/ICAD Nameserver
e-design-net	6702/tcp/udp	# e-Design network
e-design-web	6703/tcp/udp	# e-Design web
frc-hp		6704/sctp	# ForCES HP (High Priority) channel
frc-mp		6705/sctp	# ForCES MP (Medium Priority) channel
frc-lp		6706/sctp	# ForCES LP (Low priority) channel
ibprotocol	6714/tcp/udp	# Internet Backplane Protocol
fibotrader-com	6715/tcp/udp	# Fibotrader Communications
princity-agent	6716/tcp	# Princity Agent
bmc-perf-agent	6767/tcp/udp	# BMC PERFORM AGENT
bmc-perf-mgrd	6768/tcp/udp	# BMC PERFORM MGRD
adi-gxp-srvprt	6769/tcp/udp	# ADInstruments GxP Server
plysrv-http	6770/tcp/udp	# PolyServe http
plysrv-https	6771/tcp/udp	# PolyServe https
ntz-tracker	6777/tcp	# netTsunami Tracker
ntz-p2p-storage	6778/tcp	# netTsunami p2p storage system
bfd-lag		6784/udp	# Bidirectional Forwarding Detection (BFD) on Link Aggregation Group (LAG) Interfaces
dgpf-exchg	6785/tcp/udp	# DGPF Individual Exchange
smc-jmx		6786/tcp/udp	# Sun Java Web Console JMX
smc-admin	6787/tcp/udp	# Sun Web Console Admin
smc-http	6788/tcp/udp	# SMC-HTTP
radg		6789/tcp	# GSS-API for the Oracle Remote Administration Daemon
hnmp		6790/tcp/udp	# HNMP
hnm		6791/tcp/udp	# Halcyon Network Manager
acnet		6801/tcp/udp	# ACNET Control System Protocol
pentbox-sim	6817/tcp	# PenTBox Secure IM Protocol
ambit-lm	6831/tcp/udp
netmo-default	6841/tcp/udp	# Netmo Default
netmo-http	6842/tcp/udp	# Netmo HTTP
iccrushmore	6850/tcp/udp	# ICCRUSHMORE
acctopus-cc	6868/tcp	# Acctopus Command Channel
acctopus-st	6868/udp	# Acctopus Status
muse		6888/tcp/udp	# MUSE
rtimeviewer	6900/tcp	# R*TIME Viewer Data Interface
jetstream	6901/tcp	# Novell Jetstream messaging protocol
ethoscan	6935/tcp/udp	# EthoScan Service
xsmsvc		6936/tcp/udp	# XenSource Management Service
bioserver	6946/tcp/udp	# Biometrics Server
otlp		6951/tcp/udp	# OTLP
jmact3		6961/tcp/udp	# JMACT3
jmevt2		6962/tcp/udp
swismgr1	6963/tcp/udp
swismgr2	6964/tcp/udp
swistrap	6965/tcp/udp
swispol		6966/tcp/udp
acmsoda		6969/tcp/udp
conductor	6970/tcp	# Conductor test coordination protocol
conductor-mpx	6970/sctp	# conductor for multiplex
MobilitySrv	6997/tcp/udp	# Mobility XE Protocol
iatp-highpri	6998/tcp/udp	# IATP-highPri
iatp-normalpri	6999/tcp/udp	# IATP-normalPri
afs3-fileserver	7000/tcp/udp	# file server itself
afs3-callback	7001/tcp/udp	# callbacks to cache managers
afs3-prserver	7002/tcp/udp	# users & groups database
afs3-vlserver	7003/tcp/udp	# volume location database
afs3-kaserver	7004/tcp/udp	# AFS/Kerberos authentication service
afs3-volser	7005/tcp/udp	# volume managment server
afs3-errors	7006/tcp/udp	# error interpretation service
afs3-bos	7007/tcp/udp	# basic overseer process
afs3-update	7008/tcp/udp	# server-to-server updater
afs3-rmtsys	7009/tcp/udp	# remote cache manager service
ups-onlinet	7010/tcp/udp	# onlinet uninterruptable power supplies
talon-disc	7011/tcp/udp	# Talon Discovery Port
talon-engine	7012/tcp/udp	# Talon Engine
microtalon-dis	7013/tcp/udp	# Microtalon Discovery
microtalon-com	7014/tcp/udp	# Microtalon Communications
talon-webserver	7015/tcp/udp	# Talon Webserver
spg		7016/tcp/udp	# SPG Controls Carrier
grasp		7017/tcp/udp	# GeneRic Autonomic Signaling Protocol
fisa-svc	7018/tcp	# FISA Service
doceri-ctl	7019/tcp	# doceri drawing service control
doceri-view	7019/udp	# doceri drawing service screen view
dpserve		7020/tcp/udp	# DP Serve
dpserveadmin	7021/tcp/udp	# DP Serve Admin
ctdp		7022/tcp/udp	# CT Discovery Protocol
ct2nmcs		7023/tcp/udp	# Comtech T2 NMCS
vmsvc		7024/tcp/udp	# Vormetric service
vmsvc-2		7025/tcp/udp	# Vormetric Service II
loreji-panel	7026/tcp	# Loreji Webhosting Panel
op-probe	7030/tcp/udp	# ObjectPlanet probe
iposplanet	7031/tcp	# IPOSPLANET retailing multi devices protocol
quest-disc	7040/udp	# Quest application level network service discovery
arcp		7070/tcp/udp	# ARCP
iwg1		7071/tcp/udp	# IWGADTS Aircraft Housekeeping Message
iba-cfg		7072/tcp	# iba Device Configuration Protocol
iba-cfg-disc	7072/udp	# iba Device Configuration Protocol
martalk		7073/tcp	# MarTalk protocol
empowerid	7080/tcp/udp	# EmpowerID Communication
zixi-transport	7088/udp	# Zixi live video transport protocol
jdp-disc	7095/udp	# Java Discovery Protocol
lazy-ptop	7099/tcp/udp
font-service	7100/tcp/udp	# X Font Service
elcn		7101/tcp/udp	# Embedded Light Control Network
aes-x170	7107/udp	# AES-X170
rothaga		7117/tcp	# Encrypted chat and file transfer service
virprot-lm	7121/tcp/udp	# Virtual Prototypes License Manager
scenidm		7128/tcp/udp	# intelligent data manager
scenccs		7129/tcp/udp	# Catalog Content Search
cabsm-comm	7161/tcp/udp	# CA BSM Comm
caistoragemgr	7162/tcp/udp	# CA Storage Manager
cacsambroker	7163/tcp/udp	# CA Connection Broker
fsr		7164/tcp/udp	# File System Repository Agent
doc-server	7165/tcp/udp	# Document WCF Server
aruba-server	7166/tcp/udp	# Aruba eDiscovery Server
casrmagent	7167/tcp	# CA SRM Agent
cnckadserver	7168/tcp	# cncKadServer DB & Inventory Services
ccag-pib	7169/tcp/udp	# Consequor Consulting Process Integration Bridge
nsrp		7170/tcp/udp	# Adaptive Name/Service Resolution
drm-production	7171/tcp/udp	# Discovery and Retention Mgt Production
metalbend	7172/tcp	# Port used for MetalBend programmable interface
zsecure		7173/tcp	# zSecure Server
clutild		7174/tcp/udp	# Clutild
janus-disc	7181/udp	# Janus Guidewire Enterprise Discovery Service Bus
fodms		7200/tcp/udp	# FODMS FLIP
dlip		7201/tcp/udp	# DLIP
pon-ictp	7202/tcp	# Inter-Channel Termination Protocol (ICTP) for multi-wavelength PON (Passive Optical Network) systems
PS-Server	7215/tcp	# Communication ports for PaperStream Server services
PS-Capture-Pro	7216/tcp	# PaperStream Capture Professional
ramp		7227/tcp/udp	# Registry A & M Protocol
citrixupp	7228/tcp	# Citrix Universal Printing Port
citrixuppg	7229/tcp	# Citrix UPP Gateway
aspcoordination	7235/udp	# ASP Coordination Protocol
display		7236/tcp	# Wi-Fi Alliance Wi-Fi Display Protocol
pads		7237/tcp	# PADS (Public Area Display System) Server
frc-hicp	7244/tcp	# FrontRow Calypso Human Interface Control Protocol
frc-hicp-disc	7244/udp	# FrontRow Calypso Human Interface Control Protocol
cnap		7262/tcp/udp	# Calypso Network Access Protocol
watchme-7272	7272/tcp/udp	# WatchMe Monitoring 7272
oma-rlp		7273/tcp/udp	# OMA Roaming Location
oma-rlp-s	7274/tcp/udp	# OMA Roaming Location SEC
oma-ulp		7275/tcp/udp	# OMA UserPlane Location
oma-ilp		7276/tcp/udp	# OMA Internal Location Protocol
oma-ilp-s	7277/tcp/udp	# OMA Internal Location Secure Protocol
oma-dcdocbs	7278/tcp/udp	# OMA Dynamic Content Delivery over CBS
ctxlic		7279/tcp/udp	# Citrix Licensing
itactionserver1	7280/tcp/udp	# ITACTIONSERVER 1
itactionserver2	7281/tcp/udp	# ITACTIONSERVER 2
mzca-action	7282/tcp	# eventACTION/ussACTION (MZCA) server
mzca-alert	7282/udp	# eventACTION/ussACTION (MZCA) alert
genstat		7283/tcp	# General Statistics Rendezvous Protocol
lcm-server	7365/tcp/udp	# LifeKeeper Communications
mindfilesys	7391/tcp/udp	# mind-file system server
mrssrendezvous	7392/tcp/udp	# mrss-rendezvous server
nfoldman	7393/tcp/udp	# nFoldMan Remote Publish
fse		7394/tcp/udp	# File system export of backup images
winqedit	7395/tcp/udp
hexarc		7397/tcp/udp	# Hexarc Command Language
rtps-discovery	7400/tcp/udp	# RTPS Discovery
rtps-dd-ut	7401/tcp/udp	# RTPS Data-Distribution User-Traffic
rtps-dd-mt	7402/tcp/udp	# RTPS Data-Distribution Meta-Traffic
ionixnetmon	7410/tcp/udp	# Ionix Network Monitor
daqstream	7411/tcp/udp	# Streaming of measurement data
ipluminary	7420/udp	# Multichannel real-time lighting control
mtportmon	7421/tcp/udp	# Matisse Port Monitor
pmdmgr		7426/tcp/udp	# OpenView DM Postmaster Manager
oveadmgr	7427/tcp/udp	# OpenView DM Event Agent Manager
ovladmgr	7428/tcp/udp	# OpenView DM Log Agent Manager
opi-sock	7429/tcp/udp	# OpenView DM rqt communication
xmpv7		7430/tcp/udp	# OpenView DM xmpv7 api pipe
pmd		7431/tcp/udp	# OpenView DM ovc/xmpv3 api pipe
faximum		7437/tcp/udp	# Faximum
oracleas-https	7443/tcp/udp	# Oracle Application Server HTTPS
sttunnel	7471/tcp	# Stateless Transport Tunneling Protocol
rise		7473/tcp/udp	# Rise: The Vieneo Province
neo4j		7474/tcp	# Neo4j Graph Database
openit		7478/tcp	# IT Asset Management
telops-lmd	7491/tcp/udp
silhouette	7500/tcp/udp	# Silhouette User
ovbus		7501/tcp/udp	# HP OpenView Bus Daemon
adcp		7508/tcp	# Automation Device Configuration Protocol
acplt		7509/tcp	# ACPLT - process automation service
ovhpas		7510/tcp/udp	# HP OpenView Application Server
pafec-lm	7511/tcp/udp
saratoga	7542/tcp/udp	# Saratoga Transfer Protocol
atul		7543/tcp/udp	# atul server
nta-ds		7544/tcp/udp	# FlowAnalyzer DisplayServer
nta-us		7545/tcp/udp	# FlowAnalyzer UtilityServer
cfs		7546/tcp/udp	# Cisco Fabric service
cwmp		7547/tcp/udp	# DSL Forum CWMP
tidp		7548/tcp/udp	# Threat Information Distribution Protocol
nls-tl		7549/tcp/udp	# Network Layer Signaling Transport Layer
cloudsignaling	7550/udp	# Cloud Signaling Service
controlone-con	7551/tcp	# ControlONE Console signaling
sncp		7560/tcp/udp	# Sniffer Command Protocol
cfw		7563/tcp	# Control Framework
vsi-omega	7566/tcp/udp	# VSI Omega
dell-eql-asm	7569/tcp	# Dell EqualLogic Host Group Management
aries-kfinder	7570/tcp/udp	# Aries Kfinder
coherence	7574/tcp	# Oracle Coherence Cluster Service
coherence-disc	7574/udp	# Oracle Coherence Cluster discovery service
sun-lm		7588/tcp/udp	# Sun License Manager
mipi-debug	7606/tcp/udp	# MIPI Alliance Debug
indi		7624/tcp/udp	# Instrument Neutral Distributed Interface
simco		7626/tcp/sctp	# SImple Middlebox COnfiguration (SIMCO) Server
soap-http	7627/tcp/udp	# SOAP Service Port
zen-pawn	7628/tcp/udp	# Primary Agent Work Notification
xdas		7629/tcp/udp	# OpenXDAS Wire Protocol
hawk		7630/tcp	# HA Web Konsole
tesla-sys-msg	7631/tcp	# TESLA System Messaging
pmdfmgt		7633/tcp/udp	# PMDF Management
cuseeme		7648/tcp/udp	# bonjour-cuseeme
rome		7663/tcp/udp	# Proprietary immutable distributed data storage
imqstomp	7672/tcp	# iMQ STOMP Server
imqstomps	7673/tcp	# iMQ STOMP Server over SSL
imqtunnels	7674/tcp/udp	# iMQ SSL tunnel
imqtunnel	7675/tcp/udp	# iMQ Tunnel
imqbrokerd	7676/tcp/udp	# iMQ Broker Rendezvous
sun-user-https	7677/tcp/udp	# Sun App Server - HTTPS
pando-pub	7680/tcp/udp	# Pando Media Public Distribution
dmt		7683/tcp	# Cleondris DMT
bolt		7687/tcp	# Bolt database connection
collaber	7689/tcp/udp	# Collaber Network Service
klio		7697/tcp/udp	# KLIO communications
em7-secom	7700/tcp	# EM7 Secure Communications
nfapi		7701/sctp	# SCF nFAPI defining MAC/PHY split
sync-em7	7707/tcp/udp	# EM7 Dynamic Updates
scinet		7708/tcp/udp	# scientia.net
medimageportal	7720/tcp/udp	# MedImage Portal
nsdeepfreezectl	7724/tcp/udp	# Novell Snap-in Deep Freeze Control
nitrogen	7725/tcp/udp	# Nitrogen Service
freezexservice	7726/tcp/udp	# FreezeX Console Service
trident-data	7727/tcp/udp	# Trident Systems Data
osvr		7728/tcp/udp/sctp	# Open-Source Virtual Reality
smip		7734/tcp/udp	# Smith Protocol over IP
aiagent		7738/tcp/udp	# HP Enterprise Discovery Agent
scriptview	7741/tcp/udp	# ScriptView Network
msss		7742/tcp	# Mugginsoft Script Server Service
sstp-1		7743/tcp/udp	# Sakura Script Transfer Protocol
raqmon-pdu	7744/tcp/udp	# RAQMON PDU
prgp		7747/tcp/udp	# Put/Run/Get Protocol
inetfs		7775/tcp	# A File System using TLS over a wide area network
cbt		7777/tcp/udp
interwise	7778/tcp/udp	# Interwise
vstat		7779/tcp/udp	# VSTAT
accu-lmgr	7781/tcp/udp
s-bfd		7784/udp	# Seamless Bidirectional Forwarding Detection (S-BFD)
minivend	7786/tcp/udp	# MINIVEND
popup-reminders	7787/tcp/udp	# Popup Reminders Receive
office-tools	7789/tcp/udp	# Office Tools Pro Receive
q3ade		7794/tcp/udp	# Q3ADE Cluster Service
pnet-conn	7797/tcp/udp	# Propel Connector port
pnet-enc	7798/tcp/udp	# Propel Encoder port
altbsdp		7799/tcp/udp	# Alternate BSDP Service
asr		7800/tcp/udp	# Apple Software Restore
ssp-client	7801/tcp/udp	# Secure Server Protocol - client
vns-tp		7802/udp	# Virtualized Network Services Tunnel Protocol
rbt-wanopt	7810/tcp/udp	# Riverbed WAN Optimization Protocol
apc-7845	7845/tcp/udp	# APC 7845
apc-7846	7846/tcp/udp	# APC 7846
csoauth		7847/tcp	# A product key authentication protocol made by CSO
mobileanalyzer	7869/tcp	# MobileAnalyzer& MobileMonitor
rbt-smc		7870/tcp	# Riverbed Steelhead Mobile Service
mdm		7871/tcp	# Mobile Device Management
mipv6tls	7872/udp	# TLS-based Mobile IPv6 Security
owms		7878/tcp	# Opswise Message Service
pss		7880/tcp/udp	# Pearson
ubroker		7887/tcp/udp	# Universal Broker
mevent		7900/tcp/udp	# Multicast Event
tnos-sp		7901/tcp/udp	# TNOS Service Protocol
tnos-dp		7902/tcp/udp	# TNOS shell Protocol
tnos-dps	7903/tcp/udp	# TNOS Secure DiaguardProtocol
qo-secure	7913/tcp/udp	# QuickObjects secure port
t2-drm		7932/tcp/udp	# Tier 2 Data Resource Manager
t2-brm		7933/tcp/udp	# Tier 2 Business Rules Manager
generalsync	7962/tcp/udp	# Encrypted, extendable, general-purpose synchronization protocol
supercell	7967/tcp/udp	# Supercell
micromuse-ncps	7979/tcp/udp	# Micromuse-ncps
quest-vista	7980/tcp/udp	# Quest Vista
sossd-collect	7981/tcp	# Spotlight on SQL Server Desktop Collect
sossd-agent	7982/tcp	# Spotlight on SQL Server Desktop Agent
sossd-disc	7982/udp	# Spotlight on SQL Server Desktop Agent Discovery
pushns		7997/tcp	# PUSH Notification Service
usicontentpush	7998/udp	# USI Content Push Service
irdmi2		7999/tcp/udp	# iRDMI2
irdmi		8000/tcp/udp	# iRDMI
vcom-tunnel	8001/tcp/udp	# VCOM Tunnel
teradataordbms	8002/tcp/udp	# Teradata ORDBMS
mcreport	8003/tcp/udp	# Mulberry Connect Reporting Service
p2pevolvenet	8004/tcp	# Opensource Evolv Enterprise Platform P2P Network Node Connection Protocol
mxi		8005/tcp/udp	# MXI Generation II for z/OS
wpl-analytics	8006/tcp	# World Programming analytics
wpl-disc	8006/udp	# World Programming analytics discovery
warppipe	8007/tcp/udp	# I/O oriented cluster computing software
http-alt	8008/tcp/udp	# HTTP Alternate
nvme-disc	8009/tcp	# NVMe over Fabrics Discovery Service
qbdb		8019/tcp/udp	# QB DB Dynamic Port
intu-ec-svcdisc	8020/tcp/udp	# Intuit Entitlement Service and Discovery
intu-ec-client	8021/tcp/udp	# Intuit Entitlement Client
oa-system	8022/tcp/udp
arca-api	8023/tcp/udp	# ARCATrust vault API
ca-audit-da	8025/tcp/udp	# CA Audit Distribution Agent
ca-audit-ds	8026/tcp/udp	# CA Audit Distribution Server
pro-ed		8032/tcp/udp	# ProEd
mindprint	8033/tcp/udp	# MindPrint
vantronix-mgmt	8034/tcp/udp	# .vantronix Management
ampify		8040/tcp/udp	# Ampify Messaging Protocol
enguity-xccetp	8041/tcp/udp	# Xcorpeon ASIC Carrier Ethernet Transport
fs-agent	8042/tcp	# FireScope Agent
fs-server	8043/tcp	# FireScope Server
fs-mgmt		8044/tcp	# FireScope Management Interface
rocrail		8051/tcp	# Rocrail Client Service
senomix01	8052/tcp/udp	# Senomix Timesheets Server
senomix02	8053/tcp/udp	# Senomix Timesheets Client [1 year assignment]
senomix03	8054/tcp/udp	# Senomix Timesheets Server [1 year assignment]
senomix04	8055/tcp/udp	# Senomix Timesheets Server [1 year assignment]
senomix05	8056/tcp/udp	# Senomix Timesheets Server [1 year assignment]
senomix06	8057/tcp/udp	# Senomix Timesheets Client [1 year assignment]
senomix07	8058/tcp/udp	# Senomix Timesheets Client [1 year assignment]
senomix08	8059/tcp/udp	# Senomix Timesheets Client [1 year assignment]
aero		8060/udp	# Asymmetric Extended Route Optimization (AERO)
toad-bi-appsrvr	8066/tcp	# Toad BI Application Server
infi-async	8067/tcp	# Infinidat async replication
ucs-isc		8070/tcp	# Oracle Unified Communication Suite's Indexed Search Converter
gadugadu	8074/tcp/udp	# Gadu-Gadu
mles		8077/tcp	# Mles is a client-server data distribution protocol targeted to serve as a lightweight and reliable distributed publish/subscribe database service.
http-alt	8080/tcp/udp	# HTTP Alternate (see port 80)
sunproxyadmin	8081/tcp/udp	# Sun Proxy Admin Service
us-cli		8082/tcp/udp	# Utilistor (Client)
us-srv		8083/tcp/udp	# Utilistor (Server)
d-s-n		8086/tcp/udp	# Distributed SCADA Networking Rendezvous Port
simplifymedia	8087/tcp/udp	# Simplify Media SPP Protocol
radan-http	8088/tcp/udp	# Radan HTTP
opsmessaging	8090/tcp	# Vehicle to station messaging
jamlink		8091/tcp	# Jam Link Framework
sac		8097/tcp/udp	# SAC Port Id
xprint-server	8100/tcp/udp	# Xprint Server
ldoms-migr	8101/tcp	# Logical Domains Migration
kz-migr		8102/tcp	# Oracle Kernel zones migration server
mtl8000-matrix	8115/tcp/udp	# MTL8000 Matrix
cp-cluster	8116/tcp/udp	# Check Point Clustering
purityrpc	8117/tcp	# Purity replication clustering and remote management
privoxy		8118/tcp/udp	# Privoxy HTTP proxy
apollo-data	8121/tcp/udp	# Apollo Data Port
apollo-admin	8122/tcp/udp	# Apollo Admin Port
paycash-online	8128/tcp/udp	# PayCash Online Protocol
paycash-wbp	8129/tcp/udp	# PayCash Wallet-Browser
indigo-vrmi	8130/tcp/udp	# INDIGO-VRMI
indigo-vbcp	8131/tcp/udp	# INDIGO-VBCP
dbabble		8132/tcp/udp
puppet		8140/tcp	# The Puppet master service
isdd		8148/tcp/udp	# i-SDD file transfer
eor-game	8149/udp	# Edge of Reality game data
quantastor	8153/tcp	# QuantaStor Management Interface
patrol		8160/tcp/udp	# Patrol
patrol-snmp	8161/tcp/udp	# Patrol SNMP
lpar2rrd	8162/tcp	# LPAR2RRD client server communication
intermapper	8181/tcp	# Intermapper network management system
vmware-fdm	8182/tcp/udp	# VMware Fault Domain Manager
proremote	8183/tcp	# ProRemote
itach		8184/tcp/udp	# Remote iTach Connection
gcp-rphy	8190/tcp	# Generic control plane for RPHY
limnerpressure	8191/tcp	# Limner Pressure
spytechphone	8192/tcp/udp	# SpyTech Phone Service
blp1		8194/tcp/udp	# Bloomberg data API
blp2		8195/tcp/udp	# Bloomberg feed
vvr-data	8199/tcp/udp	# VVR DATA
trivnet1	8200/tcp/udp	# TRIVNET
trivnet2	8201/tcp/udp	# TRIVNET
aesop		8202/udp	# Audio+Ethernet Standard Open Protocol
lm-perfworks	8204/tcp/udp	# LM Perfworks
lm-instmgr	8205/tcp/udp	# LM Instmgr
lm-dta		8206/tcp/udp	# LM Dta
lm-sserver	8207/tcp/udp	# LM SServer
lm-webwatcher	8208/tcp/udp	# LM Webwatcher
rexecj		8230/tcp/udp	# RexecJ Server
hncp-udp-port	8231/udp	# HNCP
hncp-dtls-port	8232/udp	# HNCP over DTLS
synapse-nhttps	8243/tcp/udp	# Synapse Non Blocking HTTPS
robot-remote	8270/tcp	# Robot Framework Remote Library Interface
pando-sec	8276/tcp/udp	# Pando Media Controlled Distribution
synapse-nhttp	8280/tcp/udp	# Synapse Non Blocking HTTP
libelle		8282/tcp	# Libelle EnterpriseBus
libelle-disc	8282/udp	# Libelle EnterpriseBus discovery
blp3		8292/tcp/udp	# Bloomberg professional
hiperscan-id	8293/tcp	# Hiperscan Identification Service
blp4		8294/tcp/udp	# Bloomberg intelligent client
tmi		8300/tcp/udp	# Transport Management Interface
amberon		8301/tcp/udp	# Amberon PPC/PPS
hub-open-net	8313/tcp	# Hub Open Network
tnp-discover	8320/tcp/udp	# Thin(ium) Network Protocol
tnp		8321/tcp/udp	# Thin(ium) Network Protocol
garmin-marine	8322/tcp/udp	# Garmin Marine
server-find	8351/tcp/udp	# Server Find
cruise-enum	8376/tcp/udp	# Cruise ENUM
cruise-swroute	8377/tcp/udp	# Cruise SWROUTE
cruise-config	8378/tcp/udp	# Cruise CONFIG
cruise-diags	8379/tcp/udp	# Cruise DIAGS
cruise-update	8380/tcp/udp	# Cruise UPDATE
m2mservices	8383/tcp/udp	# M2m Services
marathontp	8384/udp	# Marathon Transport Protocol
cvd		8400/tcp/udp
sabarsd		8401/tcp/udp
abarsd		8402/tcp/udp
admind		8403/tcp/udp
svcloud		8404/tcp	# SuperVault Cloud
svbackup	8405/tcp	# SuperVault Backup
dlpx-sp		8415/tcp	# Delphix Session Protocol
espeech		8416/tcp/udp	# eSpeech Session Protocol
espeech-rtp	8417/tcp/udp	# eSpeech RTP Protocol
aritts		8423/tcp	# Aristech text-to-speech server
cybro-a-bus	8442/tcp/udp	# CyBro A-bus Protocol
pcsync-https	8443/tcp/udp	# PCsync HTTPS
pcsync-http	8444/tcp/udp	# PCsync HTTP
copy		8445/tcp	# Port for copy peer sync feature
copy-disc	8445/udp	# Port for copy discovery
npmp		8450/tcp/udp
nexentamv	8457/tcp	# Nexenta Management GUI
cisco-avp	8470/tcp	# Cisco Address Validation Protocol
pim-port	8471/tcp/sctp	# PIM over Reliable Transport
otv		8472/tcp/udp	# Overlay Transport Virtualization (OTV)
vp2p		8473/tcp/udp	# Virtual Point to Point
noteshare	8474/tcp/udp	# AquaMinds NoteShare
fmtp		8500/tcp/udp	# Flight Message Transfer Protocol
cmtp-mgt	8501/tcp	# CYTEL Message Transfer Management
cmtp-av		8501/udp	# CYTEL Message Transfer Audio and Video
ftnmtp		8502/tcp	# FTN Message Transfer Protocol
lsp-self-ping	8503/udp	# MPLS LSP Self-Ping
rtsp-alt	8554/tcp/udp	# RTSP Alternate (see port 554)
d-fence		8555/tcp/udp	# SYMAX D-FENCE
dof-tunnel	8567/tcp/udp	# DOF Tunneling Protocol
asterix		8600/tcp/udp	# Surveillance Data
canon-cpp-disc	8609/udp	# Canon Compact Printer Protocol Discovery
canon-mfnp	8610/tcp/udp	# Canon MFNP Service
canon-bjnp1	8611/tcp/udp	# Canon BJNP Port 1
canon-bjnp2	8612/tcp/udp	# Canon BJNP Port 2
canon-bjnp3	8613/tcp/udp	# Canon BJNP Port 3
canon-bjnp4	8614/tcp/udp	# Canon BJNP Port 4
imink		8615/tcp	# Imink Service Control
monetra		8665/tcp	# Monetra
monetra-admin	8666/tcp	# Monetra Administrative Access
msi-cps-rm	8675/tcp	# Motorola Solutions Customer Programming Software for Radio Management
msi-cps-rm-disc	8675/udp	# Motorola Solutions Customer Programming Software for Radio Management Discovery
sun-as-jmxrmi	8686/tcp/udp	# Sun App Server - JMX/RMI
openremote-ctrl	8688/tcp	# OpenRemote Controller HTTP/REST
vnyx		8699/tcp	# VNYX Primary Port
nvc		8711/tcp	# Nuance Voice Control
dtp-net		8732/udp	# DASGIP Net Services
ibus		8733/tcp/udp	# iBus
dey-keyneg	8750/tcp	# DEY Storage Key Negotiation
mc-appserver	8763/tcp/udp	# MC-APPSERVER
openqueue	8764/tcp/udp	# OPENQUEUE
ultraseek-http	8765/tcp/udp	# Ultraseek HTTP
amcs		8766/tcp/udp	# Agilent Connectivity Service
dpap		8770/tcp/udp	# Digital Photo Access Protocol (iPhoto)
uec		8778/tcp	# Stonebranch Universal Enterprise Controller
msgclnt		8786/tcp/udp	# Message Client
msgsrvr		8787/tcp/udp	# Message Server
acd-pm		8793/tcp/udp	# Accedian Performance Measurement
sunwebadmin	8800/tcp/udp	# Sun Web Server Admin Service
truecm		8804/tcp/udp
pfcp		8805/udp	# Destination Port number for PFCP
ssports-bcast	8808/udp	# STATSports Broadcast Service
dxspider	8873/tcp/udp	# dxspider linking protocol
cddbp-alt	8880/tcp/udp	# CDDBP
galaxy4d	8881/tcp	# Galaxy4D Online Game Engine
secure-mqtt	8883/tcp/udp	# Secure MQTT
ddi-tcp-1	8888/tcp	# NewsEDGE server TCP (TCP 1)
ddi-udp-1	8888/udp	# NewsEDGE server UDP (UDP 1)
ddi-tcp-2	8889/tcp	# Desktop Data TCP 1
ddi-udp-2	8889/udp	# NewsEDGE server broadcast
ddi-tcp-3	8890/tcp	# Desktop Data TCP 2
ddi-udp-3	8890/udp	# NewsEDGE client broadcast
ddi-tcp-4	8891/tcp	# Desktop Data TCP 3: NESS application
ddi-udp-4	8891/udp	# Desktop Data UDP 3: NESS application
ddi-tcp-5	8892/tcp	# Desktop Data TCP 4: FARM product
ddi-udp-5	8892/udp	# Desktop Data UDP 4: FARM product
ddi-tcp-6	8893/tcp	# Desktop Data TCP 5: NewsEDGE/Web application
ddi-udp-6	8893/udp	# Desktop Data UDP 5: NewsEDGE/Web application
ddi-tcp-7	8894/tcp	# Desktop Data TCP 6: COAL application
ddi-udp-7	8894/udp	# Desktop Data UDP 6: COAL application
ospf-lite	8899/tcp/udp
jmb-cds1	8900/tcp/udp	# JMB-CDS 1
jmb-cds2	8901/tcp/udp	# JMB-CDS 2
manyone-http	8910/tcp/udp
manyone-xml	8911/tcp/udp
wcbackup	8912/tcp/udp	# Windows Client Backup
dragonfly	8913/tcp/udp	# Dragonfly System Service
twds		8937/tcp	# Transaction Warehouse Data Service
ub-dns-control	8953/tcp	# unbound dns nameserver control
cumulus-admin	8954/tcp/udp	# Cumulus Admin Port
nod-provider	8980/tcp/udp	# Network of Devices Provider
nod-client	8981/udp	# Network of Devices Client
sunwebadmins	8989/tcp/udp	# Sun Web Server SSL Admin Service
http-wmap	8990/tcp/udp	# webmail HTTP service
https-wmap	8991/tcp/udp	# webmail HTTPS service
oracle-ms-ens	8997/tcp	# Oracle Messaging Server Event Notification Service
canto-roboflow	8998/tcp	# Canto RoboFlow Control
bctp		8999/tcp/udp	# Brodos Crypto Trade Protocol
cslistener	9000/tcp/udp	# CSlistener
etlservicemgr	9001/tcp/udp	# ETL Service Manager
dynamid		9002/tcp/udp	# DynamID authentication
golem		9005/tcp	# Golem Inter-System RPC
ogs-client	9007/udp	# Open Grid Services Client
ogs-server	9008/tcp	# Open Grid Services Server
pichat		9009/tcp/udp	# Pichat Server
sdr		9010/tcp	# Secure Data Replicator Protocol
d-star		9011/udp	# D-Star Routing digital voice+data for amateur radio
tambora		9020/tcp/udp	# TAMBORA
panagolin-ident	9021/tcp/udp	# Pangolin Identification
paragent	9022/tcp/udp	# PrivateArk Remote Agent
swa-1		9023/tcp/udp	# Secure Web Access - 1
swa-2		9024/tcp/udp	# Secure Web Access - 2
swa-3		9025/tcp/udp	# Secure Web Access - 3
swa-4		9026/tcp/udp	# Secure Web Access - 4
versiera	9050/tcp	# Versiera Agent Listener
fio-cmgmt	9051/tcp	# Fusion-io Central Manager Service
CardWeb-IO	9060/tcp	# CardWeb request-response I/O exchange
CardWeb-RT	9060/udp	# CardWeb realtime device data
glrpc		9080/tcp/udp	# Groove GLRPC
cisco-aqos	9081/udp	# Required for Adaptive Quality of Service
lcs-ap		9082/sctp	# LCS Application Protocol
emc-pp-mgmtsvc	9083/tcp	# EMC PowerPath Mgmt Service
aurora		9084/tcp/udp/sctp	# IBM AURORA Performance Visualizer
ibm-rsyscon	9085/tcp/udp	# IBM Remote System Console
net2display	9086/tcp/udp	# Vesa Net2Display
classic		9087/tcp/udp	# Classic Data Server
sqlexec		9088/tcp/udp	# IBM Informix SQL Interface
sqlexec-ssl	9089/tcp/udp	# IBM Informix SQL Interface - Encrypted
websm		9090/tcp/udp	# WebSM
xmltec-xmlmail	9091/tcp/udp
XmlIpcRegSvc	9092/tcp/udp	# Xml-Ipc Server Reg
copycat		9093/tcp	# Copycat database replication service
hp-pdl-datastr	9100/tcp/udp	# PDL Data Streaming Port
bacula-dir	9101/tcp/udp	# Bacula Director
bacula-fd	9102/tcp/udp	# Bacula File Daemon
bacula-sd	9103/tcp/udp	# Bacula Storage Daemon
peerwire	9104/tcp/udp	# PeerWire
xadmin		9105/tcp/udp	# Xadmin Control Service
astergate	9106/tcp	# Astergate Control Service
astergate-disc	9106/udp	# Astergate Discovery Service
astergatefax	9107/tcp	# AstergateFax Control Service
mxit		9119/tcp/udp	# MXit Instant Messaging
grcmp		9122/tcp	# Global Relay compliant mobile instant messaging protocol
grcp		9123/tcp	# Global Relay compliant instant messaging protocol
dddp		9131/tcp/udp	# Dynamic Device Discovery
apani1		9160/tcp/udp
apani2		9161/tcp/udp
apani3		9162/tcp/udp
apani4		9163/tcp/udp
apani5		9164/tcp/udp
sun-as-jpda	9191/tcp/udp	# Sun AppSvr JPDA
wap-wsp		9200/tcp/udp	# WAP connectionless session service
wap-wsp-wtp	9201/tcp/udp	# WAP session service
wap-wsp-s	9202/tcp/udp	# WAP secure connectionless session service
wap-wsp-wtp-s	9203/tcp/udp	# WAP secure session service
wap-vcard	9204/tcp/udp	# WAP vCard
wap-vcal	9205/tcp/udp	# WAP vCal
wap-vcard-s	9206/tcp/udp	# WAP vCard Secure
wap-vcal-s	9207/tcp/udp	# WAP vCal Secure
rjcdb-vcards	9208/tcp/udp	# rjcdb vCard
almobile-system	9209/tcp/udp	# ALMobile System Service
oma-mlp		9210/tcp/udp	# OMA Mobile Location Protocol
oma-mlp-s	9211/tcp/udp	# OMA Mobile Location Protocol Secure
serverviewdbms	9212/tcp/udp	# Server View dbms access
serverstart	9213/tcp/udp	# ServerStart RemoteControl
ipdcesgbs	9214/tcp/udp	# IPDC ESG BootstrapService
insis		9215/tcp/udp	# Integrated Setup and Install Service
acme		9216/tcp/udp	# Aionex Communication Management Engine
fsc-port	9217/tcp/udp	# FSC Communication Port
teamcoherence	9222/tcp/udp	# QSC Team Coherence
mon		9255/tcp/udp	# Manager On Network
traingpsdata	9277/udp	# GPS Data transmitted from train to ground network
pegasus		9278/tcp/udp	# Pegasus GPS Platform
pegasus-ctl	9279/tcp/udp	# Pegaus GPS System Control Interface
pgps		9280/tcp/udp	# Predicted GPS
swtp-port1	9281/tcp/udp	# SofaWare transport port 1
swtp-port2	9282/tcp/udp	# SofaWare transport port 2
callwaveiam	9283/tcp/udp	# CallWaveIAM
visd		9284/tcp/udp	# VERITAS Information Serve
n2h2server	9285/tcp/udp	# N2H2 Filter Service Port
n2receive	9286/udp	# n2 monitoring receiver
cumulus		9287/tcp/udp	# Cumulus
armtechdaemon	9292/tcp/udp	# ArmTech Daemon
storview	9293/tcp/udp	# StorView Client
armcenterhttp	9294/tcp/udp	# ARMCenter http Service
armcenterhttps	9295/tcp/udp	# ARMCenter https Service
vrace		9300/tcp/udp	# Virtual Racing Service
sphinxql	9306/tcp	# Sphinx search server (MySQL listener)
sphinxapi	9312/tcp	# Sphinx search server
secure-ts	9318/tcp/udp	# PKIX TimeStamp over TLS
guibase		9321/tcp/udp
mpidcmgr	9343/tcp/udp	# MpIdcMgr
mphlpdmc	9344/tcp/udp	# Mphlpdmc
rancher		9345/tcp	# Rancher Agent
ctechlicensing	9346/tcp/udp	# C Tech Licensing
fjdmimgr	9374/tcp/udp
boxp		9380/tcp/udp	# Brivs! Open Extensible Protocol
d2dconfig	9387/tcp	# D2D Configuration Service
d2ddatatrans	9388/tcp	# D2D Data Transfer Service
adws		9389/tcp	# Active Directory Web Services
otp		9390/tcp	# OpenVAS Transfer Protocol
fjinvmgr	9396/tcp/udp
mpidcagt	9397/tcp/udp	# MpIdcAgt
sec-t4net-srv	9400/tcp/udp	# Samsung Twain for Network Server
sec-t4net-clt	9401/tcp/udp	# Samsung Twain for Network Client
sec-pc2fax-srv	9402/tcp/udp	# Samsung PC2FAX for Network Server
git		9418/tcp/udp	# git pack transfer service
tungsten-https	9443/tcp/udp	# WSO2 Tungsten HTTPS
wso2esb-console	9444/tcp/udp	# WSO2 ESB Administration Console HTTPS
mindarray-ca	9445/tcp	# MindArray Systems Console Agent
sntlkeyssrvr	9450/tcp/udp	# Sentinel Keys Server
ismserver	9500/tcp/udp
sma-spw		9522/udp	# SMA Speedwire
mngsuite	9535/tcp/udp	# Management Suite Remote Control
laes-bf		9536/tcp/udp	# Surveillance buffering function
trispen-sra	9555/tcp/udp	# Trispen Secure Remote Access
ldgateway	9592/tcp/udp	# LANDesk Gateway
cba8		9593/tcp/udp	# LANDesk Management Agent (cba8)
msgsys		9594/tcp/udp	# Message System
pds		9595/tcp/udp	# Ping Discovery Service
mercury-disc	9596/tcp/udp	# Mercury Discovery
pd-admin	9597/tcp/udp	# PD Administration
vscp		9598/tcp/udp	# Very Simple Ctrl Protocol
robix		9599/tcp/udp	# Robix
micromuse-ncpw	9600/tcp/udp	# MICROMUSE-NCPW
streamcomm-ds	9612/tcp/udp	# StreamComm User Directory
iadt-tls	9614/tcp	# iADT Protocol over TLS
erunbook-agent	9616/tcp	# eRunbook Agent
erunbook-server	9617/tcp	# eRunbook Server
condor		9618/tcp/udp	# Condor Collector Service
odbcpathway	9628/tcp/udp	# ODBC Pathway Service
uniport		9629/tcp/udp	# UniPort SSO Controller
peoctlr		9630/tcp	# Peovica Controller
peocoll		9631/tcp	# Peovica Collector
mc-comm		9632/udp	# Mobile-C Communications
pqsflows	9640/tcp	# ProQueSys Flows Service
zoomcp		9666/tcp	# Zoom Control Panel Game Server Management
xmms2		9667/tcp/udp	# Cross-platform Music Multiplexing System
tec5-sdctp	9668/tcp/udp	# tec5 Spectral Device Control Protocol
client-wakeup	9694/tcp/udp	# T-Mobile Client Wakeup Message
ccnx		9695/tcp/udp	# Content Centric Networking
board-roar	9700/tcp/udp	# Board M.I.T. Service
l5nas-parchan	9747/tcp/udp	# L5NAS Parallel Channel
board-voip	9750/tcp/udp	# Board M.I.T. Synchronous Collaboration
rasadv		9753/tcp/udp
tungsten-http	9762/tcp/udp	# WSO2 Tungsten HTTP
davsrc		9800/tcp/udp	# WebDav Source Port
sstp-2		9801/tcp/udp	# Sakura Script Transfer Protocol-2
davsrcs		9802/tcp/udp	# WebDAV Source TLS/SSL
sapv1		9875/tcp/udp	# Session Announcement v1
sd		9876/tcp	# Session Director
kca-service	9878/udp	# The KX509 Kerberized Certificate Issuance Protocol in Use in 2012
cyborg-systems	9888/tcp/udp	# CYBORG Systems
gt-proxy	9889/tcp/udp	# Port for Cable network related data proxy or repeater
monkeycom	9898/tcp/udp	# MonkeyCom
sctp-tunneling	9899/udp	# SCTP TUNNELING
iua		9900/tcp/udp/sctp	# IUA
enrp		9901/udp	# enrp server channel
enrp-sctp	9901/sctp	# enrp server channel
enrp-sctp-tls	9902/sctp	# enrp/tls server channel
multicast-ping	9903/udp	# Multicast Ping Protocol
domaintime	9909/tcp/udp
sype-transport	9911/tcp/udp	# SYPECom Transport Protocol
xybrid-cloud	9925/tcp	# XYBRID Cloud
apc-9950	9950/tcp/udp	# APC 9950
apc-9951	9951/tcp/udp	# APC 9951
apc-9952	9952/tcp/udp	# APC 9952
acis		9953/tcp/udp	# 9953
hinp		9954/tcp	# HaloteC Instrument Network Protocol
alljoyn-stm	9955/tcp	# Contact Port for AllJoyn standard messaging
alljoyn-mcm	9955/udp	# Contact Port for AllJoyn multiplexed constrained messaging
alljoyn		9956/udp	# Alljoyn Name Service
odnsp		9966/tcp/udp	# OKI Data Network Setting Protocol
xybrid-rt	9978/tcp	# XYBRID RT Server
visweather	9979/tcp	# Valley Information Systems Weather station data
pumpkindb	9981/tcp	# Event sourcing database engine with a built-in programming language
dsm-scm-target	9987/tcp/udp	# DSM/SCM Target Interface
nsesrvr		9988/tcp	# Software Essentials Secure HTTP server
osm-appsrvr	9990/tcp/udp	# OSM Applet Server
osm-oev		9991/tcp/udp	# OSM Event Server
palace-1	9992/tcp/udp	# OnLive-1
palace-2	9993/tcp/udp	# OnLive-2
palace-3	9994/tcp/udp	# OnLive-3
palace-4	9995/tcp/udp	# Palace-4
palace-5	9996/tcp/udp	# Palace-5
palace-6	9997/tcp/udp	# Palace-6
distinct32	9998/tcp/udp	# Distinct32
distinct	9999/tcp/udp
ndmp		10000/tcp/udp	# Network Data Management Protocol
scp-config	10001/tcp/udp	# SCP Configuration
documentum	10002/tcp/udp	# EMC-Documentum Content Server Product
documentum-s	10003/tcp/udp	# EMC-Documentum Content Server Product
emcrmirccd	10004/tcp	# EMC Replication Manager Client
emcrmird	10005/tcp	# EMC Replication Manager Server
netapp-sync	10006/tcp	# Sync replication protocol among different NetApp platforms
mvs-capacity	10007/tcp/udp	# MVS Capacity
octopus		10008/tcp/udp	# Octopus Multiplexer
swdtp-sv	10009/tcp/udp	# Systemwalker Desktop Patrol
rxapi		10010/tcp	# ooRexx rxapi services
abb-hw		10020/tcp	# Hardware configuration and maintenance
cefd-vmp	10023/udp	# Comtech EF-Data's Vipersat Management Protocol
zabbix-agent	10050/tcp/udp	# Zabbix Agent
zabbix-trapper	10051/tcp/udp	# Zabbix Trapper
qptlmd		10055/tcp	# Quantapoint FLEXlm Licensing Service
amanda		10080/tcp/udp	# Amanda
famdc		10081/tcp/udp	# FAM Archive Server
itap-ddtp	10100/tcp/udp	# VERITAS ITAP DDTP
ezmeeting-2	10101/tcp/udp	# eZmeeting
ezproxy-2	10102/tcp/udp	# eZproxy
ezrelay		10103/tcp/udp	# eZrelay
swdtp		10104/tcp/udp	# Systemwalker Desktop Patrol
bctp-server	10107/tcp/udp	# VERITAS BCTP, server
nmea-0183	10110/tcp/udp	# NMEA-0183 Navigational Data
nmea-onenet	10111/udp	# NMEA OneNet multicast messaging
netiq-endpoint	10113/tcp/udp	# NetIQ Endpoint
netiq-qcheck	10114/tcp/udp	# NetIQ Qcheck
netiq-endpt	10115/tcp/udp	# NetIQ Endpoint
netiq-voipa	10116/tcp/udp	# NetIQ VoIP Assessor
iqrm		10117/tcp/udp	# NetIQ IQCResource Managament Svc
cimple		10125/tcp	# HotLink CIMple REST API
bmc-perf-sd	10128/tcp/udp	# BMC-PERFORM-SERVICE DAEMON
bmc-gms		10129/tcp	# BMC General Manager Server
qb-db-server	10160/tcp/udp	# QB Database Server
snmptls		10161/tcp	# SNMP-TLS
snmpdtls	10161/udp	# SNMP-DTLS
snmptls-trap	10162/tcp	# SNMP-Trap-TLS
snmpdtls-trap	10162/udp	# SNMP-Trap-DTLS
trisoap		10200/tcp/udp	# Trigence AE Soap Service
rsms		10201/tcp	# Remote Server Management Service
rscs		10201/udp	# Remote Server Control and Test Service
apollo-relay	10252/tcp/udp	# Apollo Relay Port
eapol-relay	10253/udp	# Relay of EAPOL frames
axis-wimp-port	10260/tcp/udp	# Axis WIMP Port
tile-ml		10261/tcp	# Tile remote machine learning
blocks		10288/tcp/udp	# Blocks
cosir		10321/tcp	# Computer Op System Information Report
bngsync		10439/udp	# BalanceNG session table synchronization protocol
hip-nat-t	10500/udp	# HIP NAT-Traversal
MOS-lower	10540/tcp/udp	# MOS Media Object Metadata Port
MOS-upper	10541/tcp/udp	# MOS Running Order Port
MOS-aux		10542/tcp/udp	# MOS Low Priority Port
MOS-soap	10543/tcp/udp	# MOS SOAP Default Port
MOS-soap-opt	10544/tcp/udp	# MOS SOAP Optional Port
serverdocs	10548/tcp	# Apple Document Sharing Service
printopia	10631/tcp	# Printopia Serve
gap		10800/tcp/udp	# Gestor de Acaparamiento para Pocket PCs
lpdg		10805/tcp/udp	# LUCIA Pareja Data Group
nbd		10809/tcp	# Linux Network Block Device
nmc-disc	10810/udp	# Nuance Mobile Care Discovery
helix		10860/tcp/udp	# Helix Client/Server
bveapi		10880/tcp/udp	# BVEssentials HTTP API
octopustentacle	10933/tcp	# Listen port used by the Octopus Deploy Tentacle deployment agent
rmiaux		10990/tcp/udp	# Auxiliary RMI Port
irisa		11000/tcp/udp	# IRISA
metasys		11001/tcp/udp	# Metasys
weave		11095/tcp/udp	# Nest device-to-device and device-to-service application protocol
origo-sync	11103/tcp	# OrigoDB Server Sync Interface
netapp-icmgmt	11104/tcp	# NetApp Intercluster Management
netapp-icdata	11105/tcp	# NetApp Intercluster Data
sgi-lk		11106/tcp/udp	# SGI LK Licensing service
myq-termlink	11108/udp	# Hardware Terminals Discovery and Low-Level Communication Protocol
sgi-dmfmgr	11109/tcp	# Data migration facility Manager (DMF) is a browser based interface to DMF
sgi-soap	11110/tcp	# Data migration facility (DMF) SOAP is a web server protocol to support remote access to DMF
vce		11111/tcp/udp	# Viral Computing Environment (VCE)
dicom		11112/tcp/udp	# DICOM
suncacao-snmp	11161/tcp/udp	# sun cacao snmp access point
suncacao-jmxmp	11162/tcp/udp	# sun cacao JMX-remoting access point
suncacao-rmi	11163/tcp/udp	# sun cacao rmi registry access point
suncacao-csa	11164/tcp/udp	# sun cacao command-streaming access point
suncacao-websvc	11165/tcp/udp	# sun cacao web service access point
snss		11171/udp	# Surgical Notes Security Service Discovery (SNSS)
oemcacao-jmxmp	11172/tcp	# OEM cacao JMX-remoting access point
t5-straton	11173/tcp	# Straton Runtime Programing
oemcacao-rmi	11174/tcp	# OEM cacao rmi registry access point
oemcacao-websvc	11175/tcp	# OEM cacao web service access point
smsqp		11201/tcp/udp
dcsl-backup	11202/tcp	# DCSL Network Backup Services
wifree		11208/tcp/udp	# WiFree Service
memcache	11211/tcp/udp	# Memory cache service
imip		11319/tcp/udp	# IMIP
imip-channels	11320/tcp/udp	# IMIP Channels Port
arena-server	11321/tcp/udp	# Arena Server Listen
atm-uhas	11367/tcp/udp	# ATM UHAS
hkp		11371/tcp/udp	# OpenPGP HTTP Keyserver
lsdp		11430/udp	# Lenbrook Service Discovery Protocol
asgcypresstcps	11489/tcp	# ASG Cypress Secure Only
tempest-port	11600/tcp/udp	# Tempest Protocol Port
emc-xsw-dconfig	11623/tcp	# EMC XtremSW distributed config
h323callsigalt	11720/tcp/udp	# H.323 Call Control Signalling Alternate
emc-xsw-dcache	11723/tcp/udp	# EMC XtremSW distributed cache
intrepid-ssl	11751/tcp/udp	# Intrepid SSL
lanschool	11796/tcp	# LanSchool
lanschool-mpt	11796/udp	# Lanschool Multipoint
xoraya		11876/tcp/udp	# X2E Xoraya Multichannel protocol
x2e-disc	11877/udp	# X2E service discovery protocol
sysinfo-sp	11967/tcp/udp	# SysInfo Service Protocol
wmereceiving	11997/sctp	# WorldMailExpress
wmedistribution	11998/sctp	# WorldMailExpress
wmereporting	11999/sctp	# WorldMailExpress
entextxid	12000/tcp/udp	# IBM Enterprise Extender SNA XID Exchange
entextnetwk	12001/tcp/udp	# IBM Enterprise Extender SNA COS Network Priority
entexthigh	12002/tcp/udp	# IBM Enterprise Extender SNA COS High Priority
entextmed	12003/tcp/udp	# IBM Enterprise Extender SNA COS Medium Priority
entextlow	12004/tcp/udp	# IBM Enterprise Extender SNA COS Low Priority
dbisamserver1	12005/tcp/udp	# DBISAM Database Server - Regular
dbisamserver2	12006/tcp/udp	# DBISAM Database Server - Admin
accuracer	12007/tcp/udp	# Accuracer Database System Server
accuracer-dbms	12008/tcp/udp	# Accuracer Database System Admin
ghvpn		12009/udp	# Green Hills VPN
edbsrvr		12010/tcp	# ElevateDB Server
vipera		12012/tcp/udp	# Vipera Messaging Service
vipera-ssl	12013/tcp/udp	# Vipera Messaging Service over SSL Communication
rets-ssl	12109/tcp/udp	# RETS over SSL
nupaper-ss	12121/tcp/udp	# NuPaper Session Service
cawas		12168/tcp/udp	# CA Web Access Service
hivep		12172/tcp/udp	# HiveP
linogridengine	12300/tcp/udp	# LinoGrid Engine
rads		12302/tcp	# Remote Administration Daemon (RAD) is a system service that offers secure, remote, programmatic access to Solaris system configuration and run-time state
warehouse-sss	12321/tcp/udp	# Warehouse Monitoring Syst SSS
warehouse	12322/tcp/udp	# Warehouse Monitoring Syst
italk		12345/tcp/udp	# Italk Chat System
tsaf		12753/tcp/udp	# tsaf port
netperf		12865/tcp	# control port for the netperf benchmark
i-zipqd		13160/tcp/udp	# I-ZIPQD
bcslogc		13216/tcp/udp	# Black Crow Software application logging
rs-pias		13217/tcp/udp	# R&S Proxy Installation Assistant Service
emc-vcas-tcp	13218/tcp	# EMC Virtual CAS Service
emc-vcas-udp	13218/udp	# EMV Virtual CAS Service Discovery
powwow-client	13223/tcp/udp	# PowWow Client
powwow-server	13224/tcp/udp	# PowWow Server
doip-data	13400/tcp	# DoIP Data
doip-disc	13400/udp	# DoIP Discovery
bprd		13720/tcp/udp	# BPRD Protocol (VERITAS NetBackup)
bpdbm		13721/tcp/udp	# BPDBM Protocol (VERITAS NetBackup)
bpjava-msvc	13722/tcp/udp	# BP Java MSVC Protocol
vnetd		13724/tcp/udp	# Veritas Network Utility
bpcd		13782/tcp/udp	# VERITAS NetBackup
vopied		13783/tcp/udp	# VOPIED Protocol
nbdb		13785/tcp/udp	# NetBackup Database
nomdb		13786/tcp/udp	# Veritas-nomdb
dsmcc-config	13818/tcp/udp	# DSMCC Config
dsmcc-session	13819/tcp/udp	# DSMCC Session Messages
dsmcc-passthru	13820/tcp/udp	# DSMCC Pass-Thru Messages
dsmcc-download	13821/tcp/udp	# DSMCC Download Protocol
dsmcc-ccp	13822/tcp/udp	# DSMCC Channel Change Protocol
bmdss		13823/tcp	# Blackmagic Design Streaming Server
ucontrol	13894/tcp/udp	# Ultimate Control communication protocol
dta-systems	13929/tcp/udp	# D-TA SYSTEMS
medevolve	13930/tcp	# MedEvolve Port Requester
scotty-ft	14000/tcp/udp	# SCOTTY High-Speed Filetransfer
sua		14001/tcp/udp/sctp	# SUA
scotty-disc	14002/udp	# Discovery of a SCOTTY hardware codec board
sage-best-com1	14033/tcp/udp	# sage Best! Config Server 1
sage-best-com2	14034/tcp/udp	# sage Best! Config Server 2
vcs-app		14141/tcp/udp	# VCS Application
icpp		14142/tcp/udp	# IceWall Cert Protocol
icpps		14143/tcp	# IceWall Cert Protocol over TLS
gcm-app		14145/tcp/udp	# GCM Application
vrts-tdd	14149/tcp/udp	# Veritas Traffic Director
vcscmd		14150/tcp	# Veritas Cluster Server Command Server
vad		14154/tcp/udp	# Veritas Application Director
cps		14250/tcp/udp	# Fencing Server
ca-web-update	14414/tcp/udp	# CA eTrust Web Update Service
xpra		14500/tcp	# xpra network protocol
hde-lcesrvr-1	14936/tcp/udp
hde-lcesrvr-2	14937/tcp/udp
hydap		15000/tcp/udp	# Hypack Data Aquisition
onep-tls	15002/tcp	# Open Network Environment TLS
v2g-secc	15118/udp	# v2g Supply Equipment Communication Controller Discovery Protocol
xpilot		15345/tcp/udp	# XPilot Contact Port
3link		15363/tcp/udp	# 3Link Negotiation
cisco-snat	15555/tcp/udp	# Cisco Stateful NAT
bex-xr		15660/tcp/udp	# Backup Express Restore Server
ptp		15740/tcp/udp	# Picture Transfer Protocol
2ping		15998/udp	# 2ping Bi-Directional Ping Service
programmar	15999/tcp	# ProGrammar Enterprise
fmsas		16000/tcp	# Administration Server Access
fmsascon	16001/tcp	# Administration Server Connector
gsms		16002/tcp	# GoodSync Mediation Service
alfin		16003/udp	# Automation and Control by REGULACE.ORG
jwpc		16020/tcp	# Filemaker Java Web Publishing Core
jwpc-bin	16021/tcp	# Filemaker Java Web Publishing Core Binary
sun-sea-port	16161/tcp/udp	# Solaris SEA Port
solaris-audit	16162/tcp	# Solaris Audit - secure remote audit log
etb4j		16309/tcp/udp
pduncs		16310/tcp/udp	# Policy Distribute, Update Notification
pdefmns		16311/tcp/udp	# Policy definition and update management
netserialext1	16360/tcp/udp	# Network Serial Extension Ports One
netserialext2	16361/tcp/udp	# Network Serial Extension Ports Two
netserialext3	16367/tcp/udp	# Network Serial Extension Ports Three
netserialext4	16368/tcp/udp	# Network Serial Extension Ports Four
connected	16384/tcp/udp	# Connected Corp
rdgs		16385/tcp	# Reliable Datagram Sockets
xoms		16619/tcp	# X509 Objects Management Service
axon-tunnel	16665/tcp	# Reliable multipath data transport for high latencies
vtp		16666/udp	# Vidder Tunnel Protocol
cadsisvr	16789/tcp	# This server provides callable services to mainframe External Security Managers from any TCP/IP platform
newbay-snc-mc	16900/tcp/udp	# Newbay Mobile Client Update Service
sgcip		16950/tcp/udp	# Simple Generic Client Interface Protocol
intel-rci-mp	16991/tcp/udp	# INTEL-RCI-MP
amt-soap-http	16992/tcp/udp	# Intel(R) AMT SOAP/HTTP
amt-soap-https	16993/tcp/udp	# Intel(R) AMT SOAP/HTTPS
amt-redir-tcp	16994/tcp/udp	# Intel(R) AMT Redirection/TCP
amt-redir-tls	16995/tcp/udp	# Intel(R) AMT Redirection/TLS
isode-dua	17007/tcp/udp
vestasdlp	17184/tcp	# Vestas Data Layer Protocol
soundsvirtual	17185/tcp/udp	# Sounds Virtual
chipper		17219/tcp/udp	# Chipper
avtp		17220/tcp/udp	# IEEE 1722 Transport Protocol for Time Sensitive Applications
avdecc		17221/tcp/udp	# IEEE 1722.1 AVB Discovery, Enumeration, Connection management, and Control
cpsp		17222/udp	# Control Plane Synchronization Protocol (SPSP)
isa100-gci	17223/tcp	# ISA100 GCI is a service utilizing a common interface between an ISA100 Wireless gateway and a client application
trdp-pd		17224/udp	# Train Realtime Data Protocol (TRDP) Process Data
trdp-md		17225/tcp/udp	# Train Realtime Data Protocol (TRDP) Message Data
integrius-stp	17234/tcp/udp	# Integrius Secure Tunnel Protocol
ssh-mgmt	17235/tcp/udp	# SSH Tectia Manager
db-lsp		17500/tcp	# Dropbox LanSync Protocol
db-lsp-disc	17500/udp	# Dropbox LanSync Discovery
ailith		17555/tcp	# Ailith management of routers
ea		17729/tcp/udp	# Eclipse Aviation
zep		17754/tcp/udp	# Encap. ZigBee Packets
zigbee-ip	17755/tcp/udp	# ZigBee IP Transport Service
zigbee-ips	17756/tcp/udp	# ZigBee IP Transport Secure Service
sw-orion	17777/tcp	# SolarWinds Orion
biimenu		18000/tcp/udp	# Beckman Instruments, Inc.
radpdf		18104/tcp	# RAD PDF Service
racf		18136/tcp	# z/OS Resource Access Control Facility
opsec-cvp	18181/tcp/udp	# OPSEC CVP
opsec-ufp	18182/tcp/udp	# OPSEC UFP
opsec-sam	18183/tcp/udp	# OPSEC SAM
opsec-lea	18184/tcp/udp	# OPSEC LEA
opsec-omi	18185/tcp/udp	# OPSEC OMI
ohsc		18186/tcp/udp	# Occupational Health SC
opsec-ela	18187/tcp/udp	# OPSEC ELA
checkpoint-rtm	18241/tcp/udp	# Check Point RTM
iclid		18242/tcp	# Checkpoint router monitoring
clusterxl	18243/tcp	# Checkpoint router state backup
gv-pf		18262/tcp/udp	# GV NetConfig Service
ac-cluster	18463/tcp/udp	# AC Cluster
rds-ib		18634/tcp/udp	# Reliable Datagram Service
rds-ip		18635/tcp/udp	# Reliable Datagram Service over IP
vdmmesh		18668/tcp	# Manufacturing Execution Systems Mesh Communication
vdmmesh-disc	18668/udp	# Manufacturing Execution Systems Mesh Communication
ique		18769/tcp/udp	# IQue Protocol
infotos		18881/tcp/udp	# Infotos
apc-necmp	18888/tcp/udp	# APCNECMP
igrid		19000/tcp/udp	# iGrid Server
scintilla	19007/tcp/udp	# Scintilla protocol for device services
j-link		19020/tcp	# J-Link TCP/IP Protocol
opsec-uaa	19191/tcp/udp	# OPSEC UAA
ua-secureagent	19194/tcp/udp	# UserAuthority SecureAgent
cora		19220/tcp	# Client Connection Management and Data Exchange Service
cora-disc	19220/udp	# Discovery for Client Connection Management and Data Exchange Service
keysrvr		19283/tcp/udp	# Key Server for SASSAFRAS
keyshadow	19315/tcp/udp	# Key Shadow for SASSAFRAS
mtrgtrans	19398/tcp/udp
hp-sco		19410/tcp/udp
hp-sca		19411/tcp/udp
hp-sessmon	19412/tcp/udp	# HP-SESSMON
fxuptp		19539/tcp/udp	# FXUPTP
sxuptp		19540/tcp/udp	# SXUPTP
jcp		19541/tcp/udp	# JCP Client
mle		19788/udp	# Mesh Link Establishment
iec-104-sec	19998/tcp	# IEC 60870-5-104 process control - secure
dnp-sec		19999/tcp/udp	# Distributed Network Protocol - Secure
dnp		20000/tcp/udp	# DNP
microsan	20001/tcp/udp	# MicroSAN
commtact-http	20002/tcp/udp	# Commtact HTTP
commtact-https	20003/tcp/udp	# Commtact HTTPS
openwebnet	20005/tcp/udp	# OpenWebNet protocol for electric network
ss-idi-disc	20012/udp	# Samsung Interdevice Interaction discovery
ss-idi		20013/tcp	# Samsung Interdevice Interaction
opendeploy	20014/tcp/udp	# OpenDeploy Listener
nburn-id	20034/tcp/udp	# NetBurner ID Port
tmophl7mts	20046/tcp/udp	# TMOP HL7 Message Transfer Service
mountd		20048/tcp/udp	# NFS mount protocol
nfsrdma		20049/tcp/udp/sctp	# Network File System (NFS) over RDMA
avesterra	20057/tcp	# AvesTerra Hypergraph Transfer Protocol (HGTP)
tolfab		20167/tcp/udp	# TOLfab Data Change
ipdtp-port	20202/tcp/udp	# IPD Tunneling Port
ipulse-ics	20222/tcp/udp	# iPulse-ICS
emwavemsg	20480/tcp/udp	# emWave Message Service
track		20670/tcp/udp	# Track
athand-mmp	20999/tcp/udp	# At Hand MMP
irtrans		21000/tcp/udp	# IRTrans Control
notezilla-lan	21010/tcp	# Notezilla.Lan Server
trinket-agent	21212/tcp	# Distributed artificial intelligence
aigairserver	21221/tcp	# Services for Air Server
rdm-tfs		21553/tcp	# Raima RDM TFS
dfserver	21554/tcp/udp	# MineScape Design File Server
vofr-gateway	21590/tcp/udp	# VoFR Gateway
tvpm		21800/tcp/udp	# TVNC Pro Multiplexing
webphone	21845/tcp/udp
netspeak-is	21846/tcp/udp	# NetSpeak Corp. Directory Services
netspeak-cs	21847/tcp/udp	# NetSpeak Corp. Connection Services
netspeak-acd	21848/tcp/udp	# NetSpeak Corp. Automatic Call Distribution
netspeak-cps	21849/tcp/udp	# NetSpeak Corp. Credit Processing System
snapenetio	22000/tcp/udp	# SNAPenetIO
optocontrol	22001/tcp/udp	# OptoControl
optohost002	22002/tcp/udp	# Opto Host Port 2
optohost003	22003/tcp/udp	# Opto Host Port 3
optohost004	22004/tcp/udp	# Opto Host Port 4
optohost004	22005/tcp/udp	# Opto Host Port 5
dcap		22125/tcp	# dCache Access Protocol
gsidcap		22128/tcp	# GSI dCache Access Protocol
easyengine	22222/tcp	# EasyEngine is CLI tool to manage WordPress Sites on Nginx server
wnn6		22273/tcp/udp
cis		22305/tcp/udp	# CompactIS Tunnel
shrewd-control	22335/tcp	# Initium Labs Security and Automation Control
shrewd-stream	22335/udp	# Initium Labs Security and Automation Streaming
cis-secure	22343/tcp/udp	# CompactIS Secure Tunnel
wibukey		22347/tcp/udp	# WibuKey Standard WkLan
codemeter	22350/tcp/udp	# CodeMeter Standard
codemeter-cmwan	22351/tcp	# TPC/IP requests of copy protection software to a server
caldsoft-backup	22537/tcp	# CaldSoft Backup server file transfer
vocaltec-wconf	22555/tcp	# Vocaltec Web Conference
vocaltec-phone	22555/udp	# Vocaltec Internet Phone
talikaserver	22763/tcp/udp	# Talika Main Server
aws-brf		22800/tcp/udp	# Telerate Information Platform LAN
brf-gw		22951/tcp/udp	# Telerate Information Platform WAN
inovaport1	23000/tcp/udp	# Inova LightLink Server Type 1
inovaport2	23001/tcp/udp	# Inova LightLink Server Type 2
inovaport3	23002/tcp/udp	# Inova LightLink Server Type 3
inovaport4	23003/tcp/udp	# Inova LightLink Server Type 4
inovaport5	23004/tcp/udp	# Inova LightLink Server Type 5
inovaport6	23005/tcp/udp	# Inova LightLink Server Type 6
gntp		23053/tcp	# Generic Notification Transport Protocol
s102		23272/udp	# S102 application
5afe-dir	23294/tcp	# 5AFE SDN Directory
5afe-disc	23294/udp	# 5AFE SDN Directory discovery
elxmgmt		23333/tcp/udp	# Emulex HBAnyware Remote Management
novar-dbase	23400/tcp/udp	# Novar Data
novar-alarm	23401/tcp/udp	# Novar Alarm
novar-global	23402/tcp/udp	# Novar Global
aequus		23456/tcp	# Aequus Service
aequus-alt	23457/tcp	# Aequus Service Mgmt
areaguard-neo	23546/tcp	# AreaGuard Neo - WebServer
med-ltp		24000/tcp/udp
med-fsp-rx	24001/tcp/udp
med-fsp-tx	24002/tcp/udp
med-supp	24003/tcp/udp
med-ovw		24004/tcp/udp
med-ci		24005/tcp/udp
med-net-svc	24006/tcp/udp
filesphere	24242/tcp/udp	# fileSphere
vista-4gl	24249/tcp/udp	# Vista 4GL
ild		24321/tcp/udp	# Isolv Local Directory
hid		24322/udp	# Transport of Human Interface Device data streams
intel-rci	24386/tcp/udp	# Intel RCI
tonidods	24465/tcp/udp	# Tonido Domain Server
binkp		24554/tcp/udp	# BINKP
bilobit		24577/tcp	# bilobit Service
bilobit-update	24577/udp	# bilobit Service Update
sdtvwcam	24666/tcp	# Service used by SmarDTV to communicate between a CAM and a second screen application
canditv		24676/tcp/udp	# Canditv Message Service
flashfiler	24677/tcp/udp	# FlashFiler
proactivate	24678/tcp/udp	# Turbopower Proactivate
tcc-http	24680/tcp/udp	# TCC User HTTP Service
cslg		24754/tcp	# Citrix StorageLink Gateway
assoc-disc	24850/udp	# Device Association Discovery
find		24922/tcp/udp	# Find Identification of Network Devices
icl-twobase1	25000/tcp/udp
icl-twobase2	25001/tcp/udp
icl-twobase3	25002/tcp/udp
icl-twobase4	25003/tcp/udp
icl-twobase5	25004/tcp/udp
icl-twobase6	25005/tcp/udp
icl-twobase7	25006/tcp/udp
icl-twobase8	25007/tcp/udp
icl-twobase9	25008/tcp/udp
icl-twobase10	25009/tcp/udp
rna		25471/sctp	# RNSAP User Adaptation for Iurh
sauterdongle	25576/tcp	# Sauter Dongle
idtp		25604/tcp	# Identifier Tracing Protocol
vocaltec-hos	25793/tcp/udp	# Vocaltec Address Server
tasp-net	25900/tcp/udp	# TASP Network Comm
niobserver	25901/tcp/udp	# NIObserver
nilinkanalyst	25902/tcp/udp	# NILinkAnalyst
niprobe		25903/tcp/udp	# NIProbe
bf-game		25954/udp	# Bitfighter game server
bf-master	25955/udp	# Bitfighter master server
quake		26000/tcp/udp
scscp		26133/tcp/udp	# Symbolic Computation Software Composability Protocol
wnn6-ds		26208/tcp/udp
cockroach	26257/tcp	# CockroachDB
ezproxy		26260/tcp/udp	# eZproxy
ezmeeting	26261/tcp/udp	# eZmeeting
k3software-svr	26262/tcp/udp	# K3 Software-Server
k3software-cli	26263/tcp/udp	# K3 Software-Client
exoline-tcp	26486/tcp	# EXOline-TCP
exoline-udp	26486/udp	# EXOline-UDP
exoconfig	26487/tcp/udp	# EXOconfig
exonet		26489/tcp/udp	# EXOnet
imagepump	27345/tcp/udp	# ImagePump
jesmsjc		27442/tcp/udp	# Job controller service
kopek-httphead	27504/tcp/udp	# Kopek HTTP Head Port
ars-vista	27782/tcp/udp	# ARS VISTA Application
astrolink	27876/tcp	# Astrolink Protocol
tw-auth-key	27999/tcp/udp	# TW Authentication/Key Distribution and
nxlmd		28000/tcp/udp	# NX License Manager
pqsp		28001/tcp	# PQ Service
a27-ran-ran	28119/udp	# A27 cdma2000 RAN Management
voxelstorm	28200/tcp/udp	# VoxelStorm game server
siemensgsm	28240/tcp/udp	# Siemens GSM
bosswave	28589/tcp	# Building operating system services wide area verified exchange
sgsap		29118/sctp	# SGsAP in 3GPP
otmp		29167/tcp/udp	# ObTools Message Protocol
sbcap		29168/sctp	# SBcAP in 3GPP
iuhsctpassoc	29169/sctp	# HNBAP and RUA Common Association
bingbang	29999/tcp	# data exchange protocol for IEC61850 in wind power plants
ndmps		30000/tcp	# Secure Network Data Management Protocol
pago-services1	30001/tcp/udp	# Pago Services 1
pago-services2	30002/tcp/udp	# Pago Services 2
amicon-fpsu-ra	30003/tcp/udp	# Amicon FPSU-IP Remote Administration
amicon-fpsu-s	30004/udp	# Amicon FPSU-IP VPN
rwp		30100/tcp/sctp	# Remote Window Protocol
kingdomsonline	30260/tcp/udp	# Kingdoms Online (CraigAvenue)
gs-realtime	30400/tcp	# GroundStar RealTime System
samsung-disc	30832/udp	# Samsung Convergence Discovery Protocol
ovobs		30999/tcp/udp	# OpenView Service Desk Client
ka-sddp		31016/tcp	# Kollective Agent Secure Distributed Delivery Protocol
ka-kdp		31016/udp	# Kollective Agent Kollective Delivery Protocol
autotrac-acp	31020/tcp	# Autotrac ACP 245
yawn		31029/udp	# YaWN - Yet Another Windows Notifier
pace-licensed	31400/tcp	# PACE license server
xqosd		31416/tcp/udp	# XQoS network monitor
tetrinet	31457/tcp/udp	# TetriNET Protocol
lm-mon		31620/tcp/udp
dsx-monitor	31685/tcp	# DS Expert Monitor
gamesmith-port	31765/tcp/udp	# GameSmith Port
iceedcp-tx	31948/tcp/udp	# Embedded Device Configuration Protocol TX
iceedcp-rx	31949/tcp/udp	# Embedded Device Configuration Protocol RX
iracinghelper	32034/tcp/udp	# iRacing helper service
t1distproc60	32249/tcp/udp	# T1 Distributed Processor
plex		32400/tcp	# Plex multimedia
apm-link	32483/tcp/udp	# Access Point Manager Link
sec-ntb-clnt	32635/tcp/udp	# SecureNotebook-CLNT
DMExpress	32636/tcp/udp
filenet-powsrm	32767/tcp/udp	# FileNet BPM WS-ReliableMessaging Client
filenet-tms	32768/tcp/udp	# Filenet TMS
filenet-rpc	32769/tcp/udp	# Filenet RPC
filenet-nch	32770/tcp/udp	# Filenet NCH
filenet-rmi	32771/tcp/udp	# FileNET RMI
filenet-pa	32772/tcp/udp	# FileNET Process Analyzer
filenet-cm	32773/tcp/udp	# FileNET Component Manager
filenet-re	32774/tcp/udp	# FileNET Rules Engine
filenet-pch	32775/tcp/udp	# Performance Clearinghouse
filenet-peior	32776/tcp/udp	# FileNET BPM IOR
filenet-obrok	32777/tcp/udp	# FileNet BPM CORBA
mlsn		32801/tcp/udp	# Multiple Listing Service Network
retp		32811/tcp	# Real Estate Transport Protocol
idmgratm	32896/tcp/udp	# Attachmate ID Manager
mysqlx		33060/tcp	# MySQL Database Extended Interface
aurora-balaena	33123/tcp/udp	# Aurora (Balaena Ltd)
diamondport	33331/tcp/udp	# DiamondCentral Interface
dgi-serv	33333/tcp	# Digital Gaslight Service
speedtrace	33334/tcp	# SpeedTrace TraceAgent
speedtrace-disc	33334/udp	# SpeedTrace TraceAgent Discovery
traceroute	33434/tcp/udp	# traceroute use
mtrace		33435/udp	# IP Multicast Traceroute
snip-slave	33656/tcp/udp	# SNIP Slave
turbonote-2	34249/tcp/udp	# TurboNote Relay Server Default Port
p-net-local	34378/tcp/udp	# P-Net on IP local
p-net-remote	34379/tcp/udp	# P-Net on IP remote
dhanalakshmi	34567/tcp	# dhanalakshmi.org EDI Service
edi_service	34567/udp	# dhanalakshmi.org EDI Service
profinet-rt	34962/tcp/udp	# PROFInet RT Unicast
profinet-rtm	34963/tcp/udp	# PROFInet RT Multicast
profinet-cm	34964/tcp/udp	# PROFInet Context Manager
ethercat	34980/tcp/udp	# EtherCAT Port
heathview	35000/tcp	# HeathView
rt-viewer	35001/tcp/udp	# ReadyTech Viewer
rt-sound	35002/tcp	# ReadyTech Sound Server
rt-devicemapper	35003/tcp	# ReadyTech DeviceMapper Server
rt-classmanager	35004/tcp/udp	# ReadyTech ClassManager
rt-labtracker	35005/tcp	# ReadyTech LabTracker
rt-helper	35006/tcp	# ReadyTech Helper Service
axio-disc	35100/tcp/udp	# Axiomatic discovery protocol
kitim		35354/tcp	# KIT Messenger
altova-lm	35355/tcp	# Altova License Management
altova-lm-disc	35355/udp	# Altova License Management Discovery
guttersnex	35356/tcp	# Gutters Note Exchange
openstack-id	35357/tcp	# OpenStack ID Service
allpeers	36001/tcp/udp	# AllPeers Network
wlcp		36411/udp	# Wireless LAN Control plane Protocol (WLCP)
s1-control	36412/sctp	# S1-Control Plane (3GPP)
x2-control	36422/sctp	# X2-Control Plane (3GPP)
slmap		36423/sctp	# SLm Interface Application Protocol
nq-ap		36424/sctp	# Nq and Nq' Application Protocol
m2ap		36443/sctp	# M2 Application Part
m3ap		36444/sctp	# M3 Application Part
xw-control	36462/sctp	# Xw-Control Plane (3GPP)
febooti-aw	36524/tcp	# Febooti Automation Workshop
observium-agent	36602/tcp	# Observium statistics collection agent
mapx		36700/tcp	# MapX communication
kastenxpipe	36865/tcp/udp	# KastenX Pipe
neckar		37475/tcp/udp	# science + computing's Venus Administration Port
gdrive-sync	37483/tcp	# Google Drive Sync
eftp		37601/tcp	# Epipole File Transfer Protocol
unisys-eportal	37654/tcp/udp	# Unisys ClearPath ePortal
ivs-database	38000/tcp	# InfoVista Server Database
ivs-insertion	38001/tcp	# InfoVista Server Insertion
cresco-control	38002/tcp	# Cresco Controller
crescoctrl-disc	38002/udp	# Cresco Controller Discovery
galaxy7-data	38201/tcp/udp	# Galaxy7 Data Tunnel
fairview	38202/tcp/udp	# Fairview Message Service
agpolicy	38203/tcp/udp	# AppGate Policy Server
ng-control	38412/sctp	# NG Control Plane (3GPP)
xn-control	38422/sctp	# Xn Control Plane (3GPP)
f1-control	38472/sctp	# F1 Control Plane (3GPP)
sruth		38800/tcp	# Sruth is a service for the distribution of routinely- generated but arbitrary files based on a publish/subscribe distribution model and implemented using a peer-to-peer transport mechanism
secrmmsafecopya	38865/tcp	# Security approval process for use of the secRMM SafeCopy program
turbonote-1	39681/tcp/udp	# TurboNote Default Port
safetynetp	40000/tcp/udp	# SafetyNET p
k-patentssensor	40023/udp	# K-PatentsSensorInformation
sptx		40404/tcp	# Simplify Printing TX
cscp		40841/tcp/udp	# CSCP
csccredir	40842/tcp/udp	# CSCCREDIR
csccfirewall	40843/tcp/udp	# CSCCFIREWALL
ortec-disc	40853/udp	# ORTEC Service Discovery
fs-qos		41111/tcp/udp	# Foursticks QoS Protocol
tentacle	41121/tcp	# Tentacle Server
z-wave-s	41230/tcp/udp	# Z-Wave Protocol over SSL/TLS
crestron-cip	41794/tcp/udp	# Crestron Control Port
crestron-ctp	41795/tcp/udp	# Crestron Terminal Port
crestron-cips	41796/tcp	# Crestron Secure Control Port
crestron-ctps	41797/tcp	# Crestron Secure Terminal Port
candp		42508/tcp/udp	# Computer Associates network discovery protocol
candrp		42509/tcp/udp	# CA discovery response
caerpc		42510/tcp/udp	# CA eTrust RPC
recvr-rc	43000/tcp	# Receiver Remote Control
recvr-rc-disc	43000/udp	# Receiver Remote Control Discovery
reachout	43188/tcp/udp	# REACHOUT
ndm-agent-port	43189/tcp/udp	# NDM-AGENT-PORT
ip-provision	43190/tcp/udp	# IP-PROVISION
noit-transport	43191/tcp	# Reconnoiter Agent Data Transport
shaperai	43210/tcp	# Shaper Automation Server Management
shaperai-disc	43210/udp	# Shaper Automation Server Management Discovery
hmip-routing	43438/udp	# HmIP LAN Routing
eq3-update	43439/tcp	# EQ3 firmware update
eq3-config	43439/udp	# EQ3 discovery and configuration
ew-mgmt		43440/tcp	# Cisco EnergyWise Management
ew-disc-cmd	43440/udp	# Cisco EnergyWise Discovery and Command Flooding
ciscocsdb	43441/tcp/udp	# Cisco NetMgmt DB Ports
z-wave-tunnel	44123/tcp	# Z-Wave Secure Tunnel
pmcd		44321/tcp/udp	# PCP server (pmcd)
pmcdproxy	44322/tcp/udp	# PCP server (pmcd) proxy
pmwebapi	44323/tcp	# HTTP binding for Performance Co-Pilot client API
cognex-dataman	44444/tcp	# Cognex DataMan Management Protocol
domiq		44544/udp	# DOMIQ Building Automation
rbr-debug	44553/tcp/udp	# REALbasic Remote Debug
asihpi		44600/udp	# AudioScience HPI
EtherNet-IP-2	44818/tcp/udp	# EtherNet/IP messaging
m3da		44900/tcp	# M3DA is used for efficient machine-to-machine communications
m3da-disc	44900/udp	# M3DA Discovery is used for efficient machine-to-machine communications
asmp		45000/tcp	# Nuance AutoStore Status Monitoring Protocol (data transfer)
asmp-mon	45000/udp	# Nuance AutoStore Status Monitoring Protocol (device monitoring)
asmps		45001/tcp	# Nuance AutoStore Status Monitoring Protocol (secure data transfer)
rs-status	45002/tcp	# Redspeed Status Monitor
synctest	45045/tcp	# Remote application control protocol
invision-ag	45054/tcp/udp	# InVision AG
cloudcheck	45514/tcp	# ASSIA CloudCheck WiFi Management System
cloudcheck-ping	45514/udp	# ASSIA CloudCheck WiFi Management keepalive
eba		45678/tcp/udp	# EBA PRISE
dai-shell	45824/tcp	# Server for the DAI family of client-server products
qdb2service	45825/tcp/udp	# Qpuncture Data Access Service
ssr-servermgr	45966/tcp/udp	# SSRServerMgr
inedo		46336/tcp	# Listen port used for Inedo agent communication
spremotetablet	46998/tcp	# Connection between a desktop computer or server and a signature tablet to capture handwritten signatures
mediabox	46999/tcp/udp	# MediaBox Server
mbus		47000/tcp/udp	# Message Bus
winrm		47001/tcp	# Windows Remote Management Service
jvl-mactalk	47100/udp	# Configuration of motors connected to Industrial Ethernet
dbbrowse	47557/tcp/udp	# Databeam Corporation
directplaysrvr	47624/tcp/udp	# Direct Play Server
ap		47806/tcp/udp	# ALC Protocol
bacnet		47808/tcp/udp	# Building Automation and Control Networks
presonus-ucnet	47809/udp	# PreSonus Universal Control Network Protocol
nimcontroller	48000/tcp/udp	# Nimbus Controller
nimspooler	48001/tcp/udp	# Nimbus Spooler
nimhub		48002/tcp/udp	# Nimbus Hub
nimgtw		48003/tcp/udp	# Nimbus Gateway
nimbusdb	48004/tcp	# NimbusDB Connector
nimbusdbctrl	48005/tcp	# NimbusDB Control
juliar		48048/tcp	# Juliar Programming Language Protocol
3gpp-cbsp	48049/tcp	# 3GPP Cell Broadcast Service Protocol
weandsf		48050/tcp	# WeFi Access Network Discovery and Selection Function
isnetserv	48128/tcp/udp	# Image Systems Network Services
blp5		48129/tcp/udp	# Bloomberg locator
com-bardac-dw	48556/tcp/udp
iqobject	48619/tcp/udp
robotraconteur	48653/tcp/udp	# Robot Raconteur transport
matahari	49000/tcp	# Matahari Broker
nusrp		49001/tcp	# Nuance Unity Service Request Protocol
nusdp-disc	49001/udp	# Nuance Unity Service Discovery Protocol
inspider	49150/tcp	# InSpider System
