#!/bin/bash
#
# Copyright 2015 <PERSON> <<EMAIL>>
#
# Wireshark - Network traffic analyzer
# By <PERSON> <<EMAIL>>
# Copyright 1998 <PERSON>
#
# SPDX-License-Identifier: GPL-2.0-or-later

set -e

grep -q WIRESHARK_RUN_FROM_BUILD_DIRECTORY ~/.profile || echo "export WIRESHARK_RUN_FROM_BUILD_DIRECTORY=1" >> ~/.profile
grep -q WIRESHARK_BIN_DIR ~/.profile || echo "export WIRESHARK_BIN_DIR=~/build/run" >> ~/.profile
mkdir -p build
cd build
cmake /vagrant/
make -j6
