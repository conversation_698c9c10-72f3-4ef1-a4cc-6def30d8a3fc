#! /usr/bin/sh
# build yaEty project

# 1.只在 Makefile 不存在时进行 cmake 生成 Makefile 动作;
# 2.如果 Makefile 存在，总是进行 "make rebuild_cache"，应对在 Cmakelists.txt 修改的情况;
# 3.支持 rebuild 时进行 clean 操作

# 支持以下命令
# build: 常规构建
# build yaEty: 仅构建 yaEty, 不构建 wireshark
# build rebuild: 全部重新构建，会进行 "make clean"

cwd=$(dirname $0)
cd $cwd
cwd=$(pwd)

doClean=false                   # 默认不进行 clean
coresToMake=                    # 默认使用 所有core 进行 make
ws=wireshark_2.6.2              # 需要构建的 wireshark 版本

# change locale to english so the tools(eg. svn to get verson)
# expecting english output can work correctly.
export LC_ALL="en_US.UTF-8"

# 导入 xstep 函数
. ./a_step.sh

function build_proj
{
    prjdir=$1
    clean=$2
    target=$3

    # shift to cmake opt
    shift 3
    cmake_opt=$*

    # to prjdir
    echo "-- build $target"
    cd $prjdir

    # call cmake to gen Makefile
    if [ ! -e Makefile ]
    then
        a_step cmake3 $cmake_opt ..
    fi

    # rebuild cache for CMakelists.txt being changed
    a_step make rebuild_cache

    # should do "make clean"?
    if [ $clean == true ]
    then
        a_step make clean
    fi

    # make 使用 n-1 个核心，防止系统卡死
    a_step make -j$(($(nproc) > 1 ? $(nproc) - 1 : 1)) $coresToMake $target
}

# parse opts
while getopts ":j:h" opt
 do
    case $opt in
              j ) echo "set cpu cores can used by make"
                  coresToMake=$OPTARG
                  ;;
              h ) echo "-j       : set cpu cores used by make."
                  echo "rebuild  : make clean and make."
                  echo "yaEty    : only build yaEty project."
                  exit 0
                  ;;
              ? ) echo "error opt"
                  exit 1
                  ;;
   esac
done

# 调整 args
shift $(($OPTIND - 1))

# check the mode
if [ ${1:-""} == "rebuild" ]    # use "" if there is not $1
then
    doClean=true
    shift                       # eat this arg
fi

#
if [ $# -eq 0 ]
then   # 未指定任何参数时，build wireshark
       # 不编译 wireshark, 使用静态链接，不使用 kerberos
    a_step build_proj $cwd/$ws/build true tshark \
               -DBUILD_wireshark=OFF          \
               -DENABLE_STATIC=ON             \
               -DENABLE_KERBEROS=false        \
               -DENABLE_LUA=false
    a_step build_proj $cwd/$ws/build false dumpcap \
               -DBUILD_wireshark=OFF          \
               -DENABLE_STATIC=ON             \
               -DENABLE_KERBEROS=false        \
               -DENABLE_LUA=false
    a_step build_proj $cwd/$ws/build false editcap\
               -DBUILD_wireshark=OFF          \
               -DENABLE_STATIC=ON             \
               -DENABLE_KERBEROS=false        \
               -DENABLE_LUA=false
    a_step build_proj $cwd/$ws/build false wscodecs\
               -DBUILD_wireshark=OFF          \
               -DENABLE_STATIC=ON             \
               -DENABLE_KERBEROS=false        \
               -DENABLE_LUA=false
fi

# build yaEty
a_step build_proj $cwd/yaEty/build true yaEty
a_step 'gcc -g -O0 -o ../09_辅助工具/01_tools/rtp_stream_counter yaEty/rtp_stream_counter.cpp -lpcap'
# done
a_step 'echo -- build done, dir [run] hold the output'

cd $cwd
a_step 'ln -sf ../../08_发布/yaEty.conf run/yaEty.conf'
a_step 'ln -sf ../../08_发布/g728fp run/g728fp'
