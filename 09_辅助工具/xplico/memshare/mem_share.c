/****************************************************************************************
 * 文 件 名 : yaEty_content_reassembly_share_mem.c
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : liugh      '2018-05-21
* 编    码 : liugh      '2018-05-21
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include<unistd.h>
#include<stdlib.h>
#include<stdio.h>
#include<errno.h>
#include<fcntl.h>
#include<string.h>
#include<sys/file.h>
#include<sys/wait.h>
#include<sys/mman.h>
#include<sys/ipc.h>
#include<sys/shm.h>
#include<sys/types.h>
#include <arpa/inet.h>

#define bool unsigned char
#include "mem_share.h"



static unsigned int crc_table[256];
http_picture *g_share_http;
emails_data  *g_share_email;


#if 0
http_picture* DispAttachShareMem(void)
{
	int shmid=0;
	g_share_http=NULL;

	shmid=shmget( COMM_PICTURE_MEM_ID, 0, 0 );
	if(shmid<0)
	{
		printf("##shmget attach share memory failed!\n");
		goto error;
	}
	g_share_http  = ( http_picture *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_http)//#1
	{
		printf("##shmat attch share memory failed!\n");
		goto error;
	}


	return g_share_http;
error:
	return NULL;
}

bool DispInitShareMem(void)
{
	int shmid=0;

	shmid=shmget( COMM_PICTURE_MEM_ID, HTTP_PICTURE_LIST_SIZE*HASH_SIZE, ( IPC_CREAT|0666 ) );
	if(shmid<0){
		printf("##shmget create share memory failed!\n");
		goto error;
	}
	g_share_http = (http_picture *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_http)//#1
	{
		printf("##shmat attch share memory failed!\n");
		goto error;
	}

	return 1;
error:
	return 0;
}

unsigned int DispGetFreeMemDB(int type, unsigned int hashval)
{
	unsigned int mem_index=0;
	int i;
	switch(type){
		case MEM_TYPE_HTTP_PICTURE:
			if(g_share_http[hashval].flag==0)mem_index=hashval;
			else{
				for(i=HASH_SIZE;i<HASH_SIZE*2;i++){
					if(g_share_http[i].flag==0){
						mem_index=i;
					}
				}
			}
			break;
		default:
			mem_index=0;
	}

	return mem_index;
}

bool DispSetHashdb(picture_info info, int type)
{
	http_picture *npq1=NULL;
	unsigned int index=0;
	unsigned int hashval=0;
	unsigned int cur=0;

    PktInfo ptf;
	ptf.dst_ip=info.dst_ip;
	ptf.dst_port=info.dst_port;
	ptf.src_ip=info.src_ip;
	ptf.src_port=info.src_port;
	ptf.proto=6;

	hashval=Hash(ptf);
    index=DispGetFreeMemDB(type,hashval);
	switch(type){
	case MEM_TYPE_HTTP_PICTURE:
		if(index == hashval){
			npq1=&g_share_http[index];
		}else{
			cur =hashval;
			while(g_share_http[cur].next!=0)
				cur=g_share_http[cur].next;
			g_share_http[cur].next=index;
			npq1= &g_share_http[index];
		}

		npq1->flag=1;
		npq1->next=0;
		npq1->dst_ip=info.dst_ip;
		npq1->dst_port=info.dst_port;
		npq1->src_ip=info.src_ip;
		npq1->src_port=info.src_port;
		strncpy(npq1->pic_gather[0].path,info.path,strlen(info.path));
		break;
	default:
		break;
		return 0;
	}

	return 1;
}

void *DispGetHashdb(PktInfo ptf, int type)
{
	http_picture *npq1=NULL;
	switch(type){
	case MEM_TYPE_HTTP_PICTURE:
		npq1=(http_picture *)&g_share_http[Hash(ptf)];
		while(npq1->next!=0){
			if(ptf.dst_ip==npq1->dst_ip && ptf.dst_port==npq1->dst_port &&
			   ptf.src_ip==npq1->src_ip && ptf.src_port==npq1->src_port){
				return npq1;
			}
			npq1=(http_picture *)&g_share_http[npq1->next];
		}
		if(ptf.dst_ip==npq1->dst_ip && ptf.dst_port==npq1->dst_port &&
		   ptf.src_ip==npq1->src_ip && ptf.src_port==npq1->src_port){
			return npq1;
		}
		break;
	default:
		break;
	}

	return NULL;
}



unsigned int GetSumPoly(unsigned char data)
{
	unsigned int sum_poly = data;
	int j;
	sum_poly <<= 24;
	for(j = 0; j < 8; j++)
	{
		int hi = sum_poly&0x80000000;
		sum_poly <<= 1;
		if(hi) sum_poly = sum_poly^POLY;
	}
	return sum_poly;
}

void CreateCrcTable(void)
{
	int i;
	for(i = 0; i < 256; i++){
		crc_table[i] = GetSumPoly(i&0xFF);
	}
}

unsigned int CRC20Key(unsigned char* data, int len)
{
	int i;
	unsigned int reg = 0xFFFFFFFF;
	for(i = 0; i < len; i++)
	{
		reg = (reg<<8) ^ crc_table[(reg>>24)&0xFF ^ data[i]];
	}
	return (reg&0XFFFFF);
}

int Hash( PktInfo ptf )
{
    char c;
    int i;
    int hval = 0;

    hval=CRC20Key((unsigned char *)&ptf,sizeof(PktInfo));

    return (hval % HASH_SIZE);
}
#endif


bool DispAttachShareMem(void)
{
	int shmid=0;
	g_share_http=NULL;
	g_share_email=NULL;

	shmid=shmget( COMM_PICTURE_MEM_ID, 0, 0 );
	if(shmid<0)
	{
		printf("##shmget http attach share memory failed!\n");
		goto error;
	}
	g_share_http  = ( http_picture *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_http)//#1
	{
		printf("##shmat http attach share memory failed!\n");
		goto error;
	}

	shmid=shmget( COMM_EMAIL_MEM_ID, 0, 0 );
	if(shmid<0)
	{
		printf("##shmget email attach share memory failed!\n");
		goto error;
	}
	g_share_email  = ( emails_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_email)//#2
	{
		printf("##shmat  email attch share memory failed!\n");
		goto error;
	}

	return 1;
error:
	return 0;
}


bool DispInitShareMem(void)
{
	int shmid=0;

	shmid=shmget( COMM_PICTURE_MEM_ID, HTTP_PICTURE_LIST_SIZE*HASH_SIZE*2, ( IPC_CREAT|0666 ) );
	if(shmid<0){
		printf("##shmget create share memory failed!\n");
		goto error;
	}
	g_share_http = (http_picture *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_http)//#1
	{
		printf("##shmat attach share memory failed!\n");
		goto error;
	}

    shmid=shmget( COMM_EMAIL_MEM_ID, EMAILS_DATA_LIST_SIZE*HASH_SIZE*2, ( IPC_CREAT|0666 ) );
	if(shmid<0){
		printf("##shmget create share memory failed!\n");
		goto error;
	}
	g_share_email = (emails_data *)shmat( shmid,( const void* )0,0 );
	if(NULL==g_share_email)//#1
	{
		printf("##shmat attch share memory failed!\n");
		goto error;
	}

    memset((char *)g_share_http,0,HTTP_PICTURE_LIST_SIZE*HASH_SIZE*2);
	memset((char *)g_share_email,0,EMAILS_DATA_LIST_SIZE*HASH_SIZE*2);


	CreateCrcTable();

	return 1;
error:
	return 0;
}


/*
*if hash localtion flag is 0, then insert data
*if hash localtion flag is 1, and 5tuple is same then insert picture path to sub
*if hash localtion flag is 1, and 5tuple is different then find conflict memory
*/
unsigned int DispGetFreeMemDB(data_info info, unsigned int hashval, int type, int *path_flag)
{
	unsigned int mem_index=0;
	int i;
	switch(type){
	case MEM_TYPE_HTTP_PICTURE:
		if(g_share_http[hashval].flag==0){
			mem_index=hashval;
			*path_flag=0;
		}else if(g_share_http[hashval].flag==1){
			if(g_share_http[hashval].dst_ip==info.dst_ip && g_share_http[hashval].dst_port==info.dst_port
			&& g_share_http[hashval].src_ip==info.src_ip && g_share_http[hashval].src_port==info.src_port){
				mem_index=hashval;
				*path_flag=1;
			}else{
				for(i=HASH_SIZE;i<HASH_SIZE*2;i++){
					if(g_share_http[i].flag==0){
						mem_index=i;
					}
				}
			}
		}
		break;
	case MEM_TYPE_EMAIL_DATA:
		if(g_share_email[hashval].flag==0){
			mem_index=hashval;
			*path_flag=0;
		}else if(g_share_http[hashval].flag==1){
			for(i=HASH_SIZE;i<HASH_SIZE*2;i++){
				if(g_share_http[i].flag==0){
					mem_index=i;
				}
			}
		}
		break;
	default:
		mem_index=0;
		break;
	}

	return mem_index;
}



bool DispSetHashdb(data_info info, int type)
{
	http_picture *npq1=NULL;
	emails_data  *npq2=NULL;
	unsigned int index=0;
	unsigned int hashval=0;
	unsigned int cur=0;
	int path_flag=0;
	int i;

    PktInfo ptf;
	ptf.dst_ip=info.dst_ip;
	ptf.dst_port=info.dst_port;
	ptf.src_ip=info.src_ip;
	ptf.src_port=info.src_port;
	ptf.proto=6;

	hashval=Hash(ptf);
    index=DispGetFreeMemDB(info,hashval,type,&path_flag);
	switch(type){
	case MEM_TYPE_HTTP_PICTURE:
		if(index == hashval){
			npq1=&g_share_http[index];
			if(0==path_flag){
				npq1->flag=1;
				npq1->next=0;
				npq1->dst_ip=info.dst_ip;
				npq1->dst_port=info.dst_port;
				npq1->src_ip=info.src_ip;
				npq1->src_port=info.src_port;

				if(npq1->pci_number<PICTURE_GATHER_NUMBER){
					npq1->pic_gather[npq1->pci_number].flag=1;
					strncpy(npq1->pic_gather[npq1->pci_number++].path,info.path,strlen(info.path));
				}
			}else if(1==path_flag){
				path_flag=0;
				if(npq1->pci_number<PICTURE_GATHER_NUMBER){
					npq1->pic_gather[npq1->pci_number].flag=1;
					strncpy(npq1->pic_gather[npq1->pci_number++].path,info.path,strlen(info.path));
				}
			}
		}else{
			cur =hashval;
			while(g_share_http[cur].next!=0)
				cur=g_share_http[cur].next;
			g_share_http[cur].next=index;
			npq1= &g_share_http[index];
			npq1->flag=1;
			npq1->next=0;
			npq1->dst_ip=info.dst_ip;
			npq1->dst_port=info.dst_port;
			npq1->src_ip=info.src_ip;
			npq1->src_port=info.src_port;
			if(npq1->pci_number<PICTURE_GATHER_NUMBER){
				npq1->pic_gather[npq1->pci_number].flag=1;
				strncpy(npq1->pic_gather[npq1->pci_number++].path,info.path,strlen(info.path));
			}
		}
        break;
	case MEM_TYPE_EMAIL_DATA:
		if(index == hashval){
			npq2=&g_share_email[index];
			if(0==path_flag){
				npq2->flag=1;
				npq2->next=0;
				npq2->dst_ip=info.dst_ip;
				npq2->dst_port=info.dst_port;
				npq2->src_ip=info.src_ip;
				npq2->src_port=info.src_port;
				strncpy(npq2->email_path,info.path,strlen(info.path));
			}
		}else{
			cur =hashval;
			while(g_share_email[cur].next!=0)
				cur=g_share_email[cur].next;
			g_share_email[cur].next=index;
			npq2= &g_share_email[index];
			npq2->flag=1;
			npq2->next=0;
			npq2->dst_ip=info.dst_ip;
			npq2->dst_port=info.dst_port;
			npq2->src_ip=info.src_ip;
			npq2->src_port=info.src_port;
			strncpy(npq2->email_path,info.path,strlen(info.path));
		}

		break;
	default:
		break;
	return 0;
	}

	return 1;
}


void *DispGetHashdb(PktInfo ptf, int type)
{
	http_picture *npq1=NULL;
	emails_data  *npq2=NULL;
	switch(type){
	case MEM_TYPE_HTTP_PICTURE:
		npq1=(http_picture *)&g_share_http[Hash(ptf)];
		while(npq1->next!=0){
			if(ptf.dst_ip==npq1->dst_ip && ptf.dst_port==npq1->dst_port &&
			   ptf.src_ip==npq1->src_ip && ptf.src_port==npq1->src_port){
				return npq1;
			}
			npq1=(http_picture *)&g_share_http[npq1->next];
		}
		if(ptf.dst_ip==npq1->dst_ip && ptf.dst_port==npq1->dst_port &&
		   ptf.src_ip==npq1->src_ip && ptf.src_port==npq1->src_port){
			return npq1;
		}
		break;
	case MEM_TYPE_EMAIL_DATA:
		npq2=(emails_data *)&g_share_email[Hash(ptf)];
		while(npq2->next!=0){
			if(ptf.dst_ip==npq2->dst_ip && ptf.dst_port==npq2->dst_port &&
			   ptf.src_ip==npq2->src_ip && ptf.src_port==npq2->src_port){
				return npq2;
			}
			npq2=(emails_data *)&g_share_email[npq2->next];
		}
		if(ptf.dst_ip==npq2->dst_ip && ptf.dst_port==npq2->dst_port &&
		   ptf.src_ip==npq2->src_ip && ptf.src_port==npq2->src_port){
			return npq2;
		}
		break;
	default:
		break;
	}

	return NULL;
}


unsigned int GetSumPoly(unsigned char data)
{
	unsigned int sum_poly = data;
	int j;
	sum_poly <<= 24;
	for(j = 0; j < 8; j++)
	{
		int hi = sum_poly&0x80000000;
		sum_poly <<= 1;
		if(hi) sum_poly = sum_poly^POLY;
	}
	return sum_poly;
}

void CreateCrcTable(void)
{
	int i;
	for(i = 0; i < 256; i++){
		crc_table[i] = GetSumPoly(i&0xFF);
	}
}

unsigned int CRC20Key(unsigned char* data, int len)
{
	int i;
	unsigned int reg = 0xFFFFFFFF;
	for(i = 0; i < len; i++)
	{
		reg = (reg<<8) ^ crc_table[(reg>>24)&0xFF ^ data[i]];
	}
	return (reg&0XFFFFF);
}

int Hash( PktInfo ptf )
{
    char c;
    int i;
    int hval = 0;

    hval=CRC20Key((unsigned char *)&ptf,sizeof(PktInfo));

    return (hval % HASH_SIZE);
}

char * ip2str(int ip_host_order)
{
    int ip_net_order = htonl(ip_host_order);
    struct in_addr ipAddr;
    ipAddr.s_addr = ip_net_order;

    return inet_ntoa(ipAddr);
}

void printIp(int ip_host_order, char *desc)
{
    printf("%s:0x%x %s\n",desc, ip_host_order, ip2str(ip_host_order));
}

/*just for test*/
int main()
{
	bool rel=0;
	int i,j;
	int count;
    PktInfo       ptf;
	http_picture *get_data=NULL;
    http_picture *http_file = NULL;

	CreateCrcTable();

	rel=DispAttachShareMem();
	if(!rel){
		printf("init share mem error !\n");
		exit(-1);
	}

	for(i=0;i<HASH_SIZE*2;i++){
		if(g_share_http[i].flag==1){
			printf("\n===============================\n");
            printIp(g_share_http[i].src_ip, "src_ip");
            printIp(g_share_http[i].dst_ip, "dst_ip");
			printf("src_port:%d\n",g_share_http[i].src_port);
			printf("dst_port:%d\n",g_share_http[i].dst_port);

			for(j=0;j<g_share_http[i].pci_number;j++){
				if(g_share_http[i].pic_gather[j].flag==1)
					printf("\tpicture:%s\n",g_share_http[i].pic_gather[j].path);
			}

            /* 使用 hash */
            ptf.src_ip = g_share_http[i].src_ip;
            ptf.dst_ip = g_share_http[i].dst_ip;
            ptf.src_port = g_share_http[i].src_port;
            ptf.dst_port = g_share_http[i].dst_port;
            ptf.proto = 6;

            http_file = (http_picture *)DispGetHashdb(ptf, MEM_TYPE_HTTP_PICTURE);
            if (NULL == http_file)
            {
                printf("hash not found with same ip and port.\n");
            }

		}

	}

	return 0;

}
