
说明：

本次设计共享内存表，xplico产生数据写入共享内存表中，解析协议部分用于查共享内存表；

解析协议部分整合共享内存代码说明：
	1、相关数据结构和宏全部整合进去；
	2、功能函数只能用到部分，另一部分是xplico写共享内存使用，可以全部整合进去，
	   也可只整个使用到的函数；
	   需要整合的函数：
	   bool RstAttachShareMem(void);    // 获取功能内存表位置，指针指向；
	   void *DispGetHashdb(PktInfo ptf, int type);  // 查表
	   int Hash( PktInfo ptf );   // 计算hash值
	   unsigned int CRC20Key(unsigned char* data, int len);
       void CreateCrcTable(void);
       unsigned int GetSumPoly(unsigned char data);
       
	3、初始化两个函数：
	   bool rel=0;
	   
	   CreateCrcTable();

	   rel=RstAttachShareMem();
	   if(!rel){
		  printf("init share mem error !\n");
		  exit(-1);
	   }

	 4、查表使用实例：
	 	PktInfo ptf;   // 填充五元组
		ptf.src_ip=0xac10141e;  //源ip     unsigned int类型
		ptf.dst_ip=0x3ad77623;  //目的ip   unsigned int类型
		ptf.src_port=63240;
		ptf.dst_port=80;
		ptf.proto=6;

		http_picture *get_data=NULL；
		get_data=(http_picture *)DispGetHashdb(ptf, MEM_TYPE_HTTP_PICTURE);
		if(get_data!=NULL){
			printf("\n\n************************* search result ****************************\n");
			printf("src_ip:%d\n",get_data->src_ip);
			printf("dst_ip:%d\n",get_data->dst_ip);
			printf("src_port:%d\n",get_data->src_port);
			printf("dst_port:%d\n",get_data->dst_port);
			for(j=0;j<PICTURE_GATHER_NUMBER;j++){
				if(get_data->pic_gather[j].flag==1)
					printf("\tpicture:%s\n",get_data->pic_gather[j].path);
			}
		}



clear_share.sh脚本用于清理共享内存





