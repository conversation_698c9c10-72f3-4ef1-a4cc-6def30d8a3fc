/****************************************************************************************
 * 文 件 名 : yaEty_content_reassembly_share_mem.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : liugh      '2018-05-21
* 编    码 : liugh      '2018-05-21
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _MEM_SHARE_H_
#define _MEM_SHARE_H_

#if 0
#define COMM_PICTURE_MEM_ID      30001

#define HASH_SIZE               9999

#define PICTURE_GATHER_NUMBER   50
#define PICTURE_PATH_SIZE       128
#define POLY 0x01101 // CRC20生成多项式x^20+x^12+x^8+1即:01101 CRC32:04C11DB7L

#define MEM_TYPE_HTTP_PICTURE   1


typedef struct _picture_path{
	char path[PICTURE_PATH_SIZE];
	unsigned char flag;
}picture_path;

typedef struct _http_picture{
	picture_path   pic_gather[PICTURE_GATHER_NUMBER];
	unsigned int   src_ip;
	unsigned int   dst_ip;
	unsigned short src_port;
	unsigned short dst_port;
	unsigned int   next;
	unsigned char  flag;
}http_picture;

#define HTTP_PICTURE_LIST_SIZE sizeof(http_picture)


typedef struct _picture_info{
	unsigned int   src_ip;
	unsigned short src_port;
	unsigned int   dst_ip;
	unsigned short dst_port;
	char path[PICTURE_PATH_SIZE];
}picture_info;

typedef struct _PktInfo{
	unsigned int   src_ip;
	unsigned short src_port;
	unsigned int   dst_ip;
	unsigned short dst_port;
	unsigned short proto;
}PktInfo;


#include "c_lang_linkage_start.h"


/* interface */
void          CreateCrcTable(void);
http_picture *DispAttachShareMem(void);
void         *DispGetHashdb(PktInfo ptf, int type);

/* helpers */
int content_reassembly_share_mem_test();
bool         DispInitShareMem(void);
unsigned int DispGetFreeMemDB(int type, unsigned int hashval);
bool         DispSetHashdb(picture_info info, int type);
int          Hash( PktInfo ptf );
unsigned int CRC20Key(unsigned char* data, int len);
unsigned int GetSumPoly(unsigned char data);

#include "c_lang_linkage_end.h"
#endif


/* share mem id*/
#define COMM_PICTURE_MEM_ID      30001
#define COMM_EMAIL_MEM_ID        30002

#define HASH_SIZE               9999

#define PICTURE_GATHER_NUMBER   50
#define PICTURE_PATH_SIZE       128
#define EMAILS_DATA_PATH        128
#define POLY 0x01101 // CRC20生成多项式x^20+x^12+x^8+1即:01101 CRC32:04C11DB7L


/* type */
#define MEM_TYPE_HTTP_PICTURE   1
#define MEM_TYPE_EMAIL_DATA     2

//typedef unsigned char bool;


typedef struct _picture_path{
	char path[PICTURE_PATH_SIZE];
	unsigned char flag;
}picture_path;

typedef struct _http_picture{
	picture_path   pic_gather[PICTURE_GATHER_NUMBER];
	unsigned int   src_ip;
	unsigned int   dst_ip;
	unsigned short src_port;
	unsigned short dst_port;
	unsigned short pci_number;
	unsigned int   next;
	unsigned char  flag;
}http_picture;


typedef struct _emails_data{
	char   email_path[EMAILS_DATA_PATH];
	unsigned int   src_ip;
	unsigned int   dst_ip;
	unsigned short src_port;
	unsigned short dst_port;
	unsigned int   next;
	unsigned char  flag;
}emails_data;


typedef struct _data_info{
	unsigned int   src_ip;
	unsigned short src_port;
	unsigned int   dst_ip;
	unsigned short dst_port;
	char path[PICTURE_PATH_SIZE];
}data_info;

typedef struct _PktInfo{
	unsigned int   src_ip;
	unsigned short src_port;
	unsigned int   dst_ip;
	unsigned short dst_port;
	unsigned short proto;
}PktInfo;

typedef struct _email_info{
	unsigned int   src_ip;
	unsigned short src_port;
	unsigned int   dst_ip;
	unsigned short dst_port;
	char path[PICTURE_PATH_SIZE];
}email_info;



#define HTTP_PICTURE_LIST_SIZE sizeof(http_picture)
#define EMAILS_DATA_LIST_SIZE  sizeof(emails_data)


bool DispAttachShareMem(void);
bool DispInitShareMem(void);
unsigned int DispGetFreeMemDB(data_info info, unsigned int hashval, int type, int *path_flag);

bool DispSetHashdb(data_info info, int type);
void *DispGetHashdb(PktInfo ptf, int type);


/* hash calculate */
int Hash( PktInfo ptf );
unsigned int CRC20Key(unsigned char* data, int len);
void CreateCrcTable(void);
unsigned int GetSumPoly(unsigned char data);







#endif
