Index: wiretap/libpcap.c
===================================================================
--- wiretap/libpcap.c	(revision 2397)
+++ wiretap/libpcap.c	(working copy)
@@ -542,7 +542,11 @@
 	 */
 	struct pcaprec_ss990915_hdr rec_hdr;
 	int	ret;
+    gint64 header_begin = 0;                   /* add by zhengsw@2018.12.05: record packet header pos to calculate header len */
 
+    /* mod_S by zhengsw@2018.12.05 */
+READ_HEADER:
+    header_begin = file_tell(fh);
 	if (!libpcap_read_header(wth, fh, err, err_info, &rec_hdr)) {
 		if (*err == 0) {
 			/*
@@ -565,6 +569,13 @@
 		return -1;
 	}
 
+    if (rec_hdr.hdr.ts_sec == PCAP_MAGIC)
+    {   /* oh! it's a file header in this file, skip the pcap file header */
+        file_seek(fh, sizeof(struct pcap_hdr) + sizeof PCAP_MAGIC - (file_tell(fh) - header_begin), SEEK_CUR, err);
+        goto READ_HEADER;
+    }
+    /* mod_E by zhengsw@2018.12.05 */
+
 	ret = 0;	/* start out presuming everything's OK */
 	switch (wth->file_type_subtype) {
 
@@ -705,12 +716,23 @@
 	guint orig_size;
 	int phdr_len;
 	libpcap_t *libpcap;
+    gint64 header_begin = 0;                /* add by zhengsw@2018.12.05: record packet header pos to calculate header len */
 
 	libpcap = (libpcap_t *)wth->priv;
 
-	if (!libpcap_read_header(wth, fh, err, err_info, &hdr))
-		return FALSE;
+/* mod_S by zhengsw@2018.12.04 */
+READ_HEADER:
+    header_begin = file_tell(fh);
+    if (!libpcap_read_header(wth, fh, err, err_info, &hdr))
+        return FALSE;
 
+    if (hdr.hdr.ts_sec == PCAP_MAGIC)
+    {   /* oh! it's a file header in this file, skip the pcap file header */
+        file_seek(fh, sizeof(struct pcap_hdr) + sizeof PCAP_MAGIC - (file_tell(fh) - header_begin), SEEK_CUR, err);
+        goto READ_HEADER;
+    }
+/* mod_E by zhengsw@2018.12.04 */
+
 	if (hdr.hdr.incl_len > wtap_max_snaplen_for_encap(wth->file_encap)) {
 		/*
 		 * Probably a corrupt capture file; return an error,
