0)约定：
   a) == need ==


1)编译：
  a) CMakeOption.txt 选项中关闭 wireshark，只需要 tshark 等即可。
  b) CMakeOption.txt 选项中关闭"将警告视为错误"
  c) 使用 cmake -D CMAKE_BUILD_TYPE=Debug . 生成无优化版 makefile 配置，便于调试
  d) 静态库生成：option(ENABLE_STATIC            "Build Wireshark libraries statically" ON)
  e) make epan or make tshark
  f) cmake -DCMAKE_CXX_INCLUDE_WHAT_YOU_USE="iwyu" <builddir>
  g) 下载编译依赖包：tools/win-setup.ps1


2）register.c:
 register_dissector_files ： epan/dissectors/CMakeLists.txt

2.3) CMake 系统调整的文件：
M       epan/dfilter/scanner_lex.h
M       epan/diam_dict.c
M       epan/diam_dict_lex.h
M       epan/dissectors/register.c
M       epan/dtd_preparse.c
M       epan/dtd_preparse_lex.h
M       epan/radius_dict.c
M       epan/radius_dict_lex.h
M       epan/uat_load.c
M       epan/uat_load_lex.h
M       plugins/ethercat/plugin.c
M       plugins/irda/plugin.c
M       plugins/opcua/plugin.c
M       plugins/profinet/plugin.c
M       plugins/wimax/plugin.c
M       ui/text_import_scanner.c
M       ui/text_import_scanner_lex.h
M       wiretap/ascend.c
M       wiretap/ascend.h
M       wiretap/ascend_scanner.c
M       wiretap/ascend_scanner_lex.h
M       wiretap/k12text.c
M       wiretap/k12text_lex.h

CMake 构建：
生成的文件：
tshark-tap-register.c         -> CMakeLists.txt:register_tap_files  -> cmake/modules/UseMakeTaps.cmake:MACRO(REGISTER_TAP_FILES _outputfile)
epan/dissectors/dissectors.c  -> epan/dissectors/CMakeLists.txt: COMMAND make-dissectors dissectors.c @dissectors.in.txt
	file(GENERATE
        OUTPUT "${CMAKE_CURRENT_BINARY_DIR}/dissectors.in.txt"
        CONTENT "$<JOIN:${ALL_DISSECTOR_SRC},\n>\n"
)
add_custom_command(
        OUTPUT dissectors.c
        COMMAND make-dissectors dissectors.c @dissectors.in.txt
        DEPENDS make-dissectors ${ALL_DISSECTOR_SRC}
                "${CMAKE_CURRENT_BINARY_DIR}/dissectors.in.txt"
        COMMENT "Making dissectors.c"
)

2)修改


-- The following OPTIONAL packages have not been found:

 * Qt5Core
 * BCG729, G.729 decoder, <https://www.linphone.org/technical-corner/bcg729/overview>
   Support for G.729 codec in RTP player
 * CAP
 * CARES (required version >= 1.5.0)
 * GNUTLS (required version >= 2.12.0)
 * JSONGLIB
 * LIBSSH (required version >= 0.6), Library for implementing SSH clients, <https://www.libssh.org/>
   extcap remote SSH interfaces (sshdump, ciscodump)
 * LUA
 * LZ4, LZ4 is lossless compression algorithm used in some protocol (CQL...), <http://www.lz4.org>
   LZ4 decompression in CQL and Kafka dissectors
 * MaxMindDB
 * NGHTTP2, HTTP/2 C library and tools, <https://nghttp2.org>
   Header decompression in HTTP2
 * NL
 * SBC, Bluetooth low-complexity, subband codec (SBC) decoder, <https://git.kernel.org/pub/scm/bluetooth/sbc.git>
   Support for playing SBC codec in RTP player
 * SMI
 * SNAPPY, A fast compressor/decompressor from Google, <http://google.github.io/snappy/>
   Snappy decompression in CQL and Kafka dissectors
 * SPANDSP, a library of many DSP functions for telephony, <http://www.soft-switch.org/>
   Support for G.722 and G.726 codecs in RTP player
 * Asciidoctor (required version >= 1.5)

-- Configuring done

2）dissector
声明
   int
dissect_PROTOABBREV(tvbuff_t *tvb, packet_info *pinfo, proto_tree *tree);

tvb accesor:
tvb_bytes_to_str

3) show:
   the GUI tree is drawn via
     proto_tree_draw().

4) dessector 注册：
     the file containing a dissector's "register" routine must be
    added to "DISSECTOR_SRC" in "epan/dissectors/Makefile.am"
    (and in "epan/dissectors/CMakeLists.txt");

    the "register" routine must have a name of the form
    "proto_register_XXX";

    the "register" routine must take no argument, and return no
    value;

    the "register" routine's name must appear in the source file
    either at the beginning of the line, or preceded only by "void "
    at the beginning of the line (that would typically be the
    definition) - other white space shouldn't cause a problem, e.g.:



二.解析流程：
   tvbuff_t   *tvb     // 原始数据
   proto_tree *tree    // 结构化数据


   #0  basic_request_dissector (tvb=0x5555557ac190, tree=0x0, offset=0,
    line=0x555555816d60 "GET /yt/preclk.a2h28.8313475.10270.5!2~5!3~5~5~A?logtype=2&title=%u571F%u8C46%20-%20%u53EC%u5524%u5168%u7403%u6709%u8DA3%u77ED%u89C6%u9891%uFF0C%u5168%u7403%u9886%u5148%u77ED%u89C6%u9891%u5E73%u53F0%u"...,
    lineend=0x5555558172e2 "\r\nHost: gm.mmstat.com\r\nUser-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:57.0) Gecko/20100101 Firefox/57.0\r\nAccept: */*\r\nAccept-Language: en-US,en;q=0.5\r\nAccept-Encoding: gzip, deflate\r\nRef"..., conv_data=0x7fffe6ab3d50)
    at /root/dev/wireshark/epan/dissectors/packet-http.c:1947
#1  0x00007ffff4728640 in dissect_http_message (tvb=tvb@entry=0x5555557ac190, offset=offset@entry=0, pinfo=pinfo@entry=0x5555558149e8, tree=tree@entry=0x0,
    conv_data=conv_data@entry=0x7fffe6ab3d50, proto_tag=proto_tag@entry=0x7ffff532cdf0 "HTTP", proto=62694, end_of_stream=end_of_stream@entry=0)
    at /root/dev/wireshark/epan/dissectors/packet-http.c:1417
#2  0x00007ffff472a5f0 in dissect_http_on_stream (tvb=tvb@entry=0x5555557ac190, pinfo=pinfo@entry=0x5555558149e8, tree=tree@entry=0x0,
    conv_data=conv_data@entry=0x7fffe6ab3d50, end_of_stream=0) at /root/dev/wireshark/epan/dissectors/packet-http.c:3454
#3  0x00007ffff472a8b7 in dissect_http_tcp (tvb=0x5555557ac190, pinfo=0x5555558149e8, tree=0x0, data=0x7fffffffd130)
    at /root/dev/wireshark/epan/dissectors/packet-http.c:3505
#4  0x00007ffff431b19b in call_dissector_through_handle (handle=handle@entry=0x7fffe83bbb80, tvb=tvb@entry=0x5555557ac190, pinfo=pinfo@entry=0x5555558149e8,
    tree=tree@entry=0x0, data=data@entry=0x7fffffffd130) at /root/dev/wireshark/epan/packet.c:692
#5  0x00007ffff431c2ce in call_dissector_work (handle=0x7fffe83bbb80, tvb=tvb@entry=0x5555557ac190, pinfo_arg=pinfo_arg@entry=0x5555558149e8,
    tree=tree@entry=0x0, add_proto_name=add_proto_name@entry=1, data=data@entry=0x7fffffffd130) at /root/dev/wireshark/epan/packet.c:777
#6  0x00007ffff431cc3c in dissector_try_uint_new (sub_dissectors=<optimized out>, uint_val=80, tvb=tvb@entry=0x5555557ac190,
    pinfo=pinfo@entry=0x5555558149e8, tree=tree@entry=0x0, add_proto_name=add_proto_name@entry=1, data=data@entry=0x7fffffffd130)
    at /root/dev/wireshark/epan/packet.c:1359
#7  0x00007ffff4baccf7 in decode_tcp_ports (tvb=tvb@entry=0x5555557ac1e0, offset=<optimized out>, pinfo=pinfo@entry=0x5555558149e8, tree=tree@entry=0x0,
    src_port=src_port@entry=53750, dst_port=dst_port@entry=80, tcpd=tcpd@entry=0x7fffe6ab2e10, tcpinfo=tcpinfo@entry=0x7fffffffd130)
    at /root/dev/wireshark/epan/dissectors/packet-tcp.c:5535
#8  0x00007ffff4bad0a3 in process_tcp_payload (tvb=tvb@entry=0x5555557ac1e0, offset=offset@entry=0, pinfo=pinfo@entry=0x5555558149e8, tree=tree@entry=0x0,
    tcp_tree=tcp_tree@entry=0x0, src_port=src_port@entry=53750, dst_port=dst_port@entry=80, seq=seq@entry=0, nxtseq=nxtseq@entry=0,
    is_tcp_segment=is_tcp_segment@entry=0, tcpd=tcpd@entry=0x7fffe6ab2e10, tcpinfo=tcpinfo@entry=0x7fffffffd130)
    at /root/dev/wireshark/epan/dissectors/packet-tcp.c:5619
#9  0x00007ffff4bad91b in desegment_tcp (tcpinfo=0x7fffffffd130, tcpd=0x7fffe6ab2e10, tcp_tree=0x0, tree=0x0, dport=80, sport=53750, nxtseq=1983, seq=1453,
    offset=20, pinfo=0x5555558149e8, tvb=0x5555557ac2d0) at /root/dev/wireshark/epan/dissectors/packet-tcp.c:3199
#10 dissect_tcp_payload (tvb=tvb@entry=0x5555557ac2d0, pinfo=pinfo@entry=0x5555558149e8, offset=offset@entry=20, seq=<optimized out>,
    nxtseq=nxtseq@entry=1983, sport=53750, dport=80, tree=tree@entry=0x0, tcp_tree=tcp_tree@entry=0x0, tcpd=tcpd@entry=0x7fffe6ab2e10,
    tcpinfo=tcpinfo@entry=0x7fffffffd130) at /root/dev/wireshark/epan/dissectors/packet-tcp.c:5692
#11 0x00007ffff4baf446 in dissect_tcp (tvb=0x5555557ac2d0, pinfo=<optimized out>, tree=0x0, data=<optimized out>)
    at /root/dev/wireshark/epan/dissectors/packet-tcp.c:6530
#12 0x00007ffff431b19b in call_dissector_through_handle (handle=handle@entry=0x7fffe8401dc0, tvb=tvb@entry=0x5555557ac2d0, pinfo=pinfo@entry=0x5555558149e8,
    tree=tree@entry=0x0, data=data@entry=0x7fffe6877030) at /root/dev/wireshark/epan/packet.c:692
#13 0x00007ffff431c2ce in call_dissector_work (handle=0x7fffe8401dc0, tvb=tvb@entry=0x5555557ac2d0, pinfo_arg=pinfo_arg@entry=0x5555558149e8,
    tree=tree@entry=0x0, add_proto_name=add_proto_name@entry=1, data=data@entry=0x7fffe6877030) at /root/dev/wireshark/epan/packet.c:777
#14 0x00007ffff431cc3c in dissector_try_uint_new (sub_dissectors=<optimized out>, uint_val=uint_val@entry=6, tvb=tvb@entry=0x5555557ac2d0,
    pinfo=pinfo@entry=0x5555558149e8, tree=tree@entry=0x0, add_proto_name=add_proto_name@entry=1, data=data@entry=0x7fffe6877030)
    at /root/dev/wireshark/epan/packet.c:1359
#15 0x00007ffff478e435 in ip_try_dissect (heur_first=<optimized out>, nxt=6, tvb=tvb@entry=0x5555557ac2d0, pinfo=pinfo@entry=0x5555558149e8,
    tree=tree@entry=0x0, iph=iph@entry=0x7fffe6877030) at /root/dev/wireshark/epan/dissectors/packet-ip.c:1831
#16 0x00007ffff478f5d7 in dissect_ip_v4 (tvb=0x5555557ac320, pinfo=<optimized out>, parent_tree=0x0, data=<optimized out>)
    at /root/dev/wireshark/epan/dissectors/packet-ip.c:2287
#17 0x00007ffff431b19b in call_dissector_through_handle (handle=handle@entry=0x7fffe848d250, tvb=tvb@entry=0x5555557ac320, pinfo=pinfo@entry=0x5555558149e8,
    tree=tree@entry=0x0, data=data@entry=0x0) at /root/dev/wireshark/epan/packet.c:692
#18 0x00007ffff431c2ce in call_dissector_work (handle=0x7fffe848d250, tvb=tvb@entry=0x5555557ac320, pinfo_arg=pinfo_arg@entry=0x5555558149e8,
    tree=tree@entry=0x0, add_proto_name=add_proto_name@entry=1, data=data@entry=0x0) at /root/dev/wireshark/epan/packet.c:777
#19 0x00007ffff431cc3c in dissector_try_uint_new (sub_dissectors=<optimized out>, uint_val=2048, tvb=0x5555557ac320, pinfo=pinfo@entry=0x5555558149e8,
    tree=tree@entry=0x0, add_proto_name=add_proto_name@entry=1, data=data@entry=0x0) at /root/dev/wireshark/epan/packet.c:1359
#20 0x00007ffff431cc87 in dissector_try_uint (sub_dissectors=<optimized out>, uint_val=<optimized out>, tvb=<optimized out>,
    pinfo=pinfo@entry=0x5555558149e8, tree=tree@entry=0x0) at /root/dev/wireshark/epan/packet.c:1383
#21 0x00007ffff4628c53 in dissect_ethertype (tvb=0x555555815180, pinfo=0x5555558149e8, tree=0x0, data=0x7fffffffd780)
    at /root/dev/wireshark/epan/dissectors/packet-ethertype.c:260
#22 0x00007ffff431b19b in call_dissector_through_handle (handle=handle@entry=0x7fffe83955f0, tvb=tvb@entry=0x555555815180, pinfo=pinfo@entry=0x5555558149e8,
    tree=tree@entry=0x0, data=data@entry=0x7fffffffd780) at /root/dev/wireshark/epan/packet.c:692
#23 0x00007ffff431c2ce in call_dissector_work (handle=0x7fffe83955f0, tvb=0x555555815180, pinfo_arg=0x5555558149e8, tree=0x0, add_proto_name=1,
    data=0x7fffffffd780) at /root/dev/wireshark/epan/packet.c:777
#24 0x00007ffff431dee2 in call_dissector_with_data (handle=<optimized out>, tvb=tvb@entry=0x555555815180, pinfo=pinfo@entry=0x5555558149e8,
    tree=tree@entry=0x0, data=data@entry=0x7fffffffd780) at /root/dev/wireshark/epan/packet.c:3103
#25 0x00007ffff46274ac in dissect_eth_common (tvb=tvb@entry=0x555555815180, pinfo=pinfo@entry=0x5555558149e8, parent_tree=parent_tree@entry=0x0,
    fcs_len=fcs_len@entry=-1) at /root/dev/wireshark/epan/dissectors/packet-eth.c:526
#26 0x00007ffff4628025 in dissect_eth (tvb=0x555555815180, pinfo=0x5555558149e8, tree=0x0, data=<optimized out>)
    at /root/dev/wireshark/epan/dissectors/packet-eth.c:802
#27 0x00007ffff431b19b in call_dissector_through_handle (handle=handle@entry=0x7fffe8463660, tvb=tvb@entry=0x555555815180, pinfo=pinfo@entry=0x5555558149e8,
    tree=tree@entry=0x0, data=data@entry=0x5555557e9fe0) at /root/dev/wireshark/epan/packet.c:692
#28 0x00007ffff431c2ce in call_dissector_work (handle=0x7fffe8463660, tvb=tvb@entry=0x555555815180, pinfo_arg=pinfo_arg@entry=0x5555558149e8,
    tree=tree@entry=0x0, add_proto_name=add_proto_name@entry=1, data=data@entry=0x5555557e9fe0) at /root/dev/wireshark/epan/packet.c:777
#29 0x00007ffff431cc3c in dissector_try_uint_new (sub_dissectors=<optimized out>, uint_val=1, tvb=tvb@entry=0x555555815180, pinfo=pinfo@entry=0x5555558149e8,
    tree=tree@entry=0x0, add_proto_name=add_proto_name@entry=1, data=0x5555557e9fe0) at /root/dev/wireshark/epan/packet.c:1359
#30 0x00007ffff465eee1 in dissect_frame (tvb=0x555555815180, pinfo=0x5555558149e8, parent_tree=0x0, data=0x7fffffffdc20)
    at /root/dev/wireshark/epan/dissectors/packet-frame.c:579
#31 0x00007ffff431b19b in call_dissector_through_handle (handle=handle@entry=0x7fffe8398670, tvb=tvb@entry=0x555555815180, pinfo=pinfo@entry=0x5555558149e8,
    tree=tree@entry=0x0, data=data@entry=0x7fffffffdc20) at /root/dev/wireshark/epan/packet.c:692
#32 0x00007ffff431c2ce in call_dissector_work (handle=0x7fffe8398670, tvb=0x555555815180, pinfo_arg=0x5555558149e8, tree=0x0, add_proto_name=1,
    data=0x7fffffffdc20) at /root/dev/wireshark/epan/packet.c:777
#33 0x00007ffff431dee2 in call_dissector_with_data (handle=<optimized out>, tvb=0x555555815180, pinfo=0x5555558149e8, tree=0x0, data=<optimized out>)
    at /root/dev/wireshark/epan/packet.c:3103
#34 0x00007ffff431e446 in dissect_record (edt=edt@entry=0x5555558149d0, file_type_subtype=file_type_subtype@entry=1, rec=rec@entry=0x5555557e9fa0,
#34 0x00007ffff431e446 in dissect_record (edt=edt@entry=0x5555558149d0, file_type_subtype=file_type_subtype@entry=1, rec=rec@entry=0x5555557e9fa0,
---Type <return> to continue, or q <return> to quit---
    tvb=tvb@entry=0x555555815180, fd=fd@entry=0x7fffffffdde0, cinfo=cinfo@entry=0x55555579a090 <cfile+592>) at /root/dev/wireshark/epan/packet.c:566
#35 0x00007ffff43141d4 in epan_dissect_run_with_taps (edt=edt@entry=0x5555558149d0, file_type_subtype=1, rec=rec@entry=0x5555557e9fa0, tvb=0x555555815180,
    fd=fd@entry=0x7fffffffdde0, cinfo=cinfo@entry=0x55555579a090 <cfile+592>) at /root/dev/wireshark/epan/epan.c:542
#36 0x0000555555569f23 in process_packet_single_pass (cf=cf@entry=0x555555799e40 <cfile>, edt=edt@entry=0x5555558149d0, offset=<optimized out>,
    rec=0x5555557e9fa0, pd=pd@entry=0x5555557f0e70 "(,\262\234\317`ܩ\004\207\235^\b", tap_flags=tap_flags@entry=0) at /root/dev/wireshark/tshark.c:3542
#37 0x0000555555565502 in process_cap_file (cf=0x555555799e40 <cfile>, max_byte_count=0, max_packet_count=-70, out_file_name_res=<optimized out>,
    out_file_type=<optimized out>, save_file=0x0) at /root/dev/wireshark/tshark.c:3368
#38 main (argc=<optimized out>, argv=<optimized out>) at /root/dev/wireshark/tshark.c:2053


2）output_fields 赋值：
#0  format_field_values (fields=0x5555557f5380, field_index=0x1, value=0x5555557c9c40 "192.168.1.31") at /root/dev/wireshark/epan/print.c:2339
#1  0x00007ffff3b0722c in proto_tree_get_node_field_values (node=0x7fffe6ede990, data=0x7fffffffdb80) at /root/dev/wireshark/epan/print.c:2399
#2  0x00007ffff3b1666a in proto_tree_children_foreach (tree=0x7fffe6edd8e0, func=0x7ffff3b07181 <proto_tree_get_node_field_values>, data=0x7fffffffdb80)
    at /root/dev/wireshark/epan/proto.c:683
#3  0x00007ffff3b0724f in proto_tree_get_node_field_values (node=0x7fffe6edd8e0, data=0x7fffffffdb80) at /root/dev/wireshark/epan/print.c:2406
#4  0x00007ffff3b1666a in proto_tree_children_foreach (tree=0x5555557b2cf0, func=0x7ffff3b07181 <proto_tree_get_node_field_values>, data=0x7fffffffdb80)
    at /root/dev/wireshark/epan/proto.c:683
#5  0x00007ffff3b07456 in write_specified_fields (format=FORMAT_CSV, fields=0x5555557f5380, edt=0x555555822a40, cinfo=0x5555557a8230 <cfile+592>,
    fh=0x7ffff0f92400 <_IO_2_1_stdout_>) at /root/dev/wireshark/epan/print.c:2453
#6  0x00007ffff3b02c48 in write_fields_proto_tree (fields=0x5555557f5380, edt=0x555555822a40, cinfo=0x5555557a8230 <cfile+592>,
    fh=0x7ffff0f92400 <_IO_2_1_stdout_>) at /root/dev/wireshark/epan/print.c:403
#7  0x000055555556c78d in print_packet (cf=0x5555557a7fe0 <cfile>, edt=0x555555822a40) at /root/dev/wireshark/tshark.c:3954
#8  0x000055555556b85e in process_packet_single_pass (cf=0x5555557a7fe0 <cfile>, edt=0x555555822a40, offset=4094, rec=0x5555557f8040,
    pd=0x5555557fef10 "(,\262\234\317`ܩ\004\207\235^\b", tap_flags=0) at /root/dev/wireshark/tshark.c:3559
#9  0x000055555556b1d6 in process_cap_file (cf=0x5555557a7fe0 <cfile>, save_file=0x0, out_file_type=2, out_file_name_res=0, max_packet_count=-48,
    max_byte_count=0) at /root/dev/wireshark/tshark.c:3368
#10 0x0000555555569168 in main (argc=7, argv=0x7fffffffe448) at /root/dev/wireshark/tshark.c:2053

抓取 interface 时 print_packet 调用堆栈：
(gdb) bt
#0  0x00000000004355d4 in print_packet (cf=0x483c3a0 <cfile>, edt=0x50cad50) at /root/dev/wireshark6/zshark/tshark.c:3915
#1  0x00000000004348e6 in process_packet_single_pass (cf=0x483c3a0 <cfile>, edt=0x50cad50, offset=192, rec=0x48c3430, pd=0x48ca3c0 "\377\377\377\377\377\377\b", tap_flags=0) at /root/dev/wireshark6/zshark/tshark.c:3559
#2  0x00000000004330dd in capture_input_new_packets (cap_session=0x434d260 <global_capture_session>, to_read=0) at /root/dev/wireshark6/zshark/tshark.c:2712
#3  0x00000000004597ff in sync_pipe_input_cb (source=7, user_data=0x434d260 <global_capture_session>) at /root/dev/wireshark6/capchild/capture_sync.c:1691
#4  0x0000000000432af4 in capture () at /root/dev/wireshark6/zshark/tshark.c:2509
#5  0x000000000043261e in main (argc=10, argv=0x7fffffffe428) at /root/dev/wireshark6/zshark/tshark.c:2210
(gdb)


结构：
    struct epan_dissect {
	struct epan_session *session;
	tvbuff_t	*tvb;
	proto_tree	*tree;
	packet_info	pi;
};


     print_columns：
     for (i = 0; i < cf->cinfo.num_cols; i++) {
    col_item = &cf->cinfo.columns[i];
    /* Skip columns not marked as visible. */
    if (!get_column_visible(i))
      continue;
    switch (col_item->col_fmt) {
    case COL_NUMBER:
      column_len = col_len = strlen(col_item->col_data);
      if (column_len < 5)
        column_len = 5;
      line_bufp = get_line_buf(buf_offset + column_len);
      put_spaces_string(line_bufp + buf_offset, col_item->col_data, col_len, column_len);
      break;

三.libwireshark 抽取

epan/CMakeFiles/epan.dir/build.make：
 build: make dumpabi-libwireshark
        or : make  -f CMakeFiles/Makefile2 dumpabi-libwireshark

		【dumpabi-libwireshark: epan/CMakeFiles/dumpabi-libwireshark.dir/rule】


# Build rule for subdir invocation for target.
【epan/CMakeFiles/dumpabi-libwireshark.dir/rule: cmake_check_build_system】
        $(CMAKE_COMMAND) -E cmake_progress_start /root/dev/wireshark2/CMakeFiles 74
        $(MAKE) -f CMakeFiles/Makefile2 【epan/CMakeFiles/dumpabi-libwireshark.dir/all】
        $(CMAKE_COMMAND) -E cmake_progress_start /root/dev/wireshark2/CMakeFiles 0
.PHONY : epan/CMakeFiles/dumpabi-libwireshark.dir/rule

# All Build rule for target.
epan/CMakeFiles/dumpabi-libwireshark.dir/all: 【epan/CMakeFiles/epan.dir/all】
 ===       $(MAKE) -f epan/CMakeFiles/dumpabi-libwireshark.dir/build.make 【epan/CMakeFiles/dumpabi-libwireshark.dir/depend】
           $(MAKE) -f epan/CMakeFiles/dumpabi-libwireshark.dir/build.make 【epan/CMakeFiles/dumpabi-libwireshark.dir/build】
 ===       @$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/dev/wireshark2/CMakeFiles --progress-num= "Built target dumpabi-libwireshark"
.PHONY : epan/CMakeFiles/dumpabi-libwireshark.dir/all


# Convenience name for "all" pass in the directory.
【
epan/all: epan/CMakeFiles/epan.dir/all
epan/all: epan/crypt/all
epan/all: epan/dfilter/all
epan/all: epan/dissectors/all
epan/all: epan/ftypes/all
epan/all: epan/wmem/all
epan/all: epan/wslua/all
】

.PHONY : epan/all


# All Build rule for target.
epan/CMakeFiles/epan.dir/all: CMakeFiles/version.dir/all
epan/CMakeFiles/epan.dir/all: epan/crypt/CMakeFiles/crypt.dir/all
epan/CMakeFiles/epan.dir/all: epan/dfilter/CMakeFiles/dfilter.dir/all
epan/CMakeFiles/epan.dir/all: epan/dissectors/CMakeFiles/dissectors-corba.dir/all
epan/CMakeFiles/epan.dir/all: epan/dissectors/CMakeFiles/dissectors.dir/all
epan/CMakeFiles/epan.dir/all: epan/ftypes/CMakeFiles/ftypes.dir/all
epan/CMakeFiles/epan.dir/all: epan/wmem/CMakeFiles/wmem.dir/all
epan/CMakeFiles/epan.dir/all: epan/wslua/CMakeFiles/wslua.dir/all
epan/CMakeFiles/epan.dir/all: tools/lemon/CMakeFiles/lemon.dir/all
epan/CMakeFiles/epan.dir/all: wiretap/CMakeFiles/wiretap.dir/all
epan/CMakeFiles/epan.dir/all: wsutil/CMakeFiles/wsutil.dir/all
        $(MAKE) -f epan/CMakeFiles/epan.dir/build.make epan/CMakeFiles/epan.dir/depend
        $(MAKE) -f epan/CMakeFiles/epan.dir/build.make epan/CMakeFiles/epan.dir/build
        @$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/dev/wireshark2/CMakeFiles --progress-num=67,68,69,70,71 "Built target epan
"
.PHONY : epan/CMakeFiles/epan.dir/all


三.重要结构

typedef struct _capture_session {
    ws_process_id fork_child;             /**< If not WS_INVALID_PID, in parent, process ID of child */
    int       fork_child_status;          /**< Child exit status */
#ifdef _WIN32
    int       signal_pipe_write_fd;       /**< the pipe to signal the child */
#endif
    capture_state state;                  /**< current state of the capture engine */
#ifndef _WIN32
    uid_t     owner;                      /**< owner of the cfile */
    gid_t     group;                      /**< group of the cfile */
#endif
    gboolean  session_started;
    guint32   count;                      /**< Total number of frames captured */
    capture_options *capture_opts;        /**< options for this capture */
    capture_file *cf;                     /**< ✪ handle to cfile  /
    struct _info_data *cap_data_info;          /**< stats for this capture */
} capture_session;

typedef struct _capture_file {
  epan_t      *epan;                 /* ✪ 由 tshark_epan_new 创建 */
  file_state   state;                /* Current state of capture file */
  gchar       *filename;             /* Name of capture file */
  gchar       *source;               /* Temp file source, e.g. "Pipe from elsewhere" */
  gboolean     is_tempfile;          /* Is capture file a temporary file? */
  gboolean     unsaved_changes;      /* Does the capture file have changes that have not been saved? */
  gboolean     stop_flag;            /* Stop current processing (loading, searching, etc.) */

  gint64       f_datalen;            /* Size of capture file data (uncompressed) */
  guint16      cd_t;                 /* File type of capture file */
  unsigned int open_type;            /* open_routine index+1 used, if selected, or WTAP_TYPE_AUTO */
  gboolean     iscompressed;         /* TRUE if the file is compressed */
  int          lnk_t;                /* File link-layer type; could be WTAP_ENCAP_PER_PACKET */
  GArray      *linktypes;            /* Array of packet link-layer types */
  guint32      count;                /* Total number of frames */
  guint64      packet_comment_count; /* Number of comments in frames (could be >1 per frame... */
  guint32      displayed_count;      /* Number of displayed frames */
  guint32      marked_count;         /* Number of marked frames */
  guint32      ignored_count;        /* Number of ignored frames */
  guint32      ref_time_count;       /* Number of time referenced frames */
  gboolean     drops_known;          /* TRUE if we know how many packets were dropped */
  guint32      drops;                /* Dropped packets */
  nstime_t     elapsed_time;         /* Elapsed time */
  int          snap;                 /* Maximum captured packet length; 0 if unknown */
  dfilter_t   *rfcode;               /* Compiled read filter program */
  dfilter_t   *dfcode;               /* Compiled display filter program */
  gchar       *dfilter;              /* Display filter string */
  gboolean     redissecting;         /* TRUE if currently redissecting (cf_redissect_packets) */
  /* search */
  gchar       *sfilter;              /* Filter, hex value, or string being searched */
  gboolean     hex;                  /* TRUE if "Hex value" search was last selected */
  gboolean     string;               /* TRUE if "String" search was last selected */
  gboolean     summary_data;         /* TRUE if "String" search in "Packet list" (Info column) was last selected */
  gboolean     decode_data;          /* TRUE if "String" search in "Packet details" was last selected */
  gboolean     packet_data;          /* TRUE if "String" search in "Packet data" was last selected */
  guint32      search_pos;           /* Byte position of last byte found in a hex search */
  guint32      search_len;           /* Length of bytes matching the search */
  gboolean     case_type;            /* TRUE if case-insensitive text search */
  GRegex      *regex;                /* Set if regular expression search */
  search_charset_t scs_type;         /* Character set for text search */
  search_direction dir;              /* Direction in which to do searches */
  gboolean     search_in_progress;   /* TRUE if user just clicked OK in the Find dialog or hit <control>N/B */
  /* packet data */
  wtap_rec     rec;                  /* Record header */
  Buffer       buf;                  /* Record data */
  /* packet provider */
  struct packet_provider_data provider;
  /* frames */
  guint32      first_displayed;      /* Frame number of first frame displayed */
  guint32      last_displayed;       /* Frame number of last frame displayed */
  column_info  cinfo;                /* Column formatting information */
  frame_data  *current_frame;        /* Frame data for current frame */
  gint         current_row;          /* Row number for current frame */
  epan_dissect_t *edt;               /* ✪ Protocol dissection for currently selected packet，由 epan_dissect_new 创建 */
  field_info  *finfo_selected;       /* Field info for currently selected field */
  gpointer     window;               /* Top-level window associated with file */
  gulong       computed_elapsed;     /* Elapsed time to load the file (in msec). */

  guint32      cum_bytes;
} capture_file;

/* Dissection of a single byte array. Holds tvbuff info as
 * well as proto_tree info. As long as the epan_dissect_t for a byte
 * array is in existence, you must not free or move that byte array,
 * as the structures that the epan_dissect_t contains might have pointers
 * to addresses in your byte array.
 */
struct epan_dissect_t  {
	struct epan_session *session;
	tvbuff_t	*tvb;
	proto_tree	*tree;
	packet_info	pi;
};


输出：
output_fields_t

(gdb) bt
#0  0x00000000004a5fd0 in proto_register_field_init (hfinfo=0x4861ca0, parent=-1) at /root/dev/wireshark6/epan
#1  0x00000000004a3b4f in proto_register_protocol (name=0x18fdf68 "Unreassembled Fragmented Packet", short_nam
lter_name=0x18fdf32 "_ws.unreassembled") at /root/dev/wireshark6/epan/proto.c:6524
#2  0x00000000004b89d5 in register_show_exception () at /root/dev/wireshark6/epan/show_exception.c:45
#3  0x00000000004906f8 in proto_init (register_all_protocols_list=0x4854940 = {...}, register_all_handoffs_lis
    at /root/dev/wireshark6/epan/proto.c:481
#4  0x000000000047005c in epan_init (register_all_protocols_func=0x4b6ba7 <register_all_protocols>, register_a
    0x4b6cfe <register_all_protocol_handoffs>, cb=0x0, client_data=0x0) at /root/dev/wireshark6/epan/epan.c:24
#5  0x0000000000430005 in main (argc=6, argv=0x7fffffffe458) at /root/dev/wireshark6/zshark/tshark.c:936

show_exception:malformed
#0  show_exception (tvb=0x49e1990, pinfo=0x4b4a938, tree=0x4ac8ed0, exception=2, exception_message=0x0)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/show_exception.c:80
#1  0x000000000094303b in dissect_ethertype (tvb=0x4b470c0, pinfo=0x4b4a938, tree=0x4ac8ed0, data=0x7fffffffc550)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/dissectors/packet-ethertype.c:282
#2  0x000000000046bca7 in call_dissector_through_handle (handle=0x7ffff3ed5c40, tvb=0x4b470c0, pinfo=0x4b4a938, tree=0x4ac8ed0, data=0x7fffffffc550)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:684
#3  0x000000000046be49 in call_dissector_work (handle=0x7ffff3ed5c40, tvb=0x4b470c0, pinfo_arg=0x4b4a938, tree=0x4ac8ed0, add_proto_name=1,
    data=0x7fffffffc550) at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:759
#4  0x000000000046f5be in call_dissector_only (handle=0x7ffff3ed5c40, tvb=0x4b470c0, pinfo=0x4b4a938, tree=0x4ac8ed0, data=0x7fffffffc550)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:2999
#5  0x000000000046f601 in call_dissector_with_data (handle=0x7ffff3ed5c40, tvb=0x4b470c0, pinfo=0x4b4a938, tree=0x4ac8ed0, data=0x7fffffffc550)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:3012
#6  0x0000000000941c11 in dissect_eth_common (tvb=0x4b470c0, pinfo=0x4b4a938, parent_tree=0x4ac8ed0, fcs_len=-1)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/dissectors/packet-eth.c:536
#7  0x00000000009423cd in dissect_eth (tvb=0x4b470c0, pinfo=0x4b4a938, tree=0x4ac8ed0, data=0x4b1e030)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/dissectors/packet-eth.c:801
#8  0x000000000046bca7 in call_dissector_through_handle (handle=0x7ffff3ffcba0, tvb=0x4b470c0, pinfo=0x4b4a938, tree=0x4ac8ed0, data=0x4b1e030)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:684
#9  0x000000000046be49 in call_dissector_work (handle=0x7ffff3ffcba0, tvb=0x4b470c0, pinfo_arg=0x4b4a938, tree=0x4ac8ed0, add_proto_name=1, data=0x4b1e030)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:759
#10 0x000000000046ce14 in dissector_try_uint_new (sub_dissectors=0x476b940, uint_val=1, tvb=0x4b470c0, pinfo=0x4b4a938, tree=0x4ac8ed0, add_proto_name=1,
    data=0x4b1e030) at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:1336
#11 0x0000000000989b9e in dissect_frame (tvb=0x4b470c0, pinfo=0x4b4a938, parent_tree=0x4ac8ed0, data=0x7fffffffcd20)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/dissectors/packet-frame.c:529
#12 0x000000000046bca7 in call_dissector_through_handle (handle=0x7ffff3e960e0, tvb=0x4b470c0, pinfo=0x4b4a938, tree=0x4ac8ed0, data=0x7fffffffcd20)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:684
#13 0x000000000046be49 in call_dissector_work (handle=0x7ffff3e960e0, tvb=0x4b470c0, pinfo_arg=0x4b4a938, tree=0x4ac8ed0, add_proto_name=1,
    data=0x7fffffffcd20) at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:759
#14 0x000000000046f5be in call_dissector_only (handle=0x7ffff3e960e0, tvb=0x4b470c0, pinfo=0x4b4a938, tree=0x4ac8ed0, data=0x7fffffffcd20)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:2999
#15 0x000000000046f601 in call_dissector_with_data (handle=0x7ffff3e960e0, tvb=0x4b470c0, pinfo=0x4b4a938, tree=0x4ac8ed0, data=0x7fffffffcd20)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:3012
#16 0x000000000046b6c7 in dissect_record (edt=0x4b4a920, file_type_subtype=1, phdr=0x4b1dfc0, tvb=0x4b470c0, fd=0x7fffffffcdc0, cinfo=0x7fffffffd158)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/packet.c:567
#17 0x0000000000465a2e in epan_dissect_run_with_taps (edt=0x4b4a920, file_type_subtype=1, phdr=0x4b1dfc0, tvb=0x4b470c0, fd=0x7fffffffcdc0,
    cinfo=0x7fffffffd158) at /home/<USER>/dev/trunk/07_yaEty/05_编码/epan/epan.c:473
#18 0x0000000000446991 in CapFileProcessor::ProcessPacketSinglePass (this=0x7fffffffcef0, cf=0x7fffffffcf28, edt=0x4b4a920, offset=18206, whdr=0x4b1dfc0,
    pd=0x4b24e80 "", tap_flags=0) at /home/<USER>/dev/trunk/07_yaEty/05_编码/yaEty/yaEty_cap_file_processor.cpp:201
#19 0x000000000044673b in CapFileProcessor::ProcessCap (this=0x7fffffffcef0)
    at /home/<USER>/dev/trunk/07_yaEty/05_编码/yaEty/yaEty_cap_file_processor.cpp:140


步骤：
1）打开 pcap 包：cf_open
2）process_cap_file
   a) epan_dissect_new  : edt = epan_dissect_new(cf->epan, create_proto_tree, print_packet_info && print_details);
   b) process_packet_single_pass
      b.1) epan_dissect_run_with_taps
	       |___ dissect_record
			   |___call_dissector_with_data(frame_handle, edt->tvb, &edt->pi, edt->tree, &frame_dissector_data);
			         |__ dissect_frame



全局结构：
gpa_name_map    abbrev -> hfinfo       // 在 proto_register_field_init 中进行关联，可用于检测了 abbrev 是否有效
gpa_hfinfo		list of all hfinfo	   // PROTO_REGISTRAR_GET_NTH


point:
packet_cleanup:一些全局结构的销毁

api:
proto_registrar_get_byname				: abbr -> header_field_info
proto_registrar_get_id_byname			: abbr -> hfinfo->id
proto_get_finfo_ptr_array				：获取 tree 中的某个 id 的一组 finfo

get_node_field_value
get_field_hex_value(edt->pi.data_src, fi);
bytestring_to_str
tvb_bytes_to_str
string_to_repr  wmem_free(NULL, str);
format_text
tvb_get_ptr
g_ascii_strtoll


IP:
ipv4_addr_and_mask *ipv4 = (ipv4_addr_and_mask *)fvalue_get(&finfo->value);
guint32             n_addr = ipv4_get_net_order_addr(ipv4);


hash field:

  prime_epan_dissect_with_postdissector_wanted_hfids：
    epan_dissect_prime_with_hfid_array：
	   proto_tree_prime_with_hfid：
	     hfinfo->ref_type = HF_REF_TYPE_DIRECT; // 重点

header field info ref_type 归零：
process_packet_single_pass
|__epan_dissect_reset
   |__proto_tree_reset
      |__free_GPtrArray_value


添加 field hash value:
dissect_xxx
|__proto_tree_add_item
   |__proto_tree_add_item_new
      |__proto_tree_new_item
        |__ proto_tree_add_node
          |__tree_data_add_maybe_interesting_field(pnode->tree_data, fi);
ip.version -> 64606

(gdb) p ptrs
$13 = (GPtrArray *) 0x4aefd40
(gdb) p fi
$14 = (field_info *) 0x7ffff3ad08f0
tree_data=0x4a8df80,
(gdb) p *tree_data
Python Exception <class 'gdb.error'> There is no member named keys.:
$17 = {interesting_hfids = 0x4ae9860, visible = 1, fake_protocols = 1, count = 29, pinfo = 0x4aed2d8}


treedata:
proto_tree_create_root

call_dissector_work：
  wmem_list_append(pinfo->layers, GINT_TO_POINTER(proto_get_id(handle->protocol)));


协议名称：
1) pinfo->layers
   a. top layers，top layer 会得到 rtl,image-gif 等对于应用层协议内容的子协议解析, layers 为 edt->pi.layers
   b. proto_is_frame_protocol 探测是否为有某种协议，如 http 等，探测有成本并且可能不准确，udp,gtp等
2) pinfo->cinfo + COL_PROTOCOL，

   设置与获取：
   dissect_http_message：
      col_set_str(pinfo->cinfo, COL_PROTOCOL, proto_tag);
   col_get_text

   前提：
   build_column_format_array：

   const char *get_frame_top_proto(const wmem_list_t *layers)
{
    int                proto_id = 0;
    const char        *name     = NULL;
    wmem_list_frame_t *topProto = wmem_list_tail(layers);

    proto_id = GPOINTER_TO_INT(wmem_list_frame_data(topProto));
    name     = proto_get_protocol_filter_name(proto_id);

    return name;
}

TODO:
1) wireshark 中 ipv6 也注册了 ip.version 这个字段，导致 hash 时按照 abbr name 无法得到原本是 ipv4 的 finfo，
   ipv6 在运行时该是后注册的，覆盖了上一个结果。


wireshark 中的 preference:
0）配置文件：
   C:\Users\<USER>\AppData\Roaming\Wireshark\heuristic_protos

1）
  packet-eth.c:
  /* Register configuration preferences */
  eth_module = prefs_register_protocol(proto_eth, NULL);

  prefs_register_bool_preference(eth_module, "assume_padding",
                                 "Assume short frames which include a trailer contain padding",
                                 "Some devices add trailing data to frames. When this setting is checked "
                                 "the Ethernet dissector will assume there has been added padding to the "
                                 "frame before the trailer was added. Uncheck if a device added a trailer "
                                 "before the frame was padded.",
                                 &eth_assume_padding);

	可通过配置，改变 eth_assume_padding	的值：
     a) 命令行或者 wireshark 编辑->首选项->protocols->Ethernet ...
        tshark -o eth.assume_padding:false
		./tshark -o eth.assume_padding:false -T fields -e frame.number -e ycf.mac -e ycf.username  -r /mnt/vmshare/pcap_yc/8001_2017_09_07_14_29_48_000.pcap
     b) 代码：
	    const char *pErr = NULL;
		 char buf[] = "eth.assume_padding:false";
	     prefs_set_pref(buf, &pErr);

2018.08.03 不解析 tcp 和重传包：
1) tcp 解析流程：
	dissect_tcp
      dissect_tcp_payload
		 desegment_tcp
            if((tcpd->ta) && ((tcpd->ta->flags&TCP_A_RETRANSMISSION) == TCP_A_RETRANSMISSION))
			{ // 发现是重传，返回，而解析发生在 process_tcp_payload 中
                  ...
                 return;
            }
            process_tcp_payload
              decode_tcp_ports
				dissector_try_uint_new
				 call_dissector_work

2) tcp_analyze_seq 变量决定了 tcpd->ta 的值：
    dissect_tcp:
		 if(tcp_analyze_seq) {
			tcp_analyze_sequence_number
				tcp_analyze_get_acked_struct
				   tcpd->ta = xxx
				tcpd->ta->flags|=TCP_A_RETRANSMISSION;

3) tcp_analyze_seq 是一个可配置首选项：
        prefs_register_bool_preference(tcp_module, "analyze_sequence_numbers",
        "Analyze TCP sequence numbers",
        "Make the TCP dissector analyze TCP sequence numbers to find and flag segment retransmissions, missing segments and RTT",
        &tcp_analyze_seq);


packet num: edt->pi.num
-------------------------------------------------------------
x.dump pcap

		   int          err = 0;
		   gchar       *err_info = NULL;
           wtap_rec     rec;
		   Buffer       buf;

		   wtap_seek_read(cf->provider.wth, fdata->file_off, &rec, &buf, &err,
                         &err_info);

           wtap_dumper * pdh = wtap_dump_open(save_file,
			                     out_file_type, 				// WTAP_FILE_TYPE_SUBTYPE_PCAP
			                     linktype,						// WTAP_ENCAP_ETHERNET
                                 snapshot_length, 			    // 8192
			                     FALSE /* compressed */,
			                     &err);

           wtap_dump(pdh,
					&rec,
					ws_buffer_start_ptr(&buf),
					&err,
					&err_info)

           wtap_dump_close(pdh, &err);

-------------------------------------------------------------
		   dissect_record
		     call_dissector_with_data
			  call_dissector_only
			    call_dissector_work
			     call_dissector_through_handle
				   (dissector_t)handle->dissector_func
				        dissector_try_uint
						  call_dissector_work

-.添加解析插件：
1）添加 build-in 插件
   cmake:
         epan/dissectors/CMakeListsCustom.txt
		 将自定义 packet-xxx.c 添加到 CUSTOM_DISSECTOR_SRC 变量即可。
2）添加 standalone 插件
    a) plugins/epan 目录下，由 foo 复制出新协议如 rtl，并执行替换：
        cp -r foo rtl
        sed -i "s/foo/rtl/g" rtl/*
	b) 使用 svn 外部引用创建 packet-rtl.c 文件；
    c) wireshark 2.6.2 根目录下 CMakeListsCustom.txt：
        CUSTOM_PLUGIN_SRC_DIR 中添加 epan/plugins/rtl
	d) 项目根目录执行 build.sh 脚本
	   会生成 wireshark2.6.2/build/plugins/epan/rtl 项目根目录执行
	e）wireshark2.6.2/build 目录执行：
	   make rtl
	   插件会输出在 wireshark2.6.2/build/run/plugins/epan 中



二.more about tvb_buff_t
tvb_add_to_chain(backing, tvb);
tvb_new_real_data	 

utility: 
wmem_strbuf_t 
wmem_strbuf_append
wmem_strbuf_get_str


三.dissector_try_heuristic
C:\Users\<USER>\AppData\Roaming\Wireshark\heuristic_protos

get_persconffile_dir_no_profile
BUILD_TIME_DATAFILE_DIR
DATAFILE_DIR in config.h

linux:
build/config.h:#define DATAFILE_DIR "/usr/local/share/wireshark"

global:
linux:   /usr/local/share/wireshark
windows: D:\Program Files\Wireshark

person:
windows: %APPDATA%\Wireshark
linux:   /root/.config/wireshark /root/.wireshark

四.性能影响：
1) http 与 wxf 均注册了 80 端口(hash key, 被覆盖),导致 http的


五.缺点：
1) 若包无法组全，则应用层协议无法解析
2) 同一端口不可被多个解析器注册
	   
