
一.起点

全局变量：
capture_file cfile;

创建：
cf_status_t cf_open(capture_file *cf, const char *fname, unsigned int type, gboolean is_tempfile, int *err)


capture:

/*
 * State of a capture session.
 */
typedef struct _capture_session {
    ws_process_id fork_child;             /**< If not WS_INVALID_PID, in parent, process ID of child */
    int       fork_child_status;          /**< Child exit status */
#ifdef _WIN32
    int       signal_pipe_write_fd;       /**< the pipe to signal the child */
#endif
    capture_state state;                  /**< current state of the capture engine */
#ifndef _WIN32
    uid_t     owner;                      /**< owner of the cfile */
    gid_t     group;                      /**< group of the cfile */
#endif
    gboolean  session_started;
    guint32   count;                      /**< Total number of frames captured */
    capture_options *capture_opts;        /**< options for this capture */
    capture_file *cf;                     /**< handle to cfile */
    struct _info_data *cap_data_info;          /**< stats for this capture */
} capture_session;

typedef struct _capture_file {
  epan_t      *epan;
  file_state   state;                /* Current state of capture file */
  gchar       *filename;             /* Name of capture file */
  gchar       *source;               /* Temp file source, e.g. "Pipe from elsewhere" */
  gboolean     is_tempfile;          /* Is capture file a temporary file? */
  gboolean     unsaved_changes;      /* Does the capture file have changes that have not been saved? */
  gboolean     stop_flag;            /* Stop current processing (loading, searching, etc.) */

  gint64       f_datalen;            /* Size of capture file data (uncompressed) */
  guint16      cd_t;                 /* File type of capture file */
  unsigned int open_type;            /* open_routine index+1 used, if selected, or WTAP_TYPE_AUTO */
  gboolean     iscompressed;         /* TRUE if the file is compressed */
  int          lnk_t;                /* File link-layer type; could be WTAP_ENCAP_PER_PACKET */
  GArray      *linktypes;            /* Array of packet link-layer types */
  guint32      count;                /* Total number of frames */
  guint64      packet_comment_count; /* Number of comments in frames (could be >1 per frame... */
  guint32      displayed_count;      /* Number of displayed frames */
  guint32      marked_count;         /* Number of marked frames */
  guint32      ignored_count;        /* Number of ignored frames */
  guint32      ref_time_count;       /* Number of time referenced frames */
  gboolean     drops_known;          /* TRUE if we know how many packets were dropped */
  guint32      drops;                /* Dropped packets */
  nstime_t     elapsed_time;         /* Elapsed time */
  int          snap;                 /* Maximum captured packet length; 0 if unknown */
  dfilter_t   *rfcode;               /* Compiled read filter program */
  dfilter_t   *dfcode;               /* Compiled display filter program */
  gchar       *dfilter;              /* Display filter string */
  gboolean     redissecting;         /* TRUE if currently redissecting (cf_redissect_packets) */
  gboolean     read_lock;            /* TRUE if currently processing a file (cf_read) */
  rescan_type  redissection_queued;  /* Queued redissection type. */
  /* search */
  gchar       *sfilter;              /* Filter, hex value, or string being searched */
  gboolean     hex;                  /* TRUE if "Hex value" search was last selected */
  gboolean     string;               /* TRUE if "String" search was last selected */
  gboolean     summary_data;         /* TRUE if "String" search in "Packet list" (Info column) was last selected */
  gboolean     decode_data;          /* TRUE if "String" search in "Packet details" was last selected */
  gboolean     packet_data;          /* TRUE if "String" search in "Packet data" was last selected */
  guint32      search_pos;           /* Byte position of last byte found in a hex search */
  guint32      search_len;           /* Length of bytes matching the search */
  gboolean     case_type;            /* TRUE if case-insensitive text search */
  GRegex      *regex;                /* Set if regular expression search */
  search_charset_t scs_type;         /* Character set for text search */
  search_direction dir;              /* Direction in which to do searches */
  gboolean     search_in_progress;   /* TRUE if user just clicked OK in the Find dialog or hit <control>N/B */
  /* packet data */
  wtap_rec     rec;                  /* Record header */
  Buffer       buf;                  /* Record data */
  /* packet provider */
  struct packet_provider_data provider;
  /* frames */
  guint32      first_displayed;      /* Frame number of first frame displayed */
  guint32      last_displayed;       /* Frame number of last frame displayed */
  column_info  cinfo;                /* Column formatting information */
  gboolean     columns_changed;      /**< Have the columns been changed in the prefs? (GTK+ only) */
  frame_data  *current_frame;        /* Frame data for current frame */
  gint         current_row;          /* Row number for current frame */
  epan_dissect_t *edt;               /* Protocol dissection for currently selected packet */
  field_info  *finfo_selected;       /* Field info for currently selected field */
  gpointer     window;               /* Top-level window associated with file */
  gulong       computed_elapsed;     /* Elapsed time to load the file (in msec). */

  guint32      cum_bytes;
} capture_file;

全局变量：                                     
capture_session cap_session_;			  // wireshark: main_windows.h->MainWindow->cap_session_
capture_file    cfile;            		  // ui/qt/capture_file.cpp->cfile

打开 pcap 文件：
MainWindow::openCaptureFile
>	libwireshark.dll!dissect_record(epan_dissect * edt, int file_type_subtype, wtap_rec * rec, tvbuff * tvb, _frame_data * fd, epan_column_info * cinfo) 行 474	C
 	libwireshark.dll!epan_dissect_run_with_taps(epan_dissect * edt, int file_type_subtype, wtap_rec * rec, tvbuff * tvb, _frame_data * fd, epan_column_info * cinfo) 行 551	C
 	Wireshark.exe!add_packet_to_packet_list(_frame_data * fdata, _capture_file * cf, epan_dissect * edt, epan_dfilter * dfcode, epan_column_info * cinfo, wtap_rec * rec, const unsigned char * buf, int add_to_packet_list) 行 1186	C
 	Wireshark.exe!read_record(_capture_file * cf, epan_dfilter * dfcode, epan_dissect * edt, epan_column_info * cinfo, __int64 offset) 行 1284	C [-> add_packet_to_packet_list]
 	Wireshark.exe!cf_read(_capture_file * cf, int reloading) 行 644	C	[LOOP]
 	Wireshark.exe!MainWindow::openCaptureFile(QString cf_path, QString read_filter, unsigned int type, int is_tempfile) 行 254	C++
 	Wireshark.exe!MainWindow::openCaptureFile(QString cf_path, QString display_filter) 行 276	C++
 	Wireshark.exe!MainWindow::qt_static_metacall(QObject * _o, QMetaObject::Call _c, int _id, void * * _a) 行 1412	C++

从 interface 上抓包：
1) 由MainWindow::startCapture 启动 dumpcap 进程进行实际抓包并将 pcap 文件：
   写入到 %TMP% 目录，名称以 wireshark_ 打头。
2) ws MainWindow::pipeTimeout  每隔一段时间读取 pcap 文件进行解析：   
 	libwireshark.dll!dissect_record(epan_dissect * edt, int file_type_subtype, wtap_rec * rec, tvbuff * tvb, _frame_data * fd, epan_column_info * cinfo) 行 474	C
 	libwireshark.dll!epan_dissect_run_with_taps(epan_dissect * edt, int file_type_subtype, wtap_rec * rec, tvbuff * tvb, _frame_data * fd, epan_column_info * cinfo) 行 551	C
 	Wireshark.exe!add_packet_to_packet_list(_frame_data * fdata, _capture_file * cf, epan_dissect * edt, epan_dfilter * dfcode, epan_column_info * cinfo, wtap_rec * rec, const unsigned char * buf, int add_to_packet_list) 行 1186	C
 	Wireshark.exe!read_record(_capture_file * cf, epan_dfilter * dfcode, epan_dissect * edt, epan_column_info * cinfo, __int64 offset) 行 1284	C
 	Wireshark.exe!cf_continue_tail(_capture_file * cf, volatile int to_read, int * err) 行 815	C
 	Wireshark.exe!capture_input_new_packets(_capture_session * cap_session, int to_read) 行 498	C
>	Wireshark.exe!sync_pipe_input_cb(int source, void * user_data) 行 1692	C
 	Wireshark.exe!MainWindow::pipeTimeout() 行 1171	C++
 	Wireshark.exe!MainWindow::qt_static_metacall(QObject * _o, QMetaObject::Call _c, int _id, void * * _a) 行 1451	C++
	
packet list 绘制时信息获取：
	col_str = cinfo->columns[column].col_data;
>	Wireshark.exe!PacketListRecord::cacheColumnStrings(epan_column_info * cinfo) 行 298	C++
 	Wireshark.exe!PacketListRecord::dissect(_capture_file * cap_file, bool dissect_color) 行 185	C++
 	Wireshark.exe!PacketListRecord::columnString(_capture_file * cap_file, int column, bool colorized) 行 70	C++
 	Wireshark.exe!PacketListModel::ensureRowColorized(int row) 行 740	C++
 	Wireshark.exe!PacketList::drawNearOverlay() 行 1655	C++
 	Wireshark.exe!PacketList::timerEvent(QTimerEvent * event) 行 579	C++
 	Qt5Cored.dll!QObject::event(QEvent * e) 行 1233	C++

protoTree 绘制时信息获取：	
	1) ProtoTreeModel::index 的实现中，告知 qt 有哪些 QModelIndex(嵌入了proto_node *)作为第几个节点，建立树形结构;
	2) ProtoTreeModel::setRootNode 用于在选中 packetlist 中的 packet 后进行 ProtoTreeModel 中的 root_node_ 的更新;
	3) ProtoTreeModel::data 遍历所有结点，询问每个结点的 value 进行显示(ProtoNode::labelText);