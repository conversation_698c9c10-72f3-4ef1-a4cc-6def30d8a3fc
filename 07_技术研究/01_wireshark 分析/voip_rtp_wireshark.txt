-------------------------------------------------------------
1. 显示 rtp 流：
   >Wireshark.exe!rtpstream_packet(void * arg, _packet_info * pinfo, epan_dissect * edt, const void * arg2) 行 239	C
 	libwireshark.dll!tap_push_tapped_queue(epan_dissect * edt) 行 319	C
 	libwireshark.dll!epan_dissect_run_with_taps(epan_dissect * edt, int file_type_subtype, wtap_rec * rec, tvbuff * tvb, _frame_data * fd, epan_column_info * cinfo) 行 554	C
 	Wireshark.exe!retap_packet(_capture_file * cf, _frame_data * fdata, wtap_rec * rec, const unsigned char * pd, void * argsp) 行 2207	C
 	Wireshark.exe!process_specified_records(_capture_file * cf, packet_range_tag * range, const char * string1, const char * string2, int terminate_is_stop, int(*)(_capture_file *, _frame_data *, wtap_rec *, const unsigned char *, void *) callback, void * callback_args, int show_progress_bar) 行 2171	C
 	Wireshark.exe!cf_retap_packets(_capture_file * cf) 行 2255	C
 	Wireshark.exe!rtpstream_scan(_rtpstream_tapinfo * tapinfo, _capture_file * cap_file, const char * fstring) 行 68	C
 	Wireshark.exe!RtpStreamDialog::RtpStreamDialog(QWidget & parent, CaptureFile & cf) 行 264	C++
 	Wireshark.exe!MainWindow::on_actionTelephonyRTPStreams_triggered() 行 3494	C++

2. rtp 流分析(写 payload 文件)
   >Wireshark.exe!RtpAnalysisDialog::savePayload(QTemporaryFile * tmpfile, _tap_rtp_stat_t * statinfo, _packet_info * pinfo, const _rtp_info * rtpinfo) 行 852	C++
 	Wireshark.exe!RtpAnalysisDialog::addPacket(bool forward, _packet_info * pinfo, const _rtp_info * rtpinfo) 行 771	C++
 	Wireshark.exe!RtpAnalysisDialog::tapPacket(void * tapinfo_ptr, _packet_info * pinfo, epan_dissect * __formal, const void * rtpinfo_ptr) 行 708	C++
 	libwireshark.dll!tap_push_tapped_queue(epan_dissect * edt) 行 319	C
 	libwireshark.dll!epan_dissect_run_with_taps(epan_dissect * edt, int file_type_subtype, wtap_rec * rec, tvbuff * tvb, _frame_data * fd, epan_column_info * cinfo) 行 554	C
 	Wireshark.exe!retap_packet(_capture_file * cf, _frame_data * fdata, wtap_rec * rec, const unsigned char * pd, void * argsp) 行 2207	C
 	Wireshark.exe!process_specified_records(_capture_file * cf, packet_range_tag * range, const char * string1, const char * string2, int terminate_is_stop, int(*)(_capture_file *, _frame_data *, wtap_rec *, const unsigned char *, void *) callback, void * callback_args, int show_progress_bar) 行 2171	C
 	Wireshark.exe!cf_retap_packets(_capture_file * cf) 行 2255	C
 	Wireshark.exe!CaptureFile::retapPackets() 行 221	C++
 	Wireshark.exe!RtpAnalysisDialog::RtpAnalysisDialog(QWidget & parent, CaptureFile & cf, _rtp_stream_info * stream_fwd, _rtp_stream_info * stream_rev) 行 392	C++
 	Wireshark.exe!RtpStreamDialog::on_actionAnalyze_triggered() 行 458	C++
 	Wireshark.exe!RtpStreamDialog::on_buttonBox_clicked(QAbstractButton * button) 行 639	C++
 	Wireshark.exe!RtpStreamDialog::qt_static_metacall(QObject * _o, QMetaObject::Call _c, int _id, void * * _a) 行 160	C++
 	Wireshark.exe!RtpStreamDialog::qt_metacall(QMetaObject::Call _c, int _id, void * * _a) 行 229	C++

3. payload 解码(rtp_packets_)
    Wireshark.exe!RtpAudioStream::addRtpPacket(const _packet_info * pinfo, const _rtp_info * rtp_info) 行 139	C++
 	Wireshark.exe!RtpPlayerDialog::addPacket(_packet_info * pinfo, const _rtp_info * rtpinfo) 行 592	C++
 	Wireshark.exe!RtpPlayerDialog::tapPacket(void * tapinfo_ptr, _packet_info * pinfo, epan_dissect * __formal, const void * rtpinfo_ptr) 行 581	C++
 	libwireshark.dll!tap_push_tapped_queue(epan_dissect * edt) 行 319	C
 	libwireshark.dll!epan_dissect_run_with_taps(epan_dissect * edt, int file_type_subtype, wtap_rec * rec, tvbuff * tvb, _frame_data * fd, epan_column_info * cinfo) 行 554	C
 	Wireshark.exe!retap_packet(_capture_file * cf, _frame_data * fdata, wtap_rec * rec, const unsigned char * pd, void * argsp) 行 2207	C
 	Wireshark.exe!process_specified_records(_capture_file * cf, packet_range_tag * range, const char * string1, const char * string2, int terminate_is_stop, int(*)(_capture_file *, _frame_data *, wtap_rec *, const unsigned char *, void *) callback, void * callback_args, int show_progress_bar) 行 2171	C
 	Wireshark.exe!cf_retap_packets(_capture_file * cf) 行 2255	C
 	Wireshark.exe!CaptureFile::retapPackets() 行 221	C++
 +	Wireshark.exe!RtpPlayerDialog::retapPackets() 行 222	C++
 	Wireshark.exe!RtpPlayerDialog::qt_static_metacall(QObject * _o, QMetaObject::Call _c, int _id, void * * _a) 行 181	C++

	解码
	Wireshark.exe!decode_rtp_packet(_rtp_packet * rp, short * * out_buff, _GHashTable * decoders_hash, unsigned int * channels_ptr, unsigned int * sample_rate_ptr) 行 54	C
 	Wireshark.exe!RtpAudioStream::decode() 行 217	C++
 	Wireshark.exe!RtpPlayerDialog::rescanPackets(bool rescale_axes) 行 268	C++
 	Wireshark.exe!RtpPlayerDialog::retapPackets() 行 225	C++
 	Wireshark.exe!RtpPlayerDialog::qt_static_metacall(QObject * _o, QMetaObject::Call _c, int _id, void * * _a) 行 181	C++
	
4. 播放
   RtpAudioStream::startPlaying	

-------------------------------------------------------------
rtp 流会话管理：
关键结构：
struct _rtpstream_tapinfo {
    rtpstream_tap_reset_cb tap_reset;       /**< tap reset callback */
    rtpstream_tap_draw_cb tap_draw;         /**< tap draw callback */
    tap_mark_packet_cb tap_mark_packet;     /**< packet marking callback */
    void *tap_data;                         /**< data for tap callbacks */
    int                nstreams; /**< number of streams in the list */
+   GList             *strinfo_list; /**< list of rtp_stream_info_t* */
    int                npackets; /**< total number of rtp packets of all streams */
    /* used while tapping. user shouldn't modify these */
    tap_mode_t         mode;
    rtp_stream_info_t *filter_stream_fwd; /**< used as filter in some tap modes */
    rtp_stream_info_t *filter_stream_rev; /**< used as filter in some tap modes */
    FILE              *save_file;
    gboolean           is_registered; /**< if the tap listener is currently registered or not */
};

/** Defines an rtp stream */
typedef struct _rtp_stream_info {
    address         src_addr;
    guint32         src_port;
    address         dest_addr;
    guint32         dest_port;
    guint32         ssrc;

    guint8          payload_type; /**< Numeric payload type */
    gchar          *payload_type_name; /**< Payload type name */
    gboolean        is_srtp;

    guint32         packet_count;
    gboolean        end_stream; /**< Used to track streams across payload types */
    int             rtp_event;

    int             call_num; /**< Used to match call_num in voip_calls_info_t */
    guint32         setup_frame_number; /**< frame number of setup message */
    /* Start and stop packets needed for .num and .abs_ts */
    frame_data     *start_fd;
    frame_data     *stop_fd;
    nstime_t        start_rel_time;     /**< relative start time from pinfo */
    nstime_t        stop_rel_time;      /**< relative stop time from pinfo */
    guint16         vlan_id;
    gboolean        tag_vlan_error;
    gboolean        tag_diffserv_error;

    gboolean        decode; /**< Decode this stream. GTK+ only? */
    GList          *rtp_packet_list; /**< List of RTP rtp_packet_t. GTK+ only */

    tap_rtp_stat_t  rtp_stats;  /**< here goes the RTP statistics info */
    gboolean        problem;    /**< if the streams had wrong sequence numbers or wrong timestamps */
    gchar          *ed137_info;
} rtp_stream_info_t;

struct _rtp_info {
	unsigned int  info_version;
	gboolean      info_padding_set;
	gboolean      info_marker_set;
	guint32       info_media_types;
	unsigned int  info_payload_type;
	unsigned int  info_padding_count;
	guint16       info_seq_num;
	guint32       info_timestamp;
	guint32       info_sync_src;
	guint         info_data_len;       /* length of raw rtp data as reported */
	gboolean      info_all_data_present; /* FALSE if data is cut off */
	guint         info_payload_offset; /* start of payload relative to info_data */
	guint         info_payload_len;    /* length of payload (incl padding) */
	gboolean      info_is_srtp;
	guint32       info_setup_frame_num; /* the frame num of the packet that set this RTP connection */
	const guint8* info_data;           /* pointer to raw rtp data */
	const gchar   *info_payload_type_str;
	gint          info_payload_rate;
	gboolean      info_is_ed137;
	const gchar   *info_ed137_info;
	/*
	* info_data: pointer to raw rtp data = header + payload incl. padding.
	* That should be safe because the "epan_dissect_t" constructed for the packet
	*  has not yet been freed when the taps are called.
	* (destroying the "epan_dissect_t" will end up freeing all the tvbuffs
	*  and hence invalidating pointers to their data).
	* See "add_packet_to_packet_list()" for details.
	*/
};

rtpstream_packet:

/* Defines an RTP packet */
typedef struct _rtp_packet {
    guint32 frame_num;      /* Qt only */
    struct _rtp_info *info;	/* the RTP dissected info */
    double arrive_offset;	/* arrive offset time since the beginning of the stream as ms in GTK UI and s in Qt UI */
    guint8* payload_data;
} rtp_packet_t;













-------------------------------------------------
1) RtpStreamKeeper
	a) map<rtp_stream_info_t, RtpStream>
	
2) RtpStream
    a) 	

