
1. wireshark 解析框架介绍
  1）整体解析流程
        a) read frame: wtap_read(cf->wth...)，frame buf 位于 wtap_buf_ptr(cf->wth)；
		b) epan_dissect_run_with_taps，一层一层调用相应解析器，tvbuff_t -> prototree;
		c) 示例：
		   【frame】  调用：dissect_record -> call_dissector_with_data(frame_handle,...
		              注册：proto_register_frame -> register_dissector
					        packet_cache_proto_handles -> find_dissector("frame")
		   【eth  】  调用：dissect_frame -> dissector_try_uint_new(wtap_encap_dissector_table, pinfo->pkt_encap,...
		              注册：proto_reg_handoff_eth -> dissector_add_uint("wtap_encap", WTAP_ENCAP_ETHERNET, eth_handle);       
		   【ethty】  调用：dissect_eth_common -> call_dissector_with_data(ethertype_handle,...    
                      注册：proto_reg_handoff_eth -> ethertype_handle = find_dissector_add_dependency("ethertype", proto_eth);
					        proto_register_ethertype -> register_dissector("ethertype", dissect_ethertype	
           【ip】     调用 dissect_ethertype -> dissector_try_uint(ethertype_dissector_table, ethertype_data->etype							
		              注册 proto_reg_handoff_ip -> dissector_add_uint("ethertype", ETHERTYPE_IP, ipv4_handle);
		d) 对 prototree 的应用			  
	 
  2）协议注册机制
        a）proto_register_xxx 与 proto_reg_handoff_xxx (register_dissector)，cmake 与 register.c
		b）全局变量：registered_dissectors
		c）header_field_info， field_info，
		     添加字段： proto_tree_add_item
			 显示字段(to_str):get_node_field_value
			 
  3）协议解析 tvbuff_t 与 prototree
        a）tshark 中的 print_packet,对树进行遍历
		b）希望通过 abbrev 进行 hash 得到字段；
		     proto_node->tree_data->interesting_hfids
			 proto_get_finfo_ptr_array
			 
			 dissect_xxx		 
              |__proto_tree_add_item
                 |__proto_tree_add_item_new
                    |__proto_tree_new_item	 
                      |__proto_tree_add_node		 
                        |__tree_data_add_maybe_interesting_field(pnode->tree_data, fi);	
						
			修改：		
                proto_register_field_array
                 |__ proto_register_field_common	
				  
                proto_set_field_ref_type_default_to
		c）使用：PktPostProc_field_generic：wsAbbrevName -> id -> fieldinfoArray		
				
2. yaEty 整体结构介绍
    a）ety + xplico（共享内存，ip -> 还原出的文件路径列表）
	b）ety
	   1）解析
	   2）提取（对应字段）
	      CapFileProcessor::OnPktDissected
		  ProtoFieldExtractor
		  TblRecordWriter
		  ProtoFieldDesc
	   3）输出 tbl 记录  