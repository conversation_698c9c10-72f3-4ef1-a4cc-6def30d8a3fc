#############################################
背景说明：
要求：在清洗wxf协议，需要过滤出既包含weixinnum，又包含filetype字段并且值为1或者2的数据帧【注意：仅仅过滤出指定的数据帧，不能滤出其他的数据帧】
#############################################

以下纪录一下过程：
1、直接采用汇聚分流设备的规则
执行：tcp.payload contains "weixinnum" && tcp.payload contains "filetype"
这样中做法没有办法指定filetype的字段值！

2、 使用赋乐提供的正则
执行：ip.proto == 6&&tcp.payload matches "^\\xab\\x00.*\\x77\\x65\\x69\\x78\\x69\\x6e\\x6e\\x75\\x6d.*\\x66\\x69\\x6c\\x65\\x74\\x79\\x70\\x65.{3,5}[12]"
##############就在这里踩坑了！#################
Wireshark没有提示正则有错误，但是实际上过滤出来的数据帧不仅仅有满足要求的，也有一些杂数据
执行以上语句，不仅将weixinnum，filetype的数据帧过滤出来，还引入了一些其他的数据帧（即正则并没有生效！）

3、修改赋乐的正则
执行：tcp.payload contains 77:65:69:78:69:6e:6e:75:6d && (tcp.payload contains 66:69:6c:65:74:79:70:65:00:00:00:01:31 || tcp.payload contains 66:69:6c:65:74:79:70:65:00:00:00:01:32)
达到目标，过滤出的数据帧正确，不带杂包！
备注：3中执行的指令，：换成 - 也是可以的，注意contains后面不要添加""

############################################################################################################################################################
事后分析：
重新检查2中的指令，ab开头，数据帧中有weixinnum，有filetype字段并且filetype字段值为1或者2，正则本身没有错误。
但是在Wireshark中，".*"标识任意多个非换行字符，在2中使用".*"时，假设ab和weixinnum之间没有换行符，那么可以正确匹配到weixinnum；
weixinnum到filetype之间存在换行符的话，".*"就会匹配有问题后半段的正则就会无效（这里是不是仅仅后半句失效未验证，从现象来看，会引入一些不包含weixinnum字段的数据帧，更像是整个正则失效！）

在Wireshark中重新过滤确定问题：
1、
tcp.payload matches "^\\xab\\x00.*\\x77\\x65\\x69\\x78.*\\x66\\x69\\x6c\\x65\\x74\\x79\\x70\\x65"  规则过滤后没有数据帧 （错误）
tcp.payload matches "^\xab\x00.*\x77\x65\x69\x78.*\x66\x69\x6c\x65\x74\x79\x70\x65"			规则过滤后会引入非表达式的数据帧（错误）
两者区别在于，有没有将"\\"换成"\"，从现象看，过滤规则中需要用到"\"而不能使用"\\"
即"\"的指令正确（过滤结果实际上是错误的），而"\\"的指令不正确

再看，
执行：tcp.payload matches "^\\xab\\x00[\\x00-\\xff]*\\x77\\x65\\x69\\x78[\\x00-\\xff]*\\x66\\x69\\x6c\\x65\\x74\\x79\\x70\\x65"	这个能准确过滤weixinnum和filetype（正确）
如果执行tcp.payload matches "^\xab\x00[\x00-\xff]*\x77\x65\x69\x78[\x00-\xff]*\x66\x69\x6c\x65\x74\x79\x70\x65" 这个会引进一些杂数据（错误）
即"\\"的指令正确，而"\"的指令不正确

这是两个相反的结论，为什么会这样呢？

tcp.payload matches "^\xab\x00" && tcp.payload matches".*\x77\x65\x69\x78" && tcp.payload matches".*\x66\x69\x6c\x65\x74\x79\x70\x65"   这个能准确过滤weixinnum和filetype（正确）
tcp.payload matches "^\\xab\\x00" && tcp.payload matches".*\\x77\\x65\\x69\\x78" && tcp.payload matches".*\\x66\\x69\\x6c\\x65\\x74\\x79\\x70\\x65"	这个也能准确过滤weixinnum和filetype（正确）
这两条指令，又说明，不管"\"还是"\\"都可以得到正确结果。

通过实际测试，虽然tcp.payload matches "^\xab" 和tcp.payload matches "^\\xab" 得到的结果相同，但是tcp.payload matches "^\xab\x00"时并不准确，而tcp.payload matches "^\\xab\\x00"可以正确匹配

最后总结一下：
1、在Wireshark的正则使用中，注意".*"的使用，"."表示任意非回车字符，如果想要适配任意字符，请使用"[0x00-0xff]*"(在Wireshark中应当是"[\\x00-\\xff]*")；
2、在Wireshark中匹配16进制数是"0x"前缀统一变换成"\\x"（注意是2个\）
















