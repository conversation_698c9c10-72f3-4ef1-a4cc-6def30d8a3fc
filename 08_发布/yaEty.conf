
# basic
log_verbose = 0                                                  # 是否输出丰富日志
dissect_options = esp.enable_null_encryption_decode_heuristic:true, ycf.trailer:true          # "," 分隔的控制选项，ycf.trailer:true -> 宜昌固网标签, esp.enable_null_encryption_decode_heuristic:true -> 尝试解析esp的payload，视为未加密(voip解析必开)
child_proc_num = 0                                               # 并行子进程数目, 0 表示不开启子进程
debug_mode = 0                                                   # 1 开启调试模式，所有协议输出字段最后均会添加 __FromPcap 与 __FrameNumber
trailer_type = rt                                                # 流量网络尾标类型，jsc = 金陵，hw = 恒为， rt = 戎腾, 不配不解

DATE_SUBDIR_FLAG            = 1             #是否增加日期子目录rtp_stream/20240515/

# scan
scan_in_input_dir = 1                                          # 是否对输入目录进行扫描，否则对目录中文件解析一遍后直接退出
scan_retry_times  = 1                                           # 当检测到没有文件增加时的重试次数, -1 表示无限扫描
scan_retry_iterval_in_second = 5                                 # 每次重试扫描间隔

# taskid
# 将会在 tbl 文件名末尾增加 taskid
# 如果同时配置了 task_id 与 task_file, 生效的是 task_file(内容不为空) 中配置的 id
# task_id = 42
# task_file = /root/program/yaDpiContent/taskid

# input(scan) and output
scan_ignore_suffixs  = .writing,.ext,.err,.tbl                   # 扫描时需要忽略的文件后缀
writing_suffix       = .writing                                  # “正在写”文件后缀标记，在扫描时如果没有发现需要处理的文件，但是有正在写的则程序会休眠片刻后继续扫描

# voip
#content_file_lower_limit = 1000                                 # voip还原下限, 程序默认会删除还原出来体积小于配置值的voip还原文件(.wav, .raw)。
full_zero_bytes = 0                                             #rtp_stream是否在序号有问题时切割
cut_file = 0
#scan_timeout_rtp_map_second = 60
#禁用协议，被添加的协议不会生成格式文件也不会被解析，协议名大写，用逗号隔开，中间不能有空格
# unused_proto         = VOIP,WXA,RTP,WXF,RTP_STREAM               # tbl 解析模式(不进行关于voip和wx的解析)

# 话单解析模式(仅输出voip 和 rtp_stream)
unused_proto        = GSM SMS,ISAKMP,SDP_L5,SSL/TLS,AH,BGP,CLASSIC-STUN,DHCP,DIAMETER,DNS,DTLS,EIGRP,ESP,FTP,GRE,GTP,HTTP,IAX,IMAP,L2TP,LDAP,MEGACO,MGCP,MySQL,OSPF,POP,PPTP,RADIUS,RDP,RIP,RTP,RTSP,SIP,SMTP,SNMP,SSH,STUN,TDS,TELNET,TFTP,WXA,WXF

#tbl_limits          = 2000                                      # tbl 单文件条数上限，默认值2000

# field 输出目录
# 默认为 <app>/field
# field_dir = /root/program/field

# tbl 输出目录
# 默认为 <app>/tbls
# tbl_dir = /tmp/tbls/yaEty
# voip_tbl_dir = /tmp/voip_tbls

# content 输出目录
# 默认为 <app>/content
# content_dir = /tmp/tbls/voip

# dump 目录
# 默认值为 <app>/dump
# dump_dir = /tmp/tbls/yaEty/dump

# after process
del_pcap_after_process = 0                                      # 已经处理过的文件，是删除，还是对其添加后缀重命名
rename_suffix = .ext                                             # 如果 del_pcap_after_process 为零则将处理过的文件添加此后缀
rename_suffix_err = .err

# dump                                                           # 如果不需要将包汇聚重解，则以下项均需注释
# need_to_dump_proto_list  = RTP:rtp.ssrc                          # rtp 协议需要进行 dump, 并且按照 ssrc hash 分类到不同的输出文件；<协议>:<分类字段>，多类协议使用 ',' 分隔,
# filename_not_dump_suffix = .2tbl.pcap                            # 一定不进行 dump 的 pcap 文件后缀, 即使这些文件中遇到了要求进行 dump 的协议
# dump_to_dir = <out>                                              # dump 的输出目录，可用使用 <in> <out> 指代命令行上的输入与输出目录
# dump_classify_to_n_file = 10                                     # 每类协议的 dump 包根据分类字段被 hash 到若干个不同的输出文件中，
# dump_pkts_per_file = 8000                                        # 每个文件最多存放多少个 pkt；
