#+title: yaEty
* 概述
yaEty: ya Extract Transform the Young version, 阅维小型化解析系统。

yaEty 主要用于解析离线 pcap 数据包，产生 tbl 格式的元数据解析结果。

yaEty 基于 wireshark 解析引擎，由 wireshark 解析之后将其结果转换为与 yaDpi 相同的数据类型与格式。

* 安装依赖：
yumi install libpcap-devel libgcrypt-devel glib2-devel zlib-devel \
             bcg729-devel sbc-devel speex-devel ilbc-devel spandsp-devel \
             postgresql-devel

* 构建：
cd 05_编码
./build.sh

* 运行帮助：
** 准备运行环境：
   ./run/env.sh
** 解析 pcap 目录：
   ./run/yaEty -n <xx-pcap-dir> <tbl-out>
** 查看tbl 结果：
   ~/tools/tbl.sh -f <fields> xxx.tbl
